<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>fs-paas-appframework</artifactId>
        <groupId>com.facishare</groupId>
        <version>9.6.0-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>fs-paas-app-function</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-license</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-log</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-common</artifactId>
            <version>${project.version}</version>
        </dependency>

        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-function-service-api</artifactId>
            <version>${fs-paas-function.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>com.facishare</groupId>
                    <artifactId>fs-metadata-service</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>fs-paas-app-metadata-restdriver</artifactId>
                    <groupId>com.facishare</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.powermock</groupId>
                    <artifactId>powermock-api-mockito2</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.powermock</groupId>
                    <artifactId>powermock-api-support</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.powermock</groupId>
                    <artifactId>powermock-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-app-config</artifactId>
        </dependency>

        <dependency>
            <groupId>org.bitbucket.cowwoc</groupId>
            <artifactId>diff-match-patch</artifactId>
            <version>1.2</version>
        </dependency>

        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
            <version>1.1.6</version>
        </dependency>
        <!--       业务引入函数调用-->
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-paas-function-biz-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.facishare</groupId>
            <artifactId>fs-metadata-api</artifactId>
            <version>${fs-metadata-provider.version}</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
</project>