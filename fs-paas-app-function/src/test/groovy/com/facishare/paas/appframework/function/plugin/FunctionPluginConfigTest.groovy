package com.facishare.paas.appframework.function.plugin


import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Table
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class FunctionPluginConfigTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
    }

    def "测试getFunctionApiName方法正常获取功能API名称"() {
        given: "准备测试数据和实例"
        // 准备测试数据
        def functionDefinition = new FunctionPluginConfig.FunctionDefinition("TestModule", "TestType", "TestFunction")
        def functionPluginInfo = new FunctionPluginConfig.FunctionPluginInfo("TestObj", "12345", [functionDefinition])
        def functionPluginInfos = [functionPluginInfo]
        
        // 创建测试实例
        FunctionPluginConfig functionPluginConfig = new FunctionPluginConfig()
        
        and: "直接调用loadFunctionPluginConfig方法"
        Whitebox.invokeMethod(functionPluginConfig, "loadFunctionPluginConfig", functionPluginInfos)
        
        when: "调用getFunctionApiName方法"
        def result = functionPluginConfig.getFunctionApiName("12345", "TestObj", "TestModule", "TestType")
        
        then: "验证结果"
        result == "TestFunction"
    }
    
    def "测试getFunctionApiName方法找不到对应配置时返回null"() {
        given: "准备测试数据和实例"
        // 准备测试数据
        def functionDefinition = new FunctionPluginConfig.FunctionDefinition("TestModule", "TestType", "TestFunction")
        def functionPluginInfo = new FunctionPluginConfig.FunctionPluginInfo("TestObj", "12345", [functionDefinition])
        def functionPluginInfos = [functionPluginInfo]
        
        // 创建测试实例
        FunctionPluginConfig functionPluginConfig = new FunctionPluginConfig()
        
        and: "直接调用loadFunctionPluginConfig方法"
        Whitebox.invokeMethod(functionPluginConfig, "loadFunctionPluginConfig", functionPluginInfos)
        
        when: "调用getFunctionApiName方法使用不存在的参数"
        def result = functionPluginConfig.getFunctionApiName("54321", "NonExistObj", "NonExistModule", "NonExistType")
        
        then: "验证结果为null"
        result == null
    }
    
    def "测试generateKey方法"() {
        when: "调用generateKey方法"
        def result = FunctionPluginConfig.generateKey(*keys)
        
        then: "验证结果"
        result == expected
        
        where:
        keys                        || expected
        ["a", "b"]                  || "a_b"
        ["a", "b", "c"]             || "a_b_c"
        ["a", "", "c"]              || "a_c"
        ["", "b", ""]               || "b"
        ["a"]                       || "a"
        []                          || ""
    }
    
    def "测试loadFunctionPluginConfig方法"() {
        given: "准备测试数据"
        def functionDefinition = new FunctionPluginConfig.FunctionDefinition("TestModule", "TestType", "TestFunction")
        def functionPluginInfo = new FunctionPluginConfig.FunctionPluginInfo("TestObj", "12345", [functionDefinition])
        def functionPluginInfos = [functionPluginInfo]
        
        and: "创建测试实例"
        FunctionPluginConfig functionPluginConfig = new FunctionPluginConfig()
        
        when: "调用loadFunctionPluginConfig方法"
        Whitebox.invokeMethod(functionPluginConfig, "loadFunctionPluginConfig", functionPluginInfos)
        Table<String, String, FunctionPluginConfig.FunctionDefinition> table = 
            Whitebox.getInternalState(functionPluginConfig, "functionPluginDefinition")
        
        then: "验证结果"
        table.contains("12345_TestObj", "TestModule_TestType")
        table.get("12345_TestObj", "TestModule_TestType").getFunctionApiName() == "TestFunction"
    }
    
    def "测试FunctionPluginInfo类"() {
        given: "创建FunctionPluginInfo实例"
        def functionDefinition = new FunctionPluginConfig.FunctionDefinition("TestModule", "TestType", "TestFunction")
        def functionPluginInfo = new FunctionPluginConfig.FunctionPluginInfo("TestObj", "12345", [functionDefinition])
        
        when: "获取key"
        def key = functionPluginInfo.getKey()
        
        then: "验证结果"
        key == "12345_TestObj"
        functionPluginInfo.getFunctionDefinitions().size() == 1
        functionPluginInfo.getFunctionDefinitions()[0] == functionDefinition
    }
    
    def "测试FunctionDefinition类"() {
        given: "创建FunctionDefinition实例"
        def functionDefinition = new FunctionPluginConfig.FunctionDefinition("TestModule", "TestType", "TestFunction")
        
        when: "获取key和functionApiName"
        def key = functionDefinition.getKey()
        def functionApiName = functionDefinition.getFunctionApiName()
        
        then: "验证结果"
        key == "TestModule_TestType"
        functionApiName == "TestFunction"
    }
} 