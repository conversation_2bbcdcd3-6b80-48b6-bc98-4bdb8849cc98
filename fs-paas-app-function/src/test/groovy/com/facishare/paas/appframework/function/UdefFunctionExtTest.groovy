package com.facishare.paas.appframework.function

import com.facishare.function.Function
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.metadata.api.IUdefFunction
import com.facishare.paas.metadata.impl.UdefFunction
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

class UdefFunctionExtTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
    }

    def "test of method"() {
        given: "一个UdefFunction对象"
        IUdefFunction udefFunction = new UdefFunction()
        udefFunction.setApiName("testApiName")
        udefFunction.setTenantId("74255")
        udefFunction.setVersion(1)

        when: "调用of方法"
        UdefFunctionExt ext = UdefFunctionExt.of(udefFunction)

        then: "返回UdefFunctionExt对象并保留原始数据"
        ext != null
        ext.getApiName() == "testApiName"
        ext.getTenantId() == "74255"
        ext.getVersion() == 1
    }
    
    def "test of method with UdefFunctionExt"() {
        given: "一个UdefFunctionExt对象"
        IUdefFunction udefFunction = new UdefFunction()
        udefFunction.setApiName("testApiName")
        udefFunction.setTenantId("74255")
        udefFunction.setVersion(1)
        UdefFunctionExt original = UdefFunctionExt.of(udefFunction)
        
        when: "将UdefFunctionExt作为参数调用of方法"
        UdefFunctionExt ext = UdefFunctionExt.of(original)
        
        then: "返回的是原始UdefFunction对象的包装"
        ext != null
        ext.getApiName() == "testApiName"
        ext.getTenantId() == "74255"
        ext.getVersion() == 1
    }

    def "test toFunction"() {
        given: "一个完整的UdefFunction对象"
        IUdefFunction udefFunction = new UdefFunction()
        udefFunction.setId("123")
        udefFunction.setApiName("testApiName")
        udefFunction.setBindingObjectApiName("testBindingObject")
        udefFunction.setFunctionName("testFunction")
        udefFunction.setVersion(1)
        udefFunction.setReturnType("String")
        udefFunction.setBody("function test() { return 'test'; }")
        udefFunction.setTenantId("74255")
        udefFunction.setNameSpace("testNamespace")
        udefFunction.setDeveloperInfo("developer")
        udefFunction.setType("function")
        udefFunction.setLang(1)
        
        def parameter = FunctionVSCodeExt.Parameter.builder()
                .name("param1")
                .type("String")
                .defaultValue("default")
                .remark("remark")
                .build()
        udefFunction.setParameters([parameter.toJSON()])
        
        when: "调用toFunction方法"
        UdefFunctionExt ext = UdefFunctionExt.of(udefFunction)
        Function function = ext.toFunction()
        
        then: "返回Function对象，包含UdefFunction的所有信息"
        function != null
        function.getId() == "123"
        function.getApiName() == "testApiName"
        function.getBindingObjectAPIName() == "testBindingObject"
        function.getFunctionName() == "testFunction"
        function.getVersion() == 1
        function.getReturnType() == "String"
        function.getBody() == "function test() { return 'test'; }"
        function.getTenantId() == "74255"
        function.getNameSpace() == "testNamespace"
        function.getDeveloperInfo() == "developer"
        function.getType() == "function"
        function.getLang() == 1
        function.getParameters().size() == 1
        function.getParameters()[0].getName() == "param1"
    }

    def "test validation success"() {
        given: "一个有效的UdefFunction"
        IUdefFunction udefFunction = new UdefFunction()
        udefFunction.setApiName("testApiName")
        udefFunction.setTenantId("74255")
        udefFunction.setVersion(1)
        
        when: "调用validation方法"
        UdefFunctionExt ext = UdefFunctionExt.of(udefFunction)
        ext.validation()
        
        then: "不抛出异常"
        noExceptionThrown()
    }
    
    def "test validation with invalid data"() {
        given: "一个无效的UdefFunction"
        IUdefFunction udefFunction = new UdefFunction()
        udefFunction.setTenantId("74255") // 缺少ApiName
        udefFunction.setVersion(1)
        
        when: "调用validation方法"
        UdefFunctionExt ext = UdefFunctionExt.of(udefFunction)
        ext.validation()
        
        then: "抛出ValidateException异常"
        thrown(ValidateException)
    }
    
    def "test validateBodySize with small body"() {
        given: "一个小体积的body"
        IUdefFunction udefFunction = new UdefFunction()
        udefFunction.setBody("function test() { return 'test'; }")
        
        when: "调用validateBodySize方法"
        UdefFunctionExt ext = UdefFunctionExt.of(udefFunction)
        ext.validateBodySize()
        
        then: "不抛出异常"
        noExceptionThrown()
    }
    
    def "test validateBodySize with large body"() {
        given: "一个超大体积的body"
        IUdefFunction udefFunction = new UdefFunction()
        // 创建一个超过2MB的字符串
        StringBuilder largeBody = new StringBuilder()
        for (int i = 0; i < 1024 * 1024 + 1; i++) {
            largeBody.append("abcdefghij") // 每次添加10个字符
        }
        udefFunction.setBody(largeBody.toString())
        
        when: "调用validateBodySize方法"
        UdefFunctionExt ext = UdefFunctionExt.of(udefFunction)
        ext.validateBodySize()
        
        then: "抛出ValidateException异常"
        thrown(ValidateException)
    }
    
    def "test validateBodySize with null body"() {
        given: "body为null"
        IUdefFunction udefFunction = new UdefFunction()
        udefFunction.setBody(null)
        
        when: "调用validateBodySize方法"
        UdefFunctionExt ext = UdefFunctionExt.of(udefFunction)
        ext.validateBodySize()
        
        then: "不抛出异常"
        noExceptionThrown()
    }
} 