package com.facishare.paas.appframework.function.plugin;

import com.facishare.function.biz.api.exception.FunctionExecuteException;
import com.facishare.function.biz.api.model.FuncExecuteContext;
import com.facishare.function.biz.api.model.FuncUser;
import com.facishare.function.biz.api.service.FunctionService;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.facishare.paas.appframework.core.model.RequestContext.FS_DEVICE_TYPE;

/**
 * Created by zhaooju on 2022/7/5
 */
@Slf4j
@Service
public class FunctionPluginServiceImpl implements FunctionPluginService {
    @Autowired
    @Qualifier("bizFunctionService")
    private FunctionService functionService;

    @Override
    public <T> T executeFuncMethod(FunctionPluginContext<T> context, String functionApiName, String methodName) {
        if (Strings.isNullOrEmpty(functionApiName)) {
            return context.getResult("");
        }
        List<String> requestBody = context.getArg();
        try {
            String functionResult = functionService.executeFuncMethod(buildFuncExecuteContext(context), functionApiName, methodName, requestBody, 0);
            return context.getResult(functionResult);
        } catch (AppBusinessException e) {
            throw e;
        } catch (FunctionExecuteException e) {
            log.warn("function execute fail, ei:{}, functionApiName:{}, methodName:{} ", context.getTenantId(), functionApiName, methodName, e);
            throw new FunctionException(I18NExt.text(I18NKey.FUNC_FAIL));
        } catch (Exception e) {
            log.error("function execute error, ei:{}, functionApiName:{}, methodName:{} ", context.getTenantId(), functionApiName, methodName, e);
            throw new FunctionException(I18NExt.text(I18NKey.FUNC_FAIL));
        }
    }

    private FuncExecuteContext buildFuncExecuteContext(FunctionPluginContext context) {
        return Optional.ofNullable(RequestContextManager.getContext())
                .map(it -> {
                    FuncUser funcUser = new FuncUser();
                    funcUser.setUserId(context.getUserId());
                    funcUser.setTenantId(context.getTenantId());
                    User user = it.getUser();
                    funcUser.setOutUserId(user.getOutUserId());
                    funcUser.setOutTenantId(user.getOutTenantId());
                    funcUser.setUpstreamOwnerId(user.getUpstreamOwnerId());

                    FuncExecuteContext simpleContext = new FuncExecuteContext();
                    simpleContext.setUser(funcUser);
                    simpleContext.setAppId(it.getAppId());
                    simpleContext.setThirdAppId(it.getThirdAppId());
                    simpleContext.setThirdType(it.getThirdType());
                    simpleContext.setThirdUserId(it.getThirdUserId());

                    RequestContext requestContext = RequestContextManager.getContext();
                    if (Objects.nonNull(requestContext)) {
                        String deviceType = requestContext.getAttribute(FS_DEVICE_TYPE);
                        String clientInfo = requestContext.getClientInfo();
                        simpleContext.setDeviceType(deviceType);
                        simpleContext.setClientInfo(clientInfo);
                    }
                    return simpleContext;
                }).orElseGet(() -> FuncExecuteContext.getSimpleContext(context.getTenantId(), context.getUserId()));
    }

}
