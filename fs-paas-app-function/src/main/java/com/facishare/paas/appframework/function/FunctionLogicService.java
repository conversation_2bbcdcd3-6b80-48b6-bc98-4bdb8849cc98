package com.facishare.paas.appframework.function;

import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.dto.*;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * 函数逻辑服务
 * <p>
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 2018/2/3.
 */
public interface FunctionLogicService {
    /**
     * 创建函数
     *
     * @param user
     * @param function
     * @return
     */
    IUdefFunction createUDefFunction(User user, IUdefFunction function);

    IUdefFunction createFunctionByRest(User user, IUdefFunction function);

    /**
     * 更新函数
     *
     * @param user
     * @param function
     * @return
     */
    IUdefFunction updateUDefFunction(User user, IUdefFunction function);

    IUdefFunction updateFunctionByRest(User user, IUdefFunction function);

    /**
     * 执行函数
     * 给前验证逻辑使用
     *
     * @param user
     * @param function
     * @param parameters
     * @param data
     * @return
     */
    RunResult executeUDefFunction(
            User user, IUdefFunction function, Map<String, Object> parameters, IObjectData data, Map<String, List<IObjectData>> details);

    RunResult executeUDefFunction(
            User user, IUdefFunction function, Map<String, Object> parameters, IObjectData data, Map<String,
            List<IObjectData>> details, Map<String, Object> actionParam);

    RunResult executeUDefFunction(
            User user, IUdefFunction function, Map<String, Object> parameters, IObjectData data, Map<String,
            List<IObjectData>> details, Map<String, Object> actionParam, String actionStage);

    RunResult executeUDefFunction(User user, IUdefFunction function, Map<String, Object> parameters, IObjectData data,
                                  Map<String, List<IObjectData>> details, Map<String, List<IObjectData>> relatedDataList,
                                  Map<String, Object> actionParam, String actionStage);

    RunResult executeUDefFunction(User user, IUdefFunction function, Map<String, Object> parameters, IObjectData data,
                                  Map<String, List<IObjectData>> details, Map<String, List<IObjectData>> relatedDataList,
                                  Map<String, Object> actionParam, FuncBizExtendParam.Arg funcBizExtendParam);

    /**
     * 幂等逻辑执行
     *
     * @param context
     * @param apiName
     * @param bindingObjectAPIName
     * @param parameters
     * @param bindingObjectDataId
     * @param objectIds
     * @return
     */
    RunResult executeUDefIdempotent(
            RequestContext context, String apiName, String bindingObjectAPIName, List<?> parameters, String bindingObjectDataId, List<String> objectIds);


    /**
     * 函数控制器执行函数，这个接口对外提供，对调用函数的nameSpace 有限制
     *
     * @param context
     * @param apiName
     * @param parameters
     * @return
     */
    RunResult executeFunctionController(RequestContext context, String apiName, List<?> parameters);

    /**
     * 调试函数
     *
     * @param user
     * @param function
     * @return
     */
    DebugFunction.Result debugRunUDefFunction(User user, IUdefFunction function, Map<String, Object> inputData, String dataSource, String requestId);

    /**
     * 批量数据执行函数, 函数里面只有主对象的数据,这个只会调用一次函数
     *
     * @param function
     * @param objectIds
     * @param parameters
     * @return
     */
    BatchDataExecuteFunction.Result batchDataExecuteFunction(User user, IUdefFunction function, List<String> objectIds, Map<String, Object> parameters);

    batchDataExecuteTask.Result importPreProcessingFunction(FunctionServiceContext functionServiceContext,
                                                            List<IObjectData> objectDataList);

    /**
     * 查找函数
     *
     * @param user
     * @param apiName
     * @return
     */
    IUdefFunction findUDefFunction(User user, String apiName, String bindingObjectAPIName);

    /**
     * 查找某个函数版本
     *
     * @param user
     * @param apiName
     * @param bindingObjectAPIName
     * @param version
     * @return
     */
    IUdefFunction findFunctionByVersion(User user, String apiName, String bindingObjectAPIName, Integer version);

    List<IUdefFunction> queryFunctionVersions(
            User user, String apiName, String bindingObjectAPIName, List<Integer> versions, Integer updateBy, Long beginTime, Long endTime, Integer limit);

    String diffFunction(User user, String apiName, String bindingObjectAPIName, List<Integer> versions);

    /**
     * 编译检查
     *
     * @param user
     * @param function
     */
    void compile(User user, IUdefFunction function);

    /**
     * 根据查询函数列表
     *
     * @param user
     * @param map
     * @return
     */
    QueryResult queryUDefFunction(User user, Map<String, Object> map);

    /**
     * 是否禁用
     *
     * @param user
     * @param bindingObjectAPIName
     * @param apiName
     * @return
     */
    Boolean setIsActive(User user, String bindingObjectAPIName, String apiName, Boolean isActive);

    /**
     * 对象是否绑定函数
     *
     * @param user
     * @param nameSpace
     * @param returnType
     * @param bindingObjectAPIName
     * @return
     */
    Boolean funcIsExist(User user, List<String> nameSpace, List<String> returnType, String bindingObjectAPIName);

    /**
     * 删除函数
     *
     * @param user
     * @param apiName
     * @param bindingObjectAPIName
     */
    Boolean deleteUDefFunction(User user, String bindingObjectAPIName, String apiName);

    /**
     * 函数配额限制
     *
     * @param user
     */
    void checkFunctionCountLimit(User user);

    /*
     * 函数具体绑定组件的更新，对应具体流程和按钮
     * @param user
     * @param apiName
     * @param describeApiName
     * @param status
     * @param map(type(按钮还是流程),apiName，label)
     * */
    void setFunctionUsedAction(User user, String apiName, String describeApiName, String status, Map<String, Object> usedInfo);

    List<IUdefFunction> findFunctionByExample(User user, Map<String, Object> map);

    List<IUdefFunction> findFunctionByApiNames(User user, List<String> apiNames, String bindingObjectAPIName);

    /**
     * @param user
     * @param describeApiName
     * @param functionApiName
     * @param objectInfos
     * @return key 为 data index，value 为函数返回的 编号规则
     */
    Map<String, IncrementNumberBatchExecute.IncrementNumber> incrementNumberBatchExecute(User user, String describeApiName,
                                                                                         String functionApiName, List<IncrementNumberBatchExecute.ObjectInfo> objectInfos);

    /**
     * 检查函数的编辑是否需要验证
     */
    DeveloperVerificationResult checkDeveloperVerification(User user, String sessionId);

    /**
     * 开启函数编辑校验
     *
     * @param user
     * @param openTenantId
     */
    void enableDeveloperVerification(User user, String openTenantId);

    void disableDeveloperVerification(User user, String closeTenantId);

    String checkFunctionDevelopVerificationCode(User user, String areaCode, String phone, String smsCode);

    boolean isEnableFunctionDevelopVerification(User user);

    void saveRelation(User user, List<ReferenceData> list);

    void deleteAndCreateRelation(User user, List<ReferenceData> list);

    void deleteRelation(User user, String type, String value, String funcApiName);

    void batchDeleteRelation(User user, List<ReferenceData> list);

    List<ReferenceData> findRelationBySource(User user, String sourceType, String sourceValue);

    List<FunctionVSCodeExt> downloadFunctions(User user, String apiName, String bindingObjectApiName, String type, int pageNumber, int pageSize);

    long uploadFunction(User user, String apiName, String name, String content, String metaXml, String type, long updateTime,
                        String nameSpace, String returnType, String bindingObject, String describe, String commit);

    Optional<IUdefFunction> findFunctionByFuncRelationKey(User user, String funcRelationKeyType,
                                                          String funcRelationKeyValue, String bindingObjectAPIName);

    void clearUdefFunctionUsedInfo(User user, IUdefFunction function, String describeApiName, String usedApiName, String status);

    Analyze.Result analyze(User user, IUdefFunction uDefFunction);

    String getDetailForVSCode(User user, String apiName, String bindingObjectApiName);

    void processFunctionReference(IFieldDescribe fieldDescribe, String sourceType, String functionApiName, String bindingObjectApiName, boolean isClearFuncReference);

    Map<String, Object> handleFiltersByValueType(User user, String functionBindingObjectApiName, SearchTemplateQuery query, Supplier<Tuple<IObjectData, Map<String, List<IObjectData>>>> supplier);

    Map<String, Object> handleFiltersByValueType(User user, String functionBindingObjectApiName, SearchTemplateQuery query, Supplier<Tuple<IObjectData, Map<String, List<IObjectData>>>> supplier, FuncBizExtendParam.Arg funcBizExtendParam);

    RunResult findAndExecuteFunction(User user, Supplier<Tuple<IObjectData, Map<String, List<IObjectData>>>> supplier, String bindingObjectAPIName, String functionAPIName);

    @Data
    @Builder
    class FunctionServiceContext {
        private User user;
        private IUdefFunction function;
        private Map<String, Object> parameters;
        private String taskId;
        private Boolean finalBatch;
        private Boolean unionImport;
        private Map<String, String> fileCode;
    }
}
