package com.facishare.paas.appframework.function.dto;

import com.facishare.function.Function;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by fengjy in 2020/1/19 11:30
 */
public interface Compile {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg{
        Function function;
    }

    @Data
    class Result{
        boolean success;
        String errorMessage;
    }
}
