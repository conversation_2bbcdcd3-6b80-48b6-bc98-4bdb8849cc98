package com.facishare.paas.appframework.fcp;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.ContextManager;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * Created by zhouwr on 2017/10/31
 */
@Component
@FcpService("customAction")
public class ActionController {

    @Autowired
    private ActionLocateService actionLocateService;

    @Autowired
    private SerializerManager serializerManager;

    /**
     * 执行指定的action 
     *
     * @param arg
     * @return
     */
    @FcpMethod("call")
    public Object callAction(Arg arg) {
        ActionContext context = ContextManager.buildActionContext(arg.getObjectApiName(), arg.getAction());
        JSONSerializer serializer = serializerManager.getSerializer(context.getRequestContext().getContentType());
        String payload = serializer.encode(arg.getParams());
        Action action = actionLocateService.locateAction(context, payload);

        return action.act(action.getArg());
    }

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        private String action;

        @JSONField(name = "M2")
        private String objectApiName;

        @JSONField(name = "M3")
        private Map<String, Object> params;
    }

}
