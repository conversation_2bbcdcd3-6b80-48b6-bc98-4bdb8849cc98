package com.facishare.paas.appframework.fcp;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.predef.service.ObjectRecordTypeService;
import com.facishare.paas.appframework.core.predef.service.dto.recordType.*;
import com.facishare.paas.appframework.core.model.ContextManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by linqiuying on 17/10/18.
 */
@Component
@FcpService("recordType")
public class RecordTypeController {

  @Autowired
  private ObjectRecordTypeService recordTypeService;

  private final static String SERVICE_NAME = "record_type";
  /**
   * 查询角色列表
   */
  @FcpMethod("findRoleInfoList")
  public FindRoleInfoList.Result findRoleInfoList(FindRoleInfoList.Arg arg) {
    ServiceContext context = ContextManager.buildServiceContext(SERVICE_NAME,"findRoleInfoList");
    FindRoleInfoList.Result result = recordTypeService.findRoleInfoList(arg, context);
    return result;
  }

  /**
   * 业务类型详情
   */
  @FcpMethod("findRecordInfo")
  public FindRecordInfo.Result findRecordInfo(FindRecordInfo.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"findRecordInfo");
    FindRecordInfo.Result result = recordTypeService.findRecordInfo(arg, serviceContext);
    return result;
  }

  /**
   * 获取layout列表和角色列表
   */
  @FcpMethod("findRoleAndLayout")
  public FindRoleAndLayout.Result findRoleAndLayout(FindRoleAndLayout.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"findRoleAndLayout");
    FindRoleAndLayout.Result result = recordTypeService.findRoleAndLayout(arg, serviceContext);
    return result;
  }

  /**
   * 新建recordType，并分配角色和layout
   */
  @FcpMethod("createRecordType")
  public CreateRecordType.Result createRecordType(CreateRecordType.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"create");
    CreateRecordType.Result result = recordTypeService.createRecordType(arg, serviceContext);
    return result;
  }

  /**
   * 查询角色和类型关系
   */
  @FcpMethod("findRoleAndRecordType")
  public Object findRoleAndRecordType(FindRoleAndRecordType.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"findRoleAndRecordType");
    Object result = recordTypeService.findRoleAndRecordType(arg, serviceContext);
    return result;
  }

  /**
   * 分配业务类型－已经启用的record
   */
  @FcpMethod("assignRecord")
  public AssignRecord.Result assignRecord(AssignRecord.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"assignRecord");
    AssignRecord.Result result = recordTypeService.assignRecord(arg, serviceContext);
    return result;
  }

  /**
   * 查询指定对象下业务类型列表
   */
  @FcpMethod("findRecordTypeList")
  public FindRecordTypeList.Result findRecordTypeList(FindRecordTypeList.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"findRecordTypeList");
    FindRecordTypeList.Result result = recordTypeService.findRecordTypeList(arg, serviceContext);
    return result;
  }

  /**
   * 业务类型启用
   */
  @FcpMethod("enableRecordType")
  public EnableRecordType.Result enableRecordType(EnableRecordType.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"enableRecordType");
    EnableRecordType.Result result = recordTypeService.enableRecordType(arg, serviceContext);
    return result;
  }

  /**
   * 业务类型禁用
   */
  @FcpMethod("disableRecordType")
  public DisableRecordType.Result disableRecordType(DisableRecordType.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"disableRecordType");
    DisableRecordType.Result result = recordTypeService.disableRecordType(arg, serviceContext);
    return result;
  }

  /**
   * 业务类型删除
   */
  @FcpMethod("deleteRecordType")
  public DeleteRecordType.Result deleteRecordType(DeleteRecordType.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"deleteRecordType");
    DeleteRecordType.Result result = recordTypeService.deleteRecordType(arg, serviceContext);
    return result;
  }

  /**
   * 修改业务类型（只修改基本信息）
   */
  @FcpMethod("updateRecordType")
  public UpdateRecordType.Result updateRecordType(UpdateRecordType.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"updateRecordType");
    UpdateRecordType.Result result = recordTypeService.updateRecordType(arg, serviceContext);
    return result;
  }


  /**
   * 根据业务类型查询布局
   */
  @FcpMethod("findLayoutByRecordType")
  public FindLayoutByRecordType.Result findLayoutByRecordType(FindLayoutByRecordType.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"findLayoutByRecordType");
    FindLayoutByRecordType.Result result = recordTypeService.findLayoutByRecordType(arg, serviceContext);
    return result;
  }

  /**
   * 查询布局分配情况
   */
  @FcpMethod("findAssignedLayout")
  public FindAssignedLayout.Result findAssignedLayout(FindAssignedLayout.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"findAssignedLayout");
    FindAssignedLayout.Result result = recordTypeService.findAssignedLayout(arg, serviceContext);
    return result;
  }

  /**
   * 保存分配布局
   */
  @FcpMethod("saveLayoutAssign")
  public SaveLayoutAssign.Result saveLayoutAssign(SaveLayoutAssign.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"saveLayoutAssign");
    SaveLayoutAssign.Result result = recordTypeService.saveLayoutAssign(arg, serviceContext);
    return result;
  }

  /**
   * 保存分配布局
   */
  @FcpMethod("findValidRecordTypeList")
  public FindValidRecordTypeList.Result findValidRecordTypeList(FindValidRecordTypeList.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"findValidRecordTypeList");
    FindValidRecordTypeList.Result result = recordTypeService.findValidRecordTypeList(arg, serviceContext);
    return result;
  }

  /**
   * 批量查询可用的业务类型
   */
  @FcpMethod("bulkFindRecordTypeList")
  public BulkFindRecordTypeList.Result bulkFindRecordTypeList(BulkFindRecordTypeList.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"bulkFindRecordTypeList");
    BulkFindRecordTypeList.Result result = recordTypeService.bulkFindRecordTypeList(arg, serviceContext);
    return result;
  }

  /**
   * 批量查询可用的业务类型
   */
  @FcpMethod("bulkFindValidRecordTypeList")
  public BulkFindRecordTypeList.Result bulkFindValidRecordTypeList(BulkFindRecordTypeList.Arg arg) {
    ServiceContext serviceContext = ContextManager.buildServiceContext(SERVICE_NAME,"bulkFindRecordTypeList");
    BulkFindRecordTypeList.Result result = recordTypeService.bulkFindValidRecordTypeList(arg, serviceContext);
    return result;
  }
}
