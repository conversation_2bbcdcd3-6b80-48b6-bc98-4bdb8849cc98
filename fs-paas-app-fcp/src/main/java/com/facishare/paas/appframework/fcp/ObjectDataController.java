package com.facishare.paas.appframework.fcp;

import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.appframework.core.model.ContextManager;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by zhouwr on 2017/11/8
 */
@Component
@FcpService("objectData")
public class ObjectDataController {

    @Autowired
    private ServiceFacade serviceFacade;

    @FcpMethod("invalid")
    public StandardInvalidAction.Result invalid(StandardInvalidAction.Arg arg) {
        ActionContext context = ContextManager.buildActionContext(arg.getObjectDescribeApiName(), StandardAction.Invalid.name());
        return serviceFacade.triggerAction(context, arg, StandardInvalidAction.Result.class);
    }

    @FcpMethod("bulkInvalid")
    public StandardBulkInvalidAction.Result bulkInvalid(StandardBulkInvalidAction.Arg arg) {
        Document document = Document.parse(arg.getJson());
        List<Document> dataList = document.get("dataList", List.class);
        if (CollectionUtils.empty(dataList)) {
            return StandardBulkInvalidAction.Result.builder().build();
        }
        String apiName = dataList.get(0).getString("object_describe_api_name");
        ActionContext context = ContextManager.buildActionContext(apiName, StandardAction.BulkInvalid.name());
        return serviceFacade.triggerAction(context, arg, StandardBulkInvalidAction.Result.class);
    }
}
