package com.facishare.paas.appframework.metadata.restdriver

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.metadata.api.INameCache
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.Order
import com.facishare.paas.metadata.api.action.ActionContext
import com.facishare.paas.metadata.api.action.IActionContext
import com.facishare.paas.metadata.api.condition.TermConditions
import com.facishare.paas.metadata.impl.search.SearchQuery
import com.google.common.collect.Lists
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.test.context.ContextConfiguration
import spock.lang.Specification

import java.util.stream.Collectors
/**
 * Created by liyiguang on 2017/9/18.
 */
//@ContextConfiguration(value = "classpath:applicationContext.xml")
class CRMRemoteServiceTest extends Specification {
    static {
        System.setProperty("spring.profiles.active", "ceshi113")
    }

    @Autowired
    CRMRemoteService crmRemoteService

    def setup() {
        RequestContextManager.setContext(RequestContext.builder().build())
    }

    def "test findObjectDataByIds"() {
        when:
        IActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(tenantId);
        actionContext.setUserId(userId);
        List<IObjectData> ret = crmRemoteService.findObjectDataByIds(actionContext, apiName, ids)
        then:
        ret.size() == 1

        def data = ret.get(0)
        println data.toJsonString()
        where:
        tenantId | userId | apiName      | ids
        "2"      | "1000" | "AccountObj" | Lists.asList("0076b6e65b9e424dae4d7972f171ffeb")
    }
    def "test findObjectDataById"() {
        when:
        IActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(tenantId);
        actionContext.setUserId(userId);
        IObjectData ret = crmRemoteService.findObjectDataById(actionContext, apiName, id)
        then:
        ret != null
        println ret.toJsonString()
        where:
        tenantId | userId | apiName      | id
        "2"      | "1000" | "AccountObj" | "0076b6e65b9e424dae4d7972f171ffeb"
    }

    def "test findBySearchQuery"() {
        when:
        def searchQuery=JSON.parseObject(data,SearchQuery.class);
        searchQuery.setOrders(new ArrayList<Order>())
//        searchQuery.getOrders().add(new Order("name",true))
        TermConditions termConditions = new TermConditions()
//        termConditions.addCondition("is_deleted","1000")
        searchQuery.addCondition(termConditions)

        IActionContext actionContext = new ActionContext();
        actionContext.setEnterpriseId(tenantId);
        actionContext.setUserId(userId);
        def ret = crmRemoteService.findBySearchQuery(actionContext, apiName,searchQuery )
        then:
        ret != null
        println ret.toJsonString()
        where:
        tenantId | userId | apiName      | data
        "2"      | "1000" | "SalesOrderObj" | "{\"offset\": 0, \"limit\": 10, \"orders\": [{\"field\": \"Name\", \"ascending\": true } ], \"conditions\": [{\"conditionType\": \"term_condition\", \"Conditions\": {\"name\":\"自行车vvbc\"} } ] }"
    }

    def "test invalid"() {
        when:
//        def ret = crmRemoteService.invalid(tenantId, userId, apiName, id)
//        def ret = crmRemoteService.recover(tenantId, userId, apiName, id)
        def ret = crmRemoteService.delete(tenantId, userId, apiName, id)
        then:
        ret != null
        println ret
        where:
        tenantId | userId | apiName      | id
        "2"      | "1000" | "AccountObj" | "290e2963776b44d3b83aa97312549cdc"
    }
    def "test bulkInvalid"() {
        when:
//        def ret = crmRemoteService.bulkInvalid(tenantId, userId, apiName, ids.stream().collect(Collectors.toSet()))
//        def ret = crmRemoteService.bulkRecover(tenantId, userId, apiName, ids.stream().collect(Collectors.toSet()))
        def ret = crmRemoteService.bulkDelete(tenantId, userId, apiName, ids.stream().collect(Collectors.toSet()))
        then:
        ret != null
        println ret
        where:
        tenantId | userId | apiName      | ids
        "2"      | "1000" | "AccountObj" | Lists.asList("1325d1db93934e31969ddd2c54342c74")
    }

    def "test findRecordName"() {
        when:
        List<INameCache> ret = crmRemoteService.findRecordName(tenantId,userId,apiName,ids)
        then:
        ret != null
        println ret.toListString()
        where:
        tenantId | userId | apiName      | ids
        "2"      | "1000" | "AccountObj" |  Lists.asList("0076b6e65b9e424dae4d7972f171ffeb")
    }
}
