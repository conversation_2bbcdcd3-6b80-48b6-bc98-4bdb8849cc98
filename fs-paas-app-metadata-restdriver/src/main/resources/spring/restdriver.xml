<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <import resource="classpath:spring/common.xml"></import>

    <bean id="crmRemoteServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.restdriver.CRMRemoteServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="newCrmRemoteServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.restdriver.NewCRMRemoteServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="crmTenantMetadataProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.restdriver.TenantMetadataServiceProxy">
        <property name="factory" ref="restServiceProxyFactory"></property>
    </bean>

    <bean id="industryEnterInfoServiceProxy" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.paas.appframework.metadata.restdriver.IndustryEnterInfoServiceProxy"
          p:factory-ref="restServiceProxyFactory"/>
</beans>