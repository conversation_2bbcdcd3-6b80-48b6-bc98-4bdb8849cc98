package com.facishare.paas.appframework.core.util;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.rest.InnerHeaders;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import java.util.Optional;

import static com.facishare.paas.appframework.core.model.RequestContext.*;

/**
 * Created by zhouwr on 2018/2/27
 */
public abstract class RequestUtil {
    private static final long BASE_VALUE = 100000000L;
    public static final String VERSION_635 = "635000";
    public static final String VERSION_650 = "650000";
    public static final String VERSION_660 = "660000";
    public static final String VERSION_670 = "670000";
    public static final String VERSION_673 = "673000";
    public static final String VERSION_675 = "675000";
    public static final String VERSION_680 = "680000";
    public static final String VERSION_700 = "700000";
    public static final String VERSION_705 = "705000";
    public static final String VERSION_710 = "710000";
    public static final String VERSION_715 = "715000";
    public static final String VERSION_720 = "720000";
    public static final String VERSION_725 = "725000";
    public static final String VERSION_730 = "730000";
    public static final String VERSION_735 = "735000";
    public static final String VERSION_740 = "740000";
    public static final String VERSION_745 = "745000";
    public static final String VERSION_750 = "750000";
    public static final String VERSION_765 = "765000";
    public static final String VERSION_770 = "770000";
    public static final String VERSION_780 = "780000";

    public static final String VERSION_875 = "875000";
    private static final String APP_NAME = ConfigHelper.getProcessInfo().getName();

    public static boolean isSystemTenant(String tenantId) {
        return "-100".equals(tenantId) || "-101".equals(tenantId);
    }

    public static Lang getCurrentLang() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return Lang.zh_CN;
        }
        return context.getLang() == null ? Lang.zh_CN : context.getLang();
    }

    public static String getLanguageTag() {
        return getCurrentLang().getValue();
    }

    public static boolean isInnerRequest() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return context.getRequestSource() == RequestContext.RequestSource.INNER;
    }

    public static boolean isCepRequest() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return context.getRequestSource() == RequestContext.RequestSource.CEP;
    }

    public static boolean isOriginalCepRequest() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return RequestSource.CEP.name().equals(context.getAttribute(InnerHeaders.ORIGINAL_REQUEST_SOURCE));
    }

    public static boolean needParamsIdempotent() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return isCepRequest() || (!isCepRequest() && context.isParamsIdempotent());
    }

    public static boolean isWebRequest() {
        return !isMobileRequest();
    }

    public static boolean isMobileRequest() {

        return isAndroidRequest() || isIOSRequest();
    }

    /**
     * @return 微信小程序
     */
    public static boolean isWXMiniProgram() {
        String clientInfo = getClientInfo();
        return WEB_WX_MINI_PROGRAM.equalsIgnoreCase(clientInfo);
    }

    public static boolean isWXWebRequest() {
        if (isMobileDeviceRequest()) {
            return false;
        }
        String clientInfo = getClientInfo();
        if (Strings.isNullOrEmpty(clientInfo)) {
            return false;
        }
        return clientInfo.contains(WEB_WX_CLIENT_INFO_PREFIX);
    }

    public static boolean isMobileWXWorkRequest() {
        if (!isMobileDeviceRequest()) {
            return false;
        }
        String clientInfo = getClientInfo();
        if (Strings.isNullOrEmpty(clientInfo)) {
            return false;
        }
        return clientInfo.contains(WEB_WX_WORK_CLIENT_INFO_PREFIX);
    }

    public static boolean isAndroidRequest() {
        String clientInfo = getClientInfo();
        if (Strings.isNullOrEmpty(clientInfo)) {
            return false;
        }
        return clientInfo.contains(Android_CLIENT_INFO_PREFIX);
    }

    public static boolean isIOSRequest() {
        String clientInfo = getClientInfo();
        if (Strings.isNullOrEmpty(clientInfo)) {
            return false;
        }
        return clientInfo.contains(IOS_CLIENT_INFO_PREFIX);
    }

    public static String getClientInfo() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return null;
        }
        return context.getClientInfo();
    }

    /**
     * {@link #isH5MobileRequest()}
     *
     * @return
     */
    @Deprecated
    public static boolean isH5Request() {
        RequestContext context = RequestContextManager.getContext();
        // 该灰度存在问题,对于没有适配的接口全都识别为 web,后续考虑使用 isMobileDeviceRequest 替换
        if (Objects.nonNull(context) && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.H5_REQUEST_SOURCE, context.getTenantId())) {
            return !Strings.isNullOrEmpty(context.getAttribute(UdobjGrayConfigKey.H5_REQUEST_SOURCE));
        }
        return _isH5Request();
    }

    public static boolean isH5MobileRequest() {
        // h5请求的判断逻辑灰度
        String tenantId = Optional.ofNullable(RequestContextManager.getContext())
                .map(RequestContext::getTenantId)
                .orElse(null);
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.H5_MOBILE_REQUEST_LOGIC_GRAY, tenantId)) {
            if (isMobileRequest()) {
                return false;
            }
            if (isMobileDeviceRequest()) {
                return true;
            }
            if (isWXWebRequest()) {
                return false;
            }
            return _isH5Request();
        }
        return _isH5Request() && isMobileDeviceRequest();
    }

    private static boolean _isH5Request() {
        String clientInfo = getClientInfo();
        if (Strings.isNullOrEmpty(clientInfo)) {
            return false;
        }

        return clientInfo.contains(WEB_CLOUD_HUB_CLIENT_INFO_PREFIX)
                || clientInfo.contains(WEB_WX_CLIENT_INFO_PREFIX)
                || clientInfo.contains(WEB_FS_BROWSER);
    }

    public static boolean isMobileDeviceRequest() {
        RequestContext context = RequestContextManager.getContext();
        if (Objects.isNull(context)) {
            return false;
        }
        Object deviceType = context.getAttribute(FS_DEVICE_TYPE);
        return FS_DEVICE_TYPE_MOBILE.equals(deviceType);
    }
    public static boolean isMobileOrH5Request() {
        return isH5MobileRequest() || isMobileRequest();
    }

    public static boolean isMobileOrMobileDeviceRequest() {
        return isMobileRequest() || isMobileDeviceRequest();
    }

    public static boolean isMobileRequestBeforeVersion(String version) {
        if (Strings.isNullOrEmpty(version)) {
            return false;
        }
        String appVersion = getAppVersion();
        if (Strings.isNullOrEmpty(appVersion)) {
            return false;
        }
        if (appVersion.length() < version.length()) {
            return true;
        }
        return appVersion.compareTo(version) < 0;
    }

    public static String getAppVersion() {
        if (!isMobileRequest()) {
            return null;
        }
        String clientInfo = getClientInfo();
        if (Strings.isNullOrEmpty(clientInfo)) {
            return null;
        }
        String appVersion = StringUtils.substringAfter(clientInfo, ".");
        long longValue = NumberUtils.toLong(appVersion);
        if (longValue > BASE_VALUE) {
            return String.valueOf(longValue - BASE_VALUE);
        }
        return appVersion;
    }

    public static boolean isOpenAPIRequest() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return context.isFromOpenAPI();
    }

    public static String decode(String s, String enc) {
        if (Strings.isNullOrEmpty(s)) {
            return s;
        }
        try {
            return URLDecoder.decode(s, enc);
        } catch (Exception e) {

        }
        return null;
    }

    public static String encode(String s) {
        if (Strings.isNullOrEmpty(s)) {
            return s;
        }
        try {
            return URLEncoder.encode(s, StandardCharsets.UTF_8.name());
        } catch (UnsupportedEncodingException e) {

        }
        return null;
    }

    public static boolean isBatch() {
        if (RequestContextManager.getContext() != null) {
            return RequestContextManager.getContext().isBatch();
        }
        return true;
    }

    public static String getPeerName() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return null;
        }
        if (!Strings.isNullOrEmpty(context.getPeerName())) {
            return context.getPeerName();
        }
        if (context.getBizInfo() != null) {
            return context.getBizInfo().getBiz();
        }
        return null;
    }

    public static BizInfo getBizInfo() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return null;
        }
        return context.getBizInfo();
    }

    public static String getBizId() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return null;
        }
        if (context.getBizInfo() != null) {
            return context.getBizInfo().getBizId();
        }
        return context.getBizId();
    }

    public static String getOtherBizId() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return null;
        }
        if (context.getBizInfo() != null) {
            return context.getBizInfo().getOtherBizId();
        }
        return null;
    }

    public static boolean isUseSnapshotForApproval() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return Boolean.TRUE.equals(context.getAttribute(Attributes.USE_SNAPSHOT_FOR_APPROVAL));
    }

    public static boolean isModifyLogHidden() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return Boolean.TRUE.equals(context.getAttribute(Attributes.MODIFY_LOG_HIDDEN));
    }

    public static void setModifyLogHidden() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return;
        }
        context.setAttribute(RequestContext.Attributes.MODIFY_LOG_HIDDEN, true);
    }

    public static void setSerializeEmptyFalse() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return;
        }
        ContentType contentType = context.getContentType();
        if (contentType == null) {
            return;
        }
        context.setContentType(contentType.convertToNonnullType());
    }

    public static String getAppId() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return null;
        }
        return context.getAppId();
    }

    public static String getEventId() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return null;
        }
        return context.getEventId();
    }

    public static String getOutIdentityType() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return null;
        }
        return context.getOutIdentityType();
    }

    public static void setEsRecentUpdate() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return;
        }
        context.setAttribute(RequestContext.Attributes.ES_RECENT_UPDATE, true);
    }

    public static Boolean setRecordCalculateLog(Boolean recordCalculateLog) {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return null;
        }
        Boolean oldValue = context.getAttribute(Attributes.RECORD_CALCULATE_LOG);
        if (Objects.isNull(recordCalculateLog)) {
            context.removeAttribute(Attributes.RECORD_CALCULATE_LOG);
        } else {
            context.setAttribute(Attributes.RECORD_CALCULATE_LOG, recordCalculateLog);
        }
        return oldValue;
    }

    public static boolean isCalculateLogEnable() {
        //debug模式默认打印计算日志
        if (isDebugMode()) {
            return true;
        }
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return !Boolean.FALSE.equals(context.getAttribute(Attributes.RECORD_CALCULATE_LOG));
    }

    public static boolean skipRelevantTeam() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return Boolean.TRUE.equals(context.getAttribute(Attributes.SKIP_RELEVANT_TEAM));
    }

    public static boolean skipInternationalInitContext() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return Boolean.TRUE.equals(context.getAttribute(Attributes.SKIP_INIT_INTERNATIONAL_CONTEXT));
    }

    public static boolean isFunctionIdempotent() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return Boolean.TRUE.equals(context.getAttribute(Attributes.FUNCTION_IDEMPOTENT));
    }

    public static boolean isCalculateContext() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return Boolean.TRUE.equals(context.getAttribute(Attributes.IS_CALCULATE_CONTEXT));
    }

    public static boolean isDebugMode() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return Boolean.TRUE.equals(context.getAttribute(Attributes.DEBUG));
    }

    public static boolean isFromFunction() {
        if (Objects.nonNull(APP_NAME) && APP_NAME.contains("function")) {
            return true;
        }
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return context.isFromFunction();
    }

    public static boolean isActionRequest() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return !Strings.isNullOrEmpty(context.getAttribute(Attributes.ACTION_CODE));
    }

    public static String buildSeriesKey(String seriesId, String tenantId) {
        return "series|" + seriesId + "|" + tenantId;
    }

    public static boolean isFromManage() {
        RequestContext context = RequestContextManager.getContext();
        if (context == null) {
            return false;
        }
        return context.isFromManage();
    }

    public static String getCustomHeader(String headerName) {
        if (StringUtils.isBlank(headerName)) {
            return "";
        }
        return Objects.isNull(RequestContextManager.getContext()) ?
                "" : Strings.nullToEmpty(RequestContextManager.getContext().getAttribute(headerName));
    }

}
