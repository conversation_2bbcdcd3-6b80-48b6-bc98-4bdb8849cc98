package com.facishare.paas.appframework.core.util;

import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;
import com.fxiaoke.release.GrayRule;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Map;
import java.util.Objects;

/**
 * create by z<PERSON><PERSON> on 2021/10/25
 */
public enum UdobjGrayConfig {

    INSTANCE,
    ;
    public static final String UDOBJ = UdobjGrayConfigKey.UDOBJ;
    public static final String DEFAULT = "default";
    private static final Joiner KEY_JOINER = Joiner.on("^");

    /**
     * configName: fs-gray-udobj
     */
    private final FsGrayReleaseBiz UDOBJ_GRAY = FsGrayRelease.getInstance(UdobjGrayConfigKey.UDOBJ);

    public static boolean isAllow(String rule, String euid) {
        return INSTANCE.UDOBJ_GRAY.isAllow(rule, euid);
    }

    public static boolean isAllowByKeys(String[] keys, String value) {
        String key = KEY_JOINER.join(Arrays.asList(keys));
        return isAllow(key, value);
    }

    public static boolean isAllowByGrayRule(GrayRule grayRule, String euid) {
        if (Objects.isNull(grayRule) || StringUtils.isBlank(euid)) {
            return false;
        }
        return grayRule.isAllow(euid);
    }

    public static boolean isGrayWithDescribeApiName(Map<String, GrayRule> grayRuleMap, String tenantId,
                                                    String objectApiName, boolean supportDefault) {
        if (Objects.isNull(grayRuleMap) || grayRuleMap.isEmpty()) {
            return false;
        }
        GrayRule grayRule = getWithDescribeApiName(grayRuleMap, objectApiName, supportDefault);
        return Objects.nonNull(grayRule) && grayRule.isAllow(tenantId);
    }

    public static <T> T getWithDescribeApiName(Map<String, T> paramMap, String objectApiName, boolean supportDefault) {
        if (Objects.isNull(paramMap) || paramMap.isEmpty()) {
            return null;
        }
        //优先匹配objectApiName
        T item = paramMap.get(objectApiName);
        //其次自定义对象匹配udobj
        if (Objects.isNull(item) && isCustomObject(objectApiName)) {
            item = paramMap.get(UDOBJ);
        }
        //最后匹配default
        if (Objects.isNull(item) && supportDefault) {
            item = paramMap.get(DEFAULT);
        }
        return item;
    }

    public static Map<String, Map<String, GrayRule>> parseGrayRules(Map<String, Map<String, String>> grayRuleValueMap) {
        Map<String, Map<String, GrayRule>> newGrayRules = Maps.newHashMap();
        if (Objects.isNull(grayRuleValueMap)) {
            return newGrayRules;
        }
        grayRuleValueMap.forEach((firstLevelKeys, ruleValues) -> {
            if (Objects.isNull(ruleValues)) {
                return;
            }
            Map<String, GrayRule> ruleMap = parseGrayRulesWithMap(ruleValues);
            Arrays.stream(splitKeyWithVertical(firstLevelKeys)).forEach(firstLevelKey ->
                    newGrayRules.put(firstLevelKey, ruleMap));
        });
        return newGrayRules;
    }

    public static Map<String, GrayRule> parseGrayRulesWithMap(Map<String, String> ruleValues) {
        Map<String, GrayRule> ruleMap = Maps.newHashMap();
        ruleValues.forEach((secondLevelKeys, ruleValue) -> {
            if (Strings.isNullOrEmpty(ruleValue)) {
                return;
            }
            Arrays.stream(splitKeyWithVertical(secondLevelKeys)).forEach(secondLevelKey ->
                    ruleMap.put(secondLevelKey, new GrayRule(ruleValue)));
        });
        return ruleMap;
    }

    private static String[] splitKeyWithVertical(String key) {
        return StringUtils.split(key, "|");
    }

    public static boolean isCustomObject(String objectApiName) {
        return StringUtils.endsWith(objectApiName, "__c");
    }

}
