package com.facishare.paas.appframework.core.model;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.experimental.Delegate;

/**
 * Action请求上线问
 *
 * Created by liyiguang on 2017/6/17.
 */
@AllArgsConstructor
@Getter
public class ActionContext {
    @Delegate
    private final RequestContext requestContext;
    private final String objectApiName;
    private final String actionCode;
}
