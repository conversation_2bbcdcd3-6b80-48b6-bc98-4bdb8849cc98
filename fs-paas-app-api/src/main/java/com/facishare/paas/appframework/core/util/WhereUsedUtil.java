package com.facishare.paas.appframework.core.util;

/**
 *
 * <AUTHOR>
 */
public class WhereUsedUtil {

    public enum SOURCE_MATCH_TYPE {
        EQ, START_WITH;
    }

    public enum TARGET_MATCH_TYPE {
        EQ, START_WITH;
    }

    public static final String REF_DEFINE_TYPE = "where_used";

    public static final String REF_TYPE_DELIMITER_SOURCE_DISPLAY_TYPE = "#";

    public static final String SOURCE_VALUE_DELIMITER = ".";

    public static final String SOURCE_LABEL_DELIMITER = "#%$";

    public static final String TARGET_VALUE_DELIMITER = ".";

    public static final String TARGET_LABEL_DELIMITER = "#%$";

    public static final String API_WRAPPER_PREFIX = "(";

    public static final String API_WRAPPER_SUFFIX = ")";


    public static String buildSourceValue(String... values) {
        return String.join(SOURCE_VALUE_DELIMITER, values);
    }

    public static String buildSourceLabel(String... labels) {
        return String.join(SOURCE_LABEL_DELIMITER, labels);
    }

    public static String buildTargetValue(String... values) {
        return String.join(TARGET_VALUE_DELIMITER, values);
    }

    public static String buildTargetLabel(String... labels) {
        return String.join(TARGET_LABEL_DELIMITER, labels);
    }



}
