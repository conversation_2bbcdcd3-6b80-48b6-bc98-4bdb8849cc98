package com.facishare.paas.appframework.core.rest;

import lombok.*;

@EqualsAndHashCode(callSuper = true)
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString(callSuper = true)
public class APIResult<T> extends BaseAPIResult {
    private T data;

    public static <T> APIResult<T> success(T result) {
        APIResult apiResult = new APIResult();
        apiResult.setCode(SUCCESS_CODE);
        apiResult.setMessage(OK);
        apiResult.setData(result);
        return apiResult;
    }

    public static APIResult fail(int errCode, String errMessage) {
        APIResult apiResult = new APIResult();
        apiResult.setCode(errCode);
        apiResult.setMessage(errMessage);
        return apiResult;
    }
}
