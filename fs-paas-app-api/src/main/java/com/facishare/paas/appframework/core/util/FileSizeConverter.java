package com.facishare.paas.appframework.core.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;

public class FileSizeConverter {
    private static final long KILOBYTE = 1024;
    private static final long MEGABYTE = KILOBYTE * 1024;
    private static final long GIGABYTE = MEGABYTE * 1024;
    private static final long TERABYTE = GIGABYTE * 1024;

    public static String convertFileSize(long fileSize) {
        DecimalFormat decimalFormat = new DecimalFormat("#.##");
        BigDecimal bigDecimal = new BigDecimal(fileSize);
        if (fileSize < KILOBYTE) {
            return decimalFormat.format(fileSize) + "B";
        } else if (fileSize < MEGABYTE) {
            return decimalFormat.format(bigDecimal.divide(BigDecimal.valueOf(KILOBYTE), 2, RoundingMode.HALF_UP)) + "KB";
        } else if (fileSize < GIGABYTE) {
            return decimalFormat.format(bigDecimal.divide(BigDecimal.valueOf(MEGABYTE), 2, RoundingMode.HALF_UP)) + "MB";
        } else if (fileSize < TERABYTE) {
            return decimalFormat.format(bigDecimal.divide(BigDecimal.valueOf(GIGABYTE), 2, RoundingMode.HALF_UP)) + "GB";
        } else {
            return decimalFormat.format(bigDecimal.divide(BigDecimal.valueOf(TERABYTE), 2, RoundingMode.HALF_UP)) + "TB";
        }
    }


    public static void main(String[] args) {
        System.out.println(convertFileSize(1087 * 1024 * 1024 * 1024L));
    }
}