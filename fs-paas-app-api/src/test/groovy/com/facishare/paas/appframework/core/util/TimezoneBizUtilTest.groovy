package com.facishare.paas.appframework.core.util


import com.facishare.paas.timezone.config.TimeZoneConfig
import spock.lang.Specification
import spock.lang.Unroll

class TimezoneBizUtilTest extends Specification {

    def setup() {
        // 不需要特别设置时区
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipTimezoneBusinessProcess方法在非CEP请求时的行为
     */
    @Unroll
    def "skipTimezoneBusinessProcess_非CEP请求时返回true"() {
        given:
        RequestUtil.isCepRequest() >> false

        when:
        def result = TimezoneBizUtil.skipTimezoneBusinessProcess("testTenant")

        then:
        result == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipTimezoneBusinessProcess方法在不需要转换时区时的行为
     */
    @Unroll
    def "skipTimezoneBusinessProcess_不需要转换时区时返回true"() {
        given:
        RequestUtil.isCepRequest() >> true
        TimeZoneConfig.INSTANCE.isGray(_) >>> false

        when:
        def result = TimezoneBizUtil.skipTimezoneBusinessProcess("testTenant")

        then:
        result == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipTimezoneBusinessProcess方法在默认时区时的行为
     */
    @Unroll
    def "skipTimezoneBusinessProcess_默认时区时返回true"() {
        given:
        RequestUtil.metaClass.static.isCepRequest = { -> true }
        TimeZoneConfig.INSTANCE.metaClass.isGray = { String tenantId -> true }
        UdobjGrayConfig.metaClass.static.isAllow = { String key, String tenantId -> false }
        //TimeZoneContextHolder.setTimeZone(TimeZoneContext.DEFAULT_TIME_ZONE)

        when:
        def result = TimezoneBizUtil.skipTimezoneBusinessProcess("testTenant")

        then:
        result == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试skipTimezoneBusinessProcess方法在需要进行时区转换时的行为
     */
    @Unroll
    def "skipTimezoneBusinessProcess_需要转换时区时返回false"() {
        given:
        RequestUtil.isCepRequest() >> true
        TimeZoneConfig.INSTANCE.isGray(_)  >> true
        UdobjGrayConfig.isAllow(_,_) >> false
        //TimeZoneContextHolder.setTimeZone("Asia/Shanghai")

        when:
        def result = TimezoneBizUtil.skipTimezoneBusinessProcess("testTenant")

        then:
        result == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isNoConvertTimeZone方法在非灰度租户时的行为
     */
    @Unroll
    def "isNoConvertTimeZone_非灰度租户时返回true"() {
        given:
        TimeZoneConfig.INSTANCE.metaClass.isGray = { String tenantId -> false }

        when:
        def result = TimezoneBizUtil.isNoConvertTimeZone("testTenant")

        then:
        result == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isNoConvertTimeZone方法在灰度租户且允许不转换时区时的行为
     */
    @Unroll
    def "isNoConvertTimeZone_灰度租户且允许不转换时区时返回true"() {
        given:
        TimeZoneConfig.INSTANCE.metaClass.isGray = { String tenantId -> true }
        UdobjGrayConfig.metaClass.static.isAllow = { String key, String tenantId -> true }

        when:
        def result = TimezoneBizUtil.isNoConvertTimeZone("testTenant")

        then:
        result == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isNoConvertTimeZone方法在灰度租户且需要转换时区时的行为
     */
    @Unroll
    def "isNoConvertTimeZone_灰度租户且需要转换时区时返回false"() {
        given:
        TimeZoneConfig.INSTANCE.metaClass.isGray = { String tenantId -> true }
        UdobjGrayConfig.metaClass.static.isAllow = { String key, String tenantId -> false }

        when:
        def result = TimezoneBizUtil.isNoConvertTimeZone("testTenant")

        then:
        result == true
    }

    def cleanup() {
        // 重置时区为默认值
        //TimeZoneContextHolder.setTimeZone(TimeZoneContext.DEFAULT_TIME_ZONE)
        // 清理元编程修改
        GroovySystem.metaClassRegistry.removeMetaClass(RequestUtil)
        GroovySystem.metaClassRegistry.removeMetaClass(TimeZoneConfig.INSTANCE.getClass())
        GroovySystem.metaClassRegistry.removeMetaClass(UdobjGrayConfig)
    }
} 