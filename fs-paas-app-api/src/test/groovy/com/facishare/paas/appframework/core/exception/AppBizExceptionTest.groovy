package com.facishare.paas.appframework.core.exception

import com.google.common.collect.Lists
import spock.lang.Specification

class AppBizExceptionTest extends Specification {

    def "test suppliment"() {
        given:
        def args = new String[2];
        args[0] = "1133"
        args[1] = "sdf测试"
        def exp = new TestException("testMsg", 1000, args)

        when:
        def encodedParam = exp.getEncodeSupplement();

        then:
        encodedParam != null;
    }


    class TestException extends AppBizException {
        public TestException(String message, int errorCode, String... supplements){
            super(message, errorCode, supplements)
        }



    }
}
