package com.facishare.paas.appframework.core.util

import com.facishare.organization.adapter.api.config.service.EnterpriseLanguageClient
import com.fxiaoke.i18n.client.api.Language
import spock.lang.Specification
import spock.lang.Unroll

class LanguageClientUtilTest extends Specification {

    EnterpriseLanguageClient client
    LanguageClientUtil languageClientUtil

    def setup() {
        client = Mock(EnterpriseLanguageClient)
        languageClientUtil = new LanguageClientUtil()
        languageClientUtil.setEnterpriseLanguageClient(client)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用Long类型企业ID获取语言列表的正常场景
     */
    @Unroll
    def "getLanguagesTest with Long eid"() {
        given:
        def eid = 123456L
        def expectedLanguages = [
            new Language(code: "zh_CN", name: "简体中文"),
            new Language(code: "en_US", name: "English")
        ]

        when:
        client.getLanguagesInLicenseAndSettings(eid.toString()) >> expectedLanguages

        then:
        def result = LanguageClientUtil.getLanguages(eid)
        result == expectedLanguages
        result.size() == 2
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试使用String类型租户ID获取语言列表的正常场景
     */
    @Unroll
    def "getLanguagesTest with String tenantId"() {
        given:
        def tenantId = "T123456"
        def expectedLanguages = [
            new Language(code: "zh_CN", name: "简体中文"),
            new Language(code: "en_US", name: "English")
        ]

        when:
        client.getLanguagesInLicenseAndSettings(tenantId) >> expectedLanguages

        then:
        def result = LanguageClientUtil.getLanguages(tenantId)
        result == expectedLanguages
        result.size() == 2
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当EnterpriseLanguageClient返回空列表时的场景
     */
    @Unroll
    def "getLanguagesTest when client returns empty list"() {
        given:
        def eid = 123456L

        when:
        client.getLanguagesInLicenseAndSettings(eid.toString()) >> []

        then:
        def result = LanguageClientUtil.getLanguages(eid)
        result.isEmpty()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当EnterpriseLanguageClient抛出异常时的场景
     */
    @Unroll
    def "getLanguagesError when client throws exception"() {
        given:
        def tenantId = "T123456"

        when:
        client.getLanguagesInLicenseAndSettings(tenantId) >> { throw new RuntimeException("Service unavailable") }
        LanguageClientUtil.getLanguages(tenantId)

        then:
        thrown(RuntimeException)
    }
} 