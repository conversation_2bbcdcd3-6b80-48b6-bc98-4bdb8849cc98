package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class PersonalConfigServiceTest {

    private static final String TENANT_ID = "74255";
    private static final String USER_ID = "1000";
    private static final String CONFIG_KEY = "test_config_key";
    private static final String CONFIG_VALUE = "test_config_value";

    @Mock
    private ConfigService configService;
    
    @InjectMocks
    private PersonalConfigService personalConfigService;
    
    private ServiceContext serviceContext;
    private User user;

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        RequestContextManager.setContext(requestContext);
        serviceContext = new ServiceContext(requestContext, "personal_config", "test");
    }

    @AfterEach
    void tearDown() {
        RequestContextManager.removeContext();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存个人配置成功的场景
     */
    @Test
    @DisplayName("测试保存个人配置成功")
    void testSaveConfigSuccess() {
        // Arrange
        PersonalConfigService.ConfigInfo configInfo = PersonalConfigService.ConfigInfo.builder()
                .key(CONFIG_KEY)
                .value(CONFIG_VALUE)
                .build();

        // Act
        PersonalConfigService.ConfigInfo result = personalConfigService.saveConfig(configInfo, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(CONFIG_KEY, result.getKey());
        assertEquals(CONFIG_VALUE, result.getValue());
        verify(configService).upsertUserConfig(eq(user), eq(CONFIG_KEY), eq(CONFIG_VALUE), eq(ConfigValueType.JSON));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存个人配置参数为空的场景
     */
    @Test
    @DisplayName("测试保存个人配置参数为空")
    void testSaveConfigWithNullArg() {
        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            personalConfigService.saveConfig(null, serviceContext);
        });
        
        verify(configService, never()).upsertUserConfig(any(), any(), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找个人配置成功的场景
     */
    @Test
    @DisplayName("测试查找个人配置成功")
    void testFindConfigSuccess() {
        // Arrange
        PersonalConfigService.ConfigInfo configInfo = PersonalConfigService.ConfigInfo.builder()
                .key(CONFIG_KEY)
                .build();
        
        when(configService.findUserConfig(eq(user), eq(CONFIG_KEY)))
                .thenReturn(CONFIG_VALUE);

        // Act
        PersonalConfigService.ConfigInfo result = personalConfigService.findConfig(configInfo, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(CONFIG_KEY, result.getKey());
        assertEquals(CONFIG_VALUE, result.getValue());
        verify(configService).findUserConfig(eq(user), eq(CONFIG_KEY));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找个人配置返回null的场景
     */
    @Test
    @DisplayName("测试查找个人配置返回null")
    void testFindConfigReturnsNull() {
        // Arrange
        PersonalConfigService.ConfigInfo configInfo = PersonalConfigService.ConfigInfo.builder()
                .key(CONFIG_KEY)
                .build();
        
        when(configService.findUserConfig(eq(user), eq(CONFIG_KEY)))
                .thenReturn(null);

        // Act
        PersonalConfigService.ConfigInfo result = personalConfigService.findConfig(configInfo, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(CONFIG_KEY, result.getKey());
        assertNull(result.getValue());
        verify(configService).findUserConfig(eq(user), eq(CONFIG_KEY));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找个人配置参数为空的场景
     */
    @Test
    @DisplayName("测试查找个人配置参数为空")
    void testFindConfigWithNullArg() {
        // Act & Assert
        assertThrows(ValidateException.class, () -> {
            personalConfigService.findConfig(null, serviceContext);
        });
        
        verify(configService, never()).findUserConfig(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存个人配置key为空的场景
     */
    @Test
    @DisplayName("测试保存个人配置key为空")
    void testSaveConfigWithNullKey() {
        // Arrange
        PersonalConfigService.ConfigInfo configInfo = PersonalConfigService.ConfigInfo.builder()
                .key(null)
                .value(CONFIG_VALUE)
                .build();

        // Act
        PersonalConfigService.ConfigInfo result = personalConfigService.saveConfig(configInfo, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getKey());
        assertEquals(CONFIG_VALUE, result.getValue());
        verify(configService).upsertUserConfig(eq(user), isNull(), eq(CONFIG_VALUE), eq(ConfigValueType.JSON));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存个人配置value为空的场景
     */
    @Test
    @DisplayName("测试保存个人配置value为空")
    void testSaveConfigWithNullValue() {
        // Arrange
        PersonalConfigService.ConfigInfo configInfo = PersonalConfigService.ConfigInfo.builder()
                .key(CONFIG_KEY)
                .value(null)
                .build();

        // Act
        PersonalConfigService.ConfigInfo result = personalConfigService.saveConfig(configInfo, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(CONFIG_KEY, result.getKey());
        assertNull(result.getValue());
        verify(configService).upsertUserConfig(eq(user), eq(CONFIG_KEY), isNull(), eq(ConfigValueType.JSON));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找个人配置key为空的场景
     */
    @Test
    @DisplayName("测试查找个人配置key为空")
    void testFindConfigWithNullKey() {
        // Arrange
        PersonalConfigService.ConfigInfo configInfo = PersonalConfigService.ConfigInfo.builder()
                .key(null)
                .build();
        
        when(configService.findUserConfig(eq(user), isNull()))
                .thenReturn(CONFIG_VALUE);

        // Act
        PersonalConfigService.ConfigInfo result = personalConfigService.findConfig(configInfo, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getKey());
        assertEquals(CONFIG_VALUE, result.getValue());
        verify(configService).findUserConfig(eq(user), isNull());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找个人配置返回空字符串的场景
     */
    @Test
    @DisplayName("测试查找个人配置返回空字符串")
    void testFindConfigReturnsEmptyString() {
        // Arrange
        PersonalConfigService.ConfigInfo configInfo = PersonalConfigService.ConfigInfo.builder()
                .key(CONFIG_KEY)
                .build();
        
        when(configService.findUserConfig(eq(user), eq(CONFIG_KEY)))
                .thenReturn("");

        // Act
        PersonalConfigService.ConfigInfo result = personalConfigService.findConfig(configInfo, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(CONFIG_KEY, result.getKey());
        assertEquals("", result.getValue());
        verify(configService).findUserConfig(eq(user), eq(CONFIG_KEY));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ConfigInfo构造器的场景
     */
    @Test
    @DisplayName("测试ConfigInfo构造器")
    void testConfigInfoConstructors() {
        // Test default constructor
        PersonalConfigService.ConfigInfo configInfo1 = new PersonalConfigService.ConfigInfo();
        assertNotNull(configInfo1);
        assertNull(configInfo1.getKey());
        assertNull(configInfo1.getValue());

        // Test all args constructor
        PersonalConfigService.ConfigInfo configInfo2 = new PersonalConfigService.ConfigInfo(CONFIG_KEY, CONFIG_VALUE);
        assertNotNull(configInfo2);
        assertEquals(CONFIG_KEY, configInfo2.getKey());
        assertEquals(CONFIG_VALUE, configInfo2.getValue());

        // Test builder
        PersonalConfigService.ConfigInfo configInfo3 = PersonalConfigService.ConfigInfo.builder()
                .key(CONFIG_KEY)
                .value(CONFIG_VALUE)
                .build();
        assertNotNull(configInfo3);
        assertEquals(CONFIG_KEY, configInfo3.getKey());
        assertEquals(CONFIG_VALUE, configInfo3.getValue());

        // Test setters
        configInfo1.setKey(CONFIG_KEY);
        configInfo1.setValue(CONFIG_VALUE);
        assertEquals(CONFIG_KEY, configInfo1.getKey());
        assertEquals(CONFIG_VALUE, configInfo1.getValue());
    }
}
