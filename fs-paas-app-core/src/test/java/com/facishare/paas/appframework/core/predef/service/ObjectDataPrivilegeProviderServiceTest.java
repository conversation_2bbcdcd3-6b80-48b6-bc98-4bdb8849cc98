package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProvider;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderDefinition.BatchCheckBusinessPrivilege;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderDefinition.CheckBusinessPrivilege;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderManager;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectDataPrivilegeProviderService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectDataPrivilegeProviderService单元测试")
class ObjectDataPrivilegeProviderServiceTest {

    @Mock
    private DataPrivilegeProviderManager providerManager;
    
    @Mock
    private DataPrivilegeProvider dataPrivilegeProvider;
    
    @InjectMocks
    private ObjectDataPrivilegeProviderService objectDataPrivilegeProviderService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "TestObj__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "privilege", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量检查业务权限有权限提供者的场景
     */
    @Test
    @DisplayName("测试batchCheckBusinessPrivilege有权限提供者")
    void testBatchCheckBusinessPrivilegeWithProvider() {
        // Arrange
        BatchCheckBusinessPrivilege.Arg arg = BatchCheckBusinessPrivilege.Arg.builder()
                .describeApiName(DESCRIBE_API_NAME)
                .actionCodes(Sets.newHashSet("view", "edit"))
                .dataPrivilegeMap(Maps.newHashMap())
                .dataList(Lists.newArrayList())
                .build();

        Map<String, Map<String, Permissions>> expectedResult = new HashMap<String, Map<String, Permissions>>();
        expectedResult.put("view", Maps.newHashMap());
        expectedResult.put("edit", Maps.newHashMap());

        when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(dataPrivilegeProvider);
        when(dataPrivilegeProvider.checkBusinessPrivilege(any(User.class), any(Map.class),
                any(List.class), any(List.class)))
                .thenReturn(expectedResult);

        // Act
        BatchCheckBusinessPrivilege.Result result = objectDataPrivilegeProviderService
                .batchCheckBusinessPrivilege(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(2, result.getResult().size());
        assertTrue(result.getResult().containsKey("view"));
        assertTrue(result.getResult().containsKey("edit"));
        verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量检查业务权限无权限提供者的场景
     */
    @Test
    @DisplayName("测试batchCheckBusinessPrivilege无权限提供者")
    void testBatchCheckBusinessPrivilegeWithoutProvider() {
        // Arrange
        Map<String, Permissions> dataPrivilegeMap = Maps.newHashMap();
        dataPrivilegeMap.put("data1", Permissions.READ_WRITE);

        BatchCheckBusinessPrivilege.Arg arg = BatchCheckBusinessPrivilege.Arg.builder()
                .describeApiName(DESCRIBE_API_NAME)
                .actionCodes(Sets.newHashSet("view", "edit"))
                .dataPrivilegeMap(dataPrivilegeMap)
                .build();

        when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(null);

        // Act
        BatchCheckBusinessPrivilege.Result result = objectDataPrivilegeProviderService
                .batchCheckBusinessPrivilege(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(2, result.getResult().size());
        assertEquals(dataPrivilegeMap, result.getResult().get("view"));
        assertEquals(dataPrivilegeMap, result.getResult().get("edit"));
        verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试单个检查业务权限有权限提供者的场景
     */
    @Test
    @DisplayName("测试checkBusinessPrivilege有权限提供者")
    void testCheckBusinessPrivilegeWithProvider() {
        // Arrange
        Map<String, Permissions> dataPrivilegeMap = Maps.newHashMap();
        dataPrivilegeMap.put("data1", Permissions.READ_WRITE);

        CheckBusinessPrivilege.Arg arg = CheckBusinessPrivilege.Arg.builder()
                .describeApiName(DESCRIBE_API_NAME)
                .actionCode("view")
                .dataPrivilegeMap(dataPrivilegeMap)
                .dataList(Lists.newArrayList())
                .build();

        Map<String, Permissions> expectedResult = Maps.newHashMap();
        expectedResult.put("data1", Permissions.READ_WRITE);

        when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(dataPrivilegeProvider);
        when(dataPrivilegeProvider.checkBusinessPrivilege(any(User.class), any(Map.class),
                any(List.class), eq("view")))
                .thenReturn(expectedResult);

        // Act
        CheckBusinessPrivilege.Result result = objectDataPrivilegeProviderService
                .checkBusinessPrivilege(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(1, result.getResult().size());
        assertEquals(Permissions.READ_WRITE, result.getResult().get("data1"));
        verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试单个检查业务权限无权限提供者的场景
     */
    @Test
    @DisplayName("测试checkBusinessPrivilege无权限提供者")
    void testCheckBusinessPrivilegeWithoutProvider() {
        // Arrange
        Map<String, Permissions> dataPrivilegeMap = Maps.newHashMap();
        dataPrivilegeMap.put("data1", Permissions.READ_WRITE);

        CheckBusinessPrivilege.Arg arg = CheckBusinessPrivilege.Arg.builder()
                .describeApiName(DESCRIBE_API_NAME)
                .actionCode("view")
                .dataPrivilegeMap(dataPrivilegeMap)
                .build();

        when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(null);

        // Act
        CheckBusinessPrivilege.Result result = objectDataPrivilegeProviderService
                .checkBusinessPrivilege(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        assertEquals(dataPrivilegeMap, result.getResult());
        verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量检查业务权限空数据列表的边界条件
     */
    @Test
    @DisplayName("测试batchCheckBusinessPrivilege空数据列表")
    void testBatchCheckBusinessPrivilegeWithEmptyDataList() {
        // Arrange
        Map<String, Permissions> dataPrivilegeMap = Maps.newHashMap();
        dataPrivilegeMap.put("data1", Permissions.READ_WRITE);

        BatchCheckBusinessPrivilege.Arg arg = BatchCheckBusinessPrivilege.Arg.builder()
                .describeApiName(DESCRIBE_API_NAME)
                .actionCodes(Sets.newHashSet("view"))
                .dataPrivilegeMap(dataPrivilegeMap)
                .dataList(null)
                .build();

        Map<String, Map<String, Permissions>> expectedResult = Maps.newHashMap();
        expectedResult.put("view", dataPrivilegeMap);

        when(providerManager.getLocalProvider(DESCRIBE_API_NAME)).thenReturn(dataPrivilegeProvider);
        when(dataPrivilegeProvider.checkBusinessPrivilege(any(User.class), any(Map.class),
                any(List.class), any(List.class)))
                .thenReturn(expectedResult);

        // Act
        BatchCheckBusinessPrivilege.Result result = objectDataPrivilegeProviderService
                .batchCheckBusinessPrivilege(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getResult());
        verify(providerManager).getLocalProvider(DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectDataPrivilegeProviderService);
        assertNotNull(providerManager);
    }
}
