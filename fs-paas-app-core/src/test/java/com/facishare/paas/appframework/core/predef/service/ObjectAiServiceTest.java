package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.CheckAiUserLicense;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.GenerateFieldApiName;
import com.facishare.paas.appframework.core.predef.service.dto.objectDescribe.GenerateObjectApiName;
import com.facishare.paas.appframework.metadata.ai.AiLogicService;
import com.facishare.paas.appframework.metadata.ai.AIService;
import com.facishare.paas.appframework.metadata.dto.ChatComplete;
import com.facishare.paas.appframework.metadata.dto.ai.FormulaGenerate;
import com.facishare.paas.appframework.metadata.dto.ai.GenerateSearchQuery;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.privilege.util.PrivilegeConstants;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectAiService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectAiService单元测试")
class ObjectAiServiceTest {

    @Mock
    private AiLogicService aiLogicService;
    
    @Mock
    private ServiceFacade serviceFacade;
    
    @Mock
    private AIService aiService;
    
    @InjectMocks
    private ObjectAiService objectAiService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "ai", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成字段API名称成功的正常场景
     */
    @Test
    @DisplayName("测试generateFieldApiName成功")
    void testGenerateFieldApiNameSuccess() {
        // Arrange
        GenerateFieldApiName.Arg arg = new GenerateFieldApiName.Arg();
        arg.setName("测试字段");
        
        ChatComplete.Result chatResult = new ChatComplete.Result();
        chatResult.setErrCode(0);
        chatResult.setErrMessage(null);
        ChatComplete.AIData aiData = new ChatComplete.AIData();
        aiData.setMessage("test_field__c");
        chatResult.setResult(aiData);
        
        when(aiLogicService.chatComplete(TENANT_ID, USER_ID, "FieldAgent", "测试字段"))
                .thenReturn(chatResult);

        // Act
        GenerateFieldApiName.Result result = objectAiService.generateFieldApiName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getErrorMessage());
        assertEquals("test_field__c", result.getApiName());
        verify(aiLogicService).chatComplete(TENANT_ID, USER_ID, "FieldAgent", "测试字段");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成字段API名称时输入为空的边界条件
     */
    @Test
    @DisplayName("测试generateFieldApiName输入为空")
    void testGenerateFieldApiNameWithBlankName() {
        // Arrange
        GenerateFieldApiName.Arg arg = new GenerateFieldApiName.Arg();
        arg.setName("");

        // Act
        GenerateFieldApiName.Result result = objectAiService.generateFieldApiName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNull(result.getErrorMessage());
        assertNull(result.getApiName());
        verify(aiLogicService, never()).chatComplete(anyString(), anyString(), anyString(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成字段API名称时AI服务返回错误的异常场景
     */
    @Test
    @DisplayName("测试generateFieldApiName AI服务错误")
    void testGenerateFieldApiNameWithAiError() {
        // Arrange
        GenerateFieldApiName.Arg arg = new GenerateFieldApiName.Arg();
        arg.setName("测试字段");
        
        ChatComplete.Result chatResult = new ChatComplete.Result();
        chatResult.setErrCode(1001);
        chatResult.setErrMessage("AI服务暂时不可用");
        chatResult.setResult(null);
        
        when(aiLogicService.chatComplete(TENANT_ID, USER_ID, "FieldAgent", "测试字段"))
                .thenReturn(chatResult);

        // Act
        GenerateFieldApiName.Result result = objectAiService.generateFieldApiName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("AI服务暂时不可用", result.getErrorMessage());
        assertNull(result.getApiName());
        verify(aiLogicService).chatComplete(TENANT_ID, USER_ID, "FieldAgent", "测试字段");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试AI翻译功能成功的正常场景
     */
    @Test
    @DisplayName("测试chatTranslate成功")
    void testChatTranslateSuccess() {
        // Arrange
        ChatComplete.AITransDTO arg = new ChatComplete.AITransDTO();
        arg.setTransEntrys(Lists.newArrayList("Hello World"));
        arg.setLanguages(Lists.newArrayList("zh-CN"));

        ChatComplete.TranslateResult translateResult = new ChatComplete.TranslateResult();
        translateResult.setErrCode(0);
        translateResult.setErrMessage(null);
        ChatComplete.AITranslateData translateData = new ChatComplete.AITranslateData();
        translateData.setSuccess(true);
        translateResult.setResult(translateData);
        
        when(aiLogicService.chatTranslate(TENANT_ID, USER_ID, arg))
                .thenReturn(translateResult);

        // Act
        ChatComplete.TranslateResult result = objectAiService.chatTranslate(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getErrCode());
        assertTrue(result.getResult().getSuccess());
        verify(aiLogicService).chatTranslate(TENANT_ID, USER_ID, arg);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成对象API名称成功的正常场景
     */
    @Test
    @DisplayName("测试generateObjectApiName成功")
    void testGenerateObjectApiNameSuccess() {
        // Arrange
        GenerateObjectApiName.Arg arg = new GenerateObjectApiName.Arg();
        arg.setName("客户对象");
        
        ChatComplete.Result chatResult = new ChatComplete.Result();
        chatResult.setErrCode(0);
        chatResult.setErrMessage(null);
        ChatComplete.AIData aiData = new ChatComplete.AIData();
        aiData.setMessage("Customer__c");
        chatResult.setResult(aiData);
        
        when(aiLogicService.chatComplete(TENANT_ID, USER_ID, "ObjectAgent", "客户对象"))
                .thenReturn(chatResult);

        // Act
        GenerateObjectApiName.Result result = objectAiService.generateObjectApiName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getErrorMessage());
        assertEquals("Customer__c", result.getApiName());
        verify(aiLogicService).chatComplete(TENANT_ID, USER_ID, "ObjectAgent", "客户对象");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试生成对象API名称时输入为空的边界条件
     */
    @Test
    @DisplayName("测试generateObjectApiName输入为空")
    void testGenerateObjectApiNameWithBlankName() {
        // Arrange
        GenerateObjectApiName.Arg arg = new GenerateObjectApiName.Arg();
        arg.setName(null);

        // Act
        GenerateObjectApiName.Result result = objectAiService.generateObjectApiName(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertNull(result.getErrorMessage());
        assertNull(result.getApiName());
        verify(aiLogicService, never()).chatComplete(anyString(), anyString(), anyString(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查AI用户许可证有权限的场景
     */
    @Test
    @DisplayName("测试checkAiUserLicense有权限")
    void testCheckAiUserLicenseWithPermission() {
        // Arrange
        CheckAiUserLicense.Arg arg = new CheckAiUserLicense.Arg();
        
        Map<String, Boolean> funcResult = new HashMap<String, Boolean>();
        funcResult.put(PrivilegeConstants.AI_OBJECT_USER_LICENSE, true);
        
        when(serviceFacade.checkFuncPrivilege(user, Lists.newArrayList(PrivilegeConstants.AI_OBJECT_USER_LICENSE)))
                .thenReturn(funcResult);

        // Act
        CheckAiUserLicense.Result result = objectAiService.checkAiUserLicense(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isHasAiUserLicense());
        verify(serviceFacade).checkFuncPrivilege(user, Lists.newArrayList(PrivilegeConstants.AI_OBJECT_USER_LICENSE));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查AI用户许可证无权限的场景
     */
    @Test
    @DisplayName("测试checkAiUserLicense无权限")
    void testCheckAiUserLicenseWithoutPermission() {
        // Arrange
        CheckAiUserLicense.Arg arg = new CheckAiUserLicense.Arg();
        
        Map<String, Boolean> funcResult = new HashMap<String, Boolean>();
        funcResult.put(PrivilegeConstants.AI_OBJECT_USER_LICENSE, false);
        
        when(serviceFacade.checkFuncPrivilege(user, Lists.newArrayList(PrivilegeConstants.AI_OBJECT_USER_LICENSE)))
                .thenReturn(funcResult);

        // Act
        CheckAiUserLicense.Result result = objectAiService.checkAiUserLicense(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertFalse(result.isHasAiUserLicense());
        verify(serviceFacade).checkFuncPrivilege(user, Lists.newArrayList(PrivilegeConstants.AI_OBJECT_USER_LICENSE));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试AI生成计算公式成功的场景
     */
    @Test
    @DisplayName("测试generateFormula成功")
    void testGenerateFormulaSuccess() {
        // Arrange
        FormulaGenerate.Argument arg = new FormulaGenerate.Argument();
        arg.setUserInput("计算总价");
        arg.setBindingObjectApiName("TestObj__c");

        FormulaGenerate.ExpressionResult expressionResult = new FormulaGenerate.ExpressionResult();
        expressionResult.setAnswer("price * quantity");

        when(aiService.chatComplete(user, arg)).thenReturn(expressionResult);

        // Act
        FormulaGenerate.ExpressionResult result = objectAiService.chatComplete(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals("price * quantity", result.getAnswer());
        verify(aiService).chatComplete(user, arg);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试AI生成搜索查询成功的场景
     */
    @Test
    @DisplayName("测试generateSearchQuery成功")
    void testGenerateSearchQuerySuccess() {
        // Arrange
        GenerateSearchQuery.Arg arg = new GenerateSearchQuery.Arg();
        arg.setUserInput("查找活跃客户");
        arg.setBindingObjectApiName("Customer__c");

        GenerateSearchQuery.Result searchResult = GenerateSearchQuery.Result.builder()
                .content("状态 等于 活跃")
                .searchQuery("{\"filters\":[{\"field\":\"status\",\"operator\":\"eq\",\"value\":\"active\"}]}")
                .build();

        when(aiService.generateSearchQuery(user, arg)).thenReturn(searchResult);

        // Act
        GenerateSearchQuery.Result result = objectAiService.generateSearchQuery(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals("状态 等于 活跃", result.getContent());
        assertNotNull(result.getSearchQuery());
        verify(aiService).generateSearchQuery(user, arg);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectAiService);
        assertNotNull(aiLogicService);
        assertNotNull(serviceFacade);
        assertNotNull(aiService);
    }
}
