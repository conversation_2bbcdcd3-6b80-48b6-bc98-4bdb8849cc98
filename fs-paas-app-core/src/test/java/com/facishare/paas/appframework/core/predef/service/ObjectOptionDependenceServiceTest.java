package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.predef.service.dto.option.dependence.*;
import com.facishare.paas.appframework.metadata.options.SelectFieldDependenceLogicService;
import com.facishare.paas.appframework.metadata.options.bo.SelectFieldDependence;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectOptionDependenceService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectOptionDependenceService单元测试")
class ObjectOptionDependenceServiceTest {

    @Mock
    private SelectFieldDependenceLogicService dependenceLogicService;
    
    @InjectMocks
    private ObjectOptionDependenceService objectOptionDependenceService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "TestObj__c";
    private final String FIELD_API_NAME = "testField";
    private final String CHILD_FIELD_NAME = "childField";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "option_dependence", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找所有选项依赖成功的场景
     */
    @Test
    @DisplayName("测试findAllOptionDependence成功")
    void testFindAllOptionDependenceSuccess() {
        // Arrange
        FindAllOptionDependence.Arg arg = new FindAllOptionDependence.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);

        SelectFieldDependence mockDependence = mock(SelectFieldDependence.class);
        List<SelectFieldDependence> mockDependenceList = Arrays.asList(mockDependence);
        
        when(dependenceLogicService.findFieldDependenceWitchObjectApiName(user, DESCRIBE_API_NAME))
                .thenReturn(mockDependenceList);

        // Act
        FindAllOptionDependence.Result result = objectOptionDependenceService.findAllOptionDependence(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(dependenceLogicService).findFieldDependenceWitchObjectApiName(user, DESCRIBE_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找所有选项依赖时参数为空的异常场景
     */
    @Test
    @DisplayName("测试findAllOptionDependence参数为空抛出异常")
    void testFindAllOptionDependenceThrowsValidateExceptionWhenDescribeApiNameEmpty() {
        // Arrange
        FindAllOptionDependence.Arg arg = new FindAllOptionDependence.Arg();
        arg.setDescribeApiName("");

        // Act & Assert
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            objectOptionDependenceService.findAllOptionDependence(serviceContext, arg);
        });

        assertNotNull(exception);
        verify(dependenceLogicService, never()).findFieldDependenceWitchObjectApiName(any(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找单个选项依赖成功的场景
     */
    @Test
    @DisplayName("测试findOptionDependence成功")
    void testFindOptionDependenceSuccess() {
        // Arrange
        FindOptionDependence.Arg arg = new FindOptionDependence.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setFieldApiName(FIELD_API_NAME);
        arg.setChildFieldName(CHILD_FIELD_NAME);

        SelectFieldDependence mockDependence = mock(SelectFieldDependence.class);
        when(dependenceLogicService.find(user, DESCRIBE_API_NAME, FIELD_API_NAME, CHILD_FIELD_NAME))
                .thenReturn(Optional.of(mockDependence));

        // Act
        FindOptionDependence.Result result = objectOptionDependenceService.findOptionDependence(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(dependenceLogicService).find(user, DESCRIBE_API_NAME, FIELD_API_NAME, CHILD_FIELD_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找单个选项依赖时参数为空的异常场景
     */
    @Test
    @DisplayName("测试findOptionDependence参数为空抛出异常")
    void testFindOptionDependenceThrowsValidateExceptionWhenParametersEmpty() {
        // Arrange
        FindOptionDependence.Arg arg = new FindOptionDependence.Arg();
        arg.setDescribeApiName("");
        arg.setFieldApiName(FIELD_API_NAME);
        arg.setChildFieldName(CHILD_FIELD_NAME);

        // Act & Assert
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            objectOptionDependenceService.findOptionDependence(serviceContext, arg);
        });

        assertNotNull(exception);
        verify(dependenceLogicService, never()).find(any(), anyString(), anyString(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建选项依赖成功的场景
     */
    @Test
    @DisplayName("测试createOptionDependence成功")
    void testCreateOptionDependenceSuccess() {
        // Arrange
        CreateOptionDependence.Arg arg = new CreateOptionDependence.Arg();
        FieldDependenceDTO mockFieldDependence = mock(FieldDependenceDTO.class);
        SelectFieldDependence mockSelectFieldDependence = mock(SelectFieldDependence.class);
        
        arg.setFieldDependence(mockFieldDependence);
        when(mockFieldDependence.convert()).thenReturn(mockSelectFieldDependence);

        // Act
        CreateOptionDependence.Result result = objectOptionDependenceService.createOptionDependence(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(dependenceLogicService).create(user, mockSelectFieldDependence);
        verify(mockFieldDependence).convert();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试创建选项依赖时参数为空的异常场景
     */
    @Test
    @DisplayName("测试createOptionDependence参数为空抛出异常")
    void testCreateOptionDependenceThrowsValidateExceptionWhenFieldDependenceNull() {
        // Arrange
        CreateOptionDependence.Arg arg = new CreateOptionDependence.Arg();
        arg.setFieldDependence(null);

        // Act & Assert
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            objectOptionDependenceService.createOptionDependence(serviceContext, arg);
        });

        assertNotNull(exception);
        verify(dependenceLogicService, never()).create(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试更新选项依赖成功的场景
     */
    @Test
    @DisplayName("测试updateOptionDependence成功")
    void testUpdateOptionDependenceSuccess() {
        // Arrange
        CreateOptionDependence.Arg arg = new CreateOptionDependence.Arg();
        FieldDependenceDTO mockFieldDependence = mock(FieldDependenceDTO.class);
        SelectFieldDependence mockSelectFieldDependence = mock(SelectFieldDependence.class);
        
        arg.setFieldDependence(mockFieldDependence);
        when(mockFieldDependence.convert()).thenReturn(mockSelectFieldDependence);

        // Act
        CreateOptionDependence.Result result = objectOptionDependenceService.updateOptionDependence(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(dependenceLogicService).update(user, mockSelectFieldDependence);
        verify(mockFieldDependence).convert();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除选项依赖成功的场景
     */
    @Test
    @DisplayName("测试deletedOptionDependence成功")
    void testDeletedOptionDependenceSuccess() {
        // Arrange
        DeletedOptionDependence.Arg arg = new DeletedOptionDependence.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setFieldApiName(FIELD_API_NAME);
        arg.setChildFieldName(CHILD_FIELD_NAME);

        // Act
        DeletedOptionDependence.Result result = objectOptionDependenceService.deletedOptionDependence(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(dependenceLogicService).deleted(user, DESCRIBE_API_NAME, FIELD_API_NAME, CHILD_FIELD_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除选项依赖时参数为空的异常场景
     */
    @Test
    @DisplayName("测试deletedOptionDependence参数为空抛出异常")
    void testDeletedOptionDependenceThrowsValidateExceptionWhenParametersEmpty() {
        // Arrange
        DeletedOptionDependence.Arg arg = new DeletedOptionDependence.Arg();
        arg.setDescribeApiName("");
        arg.setFieldApiName(FIELD_API_NAME);
        arg.setChildFieldName(CHILD_FIELD_NAME);

        // Act & Assert
        ValidateException exception = assertThrows(ValidateException.class, () -> {
            objectOptionDependenceService.deletedOptionDependence(serviceContext, arg);
        });

        assertNotNull(exception);
        verify(dependenceLogicService, never()).deleted(any(), anyString(), anyString(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectOptionDependenceService);
        assertNotNull(dependenceLogicService);
    }
}
