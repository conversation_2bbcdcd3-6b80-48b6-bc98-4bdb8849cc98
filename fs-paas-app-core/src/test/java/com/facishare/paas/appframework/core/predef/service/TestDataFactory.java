package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.FieldDescribeFactory;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * GenerateByAI
 * 测试数据工厂类，提供标准的测试数据构造方法
 * 严格遵循JDK8语法规范和框架标准示例
 */
public class TestDataFactory {

    /**
     * 标准测试租户ID
     */
    public static final String DEFAULT_TENANT_ID = "74255";
    
    /**
     * 标准测试用户ID
     */
    public static final String DEFAULT_USER_ID = "1000";

    /**
     * 创建标准测试用户
     * 参考框架标准示例：User.systemUser('74255')
     * 
     * @return User对象
     */
    public static User createUser() {
        return new User(DEFAULT_TENANT_ID, DEFAULT_USER_ID);
    }

    /**
     * 创建指定租户ID的用户
     * 
     * @param tenantId 租户ID
     * @return User对象
     */
    public static User createUser(String tenantId) {
        return new User(tenantId, DEFAULT_USER_ID);
    }

    /**
     * 创建指定租户ID和用户ID的用户
     * 
     * @param tenantId 租户ID
     * @param userId 用户ID
     * @return User对象
     */
    public static User createUser(String tenantId, String userId) {
        return new User(tenantId, userId);
    }

    /**
     * 创建标准的RequestContext
     * 参考框架标准示例
     * 
     * @param user 用户对象
     * @return RequestContext对象
     */
    public static RequestContext createRequestContext(User user) {
        return RequestContext.builder()
                .tenantId(user.getTenantId())
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
    }

    /**
     * 创建ServiceContext
     * 参考框架标准示例
     * 
     * @param user 用户对象
     * @param serviceName 服务名称
     * @param methodName 方法名称
     * @return ServiceContext对象
     */
    public static ServiceContext createServiceContext(User user, String serviceName, String methodName) {
        RequestContext requestContext = createRequestContext(user);
        return new ServiceContext(requestContext, serviceName, methodName);
    }

    /**
     * 创建字段描述
     * 参考框架标准示例：FieldDescribeFactory.newInstance
     * 
     * @param apiName 字段API名称
     * @param fieldType 字段类型
     * @return IFieldDescribe对象
     */
    public static IFieldDescribe createFieldDescribe(String apiName, String fieldType) {
        Map<String, Object> fieldMap = new HashMap<String, Object>();
        fieldMap.put("api_name", apiName);
        fieldMap.put("type", fieldType);
        fieldMap.put("label", "测试字段");
        fieldMap.put("is_required", false);
        fieldMap.put("is_active", true);
        return FieldDescribeFactory.newInstance(fieldMap);
    }

    /**
     * 创建员工字段描述
     * 
     * @param apiName 字段API名称
     * @return IFieldDescribe对象
     */
    public static IFieldDescribe createEmployeeFieldDescribe(String apiName) {
        return createFieldDescribe(apiName, IFieldType.EMPLOYEE);
    }

    /**
     * 创建文本字段描述
     * 
     * @param apiName 字段API名称
     * @return IFieldDescribe对象
     */
    public static IFieldDescribe createTextFieldDescribe(String apiName) {
        return createFieldDescribe(apiName, IFieldType.TEXT);
    }

    /**
     * 创建数字字段描述
     * 
     * @param apiName 字段API名称
     * @return IFieldDescribe对象
     */
    public static IFieldDescribe createNumberFieldDescribe(String apiName) {
        return createFieldDescribe(apiName, IFieldType.NUMBER);
    }

    /**
     * 创建对象描述
     * 参考框架标准示例
     * 
     * @param apiName 对象API名称
     * @return IObjectDescribe对象
     */
    public static IObjectDescribe createObjectDescribe(String apiName) {
        ObjectDescribe describe = new ObjectDescribe();
        describe.setApiName(apiName);
        describe.setTenantId(DEFAULT_TENANT_ID);
        describe.setDisplayName("测试对象");
        return describe;
    }

    /**
     * 创建带字段的对象描述
     * 
     * @param apiName 对象API名称
     * @param fieldDescribes 字段描述列表
     * @return IObjectDescribe对象
     */
    public static IObjectDescribe createObjectDescribe(String apiName, List<IFieldDescribe> fieldDescribes) {
        ObjectDescribe describe = new ObjectDescribe();
        describe.setApiName(apiName);
        describe.setTenantId(DEFAULT_TENANT_ID);
        describe.setDisplayName("测试对象");
        describe.setFieldDescribes(fieldDescribes);
        return describe;
    }

    /**
     * 创建对象数据
     * 参考框架标准示例：new ObjectData(['_id':'2','name':'产品2'])
     * 
     * @param objectApiName 对象API名称
     * @return IObjectData对象
     */
    public static IObjectData createObjectData(String objectApiName) {
        Map<String, Object> dataMap = new HashMap<String, Object>();
        dataMap.put("_id", "test_id_001");
        dataMap.put("name", "测试数据");
        dataMap.put("object_describe_api_name", objectApiName);
        return new ObjectData(dataMap);
    }

    /**
     * 创建带指定字段值的对象数据
     * 
     * @param objectApiName 对象API名称
     * @param fieldValues 字段值映射
     * @return IObjectData对象
     */
    public static IObjectData createObjectData(String objectApiName, Map<String, Object> fieldValues) {
        Map<String, Object> dataMap = new HashMap<String, Object>(fieldValues);
        dataMap.put("object_describe_api_name", objectApiName);
        if (!dataMap.containsKey("_id")) {
            dataMap.put("_id", "test_id_001");
        }
        return new ObjectData(dataMap);
    }

    /**
     * 创建对象数据列表
     * 
     * @param objectDataArray 对象数据数组
     * @return 对象数据列表
     */
    public static List<IObjectData> createObjectDataList(IObjectData... objectDataArray) {
        return Arrays.asList(objectDataArray);
    }

    /**
     * 创建简单的字段值映射
     * 
     * @param key1 字段名1
     * @param value1 字段值1
     * @return 字段值映射
     */
    public static Map<String, Object> createFieldMap(String key1, Object value1) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(key1, value1);
        return map;
    }

    /**
     * 创建两个字段的字段值映射
     * 
     * @param key1 字段名1
     * @param value1 字段值1
     * @param key2 字段名2
     * @param value2 字段值2
     * @return 字段值映射
     */
    public static Map<String, Object> createFieldMap(String key1, Object value1, String key2, Object value2) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put(key1, value1);
        map.put(key2, value2);
        return map;
    }
}
