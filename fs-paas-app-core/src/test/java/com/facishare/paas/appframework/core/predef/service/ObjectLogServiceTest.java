package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.service.dto.log.*;
import com.facishare.paas.appframework.log.dto.LogAnalysis;
import com.facishare.paas.appframework.log.dto.MobSearchResult;
import com.facishare.paas.appframework.log.dto.SearchFunctionDetailModel;
import com.facishare.paas.appframework.log.dto.WebSearchResult;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.fxiaoke.i18n.client.I18nClient;
import com.fxiaoke.i18n.client.impl.I18nServiceImpl;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.powermock.reflect.Whitebox;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ObjectLogServiceTest {

    private ObjectLogService objectLogService;
    private final String tenantId = "74255";
    
    @Mock
    private ServiceContext context;
    
    @Mock
    private ServiceFacade serviceFacade;

    @Mock
    private OptionalFeaturesService optionalFeaturesService;

    @BeforeAll
    static void setupSpec() throws Exception {
        // 创建 mock 实例
        I18nClient i18nClient = mock(I18nClient.class);
        I18nServiceImpl i18nServiceImpl = mock(I18nServiceImpl.class);

        // 给 mock 设置返回值
        when(i18nClient.getAllLanguage()).thenReturn(Collections.emptyList());

        // 设置内部字段 impl
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl);

        // 设置 SINGLETON
        Whitebox.setInternalState(I18nClient.class, "SINGLETON", i18nClient);
    }

    @BeforeEach
    void setup() throws Exception {
        RequestContext requestContext = RequestContext.builder()
                .tenantId(tenantId)
                .user(User.systemUser(tenantId))
                .build();
        RequestContextManager.setContext(requestContext);
        objectLogService = new ObjectLogService();
        context = mock(ServiceContext.class);
        
        // 注入 serviceFacade 和 optionalFeaturesService
        Whitebox.setInternalState(objectLogService, "serviceFacade", serviceFacade);
        Whitebox.setInternalState(objectLogService, "optionalFeaturesService", optionalFeaturesService);
        
        // Mock serviceFacade 方法 - 使用 lenient 避免不必要的 stubbing 错误
        ObjectDescribe mockObjectDescribe = new ObjectDescribe();
        mockObjectDescribe.setApiName("EmployeeObjectUsage");
        mockObjectDescribe.setDisplayName("员工对象使用情况");
        lenient().when(serviceFacade.findObject(anyString(), anyString())).thenReturn(mockObjectDescribe);
        lenient().when(serviceFacade.findDescribeListWithoutFields(anyString(), any())).thenReturn(Collections.emptyList());
        lenient().when(serviceFacade.findObjectsByTenantId(anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyBoolean(), anyString())).thenReturn(Collections.emptyList());
        lenient().when(serviceFacade.findTenantConfig(any(User.class), anyString())).thenReturn(null);
        lenient().when(serviceFacade.findDescribeList(anyString(), anyBoolean(), anyString(), anyBoolean(), anyBoolean(), anyBoolean(), anyString())).thenReturn(Collections.emptyList());
    }

    @Test
    void testGetLogModuleGroup() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "getLogModuleGroup");
        GetLogModuleGroup.Arg arg = new GetLogModuleGroup.Arg();
        
        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> objectLogService.getLogModuleGroup(arg, context));
    }

    @Test
    void testUpdateLogAnalysis() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "updateLogAnalysis");
        List<LogAnalysis.OperationLog> operationLogList = new ArrayList<>();
        LogAnalysis.OperationLog operationLog = new LogAnalysis.OperationLog();
        operationLog.setDescribeApiName("AccountObj");
        operationLog.setOperation(new ArrayList<String>() {{ add("1"); add("2"); }});
        operationLogList.add(operationLog);
        LogAnalysis.Arg arg = new LogAnalysis.Arg();
        arg.setLoginLog(true);
        arg.setOperationLog(true);
        arg.setOperationLogArgs(operationLogList);
        
        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> objectLogService.updateLogAnalysis(arg, context));
    }

    @Test
    void testFindLogAnalysis() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "findLogAnalysis");
        LogAnalysis.Arg arg = new LogAnalysis.Arg();
        
        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> objectLogService.findLogAnalysis(arg, context));
    }

    @Test
    void testFindOperationLogRelationship() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "findOperationLogRelationship");
        LogAnalysis.Arg arg = new LogAnalysis.Arg();
        
        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> objectLogService.findOperationLogRelationship(arg, context));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLogInfoListForWeb方法，正常获取Web端日志列表
     */
    @Test
    void testGetLogInfoListForWeb() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "getLogInfoListForWeb");
        GetLogInfoListForWeb.Arg arg = new GetLogInfoListForWeb.Arg();
        arg.setApiName("AccountObj");
        arg.setObjectId("test_object_id");
        arg.setPageSize(10);
        arg.setPageNumber(1);

        // Mock WebSearchResult
        WebSearchResult mockResult = new WebSearchResult();
        com.facishare.paas.appframework.log.dto.PageInfo pageInfo = new com.facishare.paas.appframework.log.dto.PageInfo();
        pageInfo.setTotalCount(5);
        pageInfo.setPageSize(10);
        pageInfo.setPageNumber(1);
        mockResult.setPageInfo(pageInfo);
        mockResult.setModifyRecordList(Lists.newArrayList());

        when(serviceFacade.webSearchModifyRecord(anyString(), anyString(), anyInt(), anyInt(), any(User.class)))
                .thenReturn(mockResult);

        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> {
            GetLogInfoListForWeb.Result result = objectLogService.getLogInfoListForWeb(arg, context);
            assertNotNull(result);
            assertNotNull(result.getPageInfo());
            assertEquals(Integer.valueOf(5), result.getPageInfo().getTotalCount());
            assertEquals(Integer.valueOf(10), result.getPageInfo().getPageSize());
            assertEquals(Integer.valueOf(1), result.getPageInfo().getPageNumber());
        });

        verify(serviceFacade).webSearchModifyRecord(eq("AccountObj"), eq("test_object_id"), eq(10), eq(1), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLogInfoListForWeb方法，带详情API名称的场景
     */
    @Test
    void testGetLogInfoListForWebWithDetailApiName() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "getLogInfoListForWeb");
        GetLogInfoListForWeb.Arg arg = new GetLogInfoListForWeb.Arg();
        arg.setApiName("AccountObj");
        arg.setObjectId("test_object_id");
        arg.setDetailApiName("DetailObj");
        arg.setPageSize(20);
        arg.setPageNumber(2);

        // Mock WebSearchResult
        WebSearchResult mockResult = new WebSearchResult();
        com.facishare.paas.appframework.log.dto.PageInfo pageInfo = new com.facishare.paas.appframework.log.dto.PageInfo();
        pageInfo.setTotalCount(15);
        pageInfo.setPageSize(20);
        pageInfo.setPageNumber(2);
        mockResult.setPageInfo(pageInfo);
        mockResult.setModifyRecordList(Lists.newArrayList());

        when(serviceFacade.webSearchModifyRecordForMaster(anyString(), anyString(), anyInt(), anyInt(), any(User.class)))
                .thenReturn(mockResult);

        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> {
            GetLogInfoListForWeb.Result result = objectLogService.getLogInfoListForWeb(arg, context);
            assertNotNull(result);
            assertNotNull(result.getPageInfo());
            assertEquals(Integer.valueOf(15), result.getPageInfo().getTotalCount());
            assertEquals(Integer.valueOf(20), result.getPageInfo().getPageSize());
            assertEquals(Integer.valueOf(2), result.getPageInfo().getPageNumber());
        });

        verify(serviceFacade).webSearchModifyRecordForMaster(eq("test_object_id"), eq("DetailObj"), eq(20), eq(2), any(User.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getLogInfoListForMob方法，正常获取移动端日志列表
     */
    @Test
    void testGetLogInfoListForMob() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "getLogInfoListForMob");
        GetLogInfoListForMod.Arg arg = new GetLogInfoListForMod.Arg();
        arg.setApiName("AccountObj");
        arg.setObjectId("test_object_id");
        arg.setPageSize(10);
        arg.setOperationTime(System.currentTimeMillis());

        // Mock MobSearchResult
        MobSearchResult mockResult = new MobSearchResult();
        mockResult.setTotalCount(8);
        mockResult.setModifyRecordList(Lists.newArrayList());

        when(serviceFacade.mobSearchModifyRecord(anyString(), anyString(), anyInt(), anyLong(), any(User.class)))
                .thenReturn(mockResult);

        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> {
            GetLogInfoListForMod.Result result = objectLogService.getLogInfoListForMob(arg, context);
            assertNotNull(result);
            assertEquals(Integer.valueOf(8), result.getTotalCount());
        });

        verify(serviceFacade).mobSearchModifyRecord(eq("AccountObj"), eq("test_object_id"), eq(10), anyLong(), any(User.class));
    }



    /**
     * GenerateByAI
     * 测试内容描述：测试getFunctionLogDetailList方法，正常获取函数日志详情
     */
    @Test
    void testGetFunctionLogDetailList() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "getFunctionLogDetail");
        GetFunctionLogDetail.Arg arg = new GetFunctionLogDetail.Arg();
        arg.setTraceId("test_trace_id");
        arg.setLogId("test_log_id");

        // Mock SearchFunctionDetailModel.Result
        SearchFunctionDetailModel.Result mockResult = new SearchFunctionDetailModel.Result();
        mockResult.setResults(Lists.newArrayList());

        when(serviceFacade.getFunctionLogDetail(any(User.class), anyString(), anyString()))
                .thenReturn(mockResult);

        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> {
            GetFunctionLogDetail.Result result = objectLogService.getFunctionLogDetailList(arg, context);
            assertNotNull(result);
        });

        verify(serviceFacade).getFunctionLogDetail(any(User.class), eq("test_trace_id"), eq("test_log_id"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getNewFunctionLogDetail方法，正常获取新版函数日志详情
     */
    @Test
    void testGetNewFunctionLogDetail() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "getNewFunctionLogDetail");
        GetFunctionLogDetail.Arg arg = new GetFunctionLogDetail.Arg();
        arg.setTraceId("test_trace_id");
        arg.setLogId("test_log_id");

        // Mock SearchFunctionDetailModel.Result
        SearchFunctionDetailModel.Result mockResult = new SearchFunctionDetailModel.Result();
        mockResult.setResults(Lists.newArrayList());

        when(serviceFacade.getNewFunctionLogDetail(any(User.class), anyString(), anyString()))
                .thenReturn(mockResult);

        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> {
            GetFunctionLogDetail.Result result = objectLogService.getNewFunctionLogDetail(arg, context);
            assertNotNull(result);
        });

        verify(serviceFacade).getNewFunctionLogDetail(any(User.class), eq("test_trace_id"), eq("test_log_id"));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getTenantLogInterval方法，正常获取租户日志间隔
     */
    @Test
    void testGetTenantLogInterval() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "getTenantLogInterval");
        GetTenantLogInterval.Arg arg = new GetTenantLogInterval.Arg();

        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> {
            GetTenantLogInterval.Result result = objectLogService.getTenantLogInterval(arg, context);
            assertNotNull(result);
        });
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试batchQueryObjectActions方法，正常批量查询对象操作
     */
    @Test
    void testBatchQueryObjectActions() {
        // Given
        ServiceContext context = ContextManager.buildServiceContext("modifyLog", "batchQueryObjectActions");
        BatchQueryObjectActions.Arg arg = new BatchQueryObjectActions.Arg();
        arg.setActionCodes(Lists.newArrayList("Add", "Edit", "Delete"));

        // When & Then - 不抛出异常即为成功
        assertDoesNotThrow(() -> {
            BatchQueryObjectActions.Result result = objectLogService.batchQueryObjectActions(arg, context);
            assertNotNull(result);
            assertNotNull(result.getActionInfos());
            assertEquals(3, result.getActionInfos().size());
        });
    }
}
