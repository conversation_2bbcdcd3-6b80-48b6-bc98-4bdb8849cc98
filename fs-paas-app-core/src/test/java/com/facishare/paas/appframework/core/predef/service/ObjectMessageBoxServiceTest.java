package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.MessageBoxService;
import com.facishare.paas.appframework.common.service.dto.FindTodoCount;
import com.facishare.paas.appframework.common.service.dto.FindTodoList;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.predef.service.dto.messagebox.FindTodoMessageCount;
import com.facishare.paas.appframework.core.predef.service.dto.messagebox.FindTodoMessageList;
import com.facishare.paas.appframework.metadata.MetaDataMiscServiceImpl;
import com.google.common.collect.Lists;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectMessageBoxService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("ObjectMessageBoxService单元测试")
class ObjectMessageBoxServiceTest {

    @Mock
    private MessageBoxService messageBoxService;
    
    @Mock
    private ServiceFacade serviceFacade;
    
    @Mock
    private MetaDataMiscServiceImpl miscService;
    
    @InjectMocks
    private ObjectMessageBoxService objectMessageBoxService;
    
    private User testUser;
    private ServiceContext serviceContext;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        testUser = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(testUser)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "message_box", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findTodoMessageCount方法，正常获取待办消息数量
     */
    @Test
    @DisplayName("正常场景 - 测试findTodoMessageCount方法")
    void testFindTodoMessageCount_NormalCase() {
        // 准备测试数据
        FindTodoMessageCount.Arg arg = new FindTodoMessageCount.Arg();
        
        FindTodoCount.Result mockResult = FindTodoCount.Result.builder()
                .total(5)
                .build();
        
        // 配置Mock行为
        when(messageBoxService.findTodoCount(any(User.class), any(FindTodoCount.Arg.class)))
                .thenReturn(mockResult);
        
        // 执行被测试方法
        FindTodoMessageCount.Result result = objectMessageBoxService.findTodoMessageCount(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.getTotal());
        
        // 验证Mock交互
        verify(messageBoxService).findTodoCount(any(User.class), any(FindTodoCount.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findTodoMessageCount方法，零待办消息场景
     */
    @Test
    @DisplayName("边界场景 - 测试findTodoMessageCount方法零待办消息")
    void testFindTodoMessageCount_ZeroCount() {
        // 准备测试数据
        FindTodoMessageCount.Arg arg = new FindTodoMessageCount.Arg();
        
        FindTodoCount.Result mockResult = FindTodoCount.Result.builder()
                .total(0)
                .build();
        
        // 配置Mock行为
        when(messageBoxService.findTodoCount(any(User.class), any(FindTodoCount.Arg.class)))
                .thenReturn(mockResult);
        
        // 执行被测试方法
        FindTodoMessageCount.Result result = objectMessageBoxService.findTodoMessageCount(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getTotal());
        
        // 验证Mock交互
        verify(messageBoxService).findTodoCount(any(User.class), any(FindTodoCount.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findTodoMessageList方法，正常获取待办消息列表
     */
    @Test
    @DisplayName("正常场景 - 测试findTodoMessageList方法")
    void testFindTodoMessageList_NormalCase() {
        // 准备测试数据
        FindTodoMessageList.Arg arg = new FindTodoMessageList.Arg();
        arg.setLimit(10);
        arg.setOffset(0);
        
        FindTodoList.Result mockResult = FindTodoList.Result.builder()
                .messageList(Lists.newArrayList())
                .build();
        
        // 配置Mock行为
        when(messageBoxService.findTodoList(any(User.class), any(FindTodoList.Arg.class)))
                .thenReturn(mockResult);
        
        // 执行被测试方法
        FindTodoMessageList.Result result = objectMessageBoxService.findTodoMessageList(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getMessageList());
        
        // 验证Mock交互
        verify(messageBoxService).findTodoList(any(User.class), any(FindTodoList.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findTodoMessageList方法，空列表场景
     */
    @Test
    @DisplayName("边界场景 - 测试findTodoMessageList方法空列表")
    void testFindTodoMessageList_EmptyList() {
        // 准备测试数据
        FindTodoMessageList.Arg arg = new FindTodoMessageList.Arg();
        arg.setLimit(10);
        arg.setOffset(0);

        FindTodoList.Result mockResult = FindTodoList.Result.builder()
                .messageList(Lists.newArrayList())
                .build();
        
        // 配置Mock行为
        when(messageBoxService.findTodoList(any(User.class), any(FindTodoList.Arg.class)))
                .thenReturn(mockResult);
        
        // 执行被测试方法
        FindTodoMessageList.Result result = objectMessageBoxService.findTodoMessageList(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getMessageList());
        assertTrue(result.getMessageList().isEmpty());
        
        // 验证Mock交互
        verify(messageBoxService).findTodoList(any(User.class), any(FindTodoList.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findTodoMessageList方法，大页码场景
     */
    @Test
    @DisplayName("边界场景 - 测试findTodoMessageList方法大页码")
    void testFindTodoMessageList_LargePageIndex() {
        // 准备测试数据
        FindTodoMessageList.Arg arg = new FindTodoMessageList.Arg();
        arg.setLimit(50);
        arg.setOffset(5000);

        FindTodoList.Result mockResult = FindTodoList.Result.builder()
                .messageList(Lists.newArrayList())
                .build();
        
        // 配置Mock行为
        when(messageBoxService.findTodoList(any(User.class), any(FindTodoList.Arg.class)))
                .thenReturn(mockResult);
        
        // 执行被测试方法
        FindTodoMessageList.Result result = objectMessageBoxService.findTodoMessageList(arg, serviceContext);
        
        // 验证结果
        assertNotNull(result);
        assertNotNull(result.getMessageList());
        
        // 验证Mock交互
        verify(messageBoxService).findTodoList(any(User.class), any(FindTodoList.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectMessageBoxService);
        assertNotNull(messageBoxService);
        assertNotNull(serviceFacade);
        assertNotNull(miscService);
    }
}
