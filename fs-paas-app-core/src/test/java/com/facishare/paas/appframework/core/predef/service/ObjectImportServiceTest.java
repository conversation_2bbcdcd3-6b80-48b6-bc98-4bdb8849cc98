package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.config.ConfigService;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.objectImport.*;
import com.facishare.paas.appframework.core.predef.service.dto.file.ExecuteExportTaskHookFunction;
import com.facishare.paas.appframework.metadata.dto.ImportObjectModule;
import com.facishare.paas.appframework.metadata.dto.ImportObjectInfo;
import com.facishare.paas.appframework.metadata.dto.SaveImportConfig;
import com.facishare.paas.appframework.metadata.dto.ImportSetting;
import com.facishare.paas.appframework.metadata.ExportTaskHookService;
import com.facishare.paas.appframework.metadata.importobject.ImportObjectProvider;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.FunctionRestProxy;
import com.facishare.paas.appframework.metadata.DescribeLogicService;

import com.facishare.paas.appframework.metadata.ImportService;
import com.facishare.paas.appframework.metadata.importobject.ImportObjectModuleManager;
import com.facishare.paas.appframework.metadata.importobject.ObjectImportInitManager;
import com.facishare.paas.appframework.metadata.importobject.ObjectImportInitProvider;
import com.facishare.paas.appframework.common.util.UnionImportConfig;
import com.facishare.paas.appframework.metadata.switchcache.SwitchCacheService;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.appframework.privilege.UserRoleInfoService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.service.IUniqueRuleService;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectImportService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectImportService单元测试")
class ObjectImportServiceTest {

    @Mock
    private ObjectImportInitManager objectImportInitManager;

    @Mock
    private ObjectImportInitProvider objectImportInitProvider;

    @Mock
    private ImportObjectProvider importObjectProvider;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private IUniqueRuleService uniqueRuleService;
    
    @Mock
    private UserRoleInfoService userRoleInfoService;
    
    @Mock
    private ImportObjectModuleManager importObjectModuleManager;
    
    @Mock
    private SwitchCacheService switchCacheService;
    
    @Mock
    private FunctionRestProxy functionRestProxy;
    
    @Mock
    private FunctionLogicService functionLogicService;
    
    @Mock
    private FunctionPrivilegeService functionPrivilegeService;
    
    @Mock
    private ConfigService configService;
    
    @Mock
    private ServiceFacade serviceFacade;
    
    @Mock
    private ExportTaskHookService exportTaskHookService;
    
    @Mock
    private ImportService importService;
    
    @InjectMocks
    private ObjectImportService objectImportService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String OBJECT_API_NAME = "test_object__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "object_import", "test_method");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取关联对象成功的场景
     */
    @Test
    @DisplayName("测试获取关联对象成功")
    void testGetRelatedObjectsSuccess() {
        try (MockedStatic<UnionImportConfig> mockedStatic = mockStatic(UnionImportConfig.class)) {
            // Arrange
            GetRelatedObjects.Arg arg = new GetRelatedObjects.Arg();
            arg.setDescribeApiName(OBJECT_API_NAME);

            IObjectDescribe detailDescribe1 = new ObjectDescribe();
            detailDescribe1.setApiName("detail1__c");
            detailDescribe1.setDisplayName("Detail 1");

            IObjectDescribe detailDescribe2 = new ObjectDescribe();
            detailDescribe2.setApiName("detail2__c");
            detailDescribe2.setDisplayName("Detail 2");

            List<IObjectDescribe> detailDescribes = Arrays.asList(detailDescribe1, detailDescribe2);
            List<IObjectDescribe> validDescribes = Arrays.asList(detailDescribe1);

            mockedStatic.when(() -> UnionImportConfig.canNotBeMaster(OBJECT_API_NAME)).thenReturn(false);
            when(describeLogicService.findSimpleDetailDescribes(TENANT_ID, OBJECT_API_NAME))
                    .thenReturn(detailDescribes);
            when(describeLogicService.filterDescribesWithActionCode(eq(user), any(List.class), anyString()))
                    .thenReturn(validDescribes);

            // Act
            GetRelatedObjects.Result result = objectImportService.getRelatedObjects(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getDescribes());
            assertEquals(1, result.getDescribes().size());
            verify(describeLogicService).findSimpleDetailDescribes(TENANT_ID, OBJECT_API_NAME);
            verify(describeLogicService).filterDescribesWithActionCode(eq(user), any(List.class), anyString());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取关联对象时主对象不能作为主对象的场景
     */
    @Test
    @DisplayName("测试获取关联对象 - 主对象不能作为主对象")
    void testGetRelatedObjectsWithCanNotBeMaster() {
        try (MockedStatic<UnionImportConfig> mockedStatic = mockStatic(UnionImportConfig.class)) {
            // Arrange
            GetRelatedObjects.Arg arg = new GetRelatedObjects.Arg();
            arg.setDescribeApiName("personnel__c"); // 假设这是一个不能作为主对象的API名称

            mockedStatic.when(() -> UnionImportConfig.canNotBeMaster("personnel__c")).thenReturn(true);
            when(describeLogicService.filterDescribesWithActionCode(eq(user), any(List.class), anyString()))
                    .thenReturn(Collections.emptyList());

            // Act
            GetRelatedObjects.Result result = objectImportService.getRelatedObjects(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getDescribes());
            assertTrue(result.getDescribes().isEmpty());
            verify(describeLogicService, never()).findSimpleDetailDescribes(any(), any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取导出详情对象成功的场景
     */
    @Test
    @DisplayName("测试获取导出详情对象成功")
    void testGetExportDetailObjectsSuccess() {
        // Arrange
        GetExportObjects.Arg arg = new GetExportObjects.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);

        // Act
        GetExportObjects.Result result = objectImportService.getExportDetailObjects(arg, serviceContext);

        // Assert
        assertNotNull(result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取导出详情对象时对象API名称为空的场景
     */
    @Test
    @DisplayName("测试获取导出详情对象 - 对象API名称为空")
    void testGetExportDetailObjectsWithBlankApiName() {
        // Arrange
        GetExportObjects.Arg arg = new GetExportObjects.Arg();
        arg.setDescribeApiName("");

        // Act
        GetExportObjects.Result result = objectImportService.getExportDetailObjects(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getDescribes());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取导入对象列表成功的场景
     */
    @Test
    @DisplayName("测试获取导入对象列表成功")
    void testGetImportObjectListSuccess() {
        // Arrange
        GetImportObjectList.Arg arg = new GetImportObjectList.Arg();
        arg.setManagement(false);

        // Act
        GetImportObjectList.Result result = objectImportService.getImportObjectList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getImportObjectList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取导入对象列表时为管理模式的场景
     */
    @Test
    @DisplayName("测试获取导入对象列表 - 管理模式")
    void testGetImportObjectListWithManagementMode() {
        // Arrange
        GetImportObjectList.Arg arg = new GetImportObjectList.Arg();
        arg.setManagement(true);

        // Act
        GetImportObjectList.Result result = objectImportService.getImportObjectList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getImportObjectList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取导入对象成功的场景
     */
    @Test
    @DisplayName("测试获取导入对象成功")
    void testGetImportObjectSuccess() {
        // Arrange
        GetImportObject.Arg arg = new GetImportObject.Arg();
        arg.setObjectCode(OBJECT_API_NAME);

        IObjectDescribe mockDescribe = new ObjectDescribe();
        mockDescribe.setApiName(OBJECT_API_NAME);

        when(describeLogicService.findObject(TENANT_ID, OBJECT_API_NAME)).thenReturn(mockDescribe);
        when(objectImportInitManager.getLocalProvider(OBJECT_API_NAME)).thenReturn(objectImportInitProvider);
        when(objectImportInitProvider.getImportObject(any(), any())).thenReturn(Optional.empty());

        // Act
        GetImportObject.Result result = objectImportService.getImportObject(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(describeLogicService).findObject(TENANT_ID, OBJECT_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取导入对象时包含详情对象的场景
     */
    @Test
    @DisplayName("测试获取导入对象 - 包含详情对象")
    void testGetImportObjectWithDetailObject() {
        // Arrange
        GetImportObject.Arg arg = new GetImportObject.Arg();
        arg.setObjectCode(OBJECT_API_NAME);
        arg.setDetailObjectApiName("detail_object__c");

        IObjectDescribe masterDescribe = new ObjectDescribe();
        masterDescribe.setApiName(OBJECT_API_NAME);
        
        IObjectDescribe detailDescribe = new ObjectDescribe();
        detailDescribe.setApiName("detail_object__c");

        when(describeLogicService.findObject(TENANT_ID, OBJECT_API_NAME)).thenReturn(masterDescribe);
        when(describeLogicService.findObject(TENANT_ID, "detail_object__c")).thenReturn(detailDescribe);
        when(objectImportInitManager.getLocalProvider(OBJECT_API_NAME)).thenReturn(objectImportInitProvider);
        when(objectImportInitProvider.getImportObject(any(), any())).thenReturn(Optional.empty());

        // Act
        GetImportObject.Result result = objectImportService.getImportObject(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(describeLogicService).findObject(TENANT_ID, OBJECT_API_NAME);
        verify(describeLogicService).findObject(TENANT_ID, "detail_object__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取导入对象模块成功的场景
     */
    @Test
    @DisplayName("测试获取导入对象模块成功")
    void testGetImportObjectModuleSuccess() {
        // Arrange
        ImportObjectModule.Arg arg = new ImportObjectModule.Arg();
        arg.setObjectModule("test_module");
        arg.setContext(ImportObjectProvider.ImportModuleContext.of(false));

        when(importObjectModuleManager.getLocalProvider("test_module"))
                .thenReturn(Optional.of(importObjectProvider));
        when(importObjectProvider.getImportObjectModule(eq(user), any(ImportObjectProvider.ImportModuleContext.class)))
                .thenReturn(Collections.emptyList());

        // Act
        ImportObjectModule.Result result = objectImportService.getImportObjectModule(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getImportObjectModules());
        verify(importObjectModuleManager).getLocalProvider("test_module");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取导入对象模块时模块不存在的场景
     */
    @Test
    @DisplayName("测试获取导入对象模块 - 模块不存在")
    void testGetImportObjectModuleNotFound() {
        // Arrange
        ImportObjectModule.Arg arg = new ImportObjectModule.Arg();
        arg.setObjectModule("non_existent_module");
        arg.setContext(ImportObjectProvider.ImportModuleContext.of(false));

        when(importObjectModuleManager.getLocalProvider("non_existent_module"))
                .thenReturn(Optional.empty());

        // Act
        ImportObjectModule.Result result = objectImportService.getImportObjectModule(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getImportObjectModules());
        assertTrue(result.getImportObjectModules().isEmpty());
        verify(importObjectModuleManager).getLocalProvider("non_existent_module");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试获取导入对象信息成功的场景
     */
    @Test
    @DisplayName("测试获取导入对象信息成功")
    void testGetImportObjectInfoSuccess() {
        // Arrange
        ImportObjectInfo.Arg arg = new ImportObjectInfo.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setObjectCode("test_code");

        IObjectDescribe mockDescribe = new ObjectDescribe();
        mockDescribe.setApiName(OBJECT_API_NAME);

        when(describeLogicService.findObject(TENANT_ID, OBJECT_API_NAME)).thenReturn(mockDescribe);
        when(objectImportInitManager.getLocalProvider("test_code")).thenReturn(objectImportInitProvider);
        when(objectImportInitProvider.getImportObject(any(), any())).thenReturn(Optional.empty());

        // Act
        ImportObjectInfo.Result result = objectImportService.getImportObject(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(describeLogicService).findObject(TENANT_ID, OBJECT_API_NAME);
        verify(objectImportInitManager).getLocalProvider("test_code");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存导入配置成功的场景
     */
    @Test
    @DisplayName("测试保存导入配置成功")
    void testSaveImportConfigSuccess() {
        // Arrange
        SaveImportConfig.Arg arg = new SaveImportConfig.Arg();

        // Create ImportSetting with required describeApiName
        ImportSetting insertImport = ImportSetting.builder()
                .describeApiName(OBJECT_API_NAME)
                .insertImportSwitch(true)
                .triggerWorkFlow(false)
                .triggerApprovalFlow(false)
                .build();

        ImportSetting updateImport = ImportSetting.builder()
                .describeApiName(OBJECT_API_NAME)
                .updateImportSwitch(true)
                .triggerWorkFlow(false)
                .build();

        arg.setInsertImport(insertImport);
        arg.setUpdateImport(updateImport);

        // Act
        SaveImportConfig.Result result = objectImportService.saveImportConfig(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(configService).upsertTenantConfig(eq(user), eq("importSetting_" + OBJECT_API_NAME), any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试执行导出任务钩子函数成功的场景
     */
    @Test
    @DisplayName("测试执行导出任务钩子函数成功")
    void testExecuteExportTaskHookFunctionSuccess() {
        // Arrange
        ExecuteExportTaskHookFunction.Arg arg = new ExecuteExportTaskHookFunction.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setType("before");
        arg.setFilePath("/test/path");
        arg.setOriginalFilename("test.xlsx");
        arg.setFileType("xlsx");
        arg.setExpiredDay(7);

        ExportTaskHookService.Result hookResult = ExportTaskHookService.Result.builder()
                .filePath("/test/result/path")
                .build();

        when(exportTaskHookService.before(eq(user), any(ExportTaskHookService.Arg.class)))
                .thenReturn(hookResult);

        // Act
        ExecuteExportTaskHookFunction.Result result = objectImportService.executeExportTaskHookFunction(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(exportTaskHookService).before(eq(user), any(ExportTaskHookService.Arg.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试执行导出任务钩子函数 - after类型
     */
    @Test
    @DisplayName("测试执行导出任务钩子函数 - after类型")
    void testExecuteExportTaskHookFunctionAfterType() {
        // Arrange
        ExecuteExportTaskHookFunction.Arg arg = new ExecuteExportTaskHookFunction.Arg();
        arg.setDescribeApiName(OBJECT_API_NAME);
        arg.setType("after");
        arg.setFilePath("/test/path");

        ExportTaskHookService.Result hookResult = ExportTaskHookService.Result.builder()
                .filePath("/test/result/path")
                .build();

        when(exportTaskHookService.after(eq(user), any(ExportTaskHookService.Arg.class)))
                .thenReturn(hookResult);

        // Act
        ExecuteExportTaskHookFunction.Result result = objectImportService.executeExportTaskHookFunction(serviceContext, arg);

        // Assert
        assertNotNull(result);
        verify(exportTaskHookService).after(eq(user), any(ExportTaskHookService.Arg.class));
    }
}
