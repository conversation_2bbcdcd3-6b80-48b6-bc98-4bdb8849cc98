package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.model.OutInfoChangeModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.prm.enums.ChannelNoticeBizType;
import com.facishare.paas.metadata.api.IObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * OutChannelCrmNotificationService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
@DisplayName("OutChannelCrmNotificationService单元测试")
class OutChannelCrmNotificationServiceTest {

    @Mock
    private CRMNotificationServiceImpl crmNotificationService;
    
    @Mock
    private WXOfficialAccountService wxOfficialAccountService;
    
    @InjectMocks
    private OutChannelCrmNotificationService outChannelCrmNotificationService;
    
    private User user;
    private NewCrmNotification newCrmNotification;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        newCrmNotification = mock(NewCrmNotification.class);
        when(newCrmNotification.getAppId()).thenReturn("testApp");
        when(newCrmNotification.getFullContent()).thenReturn("Test notification content");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试填充发送外部渠道信息 - 对象分配场景
     */
    @Test
    @DisplayName("测试fillSendOutChannelInfo对象分配场景")
    void testFillSendOutChannelInfoAllocateObject() {
        // Arrange
        OutInfoChangeModel outInfoChangeModel = new OutInfoChangeModel();
        outInfoChangeModel.setDisplayName("Test Object");
        outInfoChangeModel.setDataName("Test Data");
        
        IObjectData mockWxTemplate = mock(IObjectData.class);
        when(mockWxTemplate.get(eq("wxAppId"), eq(String.class))).thenReturn("wx123456");
        when(mockWxTemplate.get(eq("templateId"), eq(String.class))).thenReturn("template123");
        
        // 模拟私有方法getOpenTemplateByNotificationType的行为
        // 由于是私有方法，我们通过验证最终的服务调用来间接测试

        // Act
        outChannelCrmNotificationService.fillSendOutChannelInfo(
                user, newCrmNotification, ChannelNoticeBizType.ALLOCATE_OBJECT, outInfoChangeModel);

        // Assert
        verify(crmNotificationService).sendNewCrmNotification(eq(user), eq(newCrmNotification));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试填充发送外部渠道信息 - 更换外部团队场景
     */
    @Test
    @DisplayName("测试fillSendOutChannelInfo更换外部团队场景")
    void testFillSendOutChannelInfoChangeOutTeam() {
        // Arrange
        // 不需要额外的数据对象

        // Act
        outChannelCrmNotificationService.fillSendOutChannelInfo(
                user, newCrmNotification, ChannelNoticeBizType.CHANGE_OUT_TEAM, null);

        // Assert
        verify(crmNotificationService).sendNewCrmNotification(eq(user), eq(newCrmNotification));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试填充发送外部渠道信息 - 未实现的操作类型
     */
    @Test
    @DisplayName("测试fillSendOutChannelInfo未实现的操作类型")
    void testFillSendOutChannelInfoUnimplementedType() {
        // Arrange
        // 使用一个未在switch中处理的枚举值
        ChannelNoticeBizType unimplementedType = ChannelNoticeBizType.ALLOCATE_OBJECT; // 假设有其他类型

        // Act
        outChannelCrmNotificationService.fillSendOutChannelInfo(
                user, newCrmNotification, unimplementedType, null);

        // Assert
        verify(crmNotificationService).sendNewCrmNotification(eq(user), eq(newCrmNotification));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试填充发送外部渠道信息时用户为null
     */
    @Test
    @DisplayName("测试fillSendOutChannelInfo用户为null")
    void testFillSendOutChannelInfoWithNullUser() {
        // Act & Assert
        assertDoesNotThrow(() -> {
            outChannelCrmNotificationService.fillSendOutChannelInfo(
                    null, newCrmNotification, ChannelNoticeBizType.ALLOCATE_OBJECT, null);
        });
        
        verify(crmNotificationService).sendNewCrmNotification(eq(null), eq(newCrmNotification));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试填充发送外部渠道信息时通知对象为null
     */
    @Test
    @DisplayName("测试fillSendOutChannelInfo通知对象为null")
    void testFillSendOutChannelInfoWithNullNotification() {
        // Act & Assert
        assertDoesNotThrow(() -> {
            outChannelCrmNotificationService.fillSendOutChannelInfo(
                    user, null, ChannelNoticeBizType.ALLOCATE_OBJECT, null);
        });
        
        verify(crmNotificationService).sendNewCrmNotification(eq(user), eq(null));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试填充发送外部渠道信息时业务类型为null
     */
    @Test
    @DisplayName("测试fillSendOutChannelInfo业务类型为null")
    void testFillSendOutChannelInfoWithNullBizType() {
        // Act & Assert - null参数应该抛出NullPointerException
        assertThrows(NullPointerException.class, () -> {
            outChannelCrmNotificationService.fillSendOutChannelInfo(
                    user, newCrmNotification, null, null);
        });

        // 由于抛出异常，不会调用sendNewCrmNotification方法
        verify(crmNotificationService, never()).sendNewCrmNotification(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象分配场景时数据为null
     */
    @Test
    @DisplayName("测试fillSendOutChannelInfo对象分配数据为null")
    void testFillSendOutChannelInfoAllocateObjectWithNullData() {
        // Act
        outChannelCrmNotificationService.fillSendOutChannelInfo(
                user, newCrmNotification, ChannelNoticeBizType.ALLOCATE_OBJECT, null);

        // Assert
        verify(crmNotificationService).sendNewCrmNotification(eq(user), eq(newCrmNotification));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象分配场景时OutInfoChangeModel数据完整
     */
    @Test
    @DisplayName("测试fillSendOutChannelInfo对象分配完整数据")
    void testFillSendOutChannelInfoAllocateObjectWithCompleteData() {
        // Arrange
        OutInfoChangeModel outInfoChangeModel = new OutInfoChangeModel();
        outInfoChangeModel.setDisplayName("Complete Object");
        outInfoChangeModel.setDataName("Complete Data");
        outInfoChangeModel.setOldOutEI(1);
        outInfoChangeModel.setNewOutEI(2);
        outInfoChangeModel.setOldOutUserId(100);
        outInfoChangeModel.setNewOutUserId(200);

        // Act
        outChannelCrmNotificationService.fillSendOutChannelInfo(
                user, newCrmNotification, ChannelNoticeBizType.ALLOCATE_OBJECT, outInfoChangeModel);

        // Assert
        verify(crmNotificationService).sendNewCrmNotification(eq(user), eq(newCrmNotification));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(outChannelCrmNotificationService);
        assertNotNull(crmNotificationService);
        assertNotNull(wxOfficialAccountService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试User对象构造是否正确
     */
    @Test
    @DisplayName("测试User对象构造正确")
    void testUserConstructionSuccess() {
        // Assert
        assertNotNull(user);
        assertEquals(TENANT_ID, user.getTenantId());
        assertEquals(USER_ID, user.getUserId());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试NewCrmNotification对象构造是否正确
     */
    @Test
    @DisplayName("测试NewCrmNotification对象构造正确")
    void testNewCrmNotificationConstructionSuccess() {
        // Assert
        assertNotNull(newCrmNotification);
        assertEquals("testApp", newCrmNotification.getAppId());
        assertEquals("Test notification content", newCrmNotification.getFullContent());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试CRM通知服务调用验证
     */
    @Test
    @DisplayName("测试CRM通知服务调用验证")
    void testCrmNotificationServiceCallVerification() {
        // Arrange
        NewCrmNotification customNotification = mock(NewCrmNotification.class);
        when(customNotification.getAppId()).thenReturn("customApp");

        // Act
        outChannelCrmNotificationService.fillSendOutChannelInfo(
                user, customNotification, ChannelNoticeBizType.CHANGE_OUT_TEAM, null);

        // Assert
        verify(crmNotificationService, times(1)).sendNewCrmNotification(eq(user), eq(customNotification));
    }
}
