package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.predef.service.dto.ai.FindInsightResult;
import com.facishare.paas.appframework.core.predef.service.dto.ai.SaveInsightResult;
import com.facishare.paas.appframework.metadata.ai.AiInsightLogicService;
import com.facishare.paas.appframework.metadata.repository.model.AiInsightEntity;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectAiInsightService单元测试类（Java版本）
 * 测试AI洞察服务的所有@ServiceMethod方法
 * 严格遵循JDK8语法规范，不使用JDK8以上新特性
 */
@DisplayName("ObjectAiInsightService单元测试")
class ObjectAiInsightServiceJavaTest extends BaseServiceTest {

    @Mock
    private AiInsightLogicService aiInsightLogicService;
    
    @Mock
    private ServiceFacade serviceFacade;
    
    @Mock
    private FunctionPrivilegeService functionPrivilegeService;
    
    @InjectMocks
    private ObjectAiInsightService objectAiInsightService;

    private static final String OBJECT_API_NAME = "TestObj__c";
    private static final String DATA_ID = "data123";
    private static final String COMPONENT_API_NAME = "TestComponent";
    private static final Long GENERATE_TIME = 1624512345678L;

    @Override
    protected String getServiceName() {
        return "ai_insight";
    }

    @BeforeEach
    void setUp() {
        // 基础设置已在BaseServiceTest中完成
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找AI洞察结果成功的场景
     */
    @Test
    @DisplayName("测试findAiInsightResult成功")
    void testFindAiInsightResultSuccess() {
        // Arrange
        FindInsightResult.Arg arg = new FindInsightResult.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setComponentApiName(COMPONENT_API_NAME);

        AiInsightEntity mockEntity = AiInsightEntity.builder()
                .relateObjectApiName(OBJECT_API_NAME)
                .relateDataId(DATA_ID)
                .componentApiName(COMPONENT_API_NAME)
                .insightResult("{\"key\":\"value\"}")
                .generateTime(1624512345678L)
                .build();

        when(aiInsightLogicService.findByUniqKey(testUser, OBJECT_API_NAME, DATA_ID, COMPONENT_API_NAME))
                .thenReturn(mockEntity);

        // Act
        FindInsightResult.Result result = objectAiInsightService.findAiInsightResult(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getInsightResult());
        assertEquals(GENERATE_TIME, result.getGenerateTime());
        verify(aiInsightLogicService).findByUniqKey(testUser, OBJECT_API_NAME, DATA_ID, COMPONENT_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找AI洞察结果时实体不存在的场景
     */
    @Test
    @DisplayName("测试findAiInsightResult实体不存在")
    void testFindAiInsightResultEntityNotFound() {
        // Arrange
        FindInsightResult.Arg arg = new FindInsightResult.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setComponentApiName(COMPONENT_API_NAME);

        when(aiInsightLogicService.findByUniqKey(testUser, OBJECT_API_NAME, DATA_ID, COMPONENT_API_NAME))
                .thenReturn(null);

        // Act
        FindInsightResult.Result result = objectAiInsightService.findAiInsightResult(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getInsightResult());
        assertNull(result.getGenerateTime());
        verify(aiInsightLogicService).findByUniqKey(testUser, OBJECT_API_NAME, DATA_ID, COMPONENT_API_NAME);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存AI洞察结果成功的场景
     */
    @Test
    @DisplayName("测试saveInsightResult成功")
    void testSaveInsightResultSuccess() {
        // Arrange
        SaveInsightResult.Arg arg = new SaveInsightResult.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setComponentApiName(COMPONENT_API_NAME);
        arg.setGenerateTime(1624512345678L);
        
        Map<String, Object> insightResult = new HashMap<>();
        insightResult.put(SaveInsightResult.ORIG_RESULT, "original result");
        arg.setInsightResult(insightResult);

        // Act
        SaveInsightResult.Result result = objectAiInsightService.saveInsightResult(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(aiInsightLogicService).save(eq(testUser), any(AiInsightEntity.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存AI洞察结果时洞察结果为空的场景
     */
    @Test
    @DisplayName("测试saveInsightResult洞察结果为空")
    void testSaveInsightResultWithEmptyInsightResult() {
        // Arrange
        SaveInsightResult.Arg arg = new SaveInsightResult.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setComponentApiName(COMPONENT_API_NAME);
        arg.setInsightResult(new HashMap<>());

        // Act
        SaveInsightResult.Result result = objectAiInsightService.saveInsightResult(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(aiInsightLogicService, never()).save(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存AI洞察结果时洞察结果为null的场景
     */
    @Test
    @DisplayName("测试saveInsightResult洞察结果为null")
    void testSaveInsightResultWithNullInsightResult() {
        // Arrange
        SaveInsightResult.Arg arg = new SaveInsightResult.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setComponentApiName(COMPONENT_API_NAME);
        arg.setInsightResult(null);

        // Act
        SaveInsightResult.Result result = objectAiInsightService.saveInsightResult(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(aiInsightLogicService, never()).save(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存AI洞察结果时包含映射对象的场景
     */
    @Test
    @DisplayName("测试saveInsightResult包含映射对象")
    void testSaveInsightResultWithMappingObject() {
        // Arrange
        SaveInsightResult.Arg arg = new SaveInsightResult.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setComponentApiName(COMPONENT_API_NAME);
        arg.setGenerateTime(1624512345678L);
        arg.setMappingObjectApiName("MappingObj__c");
        
        Map<String, String> fieldMapping = new HashMap<>();
        fieldMapping.put("field1", "name");
        fieldMapping.put("field2", "description");
        arg.setFieldMapping(fieldMapping);
        
        Map<String, Object> insightResult = new HashMap<>();
        insightResult.put(SaveInsightResult.ORIG_RESULT, "original result");
        insightResult.put(SaveInsightResult.JSON_RESULT, new HashMap<>());
        arg.setInsightResult(insightResult);

        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        when(mockDescribe.isActive()).thenReturn(true);
        when(serviceFacade.findObject(TENANT_ID, "MappingObj__c")).thenReturn(mockDescribe);

        // Act
        SaveInsightResult.Result result = objectAiInsightService.saveInsightResult(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(aiInsightLogicService).save(eq(testUser), any(AiInsightEntity.class));
        verify(serviceFacade).findObject(TENANT_ID, "MappingObj__c");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存AI洞察结果时发生异常的场景
     */
    @Test
    @DisplayName("测试saveInsightResult发生异常")
    void testSaveInsightResultWithException() {
        // Arrange
        SaveInsightResult.Arg arg = new SaveInsightResult.Arg();
        arg.setObjectApiName(OBJECT_API_NAME);
        arg.setDataId(DATA_ID);
        arg.setComponentApiName(COMPONENT_API_NAME);
        
        Map<String, Object> insightResult = new HashMap<>();
        insightResult.put(SaveInsightResult.ORIG_RESULT, "original result");
        arg.setInsightResult(insightResult);

        doThrow(new RuntimeException("Save failed"))
                .when(aiInsightLogicService).save(eq(testUser), any(AiInsightEntity.class));

        // Act
        SaveInsightResult.Result result = objectAiInsightService.saveInsightResult(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(aiInsightLogicService).save(eq(testUser), any(AiInsightEntity.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectAiInsightService);
        assertNotNull(aiInsightLogicService);
        assertNotNull(serviceFacade);
        assertNotNull(functionPrivilegeService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ServiceContext构造是否正确
     */
    @Test
    @DisplayName("测试ServiceContext构造正确")
    void testServiceContextConstructionSuccess() {
        // Assert
        assertNotNull(serviceContext);
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(testUser, serviceContext.getRequestContext().getUser());
        assertEquals("ai_insight", serviceContext.getServiceName());
        assertEquals("test", serviceContext.getServiceMethod());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试User对象构造是否正确
     */
    @Test
    @DisplayName("测试User对象构造正确")
    void testUserConstructionSuccess() {
        // Assert
        assertNotNull(testUser);
        assertEquals(TENANT_ID, testUser.getTenantId());
        assertEquals(USER_ID, testUser.getUserId());
    }
}
