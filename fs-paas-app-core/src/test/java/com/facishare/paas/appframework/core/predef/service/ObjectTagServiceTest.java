package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.predef.service.dto.tag.RemoveTag;
import com.facishare.paas.appframework.core.predef.service.dto.tag.RemoveTagByDataId;
import com.facishare.paas.appframework.core.predef.service.dto.tag.SaveTag;
import com.facishare.paas.appframework.metadata.TagService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectTagService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectTagService单元测试")
class ObjectTagServiceTest {

    @Mock
    private TagService tagService;
    
    @InjectMocks
    private ObjectTagService objectTagService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "test_object__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .build();
        serviceContext = new ServiceContext(requestContext, "tag", "test_method");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存标签成功的正常场景
     */
    @Test
    @DisplayName("测试保存标签成功")
    void testSaveTagSuccess() {
        // Arrange
        SaveTag.Arg arg = new SaveTag.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        
        SaveTag.TagData tagData = new SaveTag.TagData();
        tagData.setDataId("data1");
        
        SaveTag.TagInfo tagInfo1 = new SaveTag.TagInfo();
        tagInfo1.setTagId("tag1");
        tagInfo1.setLevel1("level1");
        tagInfo1.setLevel2("level2");
        tagInfo1.setLevel3("level3");
        
        SaveTag.TagInfo tagInfo2 = new SaveTag.TagInfo();
        tagInfo2.setTagId("tag2");
        tagInfo2.setLevel1("level1_2");
        tagInfo2.setLevel2("level2_2");
        tagInfo2.setLevel3("level3_2");
        
        tagData.setTagInfoList(Arrays.asList(tagInfo1, tagInfo2));
        arg.setTagList(Arrays.asList(tagData));

        doNothing().when(tagService).saveTag(eq(user), eq(DESCRIBE_API_NAME), any(Map.class));

        // Act
        SaveTag.Result result = objectTagService.saveTag(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagService).saveTag(eq(user), eq(DESCRIBE_API_NAME), any(Map.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存标签空列表场景
     */
    @Test
    @DisplayName("测试保存标签空列表")
    void testSaveTagEmptyList() {
        // Arrange
        SaveTag.Arg arg = new SaveTag.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTagList(Arrays.asList());

        doNothing().when(tagService).saveTag(eq(user), eq(DESCRIBE_API_NAME), any(Map.class));

        // Act
        SaveTag.Result result = objectTagService.saveTag(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagService).saveTag(eq(user), eq(DESCRIBE_API_NAME), any(Map.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存标签null列表场景
     */
    @Test
    @DisplayName("测试保存标签null列表")
    void testSaveTagNullList() {
        // Arrange
        SaveTag.Arg arg = new SaveTag.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTagList(null);

        doNothing().when(tagService).saveTag(eq(user), eq(DESCRIBE_API_NAME), any(Map.class));

        // Act
        SaveTag.Result result = objectTagService.saveTag(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagService).saveTag(eq(user), eq(DESCRIBE_API_NAME), any(Map.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据数据ID删除标签成功的正常场景
     */
    @Test
    @DisplayName("测试根据数据ID删除标签成功")
    void testRemoveTagByDataIdSuccess() {
        // Arrange
        RemoveTagByDataId.Arg arg = new RemoveTagByDataId.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataIdList(Arrays.asList("data1", "data2"));

        doNothing().when(tagService).bulkRemoveTagForData(eq(user), (List<String>) eq(arg.getDataIdList()), eq(DESCRIBE_API_NAME));

        // Act
        RemoveTagByDataId.Result result = objectTagService.removeTagByDataId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagService).bulkRemoveTagForData(eq(user), (List<String>) eq(arg.getDataIdList()), eq(DESCRIBE_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据数据ID删除标签空列表场景
     */
    @Test
    @DisplayName("测试根据数据ID删除标签空列表")
    void testRemoveTagByDataIdEmptyList() {
        // Arrange
        RemoveTagByDataId.Arg arg = new RemoveTagByDataId.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataIdList(Arrays.asList());

        doNothing().when(tagService).bulkRemoveTagForData(eq(user), (List<String>) eq(arg.getDataIdList()), eq(DESCRIBE_API_NAME));

        // Act
        RemoveTagByDataId.Result result = objectTagService.removeTagByDataId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagService).bulkRemoveTagForData(eq(user), (List<String>) eq(arg.getDataIdList()), eq(DESCRIBE_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据数据ID删除标签null列表场景
     */
    @Test
    @DisplayName("测试根据数据ID删除标签null列表")
    void testRemoveTagByDataIdNullList() {
        // Arrange
        RemoveTagByDataId.Arg arg = new RemoveTagByDataId.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataIdList(null);

        doNothing().when(tagService).bulkRemoveTagForData(eq(user), (List<String>) isNull(), eq(DESCRIBE_API_NAME));

        // Act
        RemoveTagByDataId.Result result = objectTagService.removeTagByDataId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagService).bulkRemoveTagForData(eq(user), (List<String>) isNull(), eq(DESCRIBE_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除标签成功的正常场景
     */
    @Test
    @DisplayName("测试删除标签成功")
    void testRemoveTagSuccess() {
        // Arrange
        RemoveTag.Arg arg = new RemoveTag.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        
        Map<String, List<String>> tagInfo = new HashMap<>();
        tagInfo.put("tag1", Arrays.asList("data1", "data2"));
        tagInfo.put("tag2", Arrays.asList("data3"));
        arg.setTagInfo(tagInfo);

        doNothing().when(tagService).bulkRemoveTagForData(eq(user), (Map<String, List<String>>) eq(tagInfo), eq(DESCRIBE_API_NAME));

        // Act
        RemoveTag.Result result = objectTagService.removeTag(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagService).bulkRemoveTagForData(eq(user), (Map<String, List<String>>) eq(tagInfo), eq(DESCRIBE_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除标签空Map场景
     */
    @Test
    @DisplayName("测试删除标签空Map")
    void testRemoveTagEmptyMap() {
        // Arrange
        RemoveTag.Arg arg = new RemoveTag.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTagInfo(new HashMap<>());

        doNothing().when(tagService).bulkRemoveTagForData(eq(user), (Map<String, List<String>>) eq(arg.getTagInfo()), eq(DESCRIBE_API_NAME));

        // Act
        RemoveTag.Result result = objectTagService.removeTag(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagService).bulkRemoveTagForData(eq(user), (Map<String, List<String>>) eq(arg.getTagInfo()), eq(DESCRIBE_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除标签null Map场景
     */
    @Test
    @DisplayName("测试删除标签null Map")
    void testRemoveTagNullMap() {
        // Arrange
        RemoveTag.Arg arg = new RemoveTag.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setTagInfo(null);

        doNothing().when(tagService).bulkRemoveTagForData(eq(user), (Map<String, List<String>>) isNull(), eq(DESCRIBE_API_NAME));

        // Act
        RemoveTag.Result result = objectTagService.removeTag(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagService).bulkRemoveTagForData(eq(user), (Map<String, List<String>>) isNull(), eq(DESCRIBE_API_NAME));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Arrange & Act & Assert
        assertNotNull(objectTagService);
        assertNotNull(tagService);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试保存标签单个标签场景
     */
    @Test
    @DisplayName("测试保存标签单个标签")
    void testSaveTagSingleTag() {
        // Arrange
        SaveTag.Arg arg = new SaveTag.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        
        SaveTag.TagData tagData = new SaveTag.TagData();
        tagData.setDataId("data1");
        
        SaveTag.TagInfo tagInfo = new SaveTag.TagInfo();
        tagInfo.setTagId("tag1");
        tagInfo.setLevel1("level1");
        tagInfo.setLevel2("level2");
        tagInfo.setLevel3("level3");
        
        tagData.setTagInfoList(Arrays.asList(tagInfo));
        arg.setTagList(Arrays.asList(tagData));

        doNothing().when(tagService).saveTag(eq(user), eq(DESCRIBE_API_NAME), any(Map.class));

        // Act
        SaveTag.Result result = objectTagService.saveTag(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagService).saveTag(eq(user), eq(DESCRIBE_API_NAME), any(Map.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据数据ID删除标签单个数据ID场景
     */
    @Test
    @DisplayName("测试根据数据ID删除标签单个数据ID")
    void testRemoveTagByDataIdSingleDataId() {
        // Arrange
        RemoveTagByDataId.Arg arg = new RemoveTagByDataId.Arg();
        arg.setDescribeApiName(DESCRIBE_API_NAME);
        arg.setDataIdList(Arrays.asList("data1"));

        doNothing().when(tagService).bulkRemoveTagForData(eq(user), (List<String>) eq(arg.getDataIdList()), eq(DESCRIBE_API_NAME));

        // Act
        RemoveTagByDataId.Result result = objectTagService.removeTagByDataId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        verify(tagService).bulkRemoveTagForData(eq(user), (List<String>) eq(arg.getDataIdList()), eq(DESCRIBE_API_NAME));
    }
}
