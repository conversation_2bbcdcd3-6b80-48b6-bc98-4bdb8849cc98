package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.core.model.InfraServiceFacadeImpl;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.draft.*;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectDataDraft;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.lenient;

@ExtendWith(MockitoExtension.class)
class DraftServiceTest {

    private static final String TENANT_ID = "74255";
    private static final String USER_ID = "1000";
    private static final String DRAFT_ID = "test_draft_id";
    private static final String BIZ_TYPE = "Add";
    private static final String BIZ_ID = "test_biz_id";

    @Mock
    private ObjectDataDraftService objectDataDraftService;
    
    @Mock
    private MetaDataService metaDataService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @Mock
    private FunctionPrivilegeService functionPrivilegeService;
    
    @Mock
    private InfraServiceFacadeImpl infraServiceFacade;

    @Mock
    private FileStoreService fileStoreService;
    
    @InjectMocks
    private DraftService draftService;
    
    private ServiceContext serviceContext;
    private User user;

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .build();
        RequestContextManager.setContext(requestContext);
        serviceContext = new ServiceContext(requestContext, "draft", "test");

        // Mock infraServiceFacade.getFileStoreService() - use lenient to avoid unnecessary stubbing errors
        lenient().when(infraServiceFacade.getFileStoreService()).thenReturn(fileStoreService);
    }

    @AfterEach
    void tearDown() {
        RequestContextManager.removeContext();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据用户查找草稿列表成功的场景
     */
    @Test
    @DisplayName("测试根据用户查找草稿列表成功")
    void testFindDraftListByUserSuccess() {
        // Arrange
        DraftData.Arg arg = new DraftData.Arg();
        arg.setLimit(10);
        arg.setOffset(0);
        arg.setSpecifyDataSource(Lists.newArrayList("Add", "Edit"));

        // Create real ObjectDataDraft instead of mock to avoid ClassCastException
        Map<String, Object> draftData = new HashMap<>();
        draftData.put("id", DRAFT_ID);
        draftData.put("dataSource", "Add");

        // Create real masterDraftData
        Map<String, Object> masterData = new HashMap<>();
        masterData.put("describe_api_name", "test_object");
        masterData.put("id", "test_data_id");
        IObjectData masterDraftData = new com.facishare.paas.metadata.impl.ObjectData(masterData);
        draftData.put("masterDraftData", masterData);

        IObjectDataDraft draft = new com.facishare.paas.metadata.impl.ObjectDataDraft(draftData);
        draft.setMasterDraftData(masterDraftData);

        List<IObjectDataDraft> draftList = Lists.newArrayList(draft);

        @SuppressWarnings("unchecked")
        com.facishare.paas.metadata.api.QueryResult<IObjectDataDraft> queryResult =
                mock(com.facishare.paas.metadata.api.QueryResult.class);
        when(queryResult.getData()).thenReturn(draftList);
        when(queryResult.getTotalNumber()).thenReturn(1);
        when(objectDataDraftService.findDraftByTemplate(eq(user), isNull(), isNull(), anyList(), eq(10), eq(0)))
                .thenReturn(queryResult);

        // Mock describeLogicService.findObjectsWithoutCopy
        when(describeLogicService.findObjectsWithoutCopy(eq(TENANT_ID), any()))
                .thenReturn(Collections.emptyMap());

        // Act
        DraftData.Result result = draftService.findDraftListByUser(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getDrafts());
        assertEquals(1, result.getDrafts().size());
        verify(objectDataDraftService).findDraftByTemplate(eq(user), isNull(), isNull(), anyList(), eq(10), eq(0));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据用户删除草稿成功的场景
     */
    @Test
    @DisplayName("测试根据用户删除草稿成功")
    void testDeleteDraftByUserSuccess() {
        // Arrange
        DeleteDraftByUser.Arg arg = new DeleteDraftByUser.Arg();

        when(objectDataDraftService.deleteDraftByUser(eq(user))).thenReturn(true);

        // Act
        DeleteDraftByUser.Result result = draftService.deleteDraftByUser(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(objectDataDraftService).deleteDraftByUser(eq(user));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据ID列表删除草稿成功的场景
     */
    @Test
    @DisplayName("测试根据ID列表删除草稿成功")
    void testDeleteDraftByIdsSuccess() {
        // Arrange
        DeleteDraftByIds.Arg arg = new DeleteDraftByIds.Arg();
        arg.setDraftIdList(Sets.newHashSet(DRAFT_ID));

        // Act
        DeleteDraftByIds.Result result = draftService.deleteDraftByIds(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(objectDataDraftService).deleteDraftByIds(eq(TENANT_ID), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试删除空ID列表的草稿成功的场景
     */
    @Test
    @DisplayName("测试删除空ID列表的草稿成功")
    void testDeleteDraftByIdsWithEmptyList() {
        // Arrange
        DeleteDraftByIds.Arg arg = new DeleteDraftByIds.Arg();
        arg.setDraftIdList(Collections.emptySet());

        // Act
        DeleteDraftByIds.Result result = draftService.deleteDraftByIds(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(objectDataDraftService, never()).deleteDraftByIds(anyString(), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试草稿功能检查成功的场景 - 简单场景（无masterDraftData）
     */
    @Test
    @DisplayName("测试草稿功能检查成功 - 简单场景")
    void testDraftFunctionCheckSuccess() {
        // Arrange
        DraftFuncCheck.Arg arg = new DraftFuncCheck.Arg();
        arg.setDraftId(DRAFT_ID);

        IObjectDataDraft draft = mock(IObjectDataDraft.class);
        when(objectDataDraftService.findDraftById(eq(TENANT_ID), eq(DRAFT_ID)))
                .thenReturn(Optional.of(draft));

        // Act
        DraftFuncCheck.Result result = draftService.draftFunctionCheck(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(objectDataDraftService).findDraftById(eq(TENANT_ID), eq(DRAFT_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据业务类型和业务ID查找草稿ID成功的场景
     */
    @Test
    @DisplayName("测试根据业务类型和业务ID查找草稿ID成功")
    void testFindDraftIdByBizTypeAndBizIdSuccess() {
        // Arrange
        FindDraftIdByBizTypeAndBizId.Arg arg = new FindDraftIdByBizTypeAndBizId.Arg();
        arg.setBizType(BIZ_TYPE);
        arg.setBizId(BIZ_ID);

        IObjectDataDraft draft = mock(IObjectDataDraft.class);
        when(draft.getId()).thenReturn(DRAFT_ID);

        when(objectDataDraftService.findByBizTypeAndBizId(eq(user), eq(BIZ_TYPE), eq(BIZ_ID)))
                .thenReturn(draft);

        // Act
        FindDraftIdByBizTypeAndBizId.Result result = draftService.findDraftIdByBizTypeAndBizId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertEquals(DRAFT_ID, result.getDraftId());
        verify(objectDataDraftService).findByBizTypeAndBizId(eq(user), eq(BIZ_TYPE), eq(BIZ_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据业务类型和业务ID查找草稿ID返回null的场景
     */
    @Test
    @DisplayName("测试根据业务类型和业务ID查找草稿ID返回null")
    void testFindDraftIdByBizTypeAndBizIdReturnsNull() {
        // Arrange
        FindDraftIdByBizTypeAndBizId.Arg arg = new FindDraftIdByBizTypeAndBizId.Arg();
        arg.setBizType(BIZ_TYPE);
        arg.setBizId(BIZ_ID);

        when(objectDataDraftService.findByBizTypeAndBizId(eq(user), eq(BIZ_TYPE), eq(BIZ_ID)))
                .thenReturn(null);

        // Act
        FindDraftIdByBizTypeAndBizId.Result result = draftService.findDraftIdByBizTypeAndBizId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getDraftId());
        verify(objectDataDraftService).findByBizTypeAndBizId(eq(user), eq(BIZ_TYPE), eq(BIZ_ID));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据业务类型和业务ID列表删除草稿成功的场景
     */
    @Test
    @DisplayName("测试根据业务类型和业务ID列表删除草稿成功")
    void testDeleteDraftByBizTypeAndBizIdsSuccess() {
        // Arrange
        DeleteDraftByBizTypeAndBizIds.Arg arg = new DeleteDraftByBizTypeAndBizIds.Arg();
        arg.setBizType(BIZ_TYPE);
        arg.setBizIds(Lists.newArrayList(BIZ_ID));

        // Act
        DeleteDraftByBizTypeAndBizIds.Result result = draftService.deleteDraftByBizTypeAndBizIds(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(objectDataDraftService).deleteByBizTypeAndBizIds(eq(TENANT_ID), eq(BIZ_TYPE), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试检查是否存在之前的草稿成功的场景
     */
    @Test
    @DisplayName("测试检查是否存在之前的草稿成功")
    void testExistPreviousDraftSuccess() {
        // Arrange
        DraftTimestamp.Arg arg = new DraftTimestamp.Arg();
        arg.setTimestamp(System.currentTimeMillis());

        when(objectDataDraftService.existPreviousDraft(eq(user), anyLong())).thenReturn(true);

        // Act
        DraftTimestamp.Result result = draftService.existPreviousDraft(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getRet());
        verify(objectDataDraftService).existPreviousDraft(eq(user), anyLong());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据时间戳删除草稿成功的场景
     */
    @Test
    @DisplayName("测试根据时间戳删除草稿成功")
    void testDeleteByTimestampSuccess() {
        // Arrange
        DraftTimestamp.Arg arg = new DraftTimestamp.Arg();
        arg.setTimestamp(System.currentTimeMillis());

        when(objectDataDraftService.deleteDraftByTimestamp(eq(user), anyLong())).thenReturn(true);

        // Act
        DraftTimestamp.Result result = draftService.deleteByTimestamp(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getRet());
        verify(objectDataDraftService).deleteDraftByTimestamp(eq(user), anyLong());
    }
}
