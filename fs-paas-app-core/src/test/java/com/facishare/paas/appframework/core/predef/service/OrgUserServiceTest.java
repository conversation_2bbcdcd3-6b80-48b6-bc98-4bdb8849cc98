package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByUserIds;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.service.dto.orguser.FindByUserIds;
import com.facishare.paas.appframework.core.predef.service.dto.orguser.FindMainDeptInfoByUserId;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.ObjectDataFormatter;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OrgUserServiceTest {

    @Mock
    private MetaDataService metaDataService;
    
    @Mock
    private OrgService orgService;
    
    @Mock
    private DescribeLogicService describeLogicService;
    
    @InjectMocks
    private OrgUserService orgUserService;
    
    private ServiceContext serviceContext;
    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = new User("test-tenant", "test-user");
        RequestContext requestContext = RequestContext.builder()
                .tenantId("test-tenant")
                .user(testUser)
                .build();
        serviceContext = new ServiceContext(requestContext, "testService", "testMethod");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据用户ID查找用户信息，验证正常情况下的用户查询
     */
    @Test
    @DisplayName("测试查找用户信息 - 正常情况")
    void testFindUserByUserId_Success() {
        // Arrange
        List<String> userIds = Arrays.asList("user1", "user2");
        FindByUserIds.Arg arg = new FindByUserIds.Arg();
        arg.setUserIds(userIds);
        arg.setFormatData(Boolean.FALSE);

        ObjectData objectData1 = new ObjectData();
        objectData1.setId("user1");
        objectData1.setName("User One");

        ObjectData objectData2 = new ObjectData();
        objectData2.setId("user2");
        objectData2.setName("User Two");

        List<IObjectData> userList = Arrays.asList(objectData1, objectData2);

        when(metaDataService.findEmployeeInfoByUserIds(eq("test-tenant"), eq(userIds)))
                .thenReturn(userList);

        // Act
        FindByUserIds.Result result = orgUserService.findUserByUserId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getObjectDataList());
        assertEquals(2, result.getObjectDataList().size());
        verify(metaDataService).findEmployeeInfoByUserIds(eq("test-tenant"), eq(userIds));
        verify(describeLogicService, never()).findObjectWithoutCopyIfGray(anyString(), anyString());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据用户ID查找用户信息，验证空用户ID列表的情况
     */
    @Test
    @DisplayName("测试查找用户信息 - 空用户ID列表")
    void testFindUserByUserId_EmptyUserIds() {
        // Arrange
        FindByUserIds.Arg arg = new FindByUserIds.Arg();
        arg.setUserIds(Collections.emptyList());

        // Act
        FindByUserIds.Result result = orgUserService.findUserByUserId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getObjectDataList());
        assertTrue(result.getObjectDataList().isEmpty());
        verify(metaDataService, never()).findEmployeeInfoByUserIds(anyString(), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据用户ID查找用户信息，验证null用户ID列表的情况
     */
    @Test
    @DisplayName("测试查找用户信息 - null用户ID列表")
    void testFindUserByUserId_NullUserIds() {
        // Arrange
        FindByUserIds.Arg arg = new FindByUserIds.Arg();
        arg.setUserIds(null);

        // Act
        FindByUserIds.Result result = orgUserService.findUserByUserId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getObjectDataList());
        assertTrue(result.getObjectDataList().isEmpty());
        verify(metaDataService, never()).findEmployeeInfoByUserIds(anyString(), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试根据用户ID查找用户信息，验证需要格式化数据的情况
     */
    @Test
    @DisplayName("测试查找用户信息 - 需要格式化数据")
    void testFindUserByUserId_WithFormatData() {
        // Arrange
        List<String> userIds = Arrays.asList("user1");
        FindByUserIds.Arg arg = new FindByUserIds.Arg();
        arg.setUserIds(userIds);
        arg.setFormatData(Boolean.TRUE);

        ObjectData objectData = new ObjectData();
        objectData.setId("user1");
        objectData.setName("User One");

        List<IObjectData> userList = Arrays.asList(objectData);
        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);

        when(metaDataService.findEmployeeInfoByUserIds(eq("test-tenant"), eq(userIds)))
                .thenReturn(userList);
        when(describeLogicService.findObjectWithoutCopyIfGray(eq("test-tenant"), eq(ObjectDescribeExt.PERSONNEL_OBJ_API_NAME)))
                .thenReturn(mockDescribe);

        try (MockedStatic<ObjectDataFormatter> mockedFormatter = mockStatic(ObjectDataFormatter.class)) {
            ObjectDataFormatter.ObjectDataFormatterBuilder mockBuilder = mock(ObjectDataFormatter.ObjectDataFormatterBuilder.class);
            ObjectDataFormatter mockFormatter = mock(ObjectDataFormatter.class);

            mockedFormatter.when(ObjectDataFormatter::builder).thenReturn(mockBuilder);
            when(mockBuilder.describe(any())).thenReturn(mockBuilder);
            when(mockBuilder.dataList(any())).thenReturn(mockBuilder);
            when(mockBuilder.build()).thenReturn(mockFormatter);
            doNothing().when(mockFormatter).format();

            // Act
            FindByUserIds.Result result = orgUserService.findUserByUserId(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertNotNull(result.getObjectDataList());
            assertEquals(1, result.getObjectDataList().size());
            verify(describeLogicService).findObjectWithoutCopyIfGray(eq("test-tenant"), eq(ObjectDescribeExt.PERSONNEL_OBJ_API_NAME));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找用户主部门信息，验证正常情况
     */
    @Test
    @DisplayName("测试查找主部门信息 - 正常情况")
    void testFindMainDeptInfoByUserId_Success() {
        // Arrange
        Set<String> userIds = new HashSet<>(Arrays.asList("user1", "user2"));
        FindMainDeptInfoByUserId.Arg arg = new FindMainDeptInfoByUserId.Arg();
        arg.setUserIds(userIds);
        
        QueryDeptInfoByUserIds.MainDeptInfo deptInfo1 = new QueryDeptInfoByUserIds.MainDeptInfo();
        deptInfo1.setUserId("user1");
        deptInfo1.setUserName("User One");
        deptInfo1.setDeptId("dept1");
        deptInfo1.setDeptName("Department One");
        deptInfo1.setLeaderId("leader1");
        deptInfo1.setLeaderName("Leader One");
        
        QueryDeptInfoByUserIds.MainDeptInfo deptInfo2 = new QueryDeptInfoByUserIds.MainDeptInfo();
        deptInfo2.setUserId("user2");
        deptInfo2.setUserName("User Two");
        deptInfo2.setDeptId("dept2");
        deptInfo2.setDeptName("Department Two");
        deptInfo2.setLeaderId("leader2");
        deptInfo2.setLeaderName("Leader Two");
        
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoMap = new HashMap<>();
        mainDeptInfoMap.put("user1", deptInfo1);
        mainDeptInfoMap.put("user2", deptInfo2);
        
        when(orgService.getMainDeptInfo(eq("test-tenant"), eq("test-user"), anyList()))
                .thenReturn(mainDeptInfoMap);

        // Act
        FindMainDeptInfoByUserId.Result result = orgUserService.findMainDeptInfoByUserId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getMainDeptInfoList());
        assertEquals(2, result.getMainDeptInfoList().size());
        
        FindMainDeptInfoByUserId.MainDeptInfo resultInfo1 = result.getMainDeptInfoList().get(0);
        assertEquals("user1", resultInfo1.getUserId());
        assertEquals("User One", resultInfo1.getUserName());
        assertEquals("dept1", resultInfo1.getDeptId());
        assertEquals("Department One", resultInfo1.getDeptName());
        assertEquals("leader1", resultInfo1.getLeaderId());
        assertEquals("Leader One", resultInfo1.getLeaderName());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找用户主部门信息，验证空用户ID列表的情况
     */
    @Test
    @DisplayName("测试查找主部门信息 - 空用户ID列表")
    void testFindMainDeptInfoByUserId_EmptyUserIds() {
        // Arrange
        FindMainDeptInfoByUserId.Arg arg = new FindMainDeptInfoByUserId.Arg();
        arg.setUserIds(Collections.emptySet());

        // Act
        FindMainDeptInfoByUserId.Result result = orgUserService.findMainDeptInfoByUserId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getMainDeptInfoList());
        assertTrue(result.getMainDeptInfoList().isEmpty());
        verify(orgService, never()).getMainDeptInfo(anyString(), anyString(), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找用户主部门信息，验证null用户ID列表的情况
     */
    @Test
    @DisplayName("测试查找主部门信息 - null用户ID列表")
    void testFindMainDeptInfoByUserId_NullUserIds() {
        // Arrange
        FindMainDeptInfoByUserId.Arg arg = new FindMainDeptInfoByUserId.Arg();
        arg.setUserIds(null);

        // Act
        FindMainDeptInfoByUserId.Result result = orgUserService.findMainDeptInfoByUserId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getMainDeptInfoList());
        assertTrue(result.getMainDeptInfoList().isEmpty());
        verify(orgService, never()).getMainDeptInfo(anyString(), anyString(), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找用户主部门信息，验证返回空Map的情况
     */
    @Test
    @DisplayName("测试查找主部门信息 - 返回空Map")
    void testFindMainDeptInfoByUserId_EmptyResult() {
        // Arrange
        Set<String> userIds = new HashSet<>(Arrays.asList("user1"));
        FindMainDeptInfoByUserId.Arg arg = new FindMainDeptInfoByUserId.Arg();
        arg.setUserIds(userIds);
        
        when(orgService.getMainDeptInfo(eq("test-tenant"), eq("test-user"), any()))
                .thenReturn(new HashMap<>());

        // Act
        FindMainDeptInfoByUserId.Result result = orgUserService.findMainDeptInfoByUserId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getMainDeptInfoList());
        assertTrue(result.getMainDeptInfoList().isEmpty());
        verify(orgService).getMainDeptInfo(eq("test-tenant"), eq("test-user"), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找用户主部门信息，验证单个用户的情况
     */
    @Test
    @DisplayName("测试查找主部门信息 - 单个用户")
    void testFindMainDeptInfoByUserId_SingleUser() {
        // Arrange
        Set<String> userIds = new HashSet<>(Arrays.asList("user1"));
        FindMainDeptInfoByUserId.Arg arg = new FindMainDeptInfoByUserId.Arg();
        arg.setUserIds(userIds);
        
        QueryDeptInfoByUserIds.MainDeptInfo deptInfo = new QueryDeptInfoByUserIds.MainDeptInfo();
        deptInfo.setUserId("user1");
        deptInfo.setUserName("User One");
        deptInfo.setDeptId("dept1");
        deptInfo.setDeptName("Department One");
        deptInfo.setLeaderId("leader1");
        deptInfo.setLeaderName("Leader One");
        
        Map<String, QueryDeptInfoByUserIds.MainDeptInfo> mainDeptInfoMap = new HashMap<>();
        mainDeptInfoMap.put("user1", deptInfo);
        
        when(orgService.getMainDeptInfo(eq("test-tenant"), eq("test-user"), anyList()))
                .thenReturn(mainDeptInfoMap);

        // Act
        FindMainDeptInfoByUserId.Result result = orgUserService.findMainDeptInfoByUserId(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getMainDeptInfoList());
        assertEquals(1, result.getMainDeptInfoList().size());
        
        FindMainDeptInfoByUserId.MainDeptInfo resultInfo = result.getMainDeptInfoList().get(0);
        assertEquals("user1", resultInfo.getUserId());
        assertEquals("User One", resultInfo.getUserName());
    }
}