package com.facishare.paas.appframework.core.predef.service;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkDeleteAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkRecoverAction;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.appframework.core.predef.controller.StandardDetailController;
import com.facishare.paas.appframework.core.predef.service.dto.recycleBin.BulkDeleteData;
import com.facishare.paas.appframework.core.predef.service.dto.recycleBin.FindInvalidDataList;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * ObjectRecycleBinService单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ObjectRecycleBinService单元测试")
class ObjectRecycleBinServiceTest {

    @Mock
    private ServiceFacade serviceFacade;
    
    @InjectMocks
    private ObjectRecycleBinService objectRecycleBinService;
    
    private ServiceContext serviceContext;
    private User user;
    private final String TENANT_ID = "74255";
    private final String USER_ID = "1000";
    private final String DESCRIBE_API_NAME = "TestObj__c";

    @BeforeEach
    void setUp() {
        user = new User(TENANT_ID, USER_ID);
        RequestContext requestContext = RequestContext.builder()
                .tenantId(TENANT_ID)
                .user(user)
                .requestSource(RequestContext.RequestSource.CEP)
                .lang(Lang.zh_CN)
                .build();
        serviceContext = new ServiceContext(requestContext, "recycle_bin", "test");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找无效数据列表成功的场景
     */
    @Test
    @DisplayName("测试findInvalidDataList成功")
    void testFindInvalidDataListSuccess() {
        // Arrange
        FindInvalidDataList.Arg arg = new FindInvalidDataList.Arg();
        arg.setObjectDescribeAPIName(DESCRIBE_API_NAME);
        arg.setPageSize(20);
        arg.setFindExplicitTotalNum(false);
        // 设置有效的日期范围，避免NullPointerException
        arg.setStartDate(System.currentTimeMillis() - 86400000L); // 24小时前
        arg.setEndDate(System.currentTimeMillis()); // 当前时间

        // 使用真实的ObjectData对象而不是Mock，避免ClassCastException
        IObjectData mockData1 = new com.facishare.paas.metadata.impl.ObjectData();
        mockData1.set("id", "test_id_1");
        mockData1.set("name", "Test Data 1");
        IObjectData mockData2 = new com.facishare.paas.metadata.impl.ObjectData();
        mockData2.set("id", "test_id_2");
        mockData2.set("name", "Test Data 2");
        List<IObjectData> mockDataList = Arrays.asList(mockData1, mockData2);
        
        QueryResult<IObjectData> mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(mockDataList);
        when(mockQueryResult.getTotalNumber()).thenReturn(2);
        
        IObjectDescribe mockDescribe = mock(IObjectDescribe.class);
        // 使用真实的Button对象而不是Mock，避免ClassCastException
        com.facishare.paas.metadata.impl.ui.layout.Button button1 = new com.facishare.paas.metadata.impl.ui.layout.Button();
        button1.setName("button1");
        button1.setLabel("Button 1");
        button1.setAction("action1");
        button1.setActionType("default");
        com.facishare.paas.metadata.impl.ui.layout.Button button2 = new com.facishare.paas.metadata.impl.ui.layout.Button();
        button2.setName("button2");
        button2.setLabel("Button 2");
        button2.setAction("action2");
        button2.setActionType("default");
        List<IButton> mockButtons = Arrays.asList(button1, button2);

        when(serviceFacade.findInvalidData(any())).thenReturn(mockQueryResult);
        when(serviceFacade.findObject(TENANT_ID, DESCRIBE_API_NAME)).thenReturn(mockDescribe);
        when(serviceFacade.findButtonsForRecycleBin(mockDescribe, user)).thenReturn(mockButtons);

        // Act
        FindInvalidDataList.Result result = objectRecycleBinService.findInvalidDataList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNotNull(result.getDataList());
        assertEquals(20, result.getPageSize());
        assertEquals(2, result.getTotalCount());
        assertNotNull(result.getBulkButtons());
        verify(serviceFacade).findInvalidData(any());
        verify(serviceFacade).findObject(TENANT_ID, DESCRIBE_API_NAME);
        verify(serviceFacade).findButtonsForRecycleBin(mockDescribe, user);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找无效数据列表时页面大小超过限制的场景
     */
    @Test
    @DisplayName("测试findInvalidDataList页面大小超过限制")
    void testFindInvalidDataListWithExceedPageSize() {
        // Arrange
        FindInvalidDataList.Arg arg = new FindInvalidDataList.Arg();
        arg.setObjectDescribeAPIName(DESCRIBE_API_NAME);
        arg.setPageSize(10000); // 超过限制
        arg.setFindExplicitTotalNum(true);
        // 设置有效的日期范围，避免NullPointerException
        arg.setStartDate(System.currentTimeMillis() - 86400000L); // 24小时前
        arg.setEndDate(System.currentTimeMillis()); // 当前时间

        QueryResult<IObjectData> mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(Arrays.asList());
        when(mockQueryResult.getTotalNumber()).thenReturn(0);

        try (MockedStatic<AppFrameworkConfig> mockedConfig = mockStatic(AppFrameworkConfig.class)) {
            mockedConfig.when(AppFrameworkConfig::getMaxQueryLimit).thenReturn(1000);
            when(serviceFacade.findInvalidData(any())).thenReturn(mockQueryResult);

            // Act
            FindInvalidDataList.Result result = objectRecycleBinService.findInvalidDataList(arg, serviceContext);

            // Assert
            assertNotNull(result);
            assertEquals(1000, result.getPageSize()); // 应该被限制为最大值
            verify(serviceFacade).findInvalidData(any());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量删除数据成功的场景
     */
    @Test
    @DisplayName("测试bulkDeleteData成功")
    void testBulkDeleteDataSuccess() {
        // Arrange
        BulkDeleteData.Arg arg = new BulkDeleteData.Arg();
        arg.setObjectDescribeAPIName(DESCRIBE_API_NAME);
        arg.setIdList(Arrays.asList("id1", "id2", "id3"));

        StandardBulkDeleteAction.Result mockActionResult = new StandardBulkDeleteAction.Result();
        mockActionResult.setSuccess(true);

        when(serviceFacade.triggerAction(any(ActionContext.class), any(StandardBulkDeleteAction.Arg.class), 
                eq(StandardBulkDeleteAction.Result.class))).thenReturn(mockActionResult);

        // Act
        BulkDeleteData.Result result = objectRecycleBinService.bulkDeleteData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getSuccess());
        verify(serviceFacade).triggerAction(any(ActionContext.class), any(StandardBulkDeleteAction.Arg.class), 
                eq(StandardBulkDeleteAction.Result.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量恢复数据成功的场景
     */
    @Test
    @DisplayName("测试bulkRecoverData成功")
    void testBulkRecoverDataSuccess() {
        // Arrange
        StandardBulkRecoverAction.Arg arg = new StandardBulkRecoverAction.Arg();
        arg.setObjectDescribeAPIName(DESCRIBE_API_NAME);
        arg.setIdList(Arrays.asList("id1", "id2"));

        StandardBulkRecoverAction.Result mockActionResult = new StandardBulkRecoverAction.Result();
        mockActionResult.setSuccess(true);

        when(serviceFacade.triggerAction(any(ActionContext.class), any(StandardBulkRecoverAction.Arg.class), 
                eq(StandardBulkRecoverAction.Result.class))).thenReturn(mockActionResult);

        // Act
        StandardBulkRecoverAction.Result result = objectRecycleBinService.bulkRecoverData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getSuccess());
        verify(serviceFacade).triggerAction(any(ActionContext.class), any(StandardBulkRecoverAction.Arg.class), 
                eq(StandardBulkRecoverAction.Result.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试对象详情成功的场景
     */
    @Test
    @DisplayName("测试objectDetail成功")
    void testObjectDetailSuccess() {
        // Arrange
        StandardDetailController.Arg arg = new StandardDetailController.Arg();
        arg.setObjectDescribeApiName(DESCRIBE_API_NAME);

        StandardDetailController.Result mockControllerResult = new StandardDetailController.Result();

        when(serviceFacade.triggerController(any(ControllerContext.class), any(StandardDetailController.Arg.class), 
                eq(StandardDetailController.Result.class))).thenReturn(mockControllerResult);

        // Act
        StandardDetailController.Result result = objectRecycleBinService.objectDetail(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(arg.isFromRecycleBin()); // 验证设置了回收站标志
        verify(serviceFacade).triggerController(any(ControllerContext.class), any(StandardDetailController.Arg.class), 
                eq(StandardDetailController.Result.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试查找无效数据列表时显式查找总数的场景
     */
    @Test
    @DisplayName("测试findInvalidDataList显式查找总数")
    void testFindInvalidDataListWithExplicitTotalNum() {
        // Arrange
        FindInvalidDataList.Arg arg = new FindInvalidDataList.Arg();
        arg.setObjectDescribeAPIName(DESCRIBE_API_NAME);
        arg.setPageSize(10);
        arg.setFindExplicitTotalNum(true); // 显式查找总数
        // 设置有效的日期范围，避免NullPointerException
        arg.setStartDate(System.currentTimeMillis() - 86400000L); // 24小时前
        arg.setEndDate(System.currentTimeMillis()); // 当前时间

        QueryResult<IObjectData> mockQueryResult = mock(QueryResult.class);
        when(mockQueryResult.getData()).thenReturn(Arrays.asList());
        when(mockQueryResult.getTotalNumber()).thenReturn(0);

        when(serviceFacade.findInvalidData(any())).thenReturn(mockQueryResult);

        // Act
        FindInvalidDataList.Result result = objectRecycleBinService.findInvalidDataList(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertNull(result.getBulkButtons()); // 显式查找总数时不应该查找按钮
        verify(serviceFacade).findInvalidData(any());
        verify(serviceFacade, never()).findObject(anyString(), anyString());
        verify(serviceFacade, never()).findButtonsForRecycleBin(any(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试批量删除数据时ID列表为空的场景
     */
    @Test
    @DisplayName("测试bulkDeleteData ID列表为空")
    void testBulkDeleteDataWithEmptyIdList() {
        // Arrange
        BulkDeleteData.Arg arg = new BulkDeleteData.Arg();
        arg.setObjectDescribeAPIName(DESCRIBE_API_NAME);
        arg.setIdList(Arrays.asList());

        StandardBulkDeleteAction.Result mockActionResult = new StandardBulkDeleteAction.Result();
        mockActionResult.setSuccess(true);

        when(serviceFacade.triggerAction(any(ActionContext.class), any(StandardBulkDeleteAction.Arg.class), 
                eq(StandardBulkDeleteAction.Result.class))).thenReturn(mockActionResult);

        // Act
        BulkDeleteData.Result result = objectRecycleBinService.bulkDeleteData(arg, serviceContext);

        // Assert
        assertNotNull(result);
        assertTrue(result.getSuccess());
        verify(serviceFacade).triggerAction(any(ActionContext.class), any(StandardBulkDeleteAction.Arg.class), 
                eq(StandardBulkDeleteAction.Result.class));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Service实例化和依赖注入
     */
    @Test
    @DisplayName("测试Service实例化和依赖注入")
    void testServiceInstantiationAndDependencyInjection() {
        // Assert
        assertNotNull(objectRecycleBinService);
        assertNotNull(serviceFacade);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ServiceContext构造是否正确
     */
    @Test
    @DisplayName("测试ServiceContext构造正确")
    void testServiceContextConstructionSuccess() {
        // Assert
        assertNotNull(serviceContext);
        assertEquals(TENANT_ID, serviceContext.getRequestContext().getTenantId());
        assertEquals(user, serviceContext.getRequestContext().getUser());
        assertEquals("recycle_bin", serviceContext.getServiceName());
        assertEquals("test", serviceContext.getServiceMethod());
    }
}
