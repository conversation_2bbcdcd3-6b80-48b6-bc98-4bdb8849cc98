package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.query.SearchQueryContext;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.facishare.paas.token.api.ITokenService;
import com.facishare.paas.token.model.TokenInfo;
import com.github.trace.TraceContext;
import com.google.common.collect.Maps;

import java.text.DecimalFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * create by zhaoju on 2021/02/18
 */
public abstract class AbstractExportAction<A, R> extends PreDefineAction<A, R> {
    public static final String ERROR_SPLIT = "$$";
    protected static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
    public final int tokenExpireSeconds;
    protected final ITokenService tokenService;

    protected SearchTemplateQuery searchQuery;

    protected String token;
    protected boolean isGenerating;
    protected int totalCount = 0;

    public final long exportFileMaxSize;
    public final long exportEmbeddedExcelImageMaxSize;
    protected long exportFileSize = 0L;

    public AbstractExportAction() {
        tokenExpireSeconds = ImportConfig.getTokenExpireSeconds();
        tokenService = SpringUtil.getContext().getBean(ITokenService.class);
        exportFileMaxSize = ImportConfig.getExportFileMaxSize();
        exportEmbeddedExcelImageMaxSize = ImportConfig.getEmbeddedExcelImageMaxSize();
    }

    @Override
    protected void before(A arg) {
        if (hasToken()) {
            findDescribe();
            token = getToken(arg);
            isGenerating = true;
        } else {
            super.before(arg);
            searchQuery = getSearchQuery();
            totalCount = validateThrottle();
            token = generateToken();
            isGenerating = false;
            customInit();
        }
    }

    protected abstract String getToken(A arg);

    protected abstract void customInit();

    protected abstract boolean hasToken();

    protected abstract String getSearchTemplateId();

    protected abstract String getSearchQueryInfo();

    protected abstract List<String> getDataIdList();

    protected abstract String getSearchTemplateType();

    protected abstract boolean isIgnoreSceneFilter();

    protected abstract boolean isIgnoreSceneRecordType();

    protected abstract boolean isNoExportRelevantTeam();

    private SearchTemplateQuery getSearchQuery() {
        SearchTemplateQuery searchTemplateQuery;
        if (serviceFacade.isSupportOrFilter(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            searchTemplateQuery = (SearchTemplateQuery) defineQuery(actionContext.getUser(), getSearchTemplateId(),
                    getSearchQueryInfo(),
                    getDataIdList()).toSearchTemplateQuery();
        } else {
            searchTemplateQuery = generateSearchQuery(actionContext.getUser(), getSearchTemplateId(),
                    getSearchQueryInfo(), getDataIdList());
        }
        if (AppFrameworkConfig.isExportUseDbTenantId(actionContext.getTenantId())) {
            searchTemplateQuery.setSearchSource("db");
        }
        return customSearchTemplate(searchTemplateQuery);
    }

    protected Query defineQuery(User user, String searchTemplateId,
                                String searchQueryInfo, List<String> dataIdList) {
        SearchQueryContext queryContext = SearchQueryContext.builder()
                .searchTemplateType(getSearchTemplateType())
                .templateId(searchTemplateId)
                .isIgnoreSceneFilter(isIgnoreSceneFilter())
                .isIgnoreSceneRecordType(isIgnoreSceneRecordType())
                .isRelatedPage(false)
                .build();
        Query query = serviceFacade.findSearchQuery(user, objectDescribe, searchQueryInfo, queryContext);
        if (CollectionUtils.notEmpty(dataIdList)) {
            SearchQuery searchQuery = query.getSearchQuery()
                    .map(it -> it.and(FilterExt.of(Operator.IN, IObjectData.ID, dataIdList).getFilter()))
                    .orElse(SearchQueryImpl.filter(FilterExt.of(Operator.IN, IObjectData.ID, dataIdList).getFilter()));
            query.setSearchQuery(searchQuery);
        }
        // 忽略掉前端传过来的offset
        query.setOffset(0);
        return query;
    }

    protected SearchTemplateQuery generateSearchQuery(User user, String searchTemplateId,
                                                      String searchQuery, List<String> dataIdList) {
        SearchTemplateQuery query = serviceFacade.getSearchTemplateQueryWithIgnoreSceneFilter(user, ObjectDescribeExt.of(objectDescribe), searchTemplateId,
                getSearchTemplateType(), searchQuery, false, isIgnoreSceneFilter(), isIgnoreSceneRecordType());
        //忽略掉前端传过来的offset
        query.setOffset(0);
        if (CollectionUtils.notEmpty(dataIdList)) {
            SearchTemplateQueryExt.of(query).addFilter(Operator.IN, IObjectData.ID, dataIdList);
        }
        return query;
    }

    protected SearchTemplateQuery customSearchTemplate(SearchTemplateQuery searchTemplateQuery) {
        return searchTemplateQuery;
    }

    protected int validateThrottle() {
        searchQuery.setLimit(1);
        searchQuery.setOffset(0);
        // 查询精确总数，不返回数据
        searchQuery.setFindExplicitTotalNum(true);
        QueryResult<IObjectData> data = findObjectByQuery(actionContext.getUser(), objectDescribe, searchQuery);
        if (data.getTotalNumber() > getExportRowsThrottle()) {
            throw new ValidateException(buildThrottleExceedMessage());
        }
        return data.getTotalNumber();
    }

    protected String buildThrottleExceedMessage() {
        return I18N.text(I18NKey.EXPORT_EXCEED_THROTTLE, new DecimalFormat(",###").format(getExportRowsThrottle()));
    }

    protected QueryResult<IObjectData> findObjectByQuery(User user, IObjectDescribe describe,
                                                         SearchTemplateQuery query) {
        if (Boolean.TRUE.equals(isNoExportRelevantTeam())) {
            return serviceFacade.findBySearchQuery(user, describe, describe.getApiName(), query, false, true, true);
        }
        return serviceFacade.findBySearchQuery(user, describe, describe.getApiName(), query, false, true);
    }

    protected int getExportRowsThrottle() {
        return AppFrameworkConfig.getExportFileRowsThrottle(actionContext.getTenantId());
    }

    private String generateToken() {
        return tokenService.create(tokenExpireSeconds);
    }

    @Override
    protected R doAct(A arg) {
        if (!isGenerating) {
            serviceFacade.log(actionContext.getUser(), EventType.ADD, ActionType.Export,
                    objectDescribe.getApiName(), I18N.text(I18NKey.OBJECT_SPECIFY, objectDescribe.getDisplayName()));

            String traceId = TraceContext.get().getTraceId();
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
            parallelTask.submit(() -> {
                try {
                    doExport();
                } catch (AppBusinessException e) {
                    log.warn("export data failed!tenantId:{},arg:{}", actionContext.getTenantId(), arg, e);
                    TokenInfo tokenInfo = TokenInfo.buildError(token, "srcTrace:" + traceId + ERROR_SPLIT + e.getMessage());
                    tokenService.complete(tokenInfo, tokenExpireSeconds);
                } catch (Exception e) {
                    log.error("export data error!tenantId:{},arg:{}", actionContext.getTenantId(), arg, e);
                    TokenInfo tokenInfo = TokenInfo.buildError(token, "srcTrace:" + traceId);
                    tokenService.complete(tokenInfo, tokenExpireSeconds);
                }
            });
            parallelTask.run();
        }
        return generateResult(this.token);
    }

    protected abstract void doExport();

    protected void queryData() {
        Map<String, Integer> indexMap = Maps.newHashMap();
        indexMap.put(actionContext.getObjectApiName(), 0);
        int maxQueryCount = SearchTemplateQueryExt.calculateTotalPage(getExportRowsThrottle(), getDataBatchSize());
        serviceFacade.queryDataAndHandle(actionContext.getUser(), searchQuery, objectDescribe, getDataBatchSize(),
                maxQueryCount, isNoExportRelevantTeam(), queryResult -> {
                    List<IObjectData> queryData = queryResult.getData();
                    int size = queryData.size();
                    infraServiceFacade.removeGdprData(actionContext.getUser(), actionContext.getObjectApiName(), queryData, ObjectAction.BATCH_EXPORT.getActionCode());
                    // 保留图片的 NPath
                    consumerDataList(queryData);
                    indexMap.computeIfPresent(actionContext.getObjectApiName(), (key, oldValue) -> oldValue + size);
                    // 保存导出进度
                    int currentExportedDataNumber = indexMap.values().stream().mapToInt(i -> i).sum();
                    TokenInfo tokenInfo = TokenInfo.builder().id(token).progress(currentExportedDataNumber).build();
                    tokenService.update(tokenInfo, tokenExpireSeconds);
                });
    }

    protected abstract void consumerDataList(List<IObjectData> dataList);

    protected abstract int getDataBatchSize();

    protected abstract R generateResult(String token);

    protected String getFileName() {
        if (DateTimeUtils.isGrayTimeZone()) {
            return I18N.text(I18NKey.EXPORT_FILE_NAME, objectDescribe.getDisplayName(), formatter.format(LocalDate.now(TimeZoneContextHolder.getUserTimeZone())));
        }
        return I18N.text(I18NKey.EXPORT_FILE_NAME, objectDescribe.getDisplayName(), formatter.format(LocalDate.now()));
    }

}
