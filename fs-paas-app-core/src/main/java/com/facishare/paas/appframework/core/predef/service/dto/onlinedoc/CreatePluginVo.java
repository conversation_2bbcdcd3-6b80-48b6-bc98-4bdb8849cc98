package com.facishare.paas.appframework.core.predef.service.dto.onlinedoc;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums.PackagePluginDefineType;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums.PackagePluginAppType;
import com.facishare.paas.appframework.metadata.repository.model.PackagePluginEntity;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/5
 */
public interface CreatePluginVo {
    @Data
    class Arg {
        private String defineType = PackagePluginDefineType.CUSTOM.getType();
        /**
         * 名称
         */
        private String name;
        /**
         * 图标 CPath
         */
        private String icon;
        /**
         * 应用类型
         */
        private String appType;
        /**
         * 插件apiName
         */
        private String pluginApiName;
        /**
         * 扩展信息
         */
        private Map<String, String> extraInfo = Maps.newHashMap();

        /**
         * 参数校验
         */
        public void validate() {
            if (StringUtils.isBlank(defineType)) {
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_DEFINE_TYPE));
            }
            if (StringUtils.isBlank(name)) {
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_NAME));
            }
            if (StringUtils.isBlank(icon)) {
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_ICON));
            }
            if (StringUtils.isBlank(appType)) {
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_APP_TYPE));
            }
            if (StringUtils.isBlank(pluginApiName)) {
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_PLUGIN_API_NAME));
            }
            if (extraInfo == null) {
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_EXTRA_INFO));
            }
            if (PackagePluginAppType.ONLINE_DOC.getType().equals(appType)) {
                String functionApiName = extraInfo.get(PackagePluginEntity.PLUGIN_ENTITY_FILED_FUNCTION_API_NAME);
                String pwcApiName = extraInfo.get(PackagePluginEntity.PLUGIN_ENTITY_FILED_PWC_API_NAME);
                if (StringUtils.isBlank(functionApiName)) {
                    throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_FUNCTION_API_NAME));
                }
                if (StringUtils.isBlank(pwcApiName)) {
                    throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_PWC_API_NAME));
                }
            }
            if (name.length() > AppFrameworkConfig.getOnlineDocPluginNameLengthMax()) {
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_CONTENT_TOO_LONG, "name"));
            }
            if (pluginApiName.length() > AppFrameworkConfig.getOnlineDocPluginApiNameLengthMax()) {
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_CONTENT_TOO_LONG, "pluginApiName"));
            }
        }
    }

    @Data
    class Result {
        /**
         * 是否成功
         */
        private boolean success;
    }
}
