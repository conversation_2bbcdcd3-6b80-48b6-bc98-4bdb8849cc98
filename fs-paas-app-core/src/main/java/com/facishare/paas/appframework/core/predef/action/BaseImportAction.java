package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.action.ValidateFuncAction;
import com.facishare.paas.appframework.common.service.dto.ImportView;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.metadata.DuplicatedSearchExt;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchOrderByType;
import com.facishare.paas.appframework.metadata.dto.ImportReferenceMapping;
import com.facishare.paas.appframework.metadata.switchcache.provider.SwitchCacheProviderManager;
import com.facishare.paas.appframework.metadata.switchcache.provider.UniqueRuleSwitchCacheProvider;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;

import static com.facishare.paas.appframework.core.predef.action.BaseImportDataAction.MATCHING_TYPE_ID;

@Slf4j
public abstract class BaseImportAction extends PreDefineAction<BaseImportAction.Arg, BaseImportAction.Result> {
    public static int IMPORT_TYPE_ADD = 0;
    public static final String PERCENTAGE_SYMBOL = "（%）";
    public static final String NO_CONVERT_SYMBOL = "<ID>";

    protected IUniqueRule uniqueRule;
    protected List<IDuplicatedSearch> duplicatedSearchList = Lists.newArrayList();

    protected ValidateFuncAction validateFuncAction;

    protected ImportLogMessage.ImportMessageBuilder importLogMessageBuilder;

    protected Map<String, List<FieldMapping>> fieldMappings;
    protected ImportReferenceMapping importReferenceMapping;
    protected boolean importReferenceFieldMappingSwitch = false;

    @Override
    protected void before(Arg arg) {
        importLogMessageBuilder = ImportLogMessage.createAndStart(actionContext.getUser(), arg, actionContext.getActionCode());
        super.before(arg);
        // 查询接口,初始化对象的唯一性规则
        initializeUniqueRule();
        stopWatch.lap("initializeUniqueRule");
        // 初始化查重规则
        initDuplicateRule();
        stopWatch.lap("initDuplicateRule");
        validateFuncAction = serviceFacade.getBean(ValidateFuncAction.class);
        //初始化字段映射关系
        fieldMappings = initFieldMapping();
        initImportReferenceMapping();
    }

    @Override
    protected void findDescribe() {
        // RequestContext中设置是否克隆描述的字段
        addAttributeInRequestContext();
        objectDescribe = serviceFacade.findObject(actionContext.getTenantId(), actionContext.getObjectApiName());
    }

    protected void addAttributeInRequestContext() {
        RequestContext requestContext = RequestContextManager.getContext();
        if (Objects.nonNull(requestContext)) {
            requestContext.setAttribute(RequestContext.IS_NOT_NEED_DEEP_COPY, Boolean.FALSE);
        }
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Collections.emptyList();
    }

    public List<String> getValidTitles(Set<Map.Entry<String, Object>> entrySet, List<IFieldDescribe> fieldDescribes) {
        List<String> validTitles = Lists.newArrayList();
        for (Map.Entry<String, Object> entry : entrySet) {
            for (IFieldDescribe field : fieldDescribes) {
                String title = entry.getKey();
                if (validTitles(title, field)) {
                    validTitles.add(field.getLabel());
                }
            }
        }
        return validTitles;
    }

    protected boolean isSupportFieldMapping() {
        return BooleanUtils.isTrue(arg.getSupportFieldMapping());
    }


    protected Map<String, List<FieldMapping>> initFieldMapping() {
        Map<String, List<FieldMapping>> fieldMappings = Maps.newHashMap();
        if (!isSupportFieldMapping()) {
            return fieldMappings;
        }
        MasterInfo masterInfo = arg.getMasterInfo();
        fieldMappings.put(masterInfo.getApiName(), masterInfo.getFieldMapping());
        if (CollectionUtils.notEmpty(arg.getDetailInfo())) {
            List<DetailInfo> detailInfos = arg.getDetailInfo();
            for (DetailInfo detailInfo : detailInfos) {
                fieldMappings.put(detailInfo.getApiName(), detailInfo.getFieldMapping());
            }
        }
        return fieldMappings;
    }

    protected boolean validTitles(String title, IFieldDescribe fieldDescribe) {
        if ("rowNo".equals(title)) {
            return false;
        }
        title = getValidTitle(title, fieldDescribe);

        String fieldLabel = StringUtils.trim(fieldDescribe.getLabel());
        if (importReferenceFieldMappingSwitch) {
            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
            // [字段label]_唯一性标识
            if (fieldDescribeExt.isLookupField() || fieldDescribeExt.isMasterDetailField()) {
                fieldLabel = fieldLabel + "_" + I18N.text(I18NKey.UNIQUE_FILL_IN);
            }
        } else if (arg.getMatchingType() == MATCHING_TYPE_ID) {
            // 判断字段是否存在需要考虑按为一性ID导入的情况
            FieldDescribeExt fieldDescribeExt = FieldDescribeExt.of(fieldDescribe);
            // [字段label]_唯一性ID
            if (fieldDescribeExt.isLookupField() || fieldDescribeExt.isMasterDetailField()) {
                fieldLabel = fieldLabel + "_" + I18N.text(I18NKey.DATAID_LABEL);
            }
        }
        return Objects.equals(fieldLabel, title);
    }

    protected String getValidTitle(String key, IFieldDescribe fieldDescribe) {
        String newKey = key;
        if (BooleanUtils.isTrue(fieldDescribe.isRequired())) {
            newKey = newKey.replaceAll(I18N.text(I18NKey.MUST_FILL_IN) + "$", "");
        }
        if (IFieldType.PERCENTILE.equals(fieldDescribe.getType())) {
            newKey = newKey.replaceAll(PERCENTAGE_SYMBOL + "$", "");

        }
        if (Objects.equals(IFieldType.MASTER_DETAIL, fieldDescribe.getType()) ||
                Objects.equals(IFieldType.OBJECT_REFERENCE, fieldDescribe.getType()) ||
                Objects.equals(IFieldType.OBJECT_REFERENCE_MANY, fieldDescribe.getType())) {
            newKey = newKey.replace(NO_CONVERT_SYMBOL, "");
        }
        newKey = newKey.trim();
        return newKey;
    }

    protected void initializeUniqueRule() {
        uniqueRule = infraServiceFacade.findBySwitchCache(actionContext.getTenantId(),
                SwitchCacheProviderManager.UNIQUE_RULE,
                UniqueRuleSwitchCacheProvider.USE_WHEN_IMPORT_EXCEL,
                objectDescribe.getApiName(),
                () -> serviceFacade.findByDescribeApiName(actionContext.getTenantId(), objectDescribe.getApiName())
        ).orElse(null);
    }

    protected void initDuplicateRule() {
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(actionContext.getTenantId(), objectDescribe.getApiName())) {
            duplicatedSearchList = serviceFacade.getEnableDuplicateSearchRuleList(objectDescribe.getApiName(), IDuplicatedSearch.Type.NEW, actionContext.getTenantId(), false, DuplicateSearchOrderByType.ORDER_BY_SORT);
        } else {
            IDuplicatedSearch duplicatedSearch = serviceFacade.findDuplicatedSearchByApiNameAndType(actionContext.getTenantId(),
                    objectDescribe.getApiName(), IDuplicatedSearch.Type.NEW, false);
            if (DuplicatedSearchExt.isEnableDuplicate(duplicatedSearch)) {
                duplicatedSearchList = Lists.newArrayList(duplicatedSearch);
            }
        }
    }

    protected void initImportReferenceMapping() {
        importReferenceMapping = infraServiceFacade.findImportReferenceMapping(actionContext.getUser(), objectDescribe.getApiName());
        importReferenceFieldMappingSwitch = BooleanUtils.isTrue(importReferenceMapping.getReferenceFieldMappingSwitch());
    }

    protected IUniqueRule getUniqueRule() {
        return uniqueRule;
    }

    @Override
    protected void finallyDo() {
        if (Objects.nonNull(importLogMessageBuilder)) {
            ImportLogMessage logMessage = importLogMessageBuilder.build();
            logMessage.send();
            log.warn("ImportLogMessage:{}", logMessage);
        }
        super.finallyDo();
    }

    @Data
    public static class Arg {
        private String tenantId;
        private String userId;
        private String apiName;

        @JsonProperty(value = "IsEmptyValueToUpdate")
        private Boolean isEmptyValueToUpdate;

        @JsonProperty(value = "IsWorkFlowEnabled")
        private Boolean isWorkFlowEnabled;

        @JsonProperty(value = "IsApprovalFlowEnabled")
        private Boolean isApprovalFlowEnabled;

        @JsonProperty(value = "IsTextureImportEnabled")
        private Boolean isTextureImportEnabled;

        @JsonProperty(value = "ImportType")
        private Integer importType;

        @JsonProperty(value = "IsUnionDuplicateChecking")
        private Boolean isUnionDuplicateChecking;

        @JsonProperty(value = "IsVerifyEnterprise")
        private Boolean isVerifyEnterprise;

        @JsonProperty(value = "IsBackFillIndustrialAndCommercialInfo")
        private Boolean isBackFillIndustrialAndCommercialInfo;

        @JsonProperty(value = "IsBackFillOverwriteOldValue")
        private Boolean isBackFillOverwriteOldValue;

        @JsonProperty(value = "MatchingType")
        private Integer matchingType;

        @JsonProperty(value = "operationType")
        private String operationType;

        @JsonProperty(value = "unionApiNames")
        private List<String> unionApiNameList;

        @JsonProperty(value = "jobId")
        private String jobId;

        @JsonProperty(value = "specifiedField")
        private String specifiedField;

        // 部分不同老对象导入时用同一个apiName，加一个objectCode参数用于区分是哪个
        @JsonProperty(value = "objectCode")
        @SerializedName(value = "objectCode")
        private String objectCode;

        // Only for SaleEventObj
        @JsonProperty(value = "relatedApiNameList")
        private List<String> relatedApiNameList;

        private List<ObjectDataDocument> rows;

        private List<ImportView.ExcelCol> headerExcelCols;

        @JsonProperty(value = "IsCheckOutOwner")
        private Boolean checkOutOwner;

        @JsonProperty(value = "IsRemoveOutTeamMember")
        private Boolean removeOutTeamMember;

        @JsonProperty(value = "IsImportPreProcessing")
        private Boolean importPreProcessing;

        @JsonProperty(value = "IsFinalBatch")
        private Boolean finalBatch;

        @JsonProperty(value = "IsUpdateOwner")
        private Boolean updateOwner;

        @JsonProperty(value = "oldOwnerTeamMember")
        @SerializedName("oldOwnerTeamMember")
        private OldOwnerTeamMember oldOwnerTeamMember;

        @JsonProperty(value = "fileCode")
        @SerializedName("fileCode")
        private Map<String, String> fileCode;

        @JsonProperty(value = "supportFieldMapping")
        @SerializedName("supportFieldMapping")
        private Boolean supportFieldMapping;

        @JsonProperty(value = "masterInfo")
        @SerializedName("masterInfo")
        private MasterInfo masterInfo;

        @JsonProperty(value = "detailInfo")
        @SerializedName("detailInfo")
        private List<DetailInfo> detailInfo;

        @JsonProperty(value = "extendAttribute")
        @SerializedName("extendAttribute")
        private Map<String, Object> extendAttribute;


        public boolean includeTeamMember(User user) {
            if (user.isOutUser()) {
                return false;
            }

            return rows.stream()
                    .anyMatch(dataDocument -> dataDocument.keySet().stream().anyMatch(TeamMember.RoleWithPermission::contains));
        }
    }

    @Data
    public static class BaseInfo {

        @JsonProperty(value = "apiName")
        @SerializedName(value = "apiName")
        private String apiName;

        @JsonProperty(value = "fieldMapping")
        @SerializedName(value = "fieldMapping")
        private List<FieldMapping> fieldMapping;
    }

    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    public static class MasterInfo extends BaseInfo {
        private List<ObjectDataDocument> data;
    }

    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    public static class DetailInfo extends BaseInfo {
        private List<ObjectDataDocument> dataList;
    }

    @Data
    public static class FieldMapping {

        @SerializedName(value = "colIndex")
        @JsonProperty(value = "colIndex")
        private String colIndex;

        @SerializedName(value = "apiName")
        @JsonProperty(value = "apiName")
        private String apiName;

        @SerializedName(value = "importFieldMark")
        @JsonProperty(value = "importFieldMark")
        private String importFieldMark;
    }

    @Data
    public static class OldOwnerTeamMember {
        @JsonProperty(value = "strategy")
        @SerializedName("strategy")
        private String strategy;
        @JsonProperty(value = "permissionType")
        @SerializedName("permissionType")
        private String permissionType;
        @JsonProperty(value = "roleList")
        @SerializedName("roleList")
        private List<String> roleList;
        @JsonProperty(value = "otherObjects")
        @SerializedName("otherObjects")
        private List<String> otherObjects;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private boolean success;
        private String message;
        private int errorCode;
        private ImportResultValue value;
        private Boolean hasImportPreProcessingFunction;
    }

    @Data
    public static class ImportResultValue {
        @SerializedName("ImportSucceedCount")
        @JSONField(name = "ImportSucceedCount")
        @JsonProperty(value = "ImportSucceedCount")
        private int importSucceedCount;

        @SerializedName("RowErrorList")
        @JSONField(name = "RowErrorList")
        @JsonProperty(value = "RowErrorList")
        private List<ImportError> rowErrorList;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class ImportError {
        @SerializedName("RowNo")
        @JSONField(name = "RowNo")
        @JsonProperty(value = "RowNo")
        private int rowNo;

        @SerializedName("ErrorMessage")
        @JSONField(name = "ErrorMessage")
        @JsonProperty(value = "ErrorMessage")
        private String errorMessage;

        @SerializedName("MasterId")
        @JSONField(name = "MasterId")
        @JsonProperty(value = "MasterId")
        String masterId;

        @SerializedName("AssociateMark")
        @JSONField(name = "AssociateMark")
        @JsonProperty(value = "AssociateMark")
        String associateMark;

        @SerializedName("objectApiName")
        @JSONField(name = "objectApiName")
        @JsonProperty(value = "objectApiName")
        String objectApiName;

        public ImportError(int rowNo, String errorMessage) {
            this.rowNo = rowNo;
            this.errorMessage = errorMessage;
        }

        public void errorMessageAppend(String errorMsg) {
            if (errorMessage == null) {
                errorMessage = errorMsg;
            } else {
                errorMessage += ".\n" + errorMsg;
            }
        }
    }
}
