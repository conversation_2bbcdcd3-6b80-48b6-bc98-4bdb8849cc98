package com.facishare.paas.appframework.core.predef.service.dto.payment;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface GetPaymentUrl {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private String objectDataId;


        @J<PERSON>NField(name = "M2")
        private String describeApiName;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private String paymentUrl;
    }
}
