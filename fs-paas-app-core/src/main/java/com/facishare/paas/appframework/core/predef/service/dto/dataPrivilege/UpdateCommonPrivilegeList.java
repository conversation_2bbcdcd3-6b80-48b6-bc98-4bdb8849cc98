package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.privilege.dto.ObjectDataPermissionInfo;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface UpdateCommonPrivilegeList {
    @Data
    class Arg {
        @JsonProperty("ObjectDataPermissionInfos")
        @SerializedName("ObjectDataPermissionInfos")
        @JSONField(name = "M1")
        List<ObjectDataPermissionInfo> objectDataPermissionInfos;
    }
    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        String value;
    }
}
