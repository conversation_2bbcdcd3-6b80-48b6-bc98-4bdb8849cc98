package com.facishare.paas.appframework.core.predef.service.dto.orguser;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

public interface FindMainDeptInfoByUserId {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        Set<String> userIds;
    }

    @Data
    @Builder
    class Result {
        List<FindMainDeptInfoByUserId.MainDeptInfo> mainDeptInfoList;
    }

    @Data
    @Builder
    class MainDeptInfo {
        private String userId;
        private String userName;
        private String deptId;
        private String deptName;
        private String leaderId;
        private String leaderName;
    }
}
