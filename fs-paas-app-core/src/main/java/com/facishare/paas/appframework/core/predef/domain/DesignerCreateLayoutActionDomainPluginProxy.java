package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.rest.core.annotation.*;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/11/10
 */
@RestResource(
        value = "NCRM",
        desc = "新建布局领域插件RPC代理类", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface DesignerCreateLayoutActionDomainPluginProxy {
    @POST(desc = "新建布局领域插件rest接口")
    DesignerCreateLayoutActionDomainPlugin.RestResult post(@ServiceURLParam String url, @Body DesignerCreateLayoutActionDomainPlugin.Arg arg, @HeaderMap Map<String, String> header);
}
