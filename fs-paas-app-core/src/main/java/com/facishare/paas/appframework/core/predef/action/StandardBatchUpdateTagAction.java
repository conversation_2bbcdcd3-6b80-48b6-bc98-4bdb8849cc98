package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.mq.AppDefaultRocketMQProducer;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.github.mybatis.util.SpringUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 给数据添加或者删除标签
 *
 * <AUTHOR>
 * @date 2020/2/12 11:37 上午
 */
public class StandardBatchUpdateTagAction extends AbstractStandardAction<StandardBatchUpdateTagAction.Arg,
        StandardBatchUpdateTagAction.Result> {
    private IObjectData data;
    private List<String> tagIdList;
    private AppDefaultRocketMQProducer producer = SpringUtil.getBean("dataHangTagProducer");

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        // 查询数据
        data = serviceFacade.findObjectData(actionContext.getUser(), arg.getDataId(), objectDescribe);
        // 标签id
        tagIdList = CollectionUtils.nullToEmpty(arg.getTagIds());
    }

    // TODO: 2023/2/3 两个接口可能都是全量更新，会有覆盖掉系统标签的风险
    @Override
    protected Result doAct(Arg arg) {
        if (BooleanUtils.isTrue(arg.getAppend())) {
            // 在已有标签，追加新标签
            // 如果新标签[]，没有变化
            infraServiceFacade.appendTags(data.getId(), tagIdList, objectDescribe.getApiName(), actionContext.getUser());
        } else {
            // 清除已有标签，打新标签
            // 如果新标签为[]，删除所有标签
            infraServiceFacade.batchBoundTags(data.getId(), tagIdList, objectDescribe.getApiName(), actionContext.getUser());
        }
        //发送MQ，Topic:UPDATE_TAG_FOR_DATA
        if (CollectionUtils.notEmpty(tagIdList)) {
            TagMessage mqBean = TagMessage.builder()
                    .objectApiName(objectDescribe.getApiName())
                    .objectDataId(data.getId())
                    .tagIds(tagIdList)
                    .append(arg.getAppend())
                    .build();
            producer.sendMessage(JacksonUtils.toJson(mqBean).getBytes(StandardCharsets.UTF_8));
        }
        return Result.builder().success(true).build();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Arg {
        @JsonProperty("data_id")
        String dataId;
        @JsonProperty("tag_id_list")
        List<String> tagIds;
        // TODO: 2023/2/3 前端加参数
        @JsonProperty("append")
        Boolean append;
    }

    @Data
    @Builder
    public static class TagMessage {
        private String objectApiName;
        private String objectDataId;
        private List<String> tagIds;
        private Boolean append;
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        boolean success;
    }


}
