package com.facishare.paas.appframework.core.predef.handler.flowcompleted;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2023/10/7.
 */
@Component
@HandlerProvider(name = "defaultFlowCompletedAfterSendActionMqHandler")
public class DefaultFlowCompletedAfterSendActionMqHandler extends AbstractFlowCompletedAfterHandler {

    @Autowired
    private ServiceFacade serviceFacade;

    @Override
    protected Result doHandle(HandlerContext context, Arg arg) {
        ApprovalFlowTriggerType triggerType = arg.getInterfaceArg().approvalFlowTriggerType();
        switch (triggerType) {
            case UPDATE:
                sendEditActionMq(context, arg);
                break;
            case INVALID:
                sendInvalidActionMq(context, arg);
                break;
            default:
                break;
        }
        return new Result();
    }

    private void sendEditActionMq(HandlerContext context, Arg arg) {
        //先拷贝一下数据，防止并发修改报错
        List<IObjectData> cpOldData = ObjectDataExt.copyList(ObjectDataDocument.ofDataList(arg.getEditActionInfo().getFillOldData()));
        Map<String, List<IObjectData>> cpUpdateDetails = ObjectDataExt.copyMap(ObjectDataDocument.ofDataMap(arg.getEditActionInfo().getDetailsToUpdate()));
        Map<String, List<IObjectData>> cpInvalidDetails = ObjectDataExt.copyMap(ObjectDataDocument.ofDataMap(arg.getEditActionInfo().getDetailsToInvalid()));
        Map<String, List<IObjectData>> cpDeleteDetails = ObjectDataExt.copyMap(ObjectDataDocument.ofDataMap(arg.getEditActionInfo().getDetailsToDelete()));
        Map<String, List<IObjectData>> cpAddDetails = ObjectDataExt.copyMap(ObjectDataDocument.ofDataMap(arg.getEditActionInfo().getDetailsToAdd()));

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            serviceFacade.sendActionMq(context.getUser(), cpOldData, ObjectAction.UPDATE);
            cpUpdateDetails.forEach((k, v) ->
                    serviceFacade.sendActionMq(context.getUser(), v, ObjectAction.UPDATE));
            cpInvalidDetails.forEach((k, v) ->
                    serviceFacade.sendActionMq(context.getUser(), v, ObjectAction.INVALID));
            cpDeleteDetails.forEach((k, v) ->
                    serviceFacade.sendActionMq(context.getUser(), v, ObjectAction.DELETE));
            cpAddDetails.forEach((k, v) ->
                    serviceFacade.sendActionMq(context.getUser(), v, ObjectAction.CREATE));
        });
        parallelTask.run();
    }

    private void sendInvalidActionMq(HandlerContext context, Arg arg) {
        //先拷贝一下数据，防止并发修改报错
        IObjectData cpData = ObjectDataExt.of(arg.data()).copy();
        Map<String, List<IObjectData>> cpDetailDataMap = ObjectDataExt.copyMap(arg.detailDataMap());

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            serviceFacade.sendActionMq(context.getUser(), Lists.newArrayList(cpData), ObjectAction.INVALID);
            cpDetailDataMap.forEach((k, v) -> serviceFacade.sendActionMq(context.getUser(), v, ObjectAction.INVALID));
        });
        parallelTask.run();
    }
}
