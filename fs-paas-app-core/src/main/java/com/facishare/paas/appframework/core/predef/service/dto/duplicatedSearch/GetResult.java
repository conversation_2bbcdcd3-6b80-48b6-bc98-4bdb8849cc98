package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public interface GetResult {
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private String describeApiName;
        private String relatedApiName;
        private Boolean includeObjectDescribes;
        private IDuplicatedSearch.Type type;
        private ObjectDataDocument objectData;
        private Boolean isNeedDuplicate;
        private Integer pageSize;
        private Integer pageNumber;
        private String duplicateRuleApiName;
        //用于第二次查重从redis中取结果
        private String ruleApiNameIntercepted;
        private String cacheLevel;
        private Boolean returnDuplicatedRule;
        private String assignDuplicateSearchApiName;
        private boolean fromSimpleSearch;

        public Boolean getIncludeObjectDescribes() {
            return !Objects.isNull(includeObjectDescribes) && includeObjectDescribes;
        }

        public String getRelatedApiName() {
            return Objects.isNull(relatedApiName) ? "" : relatedApiName;
        }

        public Arg toPluginArg() {
            Arg arg = new Arg();
            arg.setDescribeApiName(this.getDescribeApiName());
            arg.setType(this.getType());
            arg.setDuplicateRuleApiName(this.getDuplicateRuleApiName());
            return arg;
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Result {
        private List<ObjectDataDocument> dataList;
        private ObjectDescribeDocument objectDescribe;
        private ObjectDescribeDocument describeExt;
        private List<RelatedSearchInfo> relatedSearchInfos;
        private ButtonInfo buttonInfo;
        private List<String> includeFields;
        private boolean isKeepSave;
        private boolean createPermission;
        private Page page;
        private IDuplicatedSearch.Policy matchType;
        private List<String> adminIds;
        private DuplicateRuleInfo duplicateRuleInfo;
        private IDuplicatedSearch duplicatedSearch;

        public Result toPluginResult() {
            Result result = new Result();
            result.setDataList(this.getDataList());
            return result;
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class DuplicateRuleInfo {
        private String duplicateRuleName;
        private String duplicateRuleApiName;
        private String describeApiName;
        private IDuplicatedSearch.Type type;
        private Boolean enable;
        private IDuplicatedSearch duplicatedSearch;
    }

    @Data
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    @NoArgsConstructor
    @AllArgsConstructor
    class RelatedSearchInfo {
        private ObjectDescribeDocument objectDescribe;
        private long total;
        private IDuplicatedSearch.Policy matchType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class ButtonInfo {
        private List<Button> buttons;
        // 兼容6.5以前的老版本，不去掉这个字段，由于老对象有多个按钮，增加一个字段，buttonMap中的数据在buttonMapList里面存在
        private Map<String, String> buttonMap;
        private Map<String, List<String>> buttonMapList;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Page {
        private int pageCount;
        private int pageNumber;
        private int pageSize;
        private int total;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Button {
        private String apiName;
        private String label;
    }

    enum DuplicateButtonType {
        Detail("detail", I18NKey.VIEW),
        Admin("admin", I18NKey.CONTACT_ADMIN),
        Owner("owner", I18NKey.CONTACT_OWNER);
        private String apiName;
        private String label;

        DuplicateButtonType(String apiName, String label) {
            this.apiName = apiName;
            this.label = label;
        }

        public String getApiName() {
            return apiName;
        }

        public void setApiName(String apiName) {
            this.apiName = apiName;
        }

        public String getLabel() {
            return label;
        }

        public void setLabel(String label) {
            this.label = label;
        }
    }
}
