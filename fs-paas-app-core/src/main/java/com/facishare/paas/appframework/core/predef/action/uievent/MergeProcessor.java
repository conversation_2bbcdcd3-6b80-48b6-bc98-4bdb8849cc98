package com.facishare.paas.appframework.core.predef.action.uievent;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.UIEventBusinessException;
import com.facishare.paas.metadata.api.IObjectData;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

/**
 * 合并数据字段
 * <AUTHOR>
 * @date 2019-09-04 10:55
 */
public class MergeProcessor extends AbstractProcessor {

    public MergeProcessor(ActionContainer container) {
        super(container);
    }

    /**
     * 只在编辑的时候，merge数据
     * 不用merge主对象数据，因为函数不涉及新增主对象数据
     * @param request 请求
     * @param context 处理器上下文
     */
    @Override
    public void invoke(UIEventProcess.ProcessRequest request, ProcessorContext context) {
        StopWatch stopWatch = StopWatch.create("MergeProcessor.invoke");
        IObjectData masterData = request.getMasterData();
        Map<String, List<IObjectData>> detailMap = request.getDetailDataMap();
        // 不存在ID说明当前是由新建触发的UI事件，不进行merge
        if (StringUtils.isNotBlank(masterData.getId()) && CollectionUtils.notEmpty(detailMap)) {
            ParallelUtils.ParallelTask task = ParallelUtils.createParallelTask();
            detailMap.forEach((apiName,dataList) ->
                    task.submit(()->serviceFacade.mergeWithDbData(requestContext.getTenantId(),apiName,dataList)));
            try {
                task.await(5000, TimeUnit.MILLISECONDS);
            } catch (TimeoutException e) {
                throw new UIEventBusinessException("Merge data timeout!");
            }
        }
        stopWatch.lap("mergeWithDbData");
        stopWatch.logSlow(500);
    }
}
