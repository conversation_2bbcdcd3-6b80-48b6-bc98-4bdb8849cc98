package com.facishare.paas.appframework.core.predef.service.dto.tag;

import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface TagQuery {


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class QueryInfo {
        List<Conditions> conditions;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Conditions {
        String fieldName;
        String filterValue;
        String operator;
        String type;
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class OrderInfo {
        String orderBy;
        String orderOperator;
    }

}
