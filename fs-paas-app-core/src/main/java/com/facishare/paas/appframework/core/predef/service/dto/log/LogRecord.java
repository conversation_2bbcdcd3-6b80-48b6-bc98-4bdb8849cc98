package com.facishare.paas.appframework.core.predef.service.dto.log;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.log.dto.EmpSimpleInfo;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.log.dto.ObjectInfo;
import com.facishare.paas.appframework.log.dto.TeamMemberInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by rensx on 2017/11/7.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LogRecord {

    @JSONField(name = "M1")
    private String logID;

    @JSONField(name = "M2")
    private String logMsg;

    @JSONField(name = "M3")
    private EmpSimpleInfo owner;    // 操作人员

    @JSONField(name = "M4")
    private long operationTime;

    //0是不能点,1是能点
    @JSONField(name = "M5")
    private int snapShotType;

    @JSONField(name = "M6")
    private String operationType;

    @JSONField(name = "M7")
    private LogDataDetailInfo logDataDetailInfo;

    //是否是主对象下的从对象的修改记录
    @JSONField(name = "M8")
    private boolean isDetailFromMaster;

    //来源
    @JSONField(name = "M9")
    private String peerName;

    //操作类型名称
    @JSONField(name = "M10")
    private String operationLabel;

    //对象信息
    @JSONField(name = "M11")
    private ObjectInfo objectInfo;

    @JSONField(name = "M12")
    private List<TeamMemberInfo.Msg> msgList;

    @JSONField(name = "M13")
    private List<LogInfo.DiffObjectData> objectData;    // 记录的字段信息以及变更信息 oldValue -> value

    @JSONField(name = "M14")
    private String peerReason;

    @JSONField(name = "M15")
    private List<LogInfo.DetailInfo> detailInfos;

    @JSONField(name = "M16")
    private String masterLogId;

    @JSONField(name = "M17")
    private String dataName;    // 记录的主属性

    @JSONField(name = "M18")
    private LogInfo.ApprovalFlowInfo approvalFlowInfo;

    private String configMsg;

    private String traceId; // 相关操作的traceId

    private String convertEventId;

    private ObjectInfo sourceObjectInfo;

    private List<LogInfo.DetailInfo> sourceDetailInfos;


    @Data
    public static class LogDataDetailInfo {
        private String dataId;
        private String name;
        private String describeApiName;
    }
}
