package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ContextManager;

import java.util.List;

public class StandardGdprProjectRequestLockAction extends BaseGdprProjectRequestAction {
    @Override
    protected Result doAct(Arg arg) {
        BaseObjectLockAction.Arg args = BaseObjectLockAction.Arg.of(arg.getDataId(), arg.getDetailObjStrategy(), arg.getLockRuleApiName());
        ActionContext actionContext = ContextManager.buildActionContext(arg.getApiName(), StandardAction.Lock.name());
        serviceFacade.triggerAction(actionContext, args, StandardLockAction.Result.class);
        infraServiceFacade.updateGdprProjectRequestByType(actionContext.getUser(), arg.getId(), "lock");
        return Result.builder().build();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.Lock.getFunPrivilegeCodes();
    }
}
