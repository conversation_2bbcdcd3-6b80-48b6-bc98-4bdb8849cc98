package com.facishare.paas.appframework.core.predef.service.dto.tools;

import com.facishare.paas.appframework.metadata.dto.tools.HandlerDescribe;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface GetHandler {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private String objectApiName;
        private String interfaceCode;
        private String tenantId;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<HandlerDescribe> handlerDescribes;
    }
}
