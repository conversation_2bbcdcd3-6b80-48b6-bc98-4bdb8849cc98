package com.facishare.paas.appframework.core.predef.service.dto.data;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2025/5/30
 */
public interface BuildSearchQuery {

    @Data
    class Arg {
        private String describeApiName;
        private String templateId;
        private String searchTemplateType;
        private String searchQueryInfo;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private String searchTemplateQuery;
    }
}
