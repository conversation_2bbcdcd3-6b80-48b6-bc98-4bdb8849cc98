package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.metadata.follow.FollowLogicService;
import com.facishare.paas.appframework.metadata.mongo.follow.bean.ObjectFollowInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;

public class StandardFollowAction extends AbstractStandardAction<StandardFollowAction.Arg, StandardFollowAction.Result> {

    protected IObjectData objectData;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getObjectDataId());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.FOLLOW.getButtonApiName();
    }


    @Override
    protected void init() {
        super.init();
        objectData = dataList.stream().filter(x -> Objects.equals(x.getId(), arg.getObjectDataId())).findFirst().orElse(null);
    }

    @Override
    protected IObjectData getPreObjectData() {
        return objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return objectData;
    }


    @Override
    protected Result doAct(Arg arg) {
        if (StringUtils.isBlank(arg.getObjectDataId())) {
            return new Result();
        }
        FollowLogicService followLogicService = serviceFacade.getBean(FollowLogicService.class);

        String apiName = objectDescribe.getApiName();

        List<ObjectFollowInfo> followInfoList = Lists.newArrayList();

        for (IObjectData data : dataList) {
            ObjectFollowInfo objectFollowInfo = new ObjectFollowInfo();
            objectFollowInfo.setFollowId(data.getId());
            objectFollowInfo.setFollowName(data.getName());
            objectFollowInfo.setApiName(apiName);
            objectFollowInfo.setLabel(objectDescribe.getDisplayName());
            objectFollowInfo.setLabelI18NKey(GetI18nKeyUtil.getDescribeDisplayNameKey(objectDescribe.getApiName()));
            objectFollowInfo.setBiz(ObjectFollowInfo.OBJECT_BIZ);
            followInfoList.add(objectFollowInfo);
        }
        followLogicService.bulkCreateFollowDataByUser(actionContext.getUser(), ObjectFollowInfo.OBJECT_BIZ, apiName, followInfoList);
        return new Result();
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Arg {
        private String objectDataId;

        public static StandardFollowAction.Arg of(String id) {
            return new StandardFollowAction.Arg(id);
        }
    }

    @Data
    public static class Result {

    }

}
