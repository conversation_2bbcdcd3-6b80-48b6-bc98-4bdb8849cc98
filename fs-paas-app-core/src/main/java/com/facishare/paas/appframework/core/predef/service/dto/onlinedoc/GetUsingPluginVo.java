package com.facishare.paas.appframework.core.predef.service.dto.onlinedoc;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> create by liy on 2024/6/19
 */
public interface GetUsingPluginVo {
    @Data
    class Arg {
        /**
         * 页号，从0开始
         */
        private int pageNumber;
        /**
         * 页大小
         */
        private int pageSize;
        /**
         * 应用类型
         */
        private String appType;

        public void validate() {
            if (pageSize <= 0) {
                //todo:待完善
                throw new ValidateException(I18NExt.text(I18NKey.ONLINE_DOC_ARG_ERROR_MISSING_DEFINE_TYPE));
            }
        }
    }

    @Data
    class Result {
        private List<GetAllPluginVo.PluginItem> pluginList;
        private boolean hasMore;
    }
}
