package com.facishare.paas.appframework.core.predef.service.dto.personalAuth;

import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.GetAllPluginVo;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/13
 */
public interface GetAllAuthVo {

    @Data
    class Arg {
        /**
         * 页号，从0开始
         */
        private int pageNumber;
        /**
         * 页大小
         */
        private int pageSize;
    }

    @Data
    class Result {
        /**
         * 未授权的插件,最多下发20个
         */
        private List<GetAllPluginVo.PluginItem> pluginList;
        /**
         * 授权记录
         */
        private List<AuthItem> authList;
        /**
         * 更多
         */
        private boolean hasMore;
    }

    @Data
    class AuthItem {
        /**
         * 应用名称，支持多语
         */
        private String appName;
        /**
         * 插件名称
         */
        private String pluginName;
        /**
         * app类型
         */
        private String appType;
        /**
         * 插件apiName
         */
        private String pluginApiName;
        /**
         * 扩展信息
         */
        private Map<String, String> pluginExtraInfo = Maps.newHashMap();
        /**
         * 授权时间
         */
        private long createTime;
        /**
         * 授权过期时间
         */
        private long expiredTime;
        /**
         * 图标
         */
        private String icon;
        /**
         * 完整icon路径
         */
        private String iconUrl;
    }
}
