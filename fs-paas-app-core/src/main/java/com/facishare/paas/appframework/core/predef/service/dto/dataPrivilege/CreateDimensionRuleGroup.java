package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.facishare.paas.appframework.privilege.dto.DimensionRuleGroupReceivePojo;
import com.facishare.paas.appframework.privilege.dto.DimensionRulePojo;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface CreateDimensionRuleGroup {

  @Data
  class Arg {

    String entityId;

    String ruleParse;

    int permission;

    String remark;

    int ruleType;

    List<DimensionRulePojo> rules;

    List<DimensionRuleGroupReceivePojo> receives;

  }


  @Data
  @Builder
  class Result {

    boolean success;

    String ruleCode;

  }

}
