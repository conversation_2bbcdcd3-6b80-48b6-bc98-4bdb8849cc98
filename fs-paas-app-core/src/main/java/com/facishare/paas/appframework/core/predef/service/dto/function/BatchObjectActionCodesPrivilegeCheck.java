package com.facishare.paas.appframework.core.predef.service.dto.function;

import lombok.Data;
import org.hibernate.validator.constraints.NotEmpty;

import java.util.List;
import java.util.Map;

public interface BatchObjectActionCodesPrivilegeCheck {

    @Data
    class Arg {
        @NotEmpty(message = "apiName2ActionCodes is empty")
        private Map<String, List<String>> apiName2ActionCodes;
    }
}
