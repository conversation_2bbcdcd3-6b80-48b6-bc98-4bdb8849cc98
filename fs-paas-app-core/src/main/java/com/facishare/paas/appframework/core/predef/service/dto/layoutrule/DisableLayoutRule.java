package com.facishare.paas.appframework.core.predef.service.dto.layoutrule;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface DisableLayoutRule {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private String layoutRuleApiName;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        @JSONField(name = "M1")
        boolean success;
    }
}
