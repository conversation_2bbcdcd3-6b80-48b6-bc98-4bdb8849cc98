package com.facishare.paas.appframework.core.predef.service.dto.language;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;

public interface Edit {
    @Data
    class Arg {
        @JSONField(name = "api_name")
        @JsonProperty(value = "api_name")
        String describeApiName;
    }

    @Data
    class Result {
        LanguageInfo describe;
        List<LanguageInfo> fields;
    }
}
