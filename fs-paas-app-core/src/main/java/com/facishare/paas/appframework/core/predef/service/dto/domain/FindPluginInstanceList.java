package com.facishare.paas.appframework.core.predef.service.dto.domain;

import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by zhouwr on 2022/9/13.
 */
public interface FindPluginInstanceList {
    @Data
    class Arg {
        private String searchQueryInfo;

        public Query toQuery() {
            ISearchTemplateQuery searchTemplateQuery = SearchTemplateQuery.fromJsonString(searchQueryInfo);
            return Query.fromSearchTemplateQuery(searchTemplateQuery);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        private Integer totalNumber;
        private List<DomainPluginInstanceDocument> pluginInstanceList;
    }

    @Data
    @EqualsAndHashCode(callSuper = true)
    class DomainPluginInstanceDocument extends DomainPluginInstance {
        private String pluginLabel;
        private String pluginDescription;
        private String refObjectDisplayName;
        private String recordTypeLabel;

        public static DomainPluginInstanceDocument of(DomainPluginInstance instance) {
            DomainPluginInstanceDocument document = new DomainPluginInstanceDocument();
            document.setId(instance.getId());
            document.setPluginApiName(instance.getPluginApiName());
            document.setRefObjectApiName(instance.getRefObjectApiName());
            document.setRecordTypeList(instance.getRecordTypeList());
            document.setActive(instance.isActive());
            document.setCreateTime(instance.getCreateTime());
            document.setCreateBy(instance.getCreateBy());
            document.setLastModifiedTime(instance.getLastModifiedTime());
            document.setLastModifiedBy(instance.getLastModifiedBy());
            document.setPredefined(instance.getPredefined());
            document.setExcludeRecordType(instance.getExcludeRecordType());
            return document;
        }
    }
}
