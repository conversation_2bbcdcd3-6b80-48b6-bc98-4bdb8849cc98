package com.facishare.paas.appframework.core.predef.service.dto.fieldshowname;

import com.facishare.paas.appframework.metadata.repository.model.FieldShowName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface FindFieldShowName {

    @Data
    class Arg {
        private String describeApiName;
    }


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private List<FieldShowName> fieldShowNames;
    }
}
