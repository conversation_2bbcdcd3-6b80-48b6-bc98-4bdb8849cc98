package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface DuplicatedSearchRefresh {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Arg {
        List<String> describeApiNameList;
    }

    @Data
    @NoArgsConstructor
    @Builder
    @AllArgsConstructor
    class Result {
        List<String> failDescribeApiNameList;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class BatchArg {
        Map<String, List<String>> grayTenant;
    }

    @Data
    @NoArgsConstructor
    @Builder
    @AllArgsConstructor
    class BatchResult {
        Map<String, List<String>> finalTenant;
    }
}
