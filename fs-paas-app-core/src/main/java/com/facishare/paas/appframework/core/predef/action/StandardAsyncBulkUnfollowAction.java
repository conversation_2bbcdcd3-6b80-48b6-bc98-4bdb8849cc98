package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.ObjectAction;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.stream.Collectors;

public class StandardAsyncBulkUnfollowAction extends AbstractStandardAsyncBulkAction<StandardAsyncBulkUnfollowAction.Arg, StandardUnfollowAction.Arg> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected String getDataIdByParam(StandardUnfollowAction.Arg param) {
        return param.getObjectDataId();
    }

    @Override
    protected List<StandardUnfollowAction.Arg> getButtonParams() {
        return arg.getDataIds().stream().map(StandardUnfollowAction.Arg::of).collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.UNFOLLOW.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.UNFOLLOW.getActionCode();
    }


    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class Arg {
        private List<String> dataIds;
    }
}
