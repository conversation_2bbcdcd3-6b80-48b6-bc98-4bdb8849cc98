package com.facishare.paas.appframework.core.predef.handler.add;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ServiceFacade;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.facade.ApprovalFlowServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.AuditLogServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.SaveActionServiceFacade;
import com.facishare.paas.appframework.core.predef.handler.SaveActionHandler;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.dto.MasterApprovalResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;
import java.util.Objects;

/**
 * Created by zhouwr on 2023/2/1.
 */
@Component
@HandlerProvider(name = "defaultTriggerUpdateApprovalFlowAddBeforeHandler")
public class DefaultTriggerUpdateApprovalFlowAddBeforeHandler implements AddActionHandler {

    @Autowired
    private AuditLogServiceFacade auditLogServiceFacade;
    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private ApprovalFlowServiceFacade approvalFlowServiceFacade;
    @Autowired
    private SaveActionServiceFacade saveActionServiceFacade;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        ObjectLifeStatus lastLifeStatus = ObjectDataExt.of(arg.objectData()).getLifeStatus();
        boolean triggerApprovalFlowSuccess = tryTriggerMasterUpdateApproval(context, arg);
        recordLifeStatusModifyLog(context, arg, lastLifeStatus);
        //同步更新从对象的生命状态
        updateDetailObjectDataLifeStatus(context, arg, lastLifeStatus, triggerApprovalFlowSuccess);
        if (triggerApprovalFlowSuccess) {
            throw new AcceptableValidateException(buildMasterApprovalResult(context, arg, arg.objectData()));
        }
        return new Result();
    }

    private boolean tryTriggerMasterUpdateApproval(HandlerContext context, Arg arg) {
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        if (objectDescribe.isBigObject() || objectDescribe.isPublicObject() || context.skipApprovalFlow()) {
            return false;
        }
        if (!approvalFlowServiceFacade.needTriggerMasterApproval(objectDescribe)) {
            return false;
        }
        Map<String, IObjectDescribe> objectDescribes = arg.allDescribes();
        IObjectData objectData = arg.objectData();
        Map<String, ApprovalFlowStartResult> flowStartResultMap = approvalFlowServiceFacade.tryTriggerMasterApproval(context.getUser(),
                ApprovalFlowTriggerType.UPDATE, ObjectAction.CREATE, objectData, null, objectDescribes,
                arg.getInterfaceArg().getFreeApprovalDef(), arg.getExtraCallbackData(), Collections.emptyMap());
        return flowStartResultMap.containsValue(ApprovalFlowStartResult.SUCCESS);
    }

    private void recordLifeStatusModifyLog(HandlerContext context, Arg arg, ObjectLifeStatus lastLifeStatus) {
        auditLogServiceFacade.recordLifeStatusModifyLog(context.getTenantId(), arg.objectData(), arg.getObjectDescribe(), lastLifeStatus);
    }

    private void updateDetailObjectDataLifeStatus(HandlerContext context, SaveActionHandler.Arg arg, ObjectLifeStatus lastLifeStatus,
                                                  boolean triggerApprovalFlowSuccess) {
        saveActionServiceFacade.updateDetailObjectDataLifeStatus(context, arg, lastLifeStatus, triggerApprovalFlowSuccess);
    }

    private BaseObjectSaveAction.Result buildMasterApprovalResult(HandlerContext context, Arg arg, IObjectData objectData) {
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        IObjectDescribe masterDescribe = getRealMasterDescribe(objectDescribe);
        String displayName = null;
        if (Objects.nonNull(masterDescribe)) {
            displayName = masterDescribe.getDisplayName();
        }
        IObjectData realMasterData = approvalFlowServiceFacade.getRealMasterObjectData(context.getUser(), objectDescribe, objectData);
        IObjectData masterData = new ObjectData();
        if (Objects.nonNull(realMasterData)) {
            masterData.setId(realMasterData.getId());
            masterData.setDescribeApiName(realMasterData.getDescribeApiName());
            masterData.setName(realMasterData.getName());
        }
        MasterApprovalResult masterApprovalResult = MasterApprovalResult.builder()
                .masterDisplayName(displayName)
                .masterData(ObjectDataDocument.of(masterData))
                .build();

        //终端老版本先把审批结果存到redis，在详情页提示出来
        if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_680)) {
            serviceFacade.saveMasterDataApprovalResult(objectData, masterApprovalResult);
        }

        //Web端和新版终端返回主对象的data和描述的displayName
        return BaseObjectSaveAction.Result.builder()
                .triggerApproval(true)
                .masterApprovalResult(masterApprovalResult)
                .objectData(ObjectDataDocument.of(objectData))
                .build();
    }

    private IObjectDescribe getRealMasterDescribe(IObjectDescribe objectDescribe) {
        MasterDetailFieldDescribe masterDetail = ObjectDescribeExt.of(objectDescribe).getMasterDetailFieldDescribe().orElse(null);
        if (masterDetail == null) {
            return null;
        }
        return serviceFacade.findObject(objectDescribe.getTenantId(), masterDetail.getTargetApiName());
    }
}
