package com.facishare.paas.appframework.core.model.plugin;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.function.plugin.FunctionPluginConfig;
import com.facishare.paas.appframework.metadata.repository.model.MtFunctionPluginConf;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * Created by zhouwr on 2022/4/14.
 */
public interface Plugin {

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        private String objectApiName;
        private MtFunctionPluginConf functionPluginConf;

        public Arg(String objectApiName) {
            this.objectApiName = objectApiName;
        }
    }

    class Result {

    }

    default String getFunctionApiName(PluginContext context, Arg arg) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OBJECT_EXTENSION, context.getTenantId())) {
            MtFunctionPluginConf pluginConf = arg.getFunctionPluginConf();
            String moduleType = context.getModuleType();    // RelatedList 专用参数 相关列表类型
            String functionApiName = pluginConf.getFunctionApiName();
            if (StringUtils.isEmpty(moduleType)) {
                return functionApiName;
            }
            return CollectionUtils.nullToEmpty(pluginConf.getModuleType()).contains(moduleType) ? functionApiName : "";
        }
        else {
            FunctionPluginConfig instance = FunctionPluginConfig.getInstance();
            return instance.getFunctionApiName(context.getTenantId(), context.getObjectApiName(),
                    context.getModuleCode(), context.getModuleType());
        }
    }

}
