package com.facishare.paas.appframework.core.predef.action;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.ObjectAction.CHANGE_OWNER;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/10/12
 */
public class StandardAsyncBulkChangeOwnerAction extends AbstractStandardAsyncBulkAction<StandardChangeOwnerAction.Arg, StandardChangeOwnerAction.Arg> {

    @Override
    protected String getDataIdByParam(StandardChangeOwnerAction.Arg param) {
        return param.dataIds().get(0);
    }

    @Override
    protected List<StandardChangeOwnerAction.Arg> getButtonParams() {
        return arg.getData().stream()
                .map(data -> StandardChangeOwnerAction.Arg.builder().data(Lists.newArrayList(data))
                        .oldOwnerStrategy(arg.getOldOwnerStrategy())
                        .oldOwnerTeamMemberRole(arg.getOldOwnerTeamMemberRole())
                        .oldOwnerTeamMemberRoleList(arg.getOldOwnerTeamMemberRoleList())
                        .oldOwnerTeamMemberPermissionType(arg.getOldOwnerTeamMemberPermissionType())
                        .isCascadeDealDetail(arg.isCascadeDealDetail())
                        .skipTriggerApprovalFlow(arg.isSkipTriggerApprovalFlow())
                        .relateObjectApiNames(arg.getRelateObjectApiNames())
                        .isUpdateDataOwnDepartment(arg.isUpdateDataOwnDepartment())
                        .isNoSepcSpu(arg.isNoSepcSpu())
                        .build())
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return CHANGE_OWNER.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return CHANGE_OWNER.getActionCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.ChangeOwner.getFunPrivilegeCodes();
    }
}
