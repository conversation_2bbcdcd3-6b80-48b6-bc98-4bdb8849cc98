package com.facishare.paas.appframework.core.predef.handler;

import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.function.plugin.FunctionPluginService;
import com.facishare.paas.appframework.function.plugin.FunctionPluginService.FunctionPluginContext;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * Created by zhouwr on 2023/4/21.
 */
@Slf4j
@Component
@HandlerProvider(name = "defaultAPLHandler")
public class DefaultAPLHandler implements Handler {

    @Autowired
    private FunctionPluginService functionPluginService;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        FunctionPluginContext<Result> functionPluginContext = FunctionPluginContext.<Result>builder()
                .user(context.getUser())
                .argProcess(() -> buildArg(arg))
                .resultProcess(jsonString -> buildResult(arg, jsonString))
                .build();
        return functionPluginService.executeFuncMethod(functionPluginContext, arg.getHandlerDescribe().getAplApiName(), HANDLE);
    }

    private List<String> buildArg(Arg arg) {
        return Lists.newArrayList(JacksonUtils.toJson(arg));
    }

    protected Class<? extends Result> getResultType(Arg arg) {
        return HandlerResultTypeMappings.getHandlerResultType(arg.getHandlerDescribe().getInterfaceCode());
    }

    private Result buildResult(Arg arg, String jsonString) {
        if (Strings.isNullOrEmpty(jsonString)) {
            log.warn("APL handler return empty result,aplApiName:{}", arg.getHandlerDescribe().getAplApiName());
            return null;
        }
        Class<? extends Result> resultType = getResultType(arg);
        if (Objects.isNull(resultType)) {
            throw new ValidateException("Cannot find result type for interfaceCode: " + arg.getHandlerDescribe().getInterfaceCode());
        }
        APLResult aplResult = JacksonUtils.fromJson(jsonString, APLResult.class, resultType);
        if (!aplResult.success()) {
            log.warn("execute APL handler failed,aplApiName:{},result:{}", arg.getHandlerDescribe().getAplApiName(), jsonString);
            throw new ValidateException(aplResult.getErrorMessage());
        }
        return aplResult.getResult();
    }


}
