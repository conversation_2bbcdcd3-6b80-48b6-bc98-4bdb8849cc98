package com.facishare.paas.appframework.core.model.handler;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by zhouwr on 2024/3/13.
 */
public interface ControllerHandler<A extends ControllerHandler.Arg, R extends Handler.Result> extends Handler<A, R> {
    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg<I, O> extends Handler.Arg<I> {
        private O interfaceResult;
    }
}
