package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

public interface AssignRecord {

    @Data
    class Arg{
        @JSONField(name = "M1")
        String describeApiName;
        @J<PERSON>NField(name = "M2")
        String role_list;
        private String sourceInfo;

    }

    @Data
    class Result{
        @JSO<PERSON>ield(name = "M2")
        private boolean success;
    }
}
