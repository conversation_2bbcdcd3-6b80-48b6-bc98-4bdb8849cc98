package com.facishare.paas.appframework.core.predef.service.dto.function;

import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by fengjy in 2019/3/28 14:49
 */
public interface PromotionRunFunction {

    @Data
    class Arg {

        private String apiName;

        private String bindingObjectAPIName;

        private Map<String, Object> objectData;

        private Map<String, List<Map<String, Object>>> details;
    }

    @Data
    @Builder
    class Result {
        private boolean success;
        private Object functionResult;
        private String errorInfo;
    }

}
