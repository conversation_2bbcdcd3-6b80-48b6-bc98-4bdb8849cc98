package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.GeoAddressDTO;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.SignInException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SignInFieldDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Lists;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 签到签退基类
 */
@Slf4j
public abstract class BaseObjectSignAction extends PreDefineAction<BaseObjectSignAction.Arg, BaseObjectSignAction.Result> {

    //地球半径(单位:米)
    private static final double EARTH_RADIUS = 6378137;
    private static final String LOCATION_SEPARATOR = "#%\\$";
    private static final int LOCATION_PARTS_MIN_LENGTH = 2;

    /**
     * 补偿地址信息
     *
     * @param location 原始位置信息
     * @return 补偿后的位置信息，如果补偿失败返回原始位置信息
     * @throws SignInException 当位置信息格式不正确时抛出异常
     */
    protected String compensateAddress(String location) {
        if (StringUtils.isBlank(location)) {
            throwSignInOrOutException();
        }

        String[] locationParts = location.split(LOCATION_SEPARATOR);
        if (locationParts.length < LOCATION_PARTS_MIN_LENGTH) {
            throwSignInOrOutException();
        }
        // 如果已有地址信息，直接返回
        if (locationParts.length >= 3 && StringUtils.isNotBlank(locationParts[2])) {
            return location;
        }

        // 验证经纬度格式
        try {
            if (invalidCoordinate(locationParts[0]) || invalidCoordinate(locationParts[1])) {
                throwSignInOrOutException();
            }
        } catch (NumberFormatException e) {
            log.error("Failed to parse coordinate: {}", location, e);
            throwSignInOrOutException();
        }

        // 尝试获取地址信息
        try {
            String address = getAddressFromCoordinates(locationParts[0], locationParts[1]);
            return joinLocationParts(locationParts[0], locationParts[1], address);
        } catch (Exception e) {
            log.warn("Failed to get address from coordinates: {}", location, e);
            return location;
        }
    }

    /**
     * 验证坐标值是否有效
     */
    private boolean invalidCoordinate(String coordinate) {
        if (coordinate == null || coordinate.equalsIgnoreCase("NaN")) {
            return true;
        }
        double value = Double.parseDouble(coordinate);
        return Double.isNaN(value) || Double.isInfinite(value);
    }

    /**
     * 获取地址信息
     */
    private String getAddressFromCoordinates(String longitudeStr, String latitudeStr) {
        Double longitude = Double.parseDouble(longitudeStr);
        Double latitude = Double.parseDouble(latitudeStr);

        GeoAddressDTO.ReverseGeoRequest request = new GeoAddressDTO.ReverseGeoRequest();
        request.setLongitude(longitude);
        request.setLatitude(latitude);
        request.setLanguage(actionContext.getLang().getValue());
        request.setBizName(actionContext.getActionCode());

        GeoAddressDTO.GeoResult result = infraServiceFacade.getAddressByGeo(actionContext.getUser(), request);
        return result != null ? result.getAddress() : null;
    }

    /**
     * 拼接位置信息
     */
    private String joinLocationParts(String longitude, String latitude, String address) {
        if (StringUtils.isBlank(address)) {
            return String.format("%s#%%$%s", longitude, latitude);
        }
        return String.format("%s#%%$%s#%%$%s", longitude, latitude, address);
    }

    /**
     * 抛出签到异常
     */
    private void throwSignInOrOutException() {
        String errorKey = StringUtils.equals(actionContext.getActionCode(), ObjectAction.SIGN_IN.getActionCode())
            ? I18NKey.SIGN_IN_ERROR 
            : I18NKey.SIGN_OUT_ERROR;
        throw new SignInException(I18N.text(errorKey));
    }

    protected String dataId;
    protected IObjectData afterUpdated;
    protected IObjectData data;
    private IObjectData dbData;
    protected boolean needUpdateSignInInfoList;

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        //不校验写权限
        return null;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        data = new ObjectData(Document.parse(arg.getObjectData()));
        dataId = data.getId();
        dbData = serviceFacade.findObjectData(actionContext.getTenantId(), dataId, objectDescribe);
    }

    @Override
    protected Result doAct(Arg arg) {
        signIn();
        return BaseObjectSignAction.Result.builder().build();
    }

    @Override
    protected Result after(Arg arg, Result result) {
        super.after(arg, result);
        Map<String, Object> diff = ObjectDataExt.of(dbData).diff(afterUpdated, objectDescribe);
        serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, objectDescribe, afterUpdated, diff, dbData);
        return result;
    }

    protected void handleNewSignInfo(IObjectData data, IObjectData stored, SignInFieldDescribe signInFieldDescribe, String bizType, String status) {
        List<Map> signInInfoList = parseSignInfoList(data, signInFieldDescribe);
        //元数据底层把类型改了，所以在此做一下转换
        int statusInt = convertStatus(status);
        int bizTypeInt = convertBizType(bizType);
        if (!CollectionUtils.isEmpty(signInInfoList)) {
            for (Map info : signInInfoList) {
                info.put("biz_type__c", bizTypeInt);
                info.put("status__c", statusInt);
                info.put("sign_time__c", System.currentTimeMillis());
                info.put("system_risk__c", convertSystemRisk(info.get("system_risk__c")));
            }

            List<Map> storedList = parseSignInfoList(stored, signInFieldDescribe);
            if (null == storedList) {
                storedList = Lists.newArrayList();
            }

            storedList.addAll(signInInfoList);
            stored.set(signInFieldDescribe.getSignInInfoListFieldApiName(), storedList);
            needUpdateSignInInfoList = true;
        }
    }

    protected long getDistance(SignInFieldDescribe signInFieldDescribe, IObjectData data) {
        String defaultValue = signInFieldDescribe.getQuoteField();
        String replaceStr = defaultValue.replace("$", "");
        String lookUpFieldApiName;
        String locationFieldApiName;
        IObjectData targetObjectData = null;
        if (replaceStr.contains(".")) {
            String[] split = replaceStr.split("\\.");
            lookUpFieldApiName = split[0].replace("__r", "");
            locationFieldApiName = split[1];
            ObjectReferenceFieldDescribe fieldDescribe = (ObjectReferenceFieldDescribe) objectDescribe.getFieldDescribe(lookUpFieldApiName);
            //获取当前对象数据
            String targetDataId = ObjectDataExt.of(dbData).get(lookUpFieldApiName, String.class);
            if (StringUtils.isEmpty(targetDataId)) {
                return 0;
            }
            //获取关联对象数据
            targetObjectData = serviceFacade.findObjectData(actionContext.getUser(), targetDataId, fieldDescribe.getTargetApiName());
        } else {
            locationFieldApiName = replaceStr;
            targetObjectData = dbData;
        }
        String targetLocation = targetObjectData.get(locationFieldApiName, String.class);
        String currentLocation = getCurrentLocation(data, signInFieldDescribe);
        log.info("currentLocation:{} , targetLocation:{}", currentLocation, targetLocation);
        //根据定位计算距离
        return calculateDistance(targetLocation, currentLocation);
    }

    public String getCurrentLocation(IObjectData data, SignInFieldDescribe signInFieldDescribe) {
        return null;
    }

    protected long calculateDistance(String targetLocation, String currentLocation) {
        if (targetLocation == null || currentLocation == null) {
            return 0;
        }
        String[] target = targetLocation.split("#%\\$");
        BigDecimal targetLng = BigDecimal.valueOf(Double.parseDouble(target[0]));
        BigDecimal targetLat = BigDecimal.valueOf(Double.parseDouble(target[1]));

        String[] current = currentLocation.split("#%\\$");
        BigDecimal currentLng = BigDecimal.valueOf(Double.parseDouble(current[0]));
        BigDecimal currentLat = BigDecimal.valueOf(Double.parseDouble(current[1]));

        // 经度 将一个角度测量的角度转换成以弧度表示的近似角度
        double lng1 = Math.toRadians(targetLng.doubleValue());
        double lng2 = Math.toRadians(currentLng.doubleValue());
        // 纬度 将一个角度测量的角度转换成以弧度表示的近似角度
        double lat1 = Math.toRadians(targetLat.doubleValue());
        double lat2 = Math.toRadians(currentLat.doubleValue());
        // 纬度之差
        double a = lat1 - lat2;
        // 经度之差
        double b = lng1 - lng2;
        // 计算两点距离的公式
        double s = 2 * Math.asin(Math.sqrt(Math.pow(Math.sin(a / 2), 2) +
                Math.cos(lat1) * Math.cos(lat2) * Math.pow(Math.sin(b / 2), 2)));
        // 弧长乘地球半径
        s *= EARTH_RADIUS;

        long distance = Math.round(s * 10000) / 10000;

        log.info("The distance between the destination address and the current address is ：{}", distance);

        return distance;
    }

    private int convertSystemRisk(Object systemRisk) {
        switch (String.valueOf(systemRisk)) {
            case "正常":// ignoreI18n
                return 0;
            case "IOS越狱":// ignoreI18n
                return 1;
            case "Android 作弊软件":// ignoreI18n
                return 2;
            case "Android 伪造地址":// ignoreI18n
                return 3;
            case "模拟器":// ignoreI18n
                return 4;
            case "root":
                return 5;
            default:
                return -1;
        }
    }

    private int convertBizType(String bizType) {
        switch (bizType) {
            case "sign_in":
                return 1;
            case "sign_out":
                return 2;
            default:
                return 0;
        }
    }


    private int convertStatus(String status) {
        switch (status) {
            case "sign_in_complete":
                return 1;
            case "sign_out_complete":
                return 2;
            default:
                return 3;
        }
    }

    private List<Map> parseSignInfoList(IObjectData data, SignInFieldDescribe signInFieldDescribe) {
        Object signInInfo = data.get(signInFieldDescribe.getSignInInfoListFieldApiName());
        if (null == signInInfo || !(signInInfo instanceof List)) {
            return null;
        }
        return (List<Map>) signInInfo;
    }

    private void signIn() {
        IObjectData stored = ObjectDataExt.of(dbData).copy();
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        Optional<SignInFieldDescribe> signInFieldDescribe = describeExt.getSignInFieldDescribe();
        if (!signInFieldDescribe.isPresent()) {
            throw new SignInException(I18N.text(I18NKey.NOT_FIND_SIGN_IN));
        }

        List<String> updateFields = process(signInFieldDescribe.get(), stored, data);
        if (needUpdateSignInInfoList) {
            updateFields.add(signInFieldDescribe.get().getSignInInfoListFieldApiName());
        }

        serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(stored), updateFields);
        afterUpdated = stored;
    }

    protected abstract List<String> process(SignInFieldDescribe signInFieldDescribe, IObjectData stored, IObjectData data);

    @Data
    public static class Arg {
        @JSONField(name = "M1")
        //兼容接口数据规格
        @JsonProperty("objectDataJSON")
        @SerializedName("objectDataJSON")
        private String objectData;

    }


    @Data
    @Builder
    @NoArgsConstructor
    public static class Result {

    }

    @Data
    @NoArgsConstructor
    public static class TargetValue {
        @JsonProperty("default_value")
        @SerializedName("default_value")
        private String defaultValue;
        @JsonProperty("default_is_expression")
        @SerializedName("default_is_expression")
        private Boolean defaultIsExpression;
    }
}
