package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.expression.FieldUpdateDTO;
import com.facishare.paas.appframework.metadata.expression.SimpleExpression;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2018/7/24
 */
public interface CalculateWithExpression {

    @Data
    class Arg {
        private String objectApiName;
        private ObjectDataDocument objectData;
        private Map<String, Object> extData;
        private List<SimpleExpression> expressionList;
    }

    @Data
    @AllArgsConstructor
    class Result {
        private Map<String, Object> calculateValues;
    }
}
