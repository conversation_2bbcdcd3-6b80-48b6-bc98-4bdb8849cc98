package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.relation.CalculateRelation;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * create by z<PERSON><PERSON> on 2020/07/21
 */
public interface CalculateBatchData {
    @Data
    class Arg {
        private String objectApiName;
        /**
         * data，key 为行号，value 为data
         */
        private Map<String, ObjectDataDocument> dataMap;
        /**
         * 修改数据的编号
         */
        private List<String> modifiedDataIndexList;
        /**
         * 需要计算的字段的apiName
         */
        private List<String> calculateFieldApiNames;
        /**
         * 带着计算顺序的计算字段（用于替换calculateFieldApiNames）
         */
        private List<CalculateRelation.RelateField> calculateFields;

    }

    @Data
    class Result {

        //计算结果(key是数据的编号)
        private Map<String, ObjectDataDocument> calculateResult = Maps.newHashMap();

        public void addData(String index, IObjectData data, List<String> fieldNames) {
            calculateResult.putIfAbsent(index, ObjectDataDocument.of(ObjectDataExt.of(data).toMap(fieldNames)));

            //补充lookup字段的__r和引用字段的__v
            Map<String, Object> dataMap = ObjectDataExt.of(data).toMap();
            fieldNames.forEach(x -> {
                String lookupName = FieldDescribeExt.getLookupNameByFieldName(x);
                if (dataMap.containsKey(lookupName)) {
                    calculateResult.get(index).put(lookupName, dataMap.get(lookupName));
                }

                String quotedValueName = FieldDescribeExt.getQuotedValueNameByFieldName(x);
                if (dataMap.containsKey(quotedValueName)) {
                    calculateResult.get(index).put(quotedValueName, dataMap.get(quotedValueName));
                }
            });
        }
    }
}
