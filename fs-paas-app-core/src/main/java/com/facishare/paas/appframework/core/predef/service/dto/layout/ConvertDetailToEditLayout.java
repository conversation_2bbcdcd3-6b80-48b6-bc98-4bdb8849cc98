package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import lombok.Builder;
import lombok.Data;

public interface ConvertDetailToEditLayout {

    @Data
    class Arg {
        private String layoutApiName;
        private String describeApiName;
        private boolean removeI18n = false;
    }

    @Builder
    @Data
    class Result {
        private LayoutDocument layout;
        private ObjectDescribeDocument objectDescribe;
        private ObjectDescribeDocument describeExtra;
    }
}
