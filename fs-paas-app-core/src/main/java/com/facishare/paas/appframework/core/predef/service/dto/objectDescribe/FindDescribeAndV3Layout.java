package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import lombok.Builder;
import lombok.Data;

public interface FindDescribeAndV3Layout {
    @Data
    class Arg {
        String describeApiName;
        String layoutApiName;
        boolean includeLayout = true;
    }

    @Builder
    @Data
    class Result {
        LayoutDocument layout;
        ObjectDescribeDocument objectDescribe;
    }
}
