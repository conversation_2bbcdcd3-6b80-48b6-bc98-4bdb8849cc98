package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.FundAccountBaseService;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;

import java.util.List;

public class StandardAsyncBulkCancelEntryAction extends StandardBulkCustomButtonAction {

    @Override
    protected List<String> getDataPrivilegeIds(StandardBulkCustomButtonAction.Arg arg) {
        return arg.getDataIds();
    }

    @Override
    protected String getButtonApiName() {
        return super.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.CANCEL_ENTRY.getActionCode();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.CANCEL_ENTRY.getActionCode());
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
    }
}
