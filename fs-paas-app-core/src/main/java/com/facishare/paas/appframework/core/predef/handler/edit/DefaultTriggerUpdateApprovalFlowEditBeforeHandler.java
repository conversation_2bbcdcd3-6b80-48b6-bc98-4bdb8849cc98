package com.facishare.paas.appframework.core.predef.handler.edit;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.facade.ApprovalFlowServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.AuditLogServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.SaveActionServiceFacade;
import com.facishare.paas.appframework.core.predef.handler.SaveActionHandler;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.transaction.tcc.annotation.TccTransactional;
import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Map;

/**
 * Created by zhouwr on 2023/2/1.
 */
@Slf4j
@Component
@HandlerProvider(name = "defaultTriggerUpdateApprovalFlowEditBeforeHandler")
public class DefaultTriggerUpdateApprovalFlowEditBeforeHandler implements EditActionHandler {
    public static final String TRIGGER_APPROVAL_FLOW_RESULT = "triggerApprovalFlowResult";
    public static final String LAST_LIFE_STATUS = "lastLifeStatus";
    public static final String LIFE_STATUS = "lifeStatus";
    @Autowired
    private AuditLogServiceFacade auditLogServiceFacade;
    @Autowired
    private ApprovalFlowServiceFacade approvalFlowServiceFacade;
    @Autowired
    private SaveActionServiceFacade saveActionServiceFacade;
    @Autowired
    private MetaDataActionService metaDataActionService;
    @Autowired
    private DataSnapshotLogicService dataSnapshotLogicService;
    @Autowired
    private DescribeLogicService describeLogicService;

    @Override
    @TccTransactional(name = "triggerEditApprovalBeforeEdit", confirmMethod = "confirm", cancelMethod = "cancel")
    public Result handle(HandlerContext context, Arg arg) {
        if (!needTriggerApprovalFlow(context, arg)) {
            return new Result();
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(arg.objectData());
        ObjectLifeStatus lastLifeStatus = objectDataExt.getLifeStatus();
        //尝试触发主对象的编辑审批流
        boolean triggerApprovalFlowSuccess = tryTriggerUpdateApprovalFlow(context, arg);
        if (grayPreMatchApproval(context)) {
            if (triggerApprovalFlowSuccess) {
                //成功触发了编辑审批，记录一条hidden=true的修改记录
                RequestUtil.setModifyLogHidden();
            }
            buildActionContextMap(lastLifeStatus, objectDataExt.getLifeStatus(), triggerApprovalFlowSuccess);
            return buildResult(arg, triggerApprovalFlowSuccess);
        }
        recordLogAndUpdateLifeStatus(context, arg, lastLifeStatus, triggerApprovalFlowSuccess);
        //返回触发审批触发结果
        return buildResult(arg, triggerApprovalFlowSuccess);
    }

    private void recordLogAndUpdateLifeStatus(HandlerContext context, Arg arg, ObjectLifeStatus lastLifeStatus, boolean triggerApprovalFlowSuccess) {
        //记录生命状态的修改记录
        recordLifeStatusModifyLog(context, arg, lastLifeStatus);
        //同步更新从对象的生命状态
        updateDetailObjectDataLifeStatus(context, arg, lastLifeStatus, triggerApprovalFlowSuccess);
        if (triggerApprovalFlowSuccess) {
            //成功触发了编辑审批，记录一条hidden=true的修改记录
            RequestUtil.setModifyLogHidden();
            //批量处理记录审计日志
            recordEditLog(context, arg);
        }
    }

    private void buildActionContextMap(ObjectLifeStatus lastLifeStatus, ObjectLifeStatus lifeStatus, boolean triggerApprovalFlowSuccess) {
        BranchTransactionalContext branchContext = BranchTransactionalContext.getCurrent();
        Map<String, Object> actionContextMap = Maps.newHashMap();
        actionContextMap.put(TRIGGER_APPROVAL_FLOW_RESULT, triggerApprovalFlowSuccess);
        actionContextMap.put(LAST_LIFE_STATUS, lastLifeStatus.getCode());
        actionContextMap.put(LIFE_STATUS, lifeStatus.getCode());
        branchContext.setActionContext(actionContextMap);
    }

    public void confirm(BranchTransactionalContext branchContext, HandlerContext context, Arg arg) {
        log.info("triggerEditApprovalBeforeEdit confirm start! ei:{}, actionCode:{}", context.getTenantId(), context.getInterfaceCode());
        if (!grayPreMatchApproval(context)) {
            return;
        }
        RequestContextManager.runWithContext(context.getRequestContext(), () -> {
            IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopy(context.getTenantId(), arg.getObjectApiName());
            arg.setObjectDescribe(objectDescribe);

            Map<String, Object> actionContextMap = branchContext.getActionContext();
            boolean triggerApprovalFlowSuccess = Boolean.TRUE.equals(actionContextMap.get(TRIGGER_APPROVAL_FLOW_RESULT));
            String lastLifeStatus = (String) actionContextMap.get(LAST_LIFE_STATUS);
            String lifeStatus = (String) actionContextMap.get(LIFE_STATUS);
            if (triggerApprovalFlowSuccess) {
                updateLifeStatusWhenTriggerApprovalSuccess(context, arg, lifeStatus);
                saveDataSnapshot(context.getUser(), arg);
            }
            recordLogAndUpdateLifeStatus(context, arg, ObjectLifeStatus.of(lastLifeStatus), triggerApprovalFlowSuccess);
            return true;
        });
    }

    private void updateLifeStatusWhenTriggerApprovalSuccess(HandlerContext context, Arg arg, String lifeStatus) {
        ObjectDataExt objectDataExt = ObjectDataExt.of(arg.objectData());
        objectDataExt.setLifeStatus(lifeStatus);
        metaDataActionService.batchUpdateByFields(context.getUser(), Lists.newArrayList(objectDataExt.getObjectData()),
                Lists.newArrayList(ObjectLifeStatus.LIFE_STATUS_API_NAME));
    }


    private void saveDataSnapshot(User user, Arg arg) {
        Map<String, Object> callbackData = getCallbackData(arg);
        addExtraInfo2CallbackData(callbackData, arg.getExtraCallbackData());
        Map<String, Map<String, Object>> detailChangeMap = arg.getDataDiffResult().getDetailChangeMap();
        IObjectData objectData = arg.objectData();

        ObjectDataSnapshot snapshot = ObjectDataSnapshot.builder()
                .masterSnapshot(callbackData)
                .detailSnapshot(detailChangeMap)
                .biz(RequestContext.Biz.ApprovalFlow.getCode())
                .bizId(RequestUtil.getOtherBizId())
                .subBizId(RequestUtil.getBizId())
                .build();
        dataSnapshotLogicService.createSnapshot(user, objectData.getDescribeApiName(), objectData.getId(), snapshot);
    }

    private void addExtraInfo2CallbackData(Map<String, Object> callbackData, Map<String, Object> extraCallbackData) {
        if (CollectionUtils.notEmpty(extraCallbackData)) {
            callbackData.putAll(extraCallbackData);
        }
    }

    private boolean grayPreMatchApproval(HandlerContext context) {
        return Boolean.TRUE.equals(context.getRequestAttribute(RequestContext.Attributes.PRE_MATCH_APPROVAL));
    }

    public void cancel(BranchTransactionalContext branchContext, HandlerContext context, Arg arg) {
        log.info("triggerEditApprovalBeforeEdit cancel start! ei:{}, actionCode:{}", context.getTenantId(), context.getInterfaceCode());
    }

    private Result buildResult(Arg arg, boolean triggerApprovalFlowSuccess) {
        Result result = new Result();
        if (triggerApprovalFlowSuccess) {
            BaseObjectSaveAction.Result actionResult = BaseObjectSaveAction.Result.builder()
                    .isDuplicate(false)
                    .writeDB(false)
                    .triggerApproval(true)
                    .objectData(arg.getObjectData())
                    .details(arg.getDetailObjectData())
                    .relatedDataList(arg.getRelatedObjectData())
                    .build();
            result.setInterfaceResult(actionResult);
        }
        return result;
    }

    private boolean needTriggerApprovalFlow(HandlerContext context, Arg arg) {
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        if (objectDescribe.isBigObject() || objectDescribe.isPublicObject()) {
            return false; //大对象不需要触发审批流
        }
        //审批过程中编辑数据不需要再次触发审批
        if (arg.useSnapshot()) {
            return false;
        }
        //arg里的参数优先级比queryParameter更高，cep请求不支持此参数
        if (arg.getInterfaceArg().skipApprovalFlow() && !RequestUtil.isCepRequest()) {
            return false;
        }
        return !context.skipApprovalFlow();
    }

    private boolean tryTriggerUpdateApprovalFlow(HandlerContext context, Arg arg) {
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        Map<String, Object> updatedFieldMapForApproval = arg.getDataDiffResult().getUpdatedFieldMapForApproval();
        Map<String, Object> normalUpdateMap = ObjectDataExt.of(Maps.newHashMap(updatedFieldMapForApproval))
                .removeCalculateField(objectDescribe).toMap();
        if (CollectionUtils.empty(normalUpdateMap) && !arg.getDataDiffResult().isDetailChangeForApproval()) {
            return false;
        }

        //只编辑了从对象的非灰度企业不触发审批
        if (CollectionUtils.empty(normalUpdateMap)
                && !AppFrameworkConfig.isInMasterDetailApprovalGrayList(context.getTenantId())) {
            return false;
        }

        //如果是单独编辑从对象，尝试触发主对象的审批，否则normal状态触发编辑审批
        IObjectData objectData = arg.objectData();
        Map<String, Object> callbackData = getCallbackData(arg);
        Map<String, ApprovalFlowStartResult> flowStartResultMap = Maps.newHashMap();
        if (approvalFlowServiceFacade.needTriggerMasterApproval(objectDescribe)) {
            flowStartResultMap = approvalFlowServiceFacade.tryTriggerMasterApproval(context.getUser(), ApprovalFlowTriggerType.UPDATE,
                    ObjectAction.UPDATE, objectData, callbackData, arg.masterAndDetailDescribes(),
                    arg.getInterfaceArg().getFreeApprovalDef(), arg.getExtraCallbackData(), Collections.emptyMap());
        } else if (ObjectDataExt.of(objectData).isNormal() && !ObjectDescribeExt.of(objectDescribe)
                .isSlaveObjectCreateWithMasterAndInGrayList()) {
            flowStartResultMap = approvalFlowServiceFacade.startApprovalFlow(context.getUser(), ApprovalFlowTriggerType.UPDATE, objectData,
                    updatedFieldMapForApproval, callbackData, arg.getDataDiffResult().getDetailChangeMap(), arg.masterAndDetailDescribes(),
                    arg.getInterfaceArg().getFreeApprovalDef(), arg.getExtraCallbackData(), Collections.emptyMap());
        }
        return flowStartResultMap.containsValue(ApprovalFlowStartResult.SUCCESS);
    }

    private Map<String, Object> getCallbackData(Arg arg) {
        return Maps.newHashMap(arg.getDataDiffResult().getUpdatedFieldMap());
    }

    private void recordLifeStatusModifyLog(HandlerContext context, SaveActionHandler.Arg arg, ObjectLifeStatus lastLifeStatus) {
        auditLogServiceFacade.recordLifeStatusModifyLog(context.getTenantId(), arg.objectData(), arg.getObjectDescribe(), lastLifeStatus);
    }

    private void updateDetailObjectDataLifeStatus(HandlerContext context, SaveActionHandler.Arg arg, ObjectLifeStatus lastLifeStatus,
                                                  boolean triggerApprovalFlowSuccess) {
        saveActionServiceFacade.updateDetailObjectDataLifeStatus(context, arg, lastLifeStatus, triggerApprovalFlowSuccess);
    }

    private void recordEditLog(HandlerContext context, Arg arg) {
        auditLogServiceFacade.recordEditLog(context.getUser(), arg, arg.masterAndDetailDescribes(), Maps.newHashMap());
    }
}
