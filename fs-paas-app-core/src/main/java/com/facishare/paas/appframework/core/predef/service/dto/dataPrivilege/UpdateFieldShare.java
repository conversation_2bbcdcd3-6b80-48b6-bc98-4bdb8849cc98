package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.privilege.dto.Receive;
import com.facishare.paas.appframework.privilege.dto.Rule;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface UpdateFieldShare {
    @Data
    class Arg {
        @JsonProperty("rules")
        @SerializedName("rules")
        @JSONField(name = "M1")
        List<Rule> rules;

        @JsonProperty("receives")
        @SerializedName("receives")
        @JSONField(name = "M5")
        List<Receive> receives;

        @JsonProperty("describeApiName")
        @SerializedName("describeApiName")
        @JSONField(name = "M2")
        String describeApiName;

        @JsonProperty("ruleName")
        @SerializedName("ruleName")
        @JSONField(name = "M3")
        String ruleName;

        @JsonProperty("ruleParse")
        @SerializedName("ruleParse")
        @JSONField(name = "M4")
        String ruleParse;

        @JsonProperty("ruleCode")
        @SerializedName("ruleCode")
        @JSONField(name = "M6")
        String ruleCode;

    }
    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        boolean success;
        @JSONField(name = "M2")
        String ruleId;
    }
}
