package com.facishare.paas.appframework.core.predef.service.dto.tag;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.TagDocument;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/19 2:07 下午
 */
public interface FindTagsByGroupId {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor

    class Arg {
        String tag_group_id;
        TagQuery.QueryInfo query_info;
        TagQuery.OrderInfo order_info;
        String keyword;
        String sourceInfo;
    }

    @Builder
    @Data
    class Result {
        List<TagDocument> tags;
    }
}
