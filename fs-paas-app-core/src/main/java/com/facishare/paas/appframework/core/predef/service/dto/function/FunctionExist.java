package com.facishare.paas.appframework.core.predef.service.dto.function;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by luozhq on 2018/3/27.
 */
public interface FunctionExist {
    @Data
    class Arg {
        @JSONField(name = "binding_object_api_name")
        @SerializedName(value = "binding_object_api_name")
        @JsonProperty(value = "binding_object_api_name")
        String bindingObjectApiName;

        @JSONField(name = "name_space")
        @SerializedName(value = "name_space")
        @JsonProperty(value = "name_space")
        List<String> nameSpace;

        @JSONField(name = "return_type")
        @SerializedName(value = "return_type")
        @JsonProperty(value = "return_type")
        String returnType;

        @JSONField(name = "return_type_list")
        @SerializedName(value = "return_type_list")
        @JsonProperty(value = "return_type_list")
        List<String> returnTypeList;
    }

    @Data
    @Builder
    class Result {
        Boolean isExist;
    }
}
