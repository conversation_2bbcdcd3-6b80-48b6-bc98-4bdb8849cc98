package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.ValidationResult;
import com.facishare.paas.appframework.core.model.domain.DomainProvider;
import com.facishare.paas.metadata.api.IObjectData;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2024/2/20.
 */
@Component
@DomainProvider(name = "_TestActionDomainPlugin_Add")
public class TestAddActionDomainPlugin implements AddActionDomainPlugin {
    @Override
    public Result before(ActionContext context, Arg arg) {
        Result result = new Result();
        if (CollectionUtils.notEmpty(arg.getSkippedStepKeys()) && arg.getSkippedStepKeys().contains("testAddStepKey")) {
            return result;
        }
        ValidationResult validationResult = ValidationResult.builder()
                .stepKey("testAddStepKey")
                .build();
        IObjectData objectData = arg.getObjectData().toObjectData();
        String name = objectData.getName();
        if (StringUtils.startsWith(name, "block_")) {
            validationResult.setBlock(true);
            validationResult.setMessage("This is a block test message for add");
            result.setValidationResult(validationResult);
        } else if (StringUtils.startsWith(name, "nonblock_")) {
            validationResult.setBlock(false);
            validationResult.setMessage("This is a nonblock test message for add");
            result.setValidationResult(validationResult);
        }
        return result;
    }

    @Override
    public Result after(ActionContext context, Arg arg) {
        return new Result();
    }
}
