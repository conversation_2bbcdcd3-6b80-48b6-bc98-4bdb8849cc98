package com.facishare.paas.appframework.core.predef.action;

import com.facishare.idempotent.Idempotent;
import com.facishare.idempotent.Serializer;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ActionContext;
import com.facishare.paas.appframework.core.model.MasterSlaveDataDocument;
import com.facishare.paas.appframework.core.model.MasterSlaveDataModel;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;

import java.util.List;
import java.util.Map;
import java.util.Objects;
@Idempotent(serializer = Serializer.Type.java)
public class StandardConvertSaveAction extends AbstractConvertAction {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        List<String> funcPrivilegeCodes = super.getFuncPrivilegeCodes();
        funcPrivilegeCodes.addAll(StandardAction.Add.getFunPrivilegeCodes());
        return funcPrivilegeCodes;
    }

    @Override
    protected Result doAct(Arg arg) {
        Result result = super.doAct(arg);
        List<MasterSlaveDataModel> masterSlaveDataModels = MasterSlaveDataDocument.ofDataModelList(result.getMasterSlaveDataList());
        CollectionUtils.nullToEmpty(masterSlaveDataModels).forEach(masterSlaveData -> {
            BaseObjectSaveAction.OptionInfo optionInfo = new BaseObjectSaveAction.OptionInfo();
            optionInfo.setConvertRule(arg.getRuleApiName());
            optionInfo.setFromMasterIds(masterSlaveData.getFromMasterIds());
            optionInfo.setFromDetails(masterSlaveData.getFromDetails());
            optionInfo.setFromApiName(arg.getSourceApiName());
            optionInfo.setFromTransform(true);
            BaseObjectSaveAction.Result saveResult = doSaveData(optionInfo, ObjectDataExt.of(masterSlaveData.getObjectData()),  masterSlaveData.getDetails());
            masterSlaveData.setObjectData(saveResult.getObjectData().toObjectData());
            masterSlaveData.setDetails(ObjectDataDocument.ofDataMap(saveResult.getDetails()));
        });
        result.setMasterSlaveDataList(MasterSlaveDataDocument.ofList(masterSlaveDataModels));
        return result;
    }

    private BaseObjectSaveAction.Result doSaveData(BaseObjectSaveAction.OptionInfo optionInfo, IObjectData objectData, Map<String, List<IObjectData>> mappingDetails) {
        BaseObjectSaveAction.Arg saveArg = new BaseObjectSaveAction.Arg();
        saveArg.setObjectData(ObjectDataDocument.of(objectData));
        saveArg.setDetails(ObjectDataDocument.ofMap(mappingDetails));
        saveArg.setOptionInfo(optionInfo);
        String describeApiName = objectData.getDescribeApiName();
        ActionContext context = new ActionContext(actionContext.getRequestContext(), describeApiName, "Add");
        if (!Objects.equals(describeApiName, actionContext.getObjectApiName())
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.TRIGGER_REMOTE_ACTION_GRAY_OBJECT, describeApiName)) {
            return serviceFacade.triggerRemoteAction(context, saveArg, BaseObjectSaveAction.Result.class);
        }
        return serviceFacade.triggerAction(context, saveArg, BaseObjectSaveAction.Result.class);
    }
}
