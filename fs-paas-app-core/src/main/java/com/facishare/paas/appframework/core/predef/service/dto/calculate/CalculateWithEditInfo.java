package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.facishare.paas.appframework.core.predef.action.StandardTriggerEventAction.Result.NEW_OBJECTS;
import static com.facishare.paas.appframework.core.predef.action.StandardTriggerEventAction.Result.UPDATE_OR_DELETE;

/**
 * create by z<PERSON><PERSON> on 2020/06/18
 */
public interface CalculateWithEditInfo {
    @Data
    class Arg {
        private String describeApiName;
        private DataInfo beforeDataInfo;
        private DataInfo afterDataInfo;
    }

    @Data
    class Result {
        Map<String, Map<String, Object>> data;

        public static Result of(IObjectData objectData, Map<String, List<IObjectData>> details) {
            Result result = new Result();
            result.setMasterData(objectData);
            result.batchDealDetailData(details);
            return result;
        }

        /**
         * 处理主对象数据
         */
        private void setMasterData(IObjectData objectData) {
            if (Objects.isNull(data)) {
                data = Maps.newLinkedHashMap();
            }
            data.put(objectData.getDescribeApiName(), ObjectDataDocument.of(objectData));
        }

        /**
         * 批量处理从对象数据
         */
        private void batchDealDetailData(Map<String, List<IObjectData>> detailsMap) {
            for (Map.Entry<String, List<IObjectData>> entry : detailsMap.entrySet()) {
                String apiName = entry.getKey();
                List<IObjectData> details = entry.getValue();
                batchDealDetailData(apiName, details);
            }
        }

        /**
         * 批量处理从对象数据
         */
        private void batchDealDetailData(String apiName, List<IObjectData> details) {
            data.put(apiName, detailEmptyMap());
            for (IObjectData detail : details) {
                dealDetailData(apiName, detail);
            }
        }

        /**
         * 保证返回数据结构中，从对象的"u":{}, "a":[]存在
         */
        private Map<String, Object> detailEmptyMap() {
            Map<String, Object> detailMap = Maps.newHashMap();
            detailMap.put(UPDATE_OR_DELETE, Maps.newHashMap());
            detailMap.put(NEW_OBJECTS, Lists.newArrayList());
            return detailMap;
        }

        /**
         * 处理从对象数据
         */
        void dealDetailData(String apiName, IObjectData detailData) {
            if (Objects.isNull(data)) {
                data = Maps.newLinkedHashMap();
            }
            Objects.requireNonNull(apiName);
            ObjectDataDocument detail = ObjectDataDocument.of(detailData);
            if (CollectionUtils.empty(detail)) {
                return;
            }
            String mark = (String) detail.get(ObjectDataExt.MARK_API_NAME);
            Map<String, Object> detailMap = data.computeIfAbsent(apiName, k -> Maps.newHashMap());
            // update: mark非空,   add: mark为null;
            if (StringUtils.isBlank(mark)) {
                // 根据业务数据结构，从对象返回的数据结构为:   "a":[]，这里强转是安全的
                ((List<Object>) detailMap.computeIfAbsent(NEW_OBJECTS, k -> Lists.newArrayList())).add(detail);
            } else {
                // 删除detail中"__mark__"
                detail.remove(ObjectDataExt.MARK_API_NAME);
                // 根据业务数据结构，从对象返回的数据结构为:   "u":{}
                ((Map<String, Object>) detailMap.computeIfAbsent(UPDATE_OR_DELETE, k -> Maps.newLinkedHashMap())).put(mark, detail);
            }
        }
    }

    @Data
    class DataInfo {
        ObjectDataDocument masterData;
        Map<String, List<ObjectDataDocument>> detailData = Maps.newHashMap();
    }
}
