package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.predef.handler.flowcompleted.FlowCompletedActionHandler;
import com.facishare.paas.appframework.core.predef.handler.flowstartcallback.FlowStartCallbackHandler;
import com.facishare.paas.appframework.metadata.handler.HandlerType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by zhouwr on 2023/10/12.
 */
@Service("flowStartCallbackActionServiceFacade")
public class FlowStartCallbackActionServiceFacadeImpl implements FlowStartCallbackActionServiceFacade {

    @Autowired
    private FlowCompletedActionServiceFacade flowCompletedActionServiceFacade;

    @Override
    public FlowStartCallbackHandler.Result executeTriggerActionTenantHandler(HandlerContext context,
                                                                             FlowStartCallbackHandler.Arg arg,
                                                                             List<HandlerType> handlerTypes) {
        if (CollectionUtils.empty(arg.getTriggerActionHandlerDescribes())) {
            return new FlowStartCallbackHandler.Result();
        }
        FlowCompletedActionHandler.Result flowCompletedResult = flowCompletedActionServiceFacade.executeTriggerActionTenantHandler(context,
                handlerTypes, arg.getTriggerActionHandlerDescribes(), true, arg.getInterfaceArg().triggerType(),
                arg.getCallbackData(), arg.getObjectApiName(), arg.getDescribeMap(), arg.data(), arg.detailDataMap(), arg.dbData());
        return FlowStartCallbackHandler.Result.builder()
                .data(flowCompletedResult.getData())
                .detailDataMap(flowCompletedResult.getDetailDataMap())
                .build();
    }

}
