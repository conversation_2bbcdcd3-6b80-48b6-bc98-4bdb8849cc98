package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface CreateDescribe {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private String json_data;
        @JSONField(name="M2")
        private String json_layout;
        @JSONField(name = "M3")
        private boolean include_layout;
        //非必填,默认是true
        @JSONField(name = "M4")
        private boolean active;
        @JSONField(name = "M5")
        private String json_list_layout;
    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        Object layout;

        @JSONField(name = "M2")
        Map objectDescribe;
    }
}
