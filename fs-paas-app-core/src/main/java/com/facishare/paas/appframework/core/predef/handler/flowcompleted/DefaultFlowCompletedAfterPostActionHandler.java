package com.facishare.paas.appframework.core.predef.handler.flowcompleted;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.facade.FlowCompletedActionServiceFacade;
import com.facishare.paas.appframework.metadata.handler.HandlerType;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2023/9/28.
 */
@Component
@HandlerProvider(name = "defaultFlowCompletedAfterPostActionHandler")
public class DefaultFlowCompletedAfterPostActionHandler extends AbstractFlowCompletedAfterHandler {

    @Autowired
    private FlowCompletedActionServiceFacade flowCompletedActionServiceFacade;

    @Override
    protected Result doHandle(HandlerContext context, Arg arg) {
        if(CollectionUtils.notEmpty(arg.getTriggerActionHandlerDescribes())) {
            return flowCompletedActionServiceFacade.executeTriggerActionTenantHandler(context,
                    arg, Lists.newArrayList(HandlerType.ACT, HandlerType.AFTER));
        }
        ObjectAction action = ObjectAction.of(arg.getInterfaceArg().approvalFlowTriggerType().getActionCode());
        if (action == ObjectAction.CREATE || action == ObjectAction.UPDATE || action == ObjectAction.INVALID) {
            flowCompletedActionServiceFacade.doPostAction(context.getRequestContext(), arg.getObjectApiName(), arg.data(),
                    arg.detailDataMap(), arg.getCallbackData(), action, Maps.newHashMap());
        }
        return new Result();
    }
}
