package com.facishare.paas.appframework.core.predef.handler.flowcompleted;

import com.facishare.paas.appframework.common.util.CacheContext;
import com.facishare.paas.appframework.common.util.ContextCacheKeys;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2023/9/26.
 */
@Component
@HandlerProvider(name = "defaultFlowCompletedFinallyHandler")
public class DefaultFlowCompletedFinallyHandler implements FlowCompletedActionHandler {

    @Autowired
    private RedissonService redissonService;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        RLock duplicateSearchLock = CacheContext.getContext().removeCache(ContextCacheKeys.DUPLICATE_SEARCH_LOCK);
        redissonService.unlock(duplicateSearchLock);
        return new Result();
    }
}
