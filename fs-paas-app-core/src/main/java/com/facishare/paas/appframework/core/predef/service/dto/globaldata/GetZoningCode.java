package com.facishare.paas.appframework.core.predef.service.dto.globaldata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.support.CountryAreaService.AreaInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public interface GetZoningCode {
    @Data
    class Arg {
        //地区类型：只支持省，市，区
        private String type;
        private String label;
    }

    @Data
    @Builder
    class Result {
        String code;
        String zoningCode;
        String label;
        String type;
        String parentCode;

        public static Result from(AreaInfo areaInfo) {
            ResultBuilder builder = Result.builder();
            if (Objects.isNull(areaInfo)) {
                return builder.build();
            }

            return builder.code(areaInfo.getCode())
                    .zoningCode(areaInfo.getZoningCode())
                    .label(areaInfo.getLabel())
                    .type(areaInfo.getType())
                    .parentCode(areaInfo.getParentCode())
                    .build();
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class AllResult {
        List<Result> list;

        public static AllResult from(List<AreaInfo> areaInfoList) {
            List<Result> collect = CollectionUtils.nullToEmpty(areaInfoList).stream().map(Result::from).collect(Collectors.toList());
            return AllResult.builder().list(collect).build();
        }
    }

}
