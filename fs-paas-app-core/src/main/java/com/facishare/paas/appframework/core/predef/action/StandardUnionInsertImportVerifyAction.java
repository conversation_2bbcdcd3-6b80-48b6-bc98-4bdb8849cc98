package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.appframework.metadata.DuplicatedSearchExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchOrderByType;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.ObjectReferenceManyFieldDescribe;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2019-04-08 09:58
 * 联合导入校验
 */
@Slf4j
public class StandardUnionInsertImportVerifyAction extends StandardInsertImportVerifyAction {

    /**
     * 获取导入有效字段
     */
    @Override
    public List<IFieldDescribe> getValidImportFields() {
        List<IFieldDescribe> fieldDescribeList = super.getValidImportFields();
        return customHandleFields(fieldDescribeList);
    }

    @Override
    protected void initImportReferenceMapping() {
        importReferenceMapping = infraServiceFacade.findImportReferenceMapping(actionContext.getUser(), getMasterApiName());
        importReferenceFieldMappingSwitch = BooleanUtils.isTrue(importReferenceMapping.getReferenceFieldMappingSwitch());
    }

    private String getMasterApiName() {
        if (isSupportFieldMapping()) {
            return arg.getMasterInfo().getApiName();
        }
        return arg.getUnionApiNameList().get(0);
    }

    @Override
    protected String validateDuplicatedSearchRule() {
        Map<String, List<IDuplicatedSearch>> duplicatedSearchMap;
        List<String> unionApiNameList = Lists.newArrayList();
        if (isSupportFieldMapping()) {
            String masterApiName = arg.getMasterInfo().getApiName();
            unionApiNameList.add(masterApiName);
            List<String> detailApiNames = arg.getDetailInfo().stream().map(x -> x.getApiName()).collect(Collectors.toList());
            unionApiNameList.addAll(detailApiNames);
        } else {
            unionApiNameList = arg.getUnionApiNameList();
        }
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleEi(actionContext.getTenantId())) {
            //查询新表中的查重规则
            duplicatedSearchMap = serviceFacade.findAllDuplicateSearchByApiNameAndType(actionContext.getTenantId(),
                    new HashSet<>(unionApiNameList), IDuplicatedSearch.Type.NEW, false, DuplicateSearchOrderByType.ORDER_BY_SORT);

            //查询黑名单的查重规则
            List<String> blackApiNames = CollectionUtils.nullToEmpty(unionApiNameList).stream()
                    .filter(x -> AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterBlackObj(x, actionContext.getTenantId()))
                    .collect(Collectors.toList());

            if (CollectionUtils.notEmpty(blackApiNames)) {
                Map<String, List<IDuplicatedSearch>> blackObjectDuplicatedRule = serviceFacade.findDuplicatedSearchListByApiNamesAndType(actionContext.getTenantId(),
                        blackApiNames, IDuplicatedSearch.Type.NEW, false);
                duplicatedSearchMap = Stream.concat(duplicatedSearchMap.entrySet().stream(), blackObjectDuplicatedRule.entrySet().stream())
                        .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (value1, value2) -> value1));
            }
        } else {
            //如果没灰度，就走老接口，并将返回值类型专程Map<String,List>类型
            duplicatedSearchMap = serviceFacade.findDuplicatedSearchListByApiNamesAndType(actionContext.getTenantId(),
                    unionApiNameList, IDuplicatedSearch.Type.NEW, false);
        }
        Map<String, String> resultMap = Maps.newHashMap();
        duplicatedSearchMap.forEach((apiName, duplicatedSearchList) -> {
            duplicatedSearchList.removeIf(x -> !DuplicatedSearchExt.isSupportImport(x));
            if (CollectionUtils.empty(duplicatedSearchList)) {
                return;
            }
            duplicatedSearchList.removeIf(x -> !DuplicatedSearchExt.isEffective(x));
            if (CollectionUtils.empty(duplicatedSearchList)) {
                resultMap.put(apiName, I18NExt.getOrDefault(I18NKey.DUPLICATED_SEARCH_RULE_UN_EFFECTIVE, "查重规则正在生效中，请稍后重试"));// ignoreI18n
            }
        });
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(actionContext.getTenantId(), resultMap.keySet());
        return resultMap.entrySet().stream()
                .map(entry -> {
                    IObjectDescribe describe = describeMap.get(entry.getKey());
                    return describe.getDisplayName() + entry.getValue();
                })
                .collect(Collectors.joining(","));
    }

    /**
     * 根据业务需求处理字段
     *
     * @return 有效字段
     */
    protected List<IFieldDescribe> customHandleFields(List<IFieldDescribe> fieldDescribeList) {
        if (isSupportFieldMapping()) {
            supportRelatedMark(fieldDescribeList);
            if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
                //如果是导入从对象，补充"主对象ID"字段
                supportUniqueID(fieldDescribeList);
                //从对象，去除"主从关系"字段或者指定的关联关系字段
                customDetailRemoveField(fieldDescribeList);
            } else {
                // 联合导入主对象过滤掉相关团队字段
                fieldDescribeList.removeIf(field -> {
                    Map map = field.getExtendInfo();
                    return Objects.nonNull(map) && Objects.equals(map.get(ImportExportExt.IMPORT_TYPE), ImportExportExt.TEAM_MEMBER_MARK);
                });
            }
            return fieldDescribeList;
        }
        if (CollectionUtils.notEmpty(arg.getUnionApiNameList())) {
            String apiName = arg.getApiName();
            if (arg.getUnionApiNameList().contains(apiName)) {
                //补充联合导入的"关联标识字段"
                supportRelatedMark(fieldDescribeList);
                if (!apiName.equals(arg.getUnionApiNameList().get(0))) {
                    //如果是导入从对象，补充"主对象ID"字段
                    supportUniqueID(fieldDescribeList);
                    //从对象，去除"主从关系"字段或者指定的关联关系字段
                    customDetailRemoveField(fieldDescribeList);
                }
                // 联合导入主对象过滤掉相关团队字段
                fieldDescribeList.removeIf(f -> {
                    Map map = f.getExtendInfo();
                    return Objects.nonNull(map) && Objects.equals(map.get(ImportExportExt.IMPORT_TYPE), ImportExportExt.TEAM_MEMBER_MARK);
                });

            }
        }
        return fieldDescribeList;
    }

    protected void supportRelatedMark(List<IFieldDescribe> fieldDescribeList) {
        ImportExportExt.supportRelatedMark(fieldDescribeList);
    }

    protected void supportUniqueID(List<IFieldDescribe> fieldDescribeList) {
        ImportExportExt.supportUniqueID(fieldDescribeList);
    }

    /**
     * 去掉从对象指定字段，各业务可重写
     */
    protected void customDetailRemoveField(List<IFieldDescribe> fieldDescribeList) {
        ListIterator<IFieldDescribe> lit = fieldDescribeList.listIterator();
        while (lit.hasNext()) {
            IFieldDescribe field = lit.next();
            if (IFieldType.MASTER_DETAIL.equals(field.getType())) {
                //"主从关系"字段至多有一个
                lit.remove();
            } else {
                String masterApiName;
                if (isSupportFieldMapping()) {
                    masterApiName = arg.getMasterInfo().getApiName();
                } else {
                    masterApiName = arg.getUnionApiNameList().get(0);
                }
                if (IFieldType.OBJECT_REFERENCE.equals(field.getType())) {
                    // 关联关系
                    ObjectReferenceFieldDescribe f = (ObjectReferenceFieldDescribe) field;
                    if (f.getTargetApiName().equals(masterApiName)) {    //主、从分别调用verify接口
                        lit.remove();
                    }
                } else if (IFieldType.OBJECT_REFERENCE_MANY.equals(field.getType())) {
                    // 关联关系
                    ObjectReferenceManyFieldDescribe f = (ObjectReferenceManyFieldDescribe) field;
                    if (f.getTargetApiName().equals(masterApiName)) {    //主、从分别调用verify接口
                        lit.remove();
                    }
                }
            }
        }
    }

    @Override
    protected String validateUniqueRule(List<IFieldDescribe> validFieldList, Set<Map.Entry<String, Object>> entrySet) {
        return super.validateUniqueRule(validFieldList, entrySet);
    }

    @Override
    protected List<String> getNotValidFieldsByRuleField(List<IFieldDescribe> validFieldList, List<String> ruleFieldName) {
        // 不存在关联字段，直接调用super方法
        Optional<? extends IFieldDescribe> unionField = getUnionField();
        if (!unionField.isPresent()) {
            return super.getNotValidFieldsByRuleField(validFieldList, ruleFieldName);
        }

        // 过滤掉唯一性规则中的关联字段
        List<String> ruleField = ruleFieldName.stream()
                .filter(fieldName -> !unionField.get().getApiName().equals(fieldName))
                .collect(Collectors.toList());
        return super.getNotValidFieldsByRuleField(validFieldList, ruleField);
    }

    @Override
    protected List<IFieldDescribe> getValidImportFieldList(List<IFieldDescribe> validFieldList, List<String> ruleFieldName) {
        // 不存在关联字段，直接调用super方法
        Optional<? extends IFieldDescribe> unionField = getUnionField();
        if (!unionField.isPresent()) {
            return super.getValidImportFieldList(validFieldList, ruleFieldName);
        }
        List<String> ruleField = ruleFieldName.stream()
                .filter(fieldName -> !unionField.get().getApiName().equals(fieldName))
                .collect(Collectors.toList());
        return super.getValidImportFieldList(validFieldList, ruleField);
    }

    private Optional<? extends IFieldDescribe> getUnionField() {
        String targetApiName;
        String sourceApiName;
        if (isSupportFieldMapping()) {
            if (CollectionUtils.empty(arg.getDetailInfo())) {
                return Optional.empty();
            }
            targetApiName = arg.getMasterInfo().getApiName();
            sourceApiName = arg.getDetailInfo().get(0).getApiName();
        } else {
            if (CollectionUtils.empty(arg.getUnionApiNameList()) || arg.getUnionApiNameList().size() < 2) {
                return Optional.empty();
            }
            targetApiName = arg.getUnionApiNameList().get(0);
            if (arg.getApiName().equals(targetApiName)) {
                return Optional.empty();
            }
            sourceApiName = arg.getUnionApiNameList().get(1);
        }
        IObjectDescribe sourceDescribe = serviceFacade.findObject(actionContext.getTenantId(), sourceApiName);
        Optional<MasterDetailFieldDescribe> masterDetailFieldDescribe = ObjectDescribeExt.of(sourceDescribe).getMasterDetailFieldDescribe();
        if (masterDetailFieldDescribe.isPresent()) {
            return masterDetailFieldDescribe;
        }
        return ObjectDescribeExt.of(sourceDescribe).getSystemReferenceFieldDescribesByTargetApiName(targetApiName);
    }
}
