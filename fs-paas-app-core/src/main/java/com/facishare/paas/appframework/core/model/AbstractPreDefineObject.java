package com.facishare.paas.appframework.core.model;

/**
 * 预定义对象
 *
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/6/20.
 */
public abstract class AbstractPreDefineObject implements PreDefineObject {
    String apiName;
    String packageName;

    public void setApiName(String apiName) {
        this.apiName = apiName;
    }

    public void setPackageName(String packageName) {
        this.packageName = packageName;
    }

    @Override
    public String getApiName() {
        return apiName;
    }

    @Override
    public String getPackageName() {
        return packageName;
    }

    @Override
    public ActionClassInfo getDefaultActionClassInfo(String actionCode) {
        String actionClassName = packageName + ".action." + apiName + actionCode + "Action";
        ActionClassInfo classInfo = new ActionClassInfo(actionClassName);
        return classInfo;
    }

    @Override
    public ControllerClassInfo getControllerClassInfo(String methodName) {
        String actionClassName = packageName + ".controller." + apiName + methodName + "Controller";
        ControllerClassInfo classInfo = new ControllerClassInfo(actionClassName);
        return classInfo;
    }
}
