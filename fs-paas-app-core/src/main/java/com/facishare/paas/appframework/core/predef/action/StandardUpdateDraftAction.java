package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.metadata.api.IObjectDataDraft;

/**
 * 更新草稿
 *
 * <AUTHOR>
 * @date 2019/11/11 2:50 下午
 */
public class StandardUpdateDraftAction extends BaseSaveDraftAction {
    @Override
    protected IObjectDataDraft save(IObjectDataDraft draft) {
        return infraServiceFacade.updateDraft(draft, actionContext.getUser());
    }
}
