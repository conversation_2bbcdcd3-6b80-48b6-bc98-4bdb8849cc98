package com.facishare.paas.appframework.core.predef.service.dto.convertRule;

import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by zhouwr on 2023/2/23.
 */
public interface FindConvertRuleList {
    @Data
    class Arg {
        private String ruleName;
        private String searchQueryInfo;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private List<MappingRuleDocument> ruleList;
    }
}
