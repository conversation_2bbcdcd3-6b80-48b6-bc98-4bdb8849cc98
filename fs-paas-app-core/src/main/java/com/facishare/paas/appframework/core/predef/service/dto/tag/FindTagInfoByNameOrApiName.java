package com.facishare.paas.appframework.core.predef.service.dto.tag;

import com.facishare.paas.appframework.core.model.TagDocument;
import com.facishare.paas.appframework.core.model.TagGroupDocument;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface FindTagInfoByNameOrApiName {


    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        TagQuery.QueryInfo queryInfo;
        String tagGroupId;
        String type;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Result {
        List<TagGroupDocument> tagDescribeList;
        List<TagDocument> subTagDescribeList;
    }
}
