package com.facishare.paas.appframework.core.predef.service.dto.license;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @date 2019/1/9 7:53 PM
 */
public interface ApplicationLicense {
    @Data
    class Arg {
        @JSONField(name = "items")
        @JsonProperty(value = "items")
        private Set<String> items;
        @JSONField(name = "describeApiName")
        @JsonProperty(value = "describeApiName")
        private String describeApiName;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "quota")
        @JsonProperty(value = "quota")
        private Map<String, Map<String, Integer>> quota;
    }
}
