package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.privilege.dto.Receive;
import com.facishare.paas.appframework.privilege.dto.Rule;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface AddFieldShare {
    @Data
    class Arg {
        @JSONField(name = "M1")
        List<Rule> rules;

        @JSONField(name = "M5")
        List<Receive> receives;

        @JSONField(name = "M2")
        String describeApiName;

        @JSONField(name = "M3")
        String ruleName;

        @JSONField(name = "M4")
        String ruleParse;

    }
    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        boolean success;
        @JSONField(name = "M2")
        String ruleId;
    }
}
