package com.facishare.paas.appframework.core.model;

/**
 * 预定义对象信息
 * <p>
 * Created by liyiguang on 2017/6/20.
 */
public interface PreDefineObject {

    default String getAppId() {
        return null;
    }

    String getApiName();

    String getPackageName();

    ActionClassInfo getDefaultActionClassInfo(String actionCode);

    ControllerClassInfo getControllerClassInfo(String methodName);
}
