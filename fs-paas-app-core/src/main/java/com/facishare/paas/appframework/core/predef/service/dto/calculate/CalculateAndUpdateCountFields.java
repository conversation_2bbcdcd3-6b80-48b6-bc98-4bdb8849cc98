package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by zhouwr on 2018/3/12
 */
public interface CalculateAndUpdateCountFields {
    @Data
    class Arg {
        private String objectApiName;
        private List<String> countFieldApiNames;
        private List<String> masterDataIds;
    }

    @Data
    @Builder
    class Result {
        private boolean success;
    }
}
