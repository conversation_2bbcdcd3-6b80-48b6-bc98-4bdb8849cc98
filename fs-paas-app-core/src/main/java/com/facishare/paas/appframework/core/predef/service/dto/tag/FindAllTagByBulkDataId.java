package com.facishare.paas.appframework.core.predef.service.dto.tag;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.DataAndSubTag;
import com.facishare.paas.metadata.api.describe.ISubTagDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.toList;

public interface FindAllTagByBulkDataId {
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        @JsonProperty("describe_api_name")
        @JSONField(name = "describe_api_name")
        String describeApiName;

        @JsonProperty("data_ids")
        @JSONField(name = "data_ids")
        Set<String> dataIds;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        List<FindAllTagByBulkDataId.DataTag> list;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class DataTag {
        String dataId;
        List<TagDetail> tags;

        public static List<DataTag> fromDataAndSubTagList(List<DataAndSubTag> list) {
            List<FindAllTagByBulkDataId.DataTag> resultList = CollectionUtils.nullToEmpty(list).stream().map(a -> {
                return FindAllTagByBulkDataId.DataTag.builder()
                        .dataId(a.getDataId())
                        .tags(FindAllTagByBulkDataId.TagDetail.from(a.getResultList()))
                        .build();
            }).collect(toList());
            return resultList;
        }

    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class TagDetail {
        @JsonProperty("tag_name")
        @JSONField(name = "tag_name")
        String tagName;

        @JsonProperty("tag_id")
        @JSONField(name = "tag_id")
        String tagId;

        @JsonProperty("tag_active")
        @JSONField(name = "tag_active")
        Boolean tagActive;

        public static List<TagDetail> from(List<ISubTagDescribe> list) {
            return CollectionUtils.nullToEmpty(list).stream()
                    .map(t -> new TagDetail(t.getName(), t.getId(), t.getIsActive() && t.getTagDescribeIsActive()))
                    .collect(Collectors.toList());
        }

    }


}
