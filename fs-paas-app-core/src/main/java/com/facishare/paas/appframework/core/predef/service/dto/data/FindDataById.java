package com.facishare.paas.appframework.core.predef.service.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.Builder;
import lombok.Data;

public interface FindDataById {
    @Data
    class Arg {

        /**
         * 当前对象ApiName
         */
        @JSONField(name = "M1")
        private String describeApiName;

        /**
         * 当前对象数据Id
         */
        @JSONField(name = "M2")
        private String dataId;
    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        private ObjectDataDocument objectData;

    }
}
