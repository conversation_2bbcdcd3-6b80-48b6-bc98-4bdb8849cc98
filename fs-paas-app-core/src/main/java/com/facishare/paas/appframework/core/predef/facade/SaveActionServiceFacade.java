package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.handler.SaveActionHandler;
import com.facishare.paas.appframework.core.predef.handler.edit.EditActionHandler;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.dto.ConvertRuleDataContainer;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchResult;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;

/**
 * Created by zhouwr on 2023/1/28.
 */
public interface SaveActionServiceFacade {
    List<IObjectData> getRelateDataForCreateWorkFlow(User user, IObjectDescribe objectDescribe, IObjectData objectData);

    void updateDetailObjectDataLifeStatus(HandlerContext context, SaveActionHandler.Arg arg, ObjectLifeStatus lastLifeStatus,
                                          boolean triggerApprovalFlowSuccess);

    void batchValidateEditDataConstraintByFields(User user, EditActionHandler.Arg arg);

    void buildDuplicateImportAddError(User user, List<DuplicateSearchResult.DuplicateData> duplicateDataList, IObjectDescribe objectDescribe, boolean isFromImport);

    void checkIfUpdateKeyLocked(String seriesId, String dataId, String objectApiName, User user);

    void tryLockWithUpdateKey(BaseObjectSaveAction.Arg arg, String objectApiName, User user);

    ConvertRuleDataContainer buildConvertRuleDataContainer(User user, BaseObjectSaveAction.OptionInfo optionInfo);
}
