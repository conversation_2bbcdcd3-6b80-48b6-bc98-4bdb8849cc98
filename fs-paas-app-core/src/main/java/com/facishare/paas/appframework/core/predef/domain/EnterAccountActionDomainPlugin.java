package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.domain.ActionDomainPlugin;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.rest.BaseAPIResult;
import lombok.*;
import lombok.experimental.SuperBuilder;

public interface EnterAccountActionDomainPlugin extends ActionDomainPlugin<EnterAccountActionDomainPlugin.Arg, EnterAccountActionDomainPlugin.Result> {

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class Arg extends DomainPlugin.Arg {
        private ObjectDataDocument dbObjectData;
        private ObjectDataDocument argData;
        private ObjectDataDocument fundAccountData;
        private String customerFieldName;
        private String enterAccountAmountFieldName;
    }

    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class Result extends DomainPlugin.Result {

    }

    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class RestResult extends BaseAPIResult {
        private Result data;
    }
}
