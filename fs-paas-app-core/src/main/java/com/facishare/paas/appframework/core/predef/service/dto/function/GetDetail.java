package com.facishare.paas.appframework.core.predef.service.dto.function;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface GetDetail {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String apiName;
        String bindingObjectApiName;
    }

    @Data
    @Builder
    class Result  {
        String detail;
    }
}
