package com.facishare.paas.appframework.core.predef.service.dto.validateRule;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutRuleDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON>in on 17/10/17.
 */
public interface CacheValidate {
    @Data
    class Arg {
        @JSONField(name = "layout_last_modified_time")
        @JsonProperty(value = "layout_last_modified_time")
        Long layoutLastModifiedTime;

        @JSONField(name = "layout_version")
        @JsonProperty(value = "layout_version")
        Integer layoutVersion;

        @JSONField(name = "layout_api_name")
        @JsonProperty(value = "layout_api_name")
        String layoutApiName;

        @JSONField(name = "layout_type")
        @JsonProperty(value = "layout_type")
        String layoutType;

        @JSONField(name = "layout_rule")
        @JsonProperty(value = "layout_rule")
        List<LayoutRuleDocument> layoutRule;

        String objectApiName;

        Long describeVersion;

        String recordType;

        List<DetailObjCacheValid> detailCacheValid;
    }

    @Data
    class DetailObjCacheValid {
        String objectApiName;
        Long describeVersion;
        List<DetailLayoutRelatedInfo> detailLayoutList;

    }

    @Data
    class DetailLayoutRelatedInfo {
        @JSONField(name = "layout_last_modified_time")
        @JsonProperty(value = "layout_last_modified_time")
        Long layoutLastModifiedTime;

        @JSONField(name = "layout_api_name")
        @JsonProperty(value = "layout_api_name")
        String layoutApiName;

        @JSONField(name = "layout_rule")
        @JsonProperty(value = "layout_rule")
        List<LayoutRuleDocument> layoutRule;

        String recordType;

    }



    @Data
    @Builder
    class Result {
        private String message;
        private boolean success;
    }
}
