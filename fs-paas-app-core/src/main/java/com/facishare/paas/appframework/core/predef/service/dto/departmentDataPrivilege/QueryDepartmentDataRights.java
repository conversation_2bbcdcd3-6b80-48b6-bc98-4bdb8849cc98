package com.facishare.paas.appframework.core.predef.service.dto.departmentDataPrivilege;

import com.facishare.paas.appframework.privilege.dto.DepartmentRightsResult;
import com.facishare.paas.appframework.privilege.dto.PageInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface QueryDepartmentDataRights {
    @Data
    class Arg {
        List<String> entityIds;
        List<String> departmentIds;
        int scene;
        int page;
        int size;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        PageInfo pageInfo;
        List<DepartmentRightsResult> result;
    }

}
