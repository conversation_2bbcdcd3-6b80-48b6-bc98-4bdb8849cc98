package com.facishare.paas.appframework.core.util;



import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.poi.ss.formula.functions.T;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

public class SelectOptionUtil {

    /**
     * 清理不可用选项值
     * @param value 需要清理的值
     * @param optionUsableMap 选项值是否可用的Map
     * @return 清理后的结果
     */
    public static Object clearUpOptions(Object value, Map<String, Boolean> optionUsableMap) {
        if (Objects.isNull(value) || MapUtils.isEmpty(optionUsableMap)) {
            return null;
        }
        if (value instanceof String) {
            String stringValue = (String) value;
            return BooleanUtils.isTrue(optionUsableMap.get(stringValue)) ? value : null;
        }
        if (value instanceof List) {
            @SuppressWarnings("unchecked")
            List<Object> listValue = (List) value;
            if (listValue.isEmpty()) {
                return null;
            }
            return listValue.stream()
                    .filter(ov -> BooleanUtils.isTrue(optionUsableMap.get(String.valueOf(ov))))
                    .collect(Collectors.toList());
        }
        return value;
    }
}
