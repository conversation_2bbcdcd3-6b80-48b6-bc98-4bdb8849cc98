package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface FindLayoutByRecordTypeAndRole {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private String describeApiName;
        @JSONField(name = "M2")
        private String role;
        @J<PERSON>NField(name = "M3")
        private String recordType;
        private String layoutType;
        private String appId;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        @JSONField(name = "M1")
        private LayoutDocument layout;
    }
}
