package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.predef.handler.edit.EditActionHandler;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.log.dto.ConvertSourceContainer;
import com.facishare.paas.appframework.metadata.ConvertRuleLogicService;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.dto.ConvertRuleDataContainer;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/7/12.
 */
@Service("auditLogServiceFacade")
public class AuditLogServiceFacadeImpl implements AuditLogServiceFacade {

    @Autowired
    private LogService logService;

    @Override
    public void recordLifeStatusModifyLog(String tenantId, IObjectData objectData, IObjectDescribe objectDescribe,
                                          ObjectLifeStatus lastLifeStatus) {
        if (Objects.equals(lastLifeStatus, ObjectDataExt.of(objectData).getLifeStatus())) {
            return;
        }
        Map<String, Object> updateField = Maps.newHashMap();
        updateField.put(ObjectLifeStatus.LIFE_STATUS_API_NAME, ObjectDataExt.of(objectData).getLifeStatus().getCode());
        Map<String, Object> dbData = Maps.newHashMap();
        dbData.put(ObjectLifeStatus.LIFE_STATUS_API_NAME, lastLifeStatus.getCode());
        dbData.put(ObjectDataExt.ID, objectData.getId());
        logService.log(User.systemUser(tenantId), EventType.MODIFY, ActionType.Modify,
                objectDescribe, objectData, updateField, ObjectDataExt.of(dbData).getObjectData());
    }

    @Override
    public void recordEditLog(User user, EditActionHandler.Arg arg, Map<String, IObjectDescribe> describeMap, Map<String, Object> extendsInfo) {
        Map<String, List<IObjectData>> cpDetailsToUpdate = ObjectDataExt.groupByDescribeApiName(arg.detailsToUpdate());
        Map<String, List<IObjectData>> cpDetailsToDelete = ObjectDataExt.groupByDescribeApiName(arg.detailsToDelete());
        Map<String, List<IObjectData>> cpDetailsToAdd = ObjectDataExt.groupByDescribeApiName(arg.detailsToAdd());
        recordEditLog(user, arg.objectData(), arg.dbMasterData(), arg.getDataDiffResult().getUpdatedFieldMap(),
                arg.dbDetailDataMap(), cpDetailsToAdd, cpDetailsToUpdate, cpDetailsToDelete, Collections.emptyMap(),
                arg.getDataDiffResult().getDetailChangeMap(), describeMap, extendsInfo, arg.getConvertRuleDataContainer());
    }

    @Override
    public void recordEditLog(User user,
                              IObjectData masterData,
                              IObjectData dbMasterData,
                              Map<String, Object> masterUpdateFieldMap,
                              Map<String, List<IObjectData>> dbDetailDataMap,
                              Map<String, List<IObjectData>> detailToAdd,
                              Map<String, List<IObjectData>> detailToUpdate,
                              Map<String, List<IObjectData>> detailToDelete,
                              Map<String, List<IObjectData>> detailToInvalid,
                              Map<String, Map<String, Object>> detailChangeMap,
                              Map<String, IObjectDescribe> describeMap,
                              Map<String, Object> extendsInfo) {
        recordEditLog(user, masterData, dbMasterData, masterUpdateFieldMap, dbDetailDataMap, detailToAdd, detailToUpdate, detailToDelete, detailToInvalid, detailChangeMap, describeMap, extendsInfo, null);
    }

    @Override
    public void recordEditLog(User user, IObjectData masterData, IObjectData dbMasterData, Map<String, Object> masterUpdateFieldMap, Map<String, List<IObjectData>> dbDetailDataMap, Map<String, List<IObjectData>> detailToAdd, Map<String, List<IObjectData>> detailToUpdate, Map<String, List<IObjectData>> detailToDelete, Map<String, List<IObjectData>> detailToInvalid, Map<String, Map<String, Object>> detailChangeMap, Map<String, IObjectDescribe> describeMap, Map<String, Object> extendsInfo, ConvertRuleDataContainer convertRuleDataContainer) {
        //使用拷贝的数据记日志，防止ConcurrentModificationException
        IObjectData cpObjectData = ObjectDataExt.of(masterData).copy();
        Map<String, List<IObjectData>> cpDetailsToUpdate = ObjectDataExt.copyMap(detailToUpdate);
        Map<String, List<IObjectData>> cpDetailsToDelete = ObjectDataExt.copyMap(detailToDelete);
        Map<String, List<IObjectData>> cpDetailsToInvalid = ObjectDataExt.copyMap(detailToInvalid);
        Map<String, List<IObjectData>> cpDetailsToAdd = ObjectDataExt.copyMap(detailToAdd);

        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            ConvertSourceContainer convertSourceContainer = ConvertRuleLogicService.toConvertSourceContainer(convertRuleDataContainer);
            String masterLogId = masterModifyLog(user, cpObjectData, dbMasterData, masterUpdateFieldMap, detailChangeMap,
                    describeMap, extendsInfo, convertSourceContainer);
            cpDetailsToUpdate.forEach((objectApiName, dataList) ->
                    detailModifyLog(user, masterLogId, dataList, dbDetailDataMap.get(objectApiName), detailChangeMap,
                            describeMap.get(objectApiName)));
            cpDetailsToDelete.forEach((objectApiName, dataList) ->
                    detailDeleteAuditLog(user, masterLogId, dataList, describeMap.get(objectApiName)));
            cpDetailsToInvalid.forEach((objectApiName, dataList) ->
                    detailInvalidAuditLog(user, masterLogId, dataList, describeMap.get(objectApiName)));
            cpDetailsToAdd.forEach((objectApiName, dataList) ->
                    detailAddAuditLog(user, masterLogId, dataList, describeMap.get(objectApiName),
                            convertSourceContainer));
        });
        parallelTask.run();
    }

    private String masterModifyLog(User user, IObjectData objectData, IObjectData dbData, Map<String, Object> updateFieldMap, Map detailChange,
                                   Map<String, IObjectDescribe> describeMap, Map<String, Object> extendsInfo, ConvertSourceContainer convertSourceContainer) {
        LogService.MasterLogInfo masterLog = logService.fillMasterModifyLog(user, describeMap,
                objectData, getUpdatedFieldMapForLog(updateFieldMap, objectData), dbData, detailChange, extendsInfo, convertSourceContainer);
        logService.sendLog(masterLog.getLogList());
        return masterLog.getMasterLogId();
    }

    private Map<String, Map<String, Map<String, Object>>> getUpdatedFieldMapForLog(Map<String, Object> updateFieldMap, IObjectData objectData) {
        Map<String, Map<String, Map<String, Object>>> allUpdatedFieldMap = Maps.newHashMap();
        // 获取主对象的更新字段
        Map<String, Map<String, Object>> masterUpdateMap = Maps.newHashMap();
        masterUpdateMap.put(objectData.getId(), updateFieldMap);
        allUpdatedFieldMap.put(objectData.getDescribeApiName(), masterUpdateMap);
        return allUpdatedFieldMap;
    }

    private void detailAddAuditLog(User user, String masterLogId, List<IObjectData> dataList, IObjectDescribe describe, ConvertSourceContainer convertSourceContainer) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(describe.getApiName(), describe);
        logService.log(user, EventType.ADD, ActionType.Add, describeMap, dataList, masterLogId, convertSourceContainer);
    }

    private void detailDeleteAuditLog(User user, String masterLogId, List<IObjectData> dataList, IObjectDescribe describe) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(describe.getApiName(), describe);
        logService.log(user, EventType.DELETE, ActionType.Delete, describeMap, dataList, masterLogId);
    }

    private void detailInvalidAuditLog(User user, String masterLogId, List<IObjectData> dataList, IObjectDescribe describe) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(describe.getApiName(), describe);
        logService.log(user, EventType.MODIFY, ActionType.Invalid, describeMap, dataList, masterLogId);
    }

    private void detailModifyLog(User user, String masterLogId, List<IObjectData> dataList, List<IObjectData> dbDataList,
                                 Map<String, Map<String, Object>> detailChange, IObjectDescribe describe) {
        logService.detailModifyLog(user, describe, dataList, getDetailUpdateFieldMap(detailChange, describe.getApiName()),
                dbDataList, masterLogId);
    }

    private Map<String, Map<String, Object>> getDetailUpdateFieldMap(Map<String, Map<String, Object>> detailChange, String objectApiName) {
        if (CollectionUtils.empty(detailChange.get(objectApiName))) {
            return Maps.newHashMap();
        }
        return (Map<String, Map<String, Object>>) detailChange.get(objectApiName)
                .get(ObjectAction.UPDATE.getActionCode());
    }

    @Override
    public void recordInvalidLog(User user, List<IObjectData> masterDataList, Map<String, List<IObjectData>> detailDataMap,
                                 Map<String, IObjectDescribe> describeMap) {
        if (CollectionUtils.empty(masterDataList)) {
            return;
        }
        //使用拷贝的数据记日志，防止ConcurrentModificationException
        List<IObjectData> cpMasterDataList = ObjectDataExt.copyList(masterDataList);
        ParallelUtils.createBackgroundTask().submit(() -> {
            if (CollectionUtils.empty(detailDataMap)) {
                logService.log(user, EventType.MODIFY, ActionType.Invalid, describeMap, cpMasterDataList);
            } else {
                Map<String, List<IObjectData>> cpDetailDataMap = ObjectDataExt.copyMap(detailDataMap);
                cpMasterDataList.forEach(masterData -> {
                    List<IObjectData> dataListForLog = Lists.newArrayList(masterData);
                    cpDetailDataMap.forEach((detailApiName, detailDataList) -> {
                        String masterDetailField = ObjectDescribeExt.of(describeMap.get(detailApiName))
                                .getMasterDetailField().get().getApiName();
                        dataListForLog.addAll(detailDataList.stream()
                                .filter(x -> Objects.equals(masterData.getId(), x.get(masterDetailField)))
                                .collect(Collectors.toList()));
                    });
                    logService.masterDetailLog(user, EventType.MODIFY, ActionType.Invalid, describeMap, dataListForLog);
                });
            }
        }).run();
    }

}
