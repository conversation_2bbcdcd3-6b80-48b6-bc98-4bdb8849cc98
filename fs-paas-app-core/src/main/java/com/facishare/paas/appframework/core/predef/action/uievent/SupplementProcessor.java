package com.facishare.paas.appframework.core.predef.action.uievent;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.UIEventBusinessException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

import static java.util.stream.Collectors.toMap;

/**
 * 补充关联字段，引用字段的__r等特殊标识
 *
 * <AUTHOR>
 * @date 2019-09-18 17:30
 */
@Slf4j
public class SupplementProcessor extends AbstractProcessor {
    /**
     * 主从对象的关联字段
     */
    private List<String> masterReference = Lists.newArrayList();
    private Map<String, List<String>> detailReference = Maps.newHashMap();
    /**
     * 主从对象的引用字段
     */
    private List<String> masterQuote = Lists.newArrayList();
    private Map<String, String> quoteReferenceMap = Maps.newHashMap();
    private Map<String, List<String>> detailQuote = Maps.newHashMap();
    private Map<String, Map<String, String>> detailQuoteReferenceMap = Maps.newHashMap();
    /**
     * 主从对象的人员字段
     */
    private List<String> masterEmployee = Lists.newArrayList();
    private Map<String, List<String>> detailEmployee = Maps.newHashMap();
    /**
     * 主从对象的部门字段
     */
    private List<String> masterDepartment = Lists.newArrayList();
    private Map<String, List<String>> detailDepartment = Maps.newHashMap();
    /** 保存从对象常量默认值 */

    /**
     * 主从对象的地区定位字段
     */
    private List<String> masterArea = Lists.newArrayList();
    private Map<String, List<String>> detailArea = Maps.newHashMap();
    private Map<String, Map<String, Object>> detailDefaultValues = Maps.newHashMap();
    /**
     * 主对象的维度字段
     */
    private List<String> masterDimension = Lists.newArrayList();

    public SupplementProcessor(ActionContainer container) {
        super(container);
        IObjectDescribe objectDescribe = container.getObjectDescribe();
        List<IFieldDescribe> masterFields = ObjectDescribeExt.of(objectDescribe).getActiveFieldDescribes();
        Map<String, IObjectDescribe> detailDescribe = container.getDetailDescribe();
        Map<String, List<IFieldDescribe>> details = detailDescribe.values().stream()
                .collect(toMap(IObjectDescribe::getApiName,
                        describe -> ObjectDescribeExt.of(describe).getActiveFieldDescribes()));
        // 主对象
        for (IFieldDescribe field : masterFields) {
            final String type = field.getType();
            if (IFieldType.OBJECT_REFERENCE.equals(type) || IFieldType.OBJECT_REFERENCE_MANY.equals(type)) {
                masterReference.add(field.getApiName());
            } else if (IFieldType.MASTER_DETAIL.equals(type)
                    && AppFrameworkConfig.uiEventSupportModifyMasterDetailFieldGray(objectDescribe.getTenantId(), objectDescribe.getApiName())) {
                masterReference.add(field.getApiName());
            } else if (IFieldType.QUOTE.equals(type)) {
                masterQuote.add(field.getApiName());
                buildQuoteReferenceMap(field, quoteReferenceMap);
            } else if (FieldDescribeExt.FILL_DEPARTMENT_EXT_TYPES.contains(type)) {
                masterDepartment.add(field.getApiName());
            } else if (FieldDescribeExt.FILL_EMPLOYEE_EXT_TYPES.contains(type)) {
                masterEmployee.add(field.getApiName());
            } else if (StringUtils.equalsAny(type, IFieldType.COUNTRY, IFieldType.PROVINCE,
                    IFieldType.DISTRICT, IFieldType.CITY, IFieldType.TOWN, IFieldType.VILLAGE)) {
                masterArea.add(field.getApiName());
            } else if (IFieldType.DIMENSION.equals(type)) {
                masterDimension.add(field.getApiName());
            }
        }
        // 从对象
        details.forEach((apiName, fields) -> {
            Map<String, Object> map = Maps.newHashMap();
            Map<String, String> detailQuoteRefMap = Maps.newHashMap();
            for (IFieldDescribe field : fields) {
                final String type = field.getType();
                // 默认值
                Object defaultValue = field.getDefaultValue();

                if (!IFieldType.AUTO_NUMBER.equals(type) && !ObjectDataExt.isValueEmpty(defaultValue)
                        && !field.getDefaultIsExpression()) {
                    map.put(field.getApiName(), defaultValue);
                }
                if (IFieldType.OBJECT_REFERENCE.equals(type) || IFieldType.OBJECT_REFERENCE_MANY.equals(type)) {
                    detailReference.merge(apiName, Lists.newArrayList(field.getApiName()), (old, value) -> {
                        old.addAll(value);
                        return old;
                    });
                } else if (IFieldType.QUOTE.equals(type)) {
                    detailQuote.merge(apiName, Lists.newArrayList(field.getApiName()), (old, value) -> {
                        old.addAll(value);
                        return old;
                    });
                    buildQuoteReferenceMap(field, detailQuoteRefMap);
                } else if (FieldDescribeExt.FILL_DEPARTMENT_EXT_TYPES.contains(type)) {
                    detailDepartment.merge(apiName, Lists.newArrayList(field.getApiName()), (old, value) -> {
                        old.addAll(value);
                        return old;
                    });
                } else if (FieldDescribeExt.FILL_EMPLOYEE_EXT_TYPES.contains(type)) {
                    detailEmployee.merge(apiName, Lists.newArrayList(field.getApiName()), (old, value) -> {
                        old.addAll(value);
                        return old;
                    });
                } else if (IFieldType.TOWN.equals(type)) {
                    detailArea.merge(apiName, Lists.newArrayList(field.getApiName()), (old, value) -> {
                        old.addAll(value);
                        return old;
                    });
                }
            }
            if (CollectionUtils.notEmpty(map)) {
                detailDefaultValues.put(apiName, map);
            }
            if (CollectionUtils.notEmpty(detailQuoteRefMap)) {
                detailQuoteReferenceMap.put(apiName, detailQuoteRefMap);
            }
        });
    }

    private void buildQuoteReferenceMap(IFieldDescribe field, Map<String, String> quoteReferenceMap) {
        Optional.ofNullable(field.getQuoteField()).ifPresent(quota -> {
            String[] split = quota.split("__r");
            if (ArrayUtils.isNotEmpty(split)) {
                quoteReferenceMap.put(field.getApiName(), split[0]);
            }
        });
    }

    @Override
    public void invoke(UIEventProcess.ProcessRequest request, ProcessorContext context) {
        StopWatch stopWatch = StopWatch.create("SupplementProcessor.invoke");
        // 过滤需要补充的数据
        dealMaster(request.getMasterWithOnlyChangedFields());
        stopWatch.lap("dealMaster");
        dealDetail(request.getDetailWithOnlyChangedFields());
        stopWatch.lap("dealDetail");
        stopWatch.logSlow(500);
    }

    private void dealMaster(IObjectData master) {
        StopWatch stopWatch = StopWatch.create("SupplementProcessor.dealMaster");
        // 置空引用字段，使引用字段函数设置值无效
        handleInvalidQuoteField(masterQuote, quoteReferenceMap, ObjectDataExt.of(master).toMap());

        IObjectDescribe describe = container.getObjectDescribe();
        List<IObjectData> dataList = Lists.newArrayList(master);
        if (hasRelFields(master, describe)) {
            // 补充关联字段或者主从字段
            fillRef(describe, dataList);
            stopWatch.lap("fillRef");
            if (hasQuoteFields(describe)) {
                // 补充引用字段
                fillQuote(describe, dataList);
                stopWatch.lap("fillQuote");
            }
        }
        if (hasEmployeeFields(master, describe)) {
            // 补充人员字段
            fillEmployee(describe, dataList);
            stopWatch.lap("fillEmployee");
        }
        if (hasDeptFields(master, describe)) {
            // 补充部门字段
            fillDept(describe, dataList);
            stopWatch.lap("fillDept");
        }
        if (hasAreaFields(master, describe)) {
            fillCountryAreaLabel(describe, dataList);
            stopWatch.lap("fillCountryAreaLabel");
        }
        if (hasDimensionFields(master, describe)) {
            fillDimensionFieldValue(describe, dataList);
            stopWatch.lap("fillDimensionFieldValue");
        }
        fillDataVisibilityRange(describe, dataList);
        stopWatch.lap("fillDataVisibilityRange");
        stopWatch.logSlow(500);
    }

    private void dealDetail(Map<String, List<IObjectData>> details) {
        details.forEach((apiName, dataList) -> {
            IObjectDescribe describe = container.getDetailDescribe().get(apiName);
            if (Objects.nonNull(describe)) {
                asyncFillFieldInfo(describe, dataList);
            } else {
                log.warn("detail object is not exist, api name : {}", apiName);
            }
        });
        // 处理新增从对象默认值
        details.forEach((apiName, dataList) -> {
            // 该对象下是否包含常量默认值字段
            if (detailDefaultValues.containsKey(apiName)) {
                Map<String, Object> fieldValue = detailDefaultValues.get(apiName);
                for (IObjectData data : dataList) {
                    // 新增的从对象且当前默认值字段没有值，才补充默认值
                    if (UIEventUtils.isNewDetail(data)) {
                        supplementDefaultValue(data, fieldValue);
                    }
                }
            }
        });
    }

    /*
     * 补充默认值
     * 默认值字段没有值才补充
     */
    private void supplementDefaultValue(IObjectData data, Map<String, Object> fieldValue) {
        for (Map.Entry<String, Object> entry : fieldValue.entrySet()) {
            Object value = data.get(entry.getKey());
            if (Objects.isNull(value)) {
                data.set(entry.getKey(), entry.getValue());
            }
        }
    }


    // ---------------------------------------- 判断是否包含关联、引用、人员、部门等需要补充的字段

    private boolean hasRelFields(IObjectData dataWithOnlyChangedFields, IObjectDescribe describe) {
        List<String> changedFields = Lists.newArrayList(ObjectDataExt.of(dataWithOnlyChangedFields).toMap().keySet());
        if (container.getObjectDescribe().getApiName().equals(describe.getApiName())) {
            // 主对象
            return hasFields(dataWithOnlyChangedFields, describe, masterReference);
        }
        // 从对象
        return hasFields(dataWithOnlyChangedFields, describe, detailReference.get(describe.getApiName()));
    }

    /*
     * 判断当前对象是否有引用字段
     * 有引用字段必有关联字段，但是如果没有关联字段变化就无需补充
     */
    private boolean hasQuoteFields(IObjectDescribe describe) {
        if (container.getObjectDescribe().getApiName().equals(describe.getApiName())) {
            // 主对象
            return !masterQuote.isEmpty();
        } else {
            // 从对象
            return CollectionUtils.notEmpty(detailQuote.get(describe.getApiName()));
        }
    }

    private boolean hasEmployeeFields(IObjectData dataWithOnlyChangedFields, IObjectDescribe describe) {
        if (container.getObjectDescribe().getApiName().equals(describe.getApiName())) {
            // 主对象
            return hasFields(dataWithOnlyChangedFields, describe, masterEmployee);
        }
        // 从对象
        return hasFields(dataWithOnlyChangedFields, describe, detailEmployee.get(describe.getApiName()));

    }

    private boolean hasDeptFields(IObjectData dataWithOnlyChangedFields, IObjectDescribe describe) {
        if (container.getObjectDescribe().getApiName().equals(describe.getApiName())) {
            // 主对象
            return hasFields(dataWithOnlyChangedFields, describe, masterDepartment);
        }
        // 从对象
        return hasFields(dataWithOnlyChangedFields, describe, detailDepartment.get(describe.getApiName()));
    }

    private boolean hasAreaFields(IObjectData dataWithOnlyChangedFields, IObjectDescribe describe) {
        if (container.getObjectDescribe().getApiName().equals(describe.getApiName())) {
            // 主对象
            return hasFields(dataWithOnlyChangedFields, describe, masterArea);
        }
        // 从对象
        return hasFields(dataWithOnlyChangedFields, describe, detailArea.get(describe.getApiName()));
    }

    private boolean hasDimensionFields(IObjectData dataWithOnlyChangedFields, IObjectDescribe describe) {
        if (container.getObjectDescribe().getApiName().equals(describe.getApiName())) {
            // 主对象
            return hasFields(dataWithOnlyChangedFields, describe, masterDimension);
        }
        return false;
    }

    private boolean hasFields(IObjectData dataWithOnlyChangedFields, IObjectDescribe describe, List<String> fields) {
        List<String> changedFields = Lists.newArrayList(ObjectDataExt.of(dataWithOnlyChangedFields).toMap().keySet());
        if (CollectionUtils.empty(changedFields)) {
            return false;
        }
        return hasCommonFields(changedFields, fields);
    }

    // 是否包含相同字段
    private boolean hasCommonFields(List<String> left, List<String> right) {
        return left.stream().anyMatch(right::contains);
    }


    // ---------------------------------------- 补充字段

    private void fillDimensionFieldValue(IObjectDescribe describe, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        serviceFacade.fillDimensionFieldValue(requestContext.getUser(), describe, dataList);
    }

    private void fillRef(IObjectDescribe describe, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        serviceFacade.fillObjectDataWithRefObject(describe, dataList, requestContext.getUser());
    }

    private void fillQuote(IObjectDescribe describe, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        infraServiceFacade.fillQuoteFieldValue(requestContext.getUser(), dataList, describe, null, false);
    }

    private void fillEmployee(IObjectDescribe describe, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        serviceFacade.fillUserInfo(describe, dataList, requestContext.getUser());
    }

    private void fillDept(IObjectDescribe describe, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        serviceFacade.fillDepartmentInfo(describe, dataList, requestContext.getUser());
    }

    private void fillCountryAreaLabel(IObjectDescribe describe, List<IObjectData> dataList) {
        // 目前只需补充乡镇字段的__r
        serviceFacade.fillCountryAreaLabel(describe, dataList, requestContext.getUser());
    }

    private void fillDataVisibilityRange(IObjectDescribe objectDescribe, List<IObjectData> dataList) {
        serviceFacade.fillDataVisibilityRange(requestContext.getUser(), objectDescribe, dataList);
    }

    private void asyncFillFieldInfo(IObjectDescribe describe, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        Map<String, String> quoteRefMap = detailQuoteReferenceMap.get(describe.getApiName());
        dataList.forEach(data -> handleInvalidQuoteField(detailQuote.get(describe.getApiName()), quoteRefMap, ObjectDataExt.of(data).toMap()));
        List<IObjectData> synchronizedDataList = ObjectDataExt.synchronize(dataList);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            //处理引用字段
            fillQuote(describe, synchronizedDataList);
        });
        parallelTask.submit(() -> {
            //添加lookup字段的主属性__r
            fillRef(describe, synchronizedDataList);
        });
        parallelTask.submit(() -> {
            fillEmployee(describe, synchronizedDataList);
            fillDept(describe, synchronizedDataList);
        });
        parallelTask.submit(() -> fillCountryAreaLabel(describe, synchronizedDataList));
        parallelTask.submit(() -> fillDataVisibilityRange(describe, synchronizedDataList));

        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.warn("Error in fill info of listController, ei:{}, object:{}",
                    requestContext.getTenantId(), describe.getApiName(), e);
            throw new UIEventBusinessException(I18NExt.text(I18NKey.HANDLE_SPECIAL_FIELD_TIMEOUT), e);
        }
    }

    private void handleInvalidQuoteField(List<String> quoteFieldApiNames, Map<String, String> quoteRefMap, Map<String, Object> dataMap) {
        if (CollectionUtils.empty(quoteFieldApiNames)) {
            return;
        }
        for (String quote : quoteFieldApiNames) {
            if (dataMap.containsKey(quote)) {
                String refApiName = CollectionUtils.nullToEmpty(quoteRefMap).get(quote);
                if (dataMap.containsKey(refApiName)) {
                    dataMap.put(quote, null);
                } else {
                    dataMap.remove(quote);
                }
            }
        }
    }
}
