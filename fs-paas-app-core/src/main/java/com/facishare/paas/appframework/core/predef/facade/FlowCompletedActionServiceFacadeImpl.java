package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.handler.*;
import com.facishare.paas.appframework.core.predef.action.StandardFlowCompletedAction;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.appframework.core.predef.handler.SaveActionHandler;
import com.facishare.paas.appframework.core.predef.handler.add.AddActionHandler;
import com.facishare.paas.appframework.core.predef.handler.edit.EditActionHandler;
import com.facishare.paas.appframework.core.predef.handler.flowcompleted.FlowCompletedActionHandler;
import com.facishare.paas.appframework.core.predef.handler.invalid.InvalidActionHandler;
import com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch.GetResult;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.metadata.DuplicatedSearchExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchOrderByType;
import com.facishare.paas.appframework.metadata.handler.HandlerType;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.appframework.metadata.repository.model.HandlerDefinition;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/9/26.
 */
@Service("flowCompletedActionServiceFacade")
public class FlowCompletedActionServiceFacadeImpl implements FlowCompletedActionServiceFacade {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private HandlerManager handlerManager;
    @Autowired
    private ApprovalFlowServiceFacade approvalFlowServiceFacade;

    @Override
    public void checkDuplicate(User user, IObjectDescribe objectDescribe, IObjectData data, Map<String, Object> callbackData,
                               StandardFlowCompletedAction.Arg arg) {
        if (!arg.isPass()) {
            return;
        }
        if (!ApprovalFlowTriggerType.UPDATE.equals(arg.approvalFlowTriggerType())) {
            return;
        }
        List<IDuplicatedSearch> duplicatedSearchList = Lists.newArrayList();
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(user.getTenantId(), objectDescribe.getApiName())) {

            duplicatedSearchList.addAll(serviceFacade.getEnableDuplicateSearchRuleList(objectDescribe.getApiName(),
                    IDuplicatedSearch.Type.NEW, user.getTenantId(), false, DuplicateSearchOrderByType.ORDER_BY_SORT));

        } else {
            IDuplicatedSearch duplicatedSearch = serviceFacade.findDuplicatedSearchByApiNameAndType(user.getTenantId(),
                    objectDescribe.getApiName(), IDuplicatedSearch.Type.NEW, false);
            if (DuplicatedSearchExt.isEnableDuplicate(duplicatedSearch)) {
                duplicatedSearchList.add(duplicatedSearch);
            }
        }
        for (IDuplicatedSearch duplicatedSearch : duplicatedSearchList) {
            boolean haveFuzzyType = duplicatedSearch.getUseableRules().getRules().stream()
                    .anyMatch(x -> x.getConditions().stream()
                            .anyMatch(y -> Objects.equals(IDuplicatedSearch.Policy.FUZZY, y.getFieldValue())));
            if (haveFuzzyType) {
                return;
            }
        }
        // 待更新的数据不包含查重规则中的字段,不需要调用查重
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.INCREMENT_UPDATE_DUPLICATE_SEARCH, user.getTenantId())) {
            if (duplicatedSearchList.stream().noneMatch(x -> DuplicatedSearchExt.of(x).dataContainsDuplicatedField(ObjectDataExt.of(callbackData)))) {
                return;
            }
        }
        IObjectData ret = ObjectDataExt.of(data).copy();
        ObjectDataExt.of(ret).putAll(callbackData);
        RLock duplicateSearchLock = serviceFacade.duplicateSearchLock(user, duplicatedSearchList, objectDescribe, ret);
        if (Objects.nonNull(duplicateSearchLock)) {
            CacheContext.getContext().setCache(ContextCacheKeys.DUPLICATE_SEARCH_LOCK, duplicateSearchLock);
        }
        boolean isDuplicate = getDuplicateData(ret, objectDescribe.getApiName());
        if (isDuplicate && !RequestUtil.isCepRequest() && !RequestUtil.isFromFunction()) {
            throw new ValidateException(I18N.text(I18NKey.DUPLICATED_DATA));
        }
    }

    private boolean getDuplicateData(IObjectData objectData, String objectApiName) {
        RequestContext requestContext = RequestContextManager.getContext();
        ControllerContext controllerContext = new ControllerContext(requestContext,
                objectApiName, StandardController.DuplicateSearch.name());
        GetResult.Arg duplicatedSearchArg = GetResult.Arg.builder()
                .describeApiName(objectApiName)
                .type(IDuplicatedSearch.Type.NEW)
                .objectData(ObjectDataDocument.of(objectData))
                .isNeedDuplicate(true)
                .includeObjectDescribes(false)
                .pageNumber(1)
                .pageSize(1)
                .build();
        GetResult.Result result = serviceFacade.triggerController(controllerContext, duplicatedSearchArg, GetResult.Result.class);
        return CollectionUtils.notEmpty(result.getDataList());
    }

    @Override
    public void updateChangeOrderStatus(User user, IObjectDescribe objectDescribe, IObjectData data, StandardFlowCompletedAction.Arg arg) {
        if (ApprovalFlowTriggerType.CREATE != arg.approvalFlowTriggerType()) {
            return;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        if (!describeExt.isChangeOrderObject() || describeExt.isSlaveObject()) {
            return;
        }
        if (arg.isPass()) {
            infraServiceFacade.updateChangeOrderStatusWithFlowPass(user, objectDescribe, data);
            return;
        }
        // 撤销和驳回走相同的逻辑
        if (arg.isReject() || arg.isCancel()) {
            infraServiceFacade.updateChangeOrderStatusWithFlowReject(user, objectDescribe, data);
        }
    }

    @Override
    public FlowCompletedActionHandler.Result executeTriggerActionTenantHandler(HandlerContext context,
                                                                               FlowCompletedActionHandler.Arg arg,
                                                                               List<HandlerType> handlerTypes) {
        if (CollectionUtils.empty(arg.getTriggerActionHandlerDescribes())) {
            return new FlowCompletedActionHandler.Result();
        }
        return executeTriggerActionTenantHandler(context, handlerTypes, arg.getTriggerActionHandlerDescribes(),
                false, arg.getInterfaceArg().approvalFlowTriggerType(), arg.getCallbackData(),
                arg.getObjectApiName(), arg.getDescribeMap(), arg.data(), arg.detailDataMap(), arg.dbData());
    }

    @Override
    public FlowCompletedActionHandler.Result executeTriggerActionTenantHandler(HandlerContext context,
                                                                               List<HandlerType> handlerTypes,
                                                                               List<SimpleHandlerDescribe> handlerDescribes,
                                                                               boolean executeByFlowStartCallback,
                                                                               ApprovalFlowTriggerType triggerType,
                                                                               Map<String, Object> callbackData,
                                                                               String objectApiName,
                                                                               Map<String, IObjectDescribe> describeMap,
                                                                               IObjectData data,
                                                                               Map<String, List<IObjectData>> detailDataMap,
                                                                               IObjectData dbData) {
        FlowCompletedActionHandler.Result tenantHandlerResult = new FlowCompletedActionHandler.Result();
        if (CollectionUtils.empty(handlerTypes)) {
            return tenantHandlerResult;
        }
        ObjectAction action = getActionByTriggerType(triggerType);
        //需要跳过按钮后动作的话，也不执行业务链
        if (action == ObjectAction.CREATE && skipCreatePostAction(callbackData)) {
            return tenantHandlerResult;
        }
        List<String> handlerTypeCodes = handlerTypes.stream().map(HandlerType::getCode).collect(Collectors.toList());
        List<SimpleHandlerDescribe> executeHandlerDescribes = SimpleHandlerDescribe.filterTenantHandlerAfterApprovalHandler(handlerDescribes,
                action, triggerType, handlerTypeCodes);
        if (CollectionUtils.empty(executeHandlerDescribes)) {
            return tenantHandlerResult;
        }
        Map<String, HandlerFunctions.ExecuteFunction> executeFunctions = Maps.newHashMap();
        executeFunctions.put(HandlerDefinition.DEFAULT_POST_ACTION_HANDLER, () -> doPostAction(context.getRequestContext(),
                objectApiName, data, detailDataMap, callbackData, action, Maps.newHashMap()));
        HandlerContext handlerContext = HandlerContext.builder()
                .requestContext(context.getRequestContext())
                .interfaceCode(action.getInterfaceCode())
                .build();
        handlerTypeCodes.forEach(handlerTypeCode -> {
            HandlerExecutor.builder()
                    .handlerManager(handlerManager)
                    .executeFunctions(executeFunctions)
                    .handlerType(handlerTypeCode)
                    .interfaceCode(action.getInterfaceCode())
                    .objectApiName(objectApiName)
                    .handlerDescribeList(executeHandlerDescribes)
                    .handlerContext(handlerContext)
                    .buildArgFunction(handlerDescribe -> {
                        ActionHandler.Arg handlerArg = buildTriggerActionTenantHandlerArg(action, data, detailDataMap, dbData);
                        if (Objects.nonNull(handlerArg)) {
                            if (executeByFlowStartCallback) {
                                handlerArg.setExecuteByFlowStartCallback(true);
                            } else {
                                handlerArg.setExecuteByFlowCompleted(true);
                            }
                            handlerArg.setHandlerDescribe(handlerDescribe);
                            handlerArg.setObjectApiName(objectApiName);
                        }
                        return handlerArg;
                    })
                    .processResultFunction((handlerDescribe, handlerArg, handlerResult) ->
                            processTriggerActionTenantHandlerResult(tenantHandlerResult, handlerResult, objectApiName, describeMap))
                    .build()
                    .execute();
        });
        return tenantHandlerResult;
    }

    private ObjectAction getActionByTriggerType(ApprovalFlowTriggerType triggerType) {
        return ObjectAction.of(triggerType.getActionCode());
    }

    /**
     * 判断流程回调是否需要执行新建按钮的后动作
     * <p>
     * 相关对象一起新建,新建导入等创建数据的操作,不执行新建按钮的后动作
     *
     * @return true 跳过新建后动作 false 执行新建后动作
     */
    private boolean skipCreatePostAction(Map<String, Object> callbackData) {
        if (CollectionUtils.empty(callbackData)) {
            return false;
        }
        return Objects.nonNull(callbackData.get(ExtraDataKeys.TRIGGER_FROM));
    }

    private ActionHandler.Arg buildTriggerActionTenantHandlerArg(ObjectAction action,
                                                                 IObjectData data,
                                                                 Map<String, List<IObjectData>> detailDataMap,
                                                                 IObjectData dbData) {
        switch (action) {
            case CREATE:
                AddActionHandler.Arg addHandlerArg = new AddActionHandler.Arg();
                addHandlerArg.setObjectData(ObjectDataDocument.of(data));
                addHandlerArg.setDetailObjectData(ObjectDataDocument.ofMap(detailDataMap));
                return addHandlerArg;
            case UPDATE:
                EditActionHandler.Arg editHandlerArg = new EditActionHandler.Arg();
                editHandlerArg.setObjectData(ObjectDataDocument.of(data));
                editHandlerArg.setDetailObjectData(ObjectDataDocument.ofMap(detailDataMap));
                editHandlerArg.setDbMasterData(ObjectDataDocument.of(dbData));
                return editHandlerArg;
            case INVALID:
            case BULK_INVALID:
                InvalidActionHandler.Arg invalidHandlerArg = new InvalidActionHandler.Arg();
                invalidHandlerArg.setObjectDataList(Lists.newArrayList(ObjectDataDocument.of(data)));
                invalidHandlerArg.setDetailObjectData(ObjectDataDocument.ofMap(detailDataMap));
                return invalidHandlerArg;
            default:
                break;
        }
        return null;
    }

    private void processTriggerActionTenantHandlerResult(FlowCompletedActionHandler.Result tenantHandlerResult,
                                                         Handler.Result handlerResult,
                                                         String objectApiName,
                                                         Map<String, IObjectDescribe> describeMap) {
        if (handlerResult instanceof SaveActionHandler.Result) {
            SaveActionHandler.Result actionResult = (SaveActionHandler.Result) handlerResult;
            if (Objects.nonNull(actionResult.getObjectData())) {
                if (Objects.isNull(tenantHandlerResult.getData())) {
                    tenantHandlerResult.setData(actionResult.getObjectData());
                } else {
                    tenantHandlerResult.getData().putAll(actionResult.getObjectData());
                }
            }
            if (Objects.nonNull(actionResult.getDetailObjectData())) {
                if (Objects.isNull(tenantHandlerResult.getDetailDataMap())) {
                    tenantHandlerResult.setDetailDataMap(Maps.newHashMap());
                }
                actionResult.getDetailObjectData().forEach((k, v) -> {
                    if (describeMap.containsKey(k) && !objectApiName.equals(k)) {
                        tenantHandlerResult.getDetailDataMap().put(k, v);
                    }
                });
            }
        }
    }

    @Override
    public void doPostAction(RequestContext requestContext,
                             String objectApiName,
                             IObjectData data,
                             Map<String, List<IObjectData>> detailDataMap,
                             Map<String, Object> callbackData,
                             ObjectAction action,
                             Map<String, Object> actionParam) {
        if (ObjectAction.CREATE == action && skipCreatePostAction(callbackData)) {
            return;
        }
        String buttonApiName = action.getButtonApiName();
        if (StringUtils.equalsAny(buttonApiName, ObjectAction.CREATE.getButtonApiName(), ObjectAction.UPDATE.getButtonApiName())
                && AppFrameworkConfig.isAddEditUIActionGray(requestContext.getTenantId(), objectApiName)) {
            buttonApiName = StringUtils.equals(buttonApiName, ObjectAction.CREATE.getButtonApiName())
                    ? ObjectAction.CREATE_SAVE.getButtonApiName() : ObjectAction.UPDATE_SAVE.getButtonApiName();
        }
        ButtonExecutor.Arg executorArg = getButtonPostActionArg(objectApiName, buttonApiName, data,
                actionParam, detailDataMap);
        executorArg.setActionStage("post");
        if (CollectionUtils.notEmpty(callbackData)) {
            Map<String, Object> optionInfoArg = Maps.newHashMap();
            Map<String, Object> args = (Map<String, Object>) callbackData.get(ExtraDataKeys.ARGS);
            if (CollectionUtils.notEmpty(args)) {
                optionInfoArg.putAll(args);
            }
            if (callbackData.containsKey(ExtraDataKeys.OPTION_INFO)) {
                optionInfoArg.put(ExtraDataKeys.OPTION_INFO, callbackData.get(ExtraDataKeys.OPTION_INFO));
            }
            executorArg.setArgs(optionInfoArg);
        }
        //回调后函数支持幂等
        requestContext.setAttribute(RequestContext.Attributes.FUNCTION_IDEMPOTENT, Boolean.TRUE);
        infraServiceFacade.triggerValidationFunction(requestContext.getUser(), executorArg);
    }

    private ButtonExecutor.Arg getButtonPostActionArg(String objectApiName, String buttonApiName, IObjectData objectData,
                                                      Map<String, Object> actionParam, Map<String, List<IObjectData>> detailObjectData) {
        return ButtonExecutor.Arg.builder()
                .actionStage("post")
                .actionParams(actionParam)
                .buttonApiName(buttonApiName)
                .describeApiName(objectApiName)
                .objectData(objectData)
                .details(detailObjectData)
                .build();
    }

    @Override
    public void updateDetailObjectDataLifeStatus(ApprovalFlowTriggerType triggerType,
                                                 User user,
                                                 IObjectData data,
                                                 IObjectData dbData,
                                                 Map<String, List<IObjectData>> detailDataMap,
                                                 Map<String, IObjectDescribe> detailDescribeMap) {
        if (ApprovalFlowTriggerType.CREATE == triggerType || ApprovalFlowTriggerType.UPDATE == triggerType) {
            approvalFlowServiceFacade.updateDetailObjectDataLifeStatus(triggerType.getActionCode(), user,
                    ObjectDataExt.of(dbData).getLifeStatus(), data, detailDataMap, detailDescribeMap,
                    null, false);
        }
    }

}
