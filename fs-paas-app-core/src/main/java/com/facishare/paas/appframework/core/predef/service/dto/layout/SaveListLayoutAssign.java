package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/11/18
 */
public interface SaveListLayoutAssign {

    @Data
    class Arg {
        private String describeApiName;
        private String whatDescribeApiName;
        @JsonProperty("role_list")
        private String roleList;
        private String layoutType;
        private String sourceInfo;
        private String appId;
    }

    @Builder
    @Data
    class Result {
        private boolean success;
    }
}
