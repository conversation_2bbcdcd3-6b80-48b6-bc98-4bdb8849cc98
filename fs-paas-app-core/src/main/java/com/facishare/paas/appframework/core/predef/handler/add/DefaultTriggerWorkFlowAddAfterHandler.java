package com.facishare.paas.appframework.core.predef.handler.add;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.facade.ApprovalFlowServiceFacade;
import com.facishare.paas.appframework.core.predef.facade.SaveActionServiceFacade;
import com.facishare.paas.appframework.flow.mq.WorkflowProducer;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by zhouwr on 2023/1/30.
 */
@Slf4j
@Component
@HandlerProvider(name = "defaultTriggerWorkFlowAddAfterHandler")
public class DefaultTriggerWorkFlowAddAfterHandler implements AddActionHandler {

    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private SaveActionServiceFacade saveActionServiceFacade;
    @Autowired
    private ApprovalFlowServiceFacade approvalFlowServiceFacade;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        if (!context.getRequestContext().needTriggerWorkFlow() || objectDescribe.isPublicObject()) {
            return new Result();
        }
        //触发主从对象的工作流
        triggerWorkFlow(context, arg);
        //触发相关对象的工作流
        triggerRelatedWorkFlow(context, arg);
        return new Result();
    }

    private void triggerWorkFlow(HandlerContext context, Arg arg) {
        // 主从审批的白名单企业在主从一起新建时，即使触发了主的审批，也需要触发从对象的工作流
        //批量处理触发工作流以及处理过滤器异常的数据
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            List<IObjectData> dataListForCreateWorkFlow = getDataListForCreateWorkFlow(context, arg);
            doTriggerCreateWorkFlow(context, dataListForCreateWorkFlow);
        });
        parallelTask.run();
    }

    private List<IObjectData> getDataListForCreateWorkFlow(HandlerContext context, Arg arg) {
        List<IObjectData> dataListForCreateWorkFlow = Lists.newArrayList();
        //主从白名单或没有触发主的审批的从对象需要触发工作流
        if (AppFrameworkConfig.isInMasterDetailApprovalWhiteList(context.getTenantId()) || isApprovalNotExist()) {
            CollectionUtils.nullToEmpty(arg.detailObjectData()).forEach((k, v) -> dataListForCreateWorkFlow.addAll(v));
        }
        //本对象是normal状态或者主对象是in_change状态的从对象需要触发工作流
        IObjectData objectData = arg.objectData();
        if (ObjectDataExt.of(objectData).isNormal() || ObjectDataExt.of(objectData).isInChange()) {
            dataListForCreateWorkFlow.add(objectData);
        }
        //从对象，主是ineffective状态且没有触发审批，主和从都需要触发工作流
        List<IObjectData> relateDataForWorkFlow = getRelateDataForCreateWorkFlow(context, arg);
        if (dataListForCreateWorkFlow.stream().anyMatch(x -> x.getDescribeApiName().equals(objectData.getDescribeApiName())
                && x.getId().equals(objectData.getId()))) {
            relateDataForWorkFlow.removeIf(x -> x.getDescribeApiName().equals(objectData.getDescribeApiName())
                    && x.getId().equals(objectData.getId()));
        }
        dataListForCreateWorkFlow.addAll(relateDataForWorkFlow);
        return dataListForCreateWorkFlow;
    }

    private boolean isApprovalNotExist() {
        return approvalFlowServiceFacade.isApprovalNotExist();
    }

    private List<IObjectData> getRelateDataForCreateWorkFlow(HandlerContext context, Arg arg) {
        return saveActionServiceFacade.getRelateDataForCreateWorkFlow(context.getUser(), arg.getObjectDescribe(),
                arg.objectData());
    }

    private void triggerRelatedWorkFlow(HandlerContext context, Arg arg) {
        if (CollectionUtils.empty(arg.getRelatedObjectData())) {
            return;
        }
        // 只有没有触发审批流程才考虑触发相关对象的工作流
        if (!context.getRequestContext().needTriggerApprovalFlow()) {
            ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
            parallelTask.submit(() -> arg.relatedDataMap().forEach((apiName, dataList) -> doTriggerCreateWorkFlow(context, dataList)));
            parallelTask.run();
        }
    }

    private void doTriggerCreateWorkFlow(HandlerContext context, List<IObjectData> dataListForCreateWorkFlow) {
        dataListForCreateWorkFlow.forEach(data -> {
            try {
                infraServiceFacade.startWorkFlow(data.getId(), data.getDescribeApiName(), WorkflowProducer.TRIGGER_START,
                        context.getUser(), Maps.newHashMap(), context.getRequestContext().getEventId());
            } catch (Exception e) {
                log.error("startCreateWorkFlow error,tenantId:{},describeApiName:{},dataId:{},eventId:{}", context.getTenantId(),
                        data.getDescribeApiName(), data.getId(), context.getRequestContext().getEventId(), e);
            }
        });
    }
}
