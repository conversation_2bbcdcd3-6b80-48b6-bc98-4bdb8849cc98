package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2021/02/19
 */
public class StandardExportFileVerifyAction extends BaseExportVerifyAction {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.ExportFileVerify.getFunPrivilegeCodes();
    }

    @Override
    protected int getExportRowsThrottle() {
        return AppFrameworkConfig.getExportFileRowsThrottle(actionContext.getTenantId());
    }

    @Override
    protected void customValidate() {
        if (CollectionUtils.empty(arg.getFieldApiNames())) {
            return;
        }
        Map<String, Set<String>> userNoExportFieldPrivilege = serviceFacade.getUserNoExportFieldPrivilege(actionContext.getUser(),
                Sets.newHashSet(objectDescribe.getApiName()));
        if (CollectionUtils.empty(userNoExportFieldPrivilege)) {
            return;
        }
        Set<String> noExportPrivilegeFields = userNoExportFieldPrivilege.get(objectDescribe.getApiName());
        if (CollectionUtils.empty(noExportPrivilegeFields)) {
            return;
        }
        Set<String> noPermissionFields = arg.getFieldApiNames().stream().filter(noExportPrivilegeFields::contains).collect(Collectors.toSet());
        if (CollectionUtils.notEmpty(noPermissionFields)) {
            throw new ValidateException(buildExportFieldErrorMessage(Lists.newArrayList(noPermissionFields)));
        }
    }

    private String buildExportFieldErrorMessage(List<String> fieldApiNames) {
        String fieldLabels = ObjectDescribeExt.of(objectDescribe).getFieldByApiNames(fieldApiNames).stream()
                .map(IFieldDescribe::getLabel).collect(Collectors.joining(","));
        return I18NExt.text(I18NKey.EXPORT_FILE_FIELD_NO_PERMISSION, fieldLabels);
    }
}
