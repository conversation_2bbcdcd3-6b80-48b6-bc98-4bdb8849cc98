package com.facishare.paas.appframework.core.predef.service.dto.scene;

import com.facishare.paas.appframework.metadata.OrderByExt;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Builder;
import lombok.Data;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/06/05
 */
public interface DefaultSceneOrders {
    @Data
    @JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy.class)
    class Arg {
        private OrderByExt orders;
        private String describeApiName;
        private String sceneApiName;
        private String extendAttribute;
    }

    @Data
    @Builder
    class Result {
        private IScene scene;
    }

}
