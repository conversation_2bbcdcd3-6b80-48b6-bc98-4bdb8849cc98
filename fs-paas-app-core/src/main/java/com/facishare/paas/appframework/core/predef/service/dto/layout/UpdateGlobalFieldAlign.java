package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.facishare.paas.appframework.metadata.fieldalign.GlobalFieldAlign;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/5/13
 */
public interface UpdateGlobalFieldAlign {

    @Data
    class Arg {
        @JsonProperty("global_field_align")
        private GlobalFieldAlignVO globalFieldAlign;
    }

    class Result {

    }

    @Data
    class GlobalFieldAlignVO {
        /**
         * left 左对齐
         * center 具中
         * top_and_bottom 上下
         */
        @JsonProperty("field_align")
        private String fieldAlign;
        /**
         * responsive 自适应布局
         * static 固定布局
         */
        @JsonProperty("detail_form_layout")
        private String detailFormLayout;

        public static GlobalFieldAlignVO fromGlobalFieldAlign(GlobalFieldAlign fieldAlign) {
            GlobalFieldAlign.FieldAlign align = fieldAlign.getFieldAlign();
            GlobalFieldAlignVO result = new GlobalFieldAlignVO();
            result.setFieldAlign(align.getType());
            result.setDetailFormLayout(fieldAlign.getDetailFormLayout());
            return result;
        }
    }
}
