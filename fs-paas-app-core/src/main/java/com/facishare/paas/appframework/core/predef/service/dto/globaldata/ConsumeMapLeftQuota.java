package com.facishare.paas.appframework.core.predef.service.dto.globaldata;

import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface ConsumeMapLeftQuota {
    @Data
    class Arg {
        private List<MapConsumption> consumptionList;
    }

    @Data
    @Builder
    class Result {
        boolean success;
    }

    @Data
    class MapConsumption {
        private String api;
        private int count;
    }

}
