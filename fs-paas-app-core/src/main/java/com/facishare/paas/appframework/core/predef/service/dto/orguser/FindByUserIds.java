package com.facishare.paas.appframework.core.predef.service.dto.orguser;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;

/**
 * Created by liyiguang on 2018/4/13.
 */
public interface FindByUserIds {

    @Data
    class Arg {
        List<String> userIds;

        private Boolean formatData;

        public boolean formatData() {
            return BooleanUtils.isTrue(formatData);
        }
    }

    @Data
    @Builder
    class Result {
        List<ObjectDataDocument> objectDataList;
    }
}
