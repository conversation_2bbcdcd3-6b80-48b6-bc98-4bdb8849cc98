package com.facishare.paas.appframework.core.predef.service.dto.resource;

import com.facishare.paas.appframework.metadata.mtresource.model.ConfigurationPackageResource;
import com.facishare.paas.appframework.metadata.mtresource.model.ResourceQuery;
import lombok.Data;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/7/12
 */
public interface FindObjectResource {

    @Data
    class Arg {
        private String resourceParentValue;
        private String resourceType;
        private ResourceQuery resourceQuery;
        private String sourceType;
        private String sourceValue;
    }

    @Data
    class Result {
        private List<ConfigurationPackageResource> result;
    }
}
