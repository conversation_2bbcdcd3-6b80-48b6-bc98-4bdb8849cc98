package com.facishare.paas.appframework.core.predef.handler.edit;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.handler.AbstractValidationFunctionSaveBeforeHandler;
import com.facishare.paas.appframework.metadata.ButtonExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.state.MergeStateContainer;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/1/28.
 */
@Slf4j
@Component
@HandlerProvider(name = "defaultValidationFunctionEditBeforeHandler")
public class DefaultValidationFunctionEditBeforeHandler extends AbstractValidationFunctionSaveBeforeHandler<EditActionHandler.Arg, EditActionHandler.Result> {

    @Override
    protected EditActionHandler.Result buildResult(HandlerContext context, EditActionHandler.Arg arg) {
        return new EditActionHandler.Result();
    }

    @Override
    protected String getButtonApiName(HandlerContext context, EditActionHandler.Arg arg) {
        if (AppFrameworkConfig.isAddEditUIActionGray(context.getTenantId(), arg.getObjectApiName())) {
            return ObjectAction.UPDATE_SAVE.getButtonApiName();
        }
        return ButtonExt.DEFAULT_EDIT_BUTTON_API_NAME;
    }

    @Override
    protected MergeStateContainer getSourceMergeStateContainer(HandlerContext context, EditActionHandler.Arg arg) {
        List<IObjectData> detailsToDelete = ObjectDataExt.copyList(arg.detailsToDelete());
        ObjectDataExt.fillTemporaryId(detailsToDelete);
        return MergeStateContainer.of(arg.detailsToAdd(), arg.detailsToUpdate(), detailsToDelete);
    }

    @Override
    protected void replaceDetailDataMergeResult(HandlerContext context, EditActionHandler.Arg arg, MergeStateContainer sourceMergeStateContainer) {
        List<IObjectData> detailsToAdd = dataToAdd(arg, sourceMergeStateContainer);
        List<IObjectData> detailsToUpdate = dataToUpdate(sourceMergeStateContainer);
        Map<String, List<IObjectData>> details = Maps.newHashMap();
        detailsToAdd.forEach(data -> details.computeIfAbsent(data.getDescribeApiName(), k -> Lists.newArrayList()).add(data));
        detailsToUpdate.forEach(data -> details.computeIfAbsent(data.getDescribeApiName(), k -> Lists.newArrayList()).add(data));
        // 按已有的从对象 order_by 排一下序
        details.forEach((apiName, objectDataList) -> ObjectDataExt.sortByOrderBy(objectDataList));
        // 用合并后的数据替换 detailObjectData
        arg.setDetailObjectData(ObjectDataDocument.ofMap(details));
    }

    private List<IObjectData> dataToUpdate(MergeStateContainer sourceMergeStateContainer) {
        List<IObjectData> dataToUpdate = sourceMergeStateContainer.detailsToUpdate();
        if (CollectionUtils.empty(dataToUpdate)) {
            return Lists.newArrayList();
        }
        for (IObjectData data : dataToUpdate) {
            ObjectDataExt.of(data).remove(ObjectDataExt.RELEVANT_TEAM);
        }
        return dataToUpdate;
    }

    private List<IObjectData> dataToAdd(EditActionHandler.Arg arg, MergeStateContainer sourceMergeStateContainer) {
        List<IObjectData> dataToAdd = sourceMergeStateContainer.detailsToAdd();
        if (CollectionUtils.empty(dataToAdd)) {
            return Lists.newArrayList();
        }
        String masterId = arg.objectData().getId();
        Map<String, List<IObjectData>> detailDataMap = dataToAdd.stream()
                .collect(Collectors.groupingBy(IObjectData::getDescribeApiName, Collectors.mapping(Function.identity(), Collectors.toList())));
        List<IObjectData> result = Lists.newArrayList();
        detailDataMap.forEach((detailApiName, dataList) -> {
//            modifyDetailObjectDataToAddWhenEdit(objectData, detailApiName, dataList);
            addMasterDetailFieldIntoDetailDataList(arg.getObjectApiName(), masterId, arg.getDetailDescribe(detailApiName), dataList);
            result.addAll(dataList);
        });
        return result;
    }
}
