package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.OrganizationInfo;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.UniqueRuleValidationException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.OutInfoChangeModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.domain.ActionDomainPlugin;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.model.handler.HandlerAttributes;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.predef.domain.AddActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.handler.SaveActionHandler;
import com.facishare.paas.appframework.core.predef.handler.add.AddActionHandler;
import com.facishare.paas.appframework.core.predef.service.PartnerRemindOutUserService;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderConfig;
import com.facishare.paas.appframework.metadata.changeorder.ChangeOrderEffectiveStatus;
import com.facishare.paas.appframework.metadata.dto.MasterApprovalResult;
import com.facishare.paas.appframework.metadata.dto.SaveMasterAndDetailData;
import com.facishare.paas.appframework.metadata.dto.UniqueRuleSearchResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.exception.MetadataDataDuplicateBusinessException;
import com.facishare.paas.appframework.metadata.handler.SimpleHandlerDescribe;
import com.facishare.paas.appframework.metadata.relation.CalculateFields;
import com.facishare.paas.appframework.metadata.repository.model.MtChangeOrderRule;
import com.facishare.paas.appframework.metadata.state.MergeStateContainer;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectReferenceField;
import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.ObjectData;
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.common.hash.Hashing;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.model.User.COMPANY_ID;
import static com.facishare.paas.appframework.core.model.User.SUPPER_ADMIN_USER_ID;

/**
 * Created by liyiguang on 2019/3/28.
 */
@Slf4j
public class AbstractStandardAddAction<A extends BaseObjectSaveAction.Arg> extends BaseObjectSaveAction<A> {

    protected PartnerRemindOutUserService partnerRemindOutUserService;

    private List<OutInfoChangeModel> outInfoChangeModelList = Lists.newArrayList();
    private boolean triggerMasterApproval = false;
    private boolean isCurrencyEmpty = false;

    private List<IObjectData> noIdDataList;

    private UniqueRuleSearchResult.DuplicateData uniqueRuleDuplicateResult;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        if (arg.fromImport()) {
            return Lists.newArrayList();
        }
        // 变更单的新建不需要功能权限
        findDescribe();
        // 变更单的从对象不支持单独新建
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            return StandardAction.Add.getFunPrivilegeCodes();
        }
        String originalDescribeApiName = objectDescribe.getOriginalDescribeApiName();
        if (!Strings.isNullOrEmpty(originalDescribeApiName)
                && ChangeOrderConfig.changeOrderDescribeGray(actionContext.getTenantId(), originalDescribeApiName)) {
            return Lists.newArrayList();
        }
        return StandardAction.Add.getFunPrivilegeCodes();
    }

    @Override
    protected List<String> getDataPrivilegeIds(A arg) {
        return Collections.emptyList();
    }

    @Override
    protected String getIRule() {
        return IRule.CREATE;
    }

    @Override
    protected ObjectAction getObjectAction() {
        return ObjectAction.CREATE;
    }

    @Override
    protected final List<String> getRecordTypes() {
        return Lists.newArrayList(objectData.getRecordType());
    }

    @Override
    protected final AddActionDomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        //给从对象补充id，后面合并数据需要用
        if (ActionDomainPlugin.BEFORE.equals(method)) {
            noIdDataList = ObjectDataExt.fillDataId(detailObjectData);
        }
        return AddActionDomainPlugin.Arg.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .detailObjectData(ObjectDataDocument.ofMap(detailObjectData))
                .isApprovalFlowStartSuccess(isApprovalFlowStartSuccess(objectData.getId()))
                .build();
    }

    @Override
    protected final void processDomainPluginResult(String method, DomainPlugin.Arg pluginArg, DomainPlugin.Result pluginResult) {
        AddActionDomainPlugin.Result addPluginResult = (AddActionDomainPlugin.Result) pluginResult;
        //合并插件返回结果
        ObjectDataMerger.builder()
                .origMasterData(objectData)
                .origDetailDataMap(detailObjectData)
                .masterDataToUpdate(addPluginResult.masterDataToUpdate())
                .detailDataToAdd(ObjectDataDocument.ofDataMap(addPluginResult.getDetailsToAdd()))
                .detailDataToUpdate(ObjectDataDocument.ofDataMap(addPluginResult.getDetailsToUpdate()))
                .detailDataToDelete(addPluginResult.getDetailsToDelete())
                .build()
                .doMerge();
        //合并审批流回调参数
        if (CollectionUtils.notEmpty(addPluginResult.getCustomCallbackData())) {
            customCallbackData.putAll(addPluginResult.getCustomCallbackData());
        }
        //清理原来没有id的从对象的id
        ObjectDataExt.removeDataId(noIdDataList);
    }

    @Override
    protected final Handler.Arg<A> buildHandlerArg(SimpleHandlerDescribe handlerDescribe) {
        AddActionHandler.Arg handlerArg = new AddActionHandler.Arg();
        handlerArg.setDetailDescribeMap(detailDescribeMap);
        handlerArg.setRelatedDescribeMap(relatedDescribes);
        handlerArg.setObjectData(ObjectDataDocument.of(objectData));
        handlerArg.setDetailObjectData(ObjectDataDocument.ofMap(detailObjectData));
        handlerArg.setRelatedObjectData(RelatedDataDocument.fromMap(relatedObjectData));
        handlerArg.setAssigneesExceptional(isAssigneesExceptional);
        handlerArg.setUniqueRule(uniqueRule);
        handlerArg.setExtraCallbackData(getExtraCallbackData());
        handlerArg.setConvertRuleDataContainer(convertRuleDataContainer);
        return (Handler.Arg<A>) handlerArg;
    }

    @Override
    protected final void processHandlerResult(HandlerContext handlerContext, Handler.Arg<A> handlerArg, Handler.Result<Result> handlerResult) {
        super.processHandlerResult(handlerContext, handlerArg, handlerResult);
        if (handlerResult instanceof SaveActionHandler.Result) {
            SaveActionHandler.Result saveHandlerResult = (SaveActionHandler.Result) handlerResult;
            if (Objects.nonNull(saveHandlerResult.getObjectData())) {
                ObjectDataExt.of(this.objectData).putAll(saveHandlerResult.getObjectData());
            }
            if (Objects.nonNull(saveHandlerResult.getDetailObjectData())) {
                this.detailObjectData.putAll(ObjectDataDocument.ofDataMap(saveHandlerResult.getDetailObjectData()));
            }
            if (Objects.nonNull(saveHandlerResult.getSkipDuplicateSearchCheck())) {
                handlerContext.setAttribute(HandlerAttributes.SKIP_DUPLICATE_SEARCH_CHECK, saveHandlerResult.getSkipDuplicateSearchCheck());
            }
            if (Objects.nonNull(saveHandlerResult.getSkipUniqueRuleCheck())) {
                handlerContext.setAttribute(HandlerAttributes.SKIP_UNIQUE_RULE_CHECK, saveHandlerResult.getSkipUniqueRuleCheck());
            }
            if (Objects.nonNull(saveHandlerResult.getSkipValidationFunctionCheck())) {
                handlerContext.setAttribute(HandlerAttributes.SKIP_VALIDATION_FUNCTION_CHECK, saveHandlerResult.getSkipValidationFunctionCheck());
            }
            if (Objects.nonNull(saveHandlerResult.getSkipValidationRuleCheck())) {
                handlerContext.setAttribute(HandlerAttributes.SKIP_VALIDATION_RULE_CHECK, saveHandlerResult.getSkipValidationRuleCheck());
            }
        }
    }

    @Override
    protected void init() {
        super.init();
        partnerRemindOutUserService = serviceFacade.getBean(PartnerRemindOutUserService.class);
        stopWatch.lap("getPartnerRemindOutUserService");
        setOrderForDetailData(detailObjectData);
        stopWatch.lap("setOrderForDetailData");
        setIdForDetailData();
        stopWatch.lap("setIdForDetailData");
        setIdForRelatedData();
        stopWatch.lap("setIdForRelatedData");
        // 修改objectData,写入tenantId、创建人、最后修改人、最后修改时间、生命周期等系统字段。
        setDefaultSystemInfo(objectData);
        stopWatch.lap("setDefaultSystemInfo");
        // 设置默认业务类型
        setDefaultRecordType(objectData, objectDescribe);
        stopWatch.lap("setDefaultRecordType");
        // 补充数据负责人（下游用户）、外部负责人、外部企业、相关团队
        fillOutOwnerAndOutTeamMember();
        stopWatch.lap("fillOutOwnerAndOutTeamMember");
        //调整统计字段和相关计算字段的值，防止接口中传入错误的值
        adjustCountAndRelateFieldValues();
    }

    @Override
    protected IObjectData findDbMasterData() {
        return null;
    }

    private void adjustCountAndRelateFieldValues() {
        Map<String, Object> masterChanges = Maps.newHashMap();
        ObjectDescribeExt.of(objectDescribe).getCountFields().forEach(count -> {
            if (this.detailObjectData.containsKey(count.getSubObjectDescribeApiName())) {
                return;
            }
            Object value = CountExt.of(count).formatResult(null);
            objectData.set(count.getApiName(), value);
            masterChanges.put(count.getApiName(), value);
        });
        Map<String, Map<String, Object>> detailChanges = Maps.newHashMap();
        this.detailObjectData.forEach((detailApiName, detailDataList) -> {
            IObjectDescribe detailObjectDescribe = objectDescribes.get(detailApiName);
            Map<String, Object> detailChange = Maps.newHashMap();
            ObjectDescribeExt.of(detailObjectDescribe).getCountFields().forEach(count -> {
                Object value = CountExt.of(count).formatResult(null);
                detailDataList.forEach(detailData -> detailData.set(count.getApiName(), value));
                detailChange.put(count.getApiName(), value);
            });
            if (CollectionUtils.notEmpty(detailChange)) {
                detailChanges.putIfAbsent(detailApiName, Maps.newHashMap());
                detailChanges.get(detailApiName).putIfAbsent(ObjectAction.UPDATE.getActionCode(), Maps.newHashMap());
                ((Map) detailChanges.get(detailApiName).get(ObjectAction.UPDATE.getActionCode())).putIfAbsent("_vid", detailChange);
            }
        });
        if (CollectionUtils.empty(masterChanges) && CollectionUtils.empty(detailChanges)) {
            return;
        }
        List<IObjectDescribe> detailDescribes = Lists.newArrayList(detailDescribeMap.values()).stream()
                .filter(x -> detailObjectData.containsKey(x.getApiName()))
                .collect(Collectors.toList());
        CalculateFields calculateFields = infraServiceFacade.getCalculateFieldsByChanges(objectDescribe, detailDescribes,
                masterChanges, detailChanges);
        stopWatch.lap("getCalculateFieldsByChanges");
        if (CollectionUtils.notEmpty(calculateFields.getCalculateFieldMap())) {
            serviceFacade.batchCalculateBySortFields(actionContext.getUser(), objectData, detailObjectData, calculateFields);
            stopWatch.lap("batchCalculateBySortFields");
        }
    }

    private void setIdForRelatedData() {
        if (StringUtils.isEmpty(objectData.getId())) {
            objectData.setId(serviceFacade.generateId());
        }

        if (CollectionUtils.empty(relatedObjectData)) {
            return;
        }
        String masterDataId = objectData.getId();
        relatedObjectData.forEach((apiName, relatedObjectDataList) ->
                relatedObjectDataList.forEach(relatedObjectData -> {
                    String relatedFieldName = relatedObjectData.getRelatedFieldName();
                    List<IObjectData> dataList = relatedObjectData.getDataList();
                    // 补充关联字段
                    dataList.forEach(data -> data.set(relatedFieldName, masterDataId));
                    //补充 id
                    ObjectDataExt.fillDataId(dataList);
                }));
    }

    @Override
    protected final void validateMasterDetailCurrency() {
        //先判断数据参数中是否有币种，后面校验lookup字段过滤条件会用到
        isCurrencyEmpty = ObjectDataExt.isValueEmpty(ObjectDataExt.of(objectData).getCurrency());
        //补充币种、汇率等字段
        //没有传币种的从对象的币种和主对象保持一致
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            ObjectDescribeExt.of(objectDescribe).getMasterDetailFieldDescribe().ifPresent(masterDetail -> {
                String masterDataId = String.valueOf(objectData.get(masterDetail.getApiName()));
                IObjectData masterData = serviceFacade.findObjectDataIgnoreAll(actionContext.getUser(), masterDataId, masterDetail.getTargetApiName());
                ObjectDataExt.of(objectData).checkDetailCurrency(masterData);
            });
        } else {
            String personCurrencyCode = serviceFacade.getMultiCurrencyLogicService().findPersonCurrencyCode(actionContext.getUser());
            List<MtCurrency> currencyList = serviceFacade.getMultiCurrencyLogicService().findCurrencyList(actionContext.getUser());
            ObjectDataExt.of(objectData).fillMultiCurrencyFields(currencyList, personCurrencyCode);
            CollectionUtils.nullToEmpty(detailObjectData).forEach((apiName, dataList) ->
                    dataList.forEach(data -> ObjectDataExt.of(data).checkDetailCurrency(objectData)));
        }
    }

    @Override
    protected void validateChangeRuleWithChangeData() {
        if (Strings.isNullOrEmpty(getChangeRuleName())) {
            return;
        }
        infraServiceFacade.validateChangeRuleWithChangeData(actionContext.getUser(), objectDescribe, objectData, MtChangeOrderRule.CalibrationType.SUBMIT);
    }

    private String getChangeRuleName() {
        return objectData.get(ObjectDataExt.CHANGE_ORDER_RULE, String.class);
    }

    @Override
    protected void validateLookupData(IObjectData validateData, IObjectDescribe describe) {
        IObjectData cpData = validateData;
        //如果数据参数中没有币种，则先清空币种，再校验lookup字段的过滤条件，防止本位币校验通不过
        if (isCurrencyEmpty) {
            cpData = ObjectDataExt.of(validateData).copy();
            ObjectDataExt.of(cpData).setCurrency(null);
        }
        super.validateLookupData(cpData, describe);
    }

    @Override
    protected void checkUniquenessRule() {
        if (!actionContext.needCheckUniquenessRule()) {
            return;
        }

        if (Objects.isNull(uniqueRule) || !uniqueRule.isUseWhenCallOpenApi()) {
            return;
        }

        if (!UniqueRuleExt.isUseable(uniqueRule)) {
            log.warn("Uniqueness rule is in effect, describeApiName=>{}, user=>{}", objectDescribe.getApiName(),
                    JSON.toJSONString(actionContext.getUser()));
            throw new MetaDataBusinessException(I18N.text(I18NKey.UNIQUENESS_RULE_IS_IN_EFFECT));
        }
        uniqueRuleDuplicateResult = getDuplicateDataByUniqueRuleSearch().orElse(null);
        if (Objects.isNull(uniqueRuleDuplicateResult)) {
            return;
        }
        log.warn("find duplicate data tenantId=>{}, describeApiName=>{}, duplicateData=>{}",
                actionContext.getTenantId(), objectDescribe.getApiName(), JSON.toJSONString(uniqueRuleDuplicateResult));
        if (!arg.enableUniqueCheckResult()) {
            throw new UniqueRuleValidationException(I18N.text(I18NKey.VALID_UNIQUENESS_MESSAGE_DB,
                    UniqueRuleExt.of(uniqueRule).joiningFieldLabel(" and ", objectDescribe)));
        }

        throw new AcceptableValidateException(buildValidateResult());
    }

    protected void setIdForDetailData() {
        if (StringUtils.isEmpty(objectData.getId())) {
            objectData.setId(serviceFacade.generateId());
        }
        if (CollectionUtils.notEmpty(detailObjectData)) {
            for (Map.Entry<String, List<IObjectData>> entry : detailObjectData.entrySet()) {
                List<IObjectData> detailDataList = detailObjectData.get(entry.getKey());
                addMasterDetailFieldIntoDetailDataList(objectData.getId(), objectDescribes.get(entry.getKey()), detailDataList);
                ObjectDataExt.fillDataId(detailDataList);
            }
        }
    }

    private void syncLifeStatusFromMaster() {
        if (!ObjectDescribeExt.of(objectDescribe).isSlaveObjectCreateWithMasterAndInGrayList()) {
            return;
        }
        IObjectData realMasterData = getRealMasterObjectData(objectData);
        ObjectDataExt.of(objectData).setLifeStatus(ObjectDataExt.of(realMasterData).getLifeStatus());
        ObjectDataExt.of(objectData).setLockStatus(ObjectDataExt.of(realMasterData).getLockStatus());
        stopWatch.lap("syncLifeStatusFromMaster");
    }

    @Override
    protected BaseObjectSaveAction.Result doAct(A arg) {
        //在之前处理
        modifyObjectDataBeforeCreate(objectData, objectDescribe);
        stopWatch.lap("modifyObjectData");

        modifyDataOwnOrgAndDept(objectData, objectDescribe);
        stopWatch.lap("modifyDataOwnOrgAndDept");

        serviceFacade.fillOutDataOwnDeptAndOrgByOutOwner(actionContext.getUser(), objectDescribe, objectData);
        stopWatch.lap("modifyOutDataOwnOrgAndDept");

        //将互联部门补充填充到相关团队中
        addInterconnectToTeamMember(actionContext.getUser(), objectDescribe, objectData);
        stopWatch.lap("addInterconnectToTeamMember");

        syncLifeStatusFromMaster();

        //在正式更新从对象之前,修改从对象的objectData中的一些值,比如修改时间、业务类型、signIn字段的默认值等。
        modifyDetailObjectDataBeforeCreate(objectData, detailObjectData);
        stopWatch.lap("modifyDetail");
        //在保存数据之前,修改相关对象的一些值
        modifyRelatedObjectDataBeforeCreate();
        stopWatch.lap("modifyRelatedObjectData");

        calculateCountAndFormulaFields();
        stopWatch.lap("calculateCountAndFormulaFields");

        resetLastLifeStatus();

        tryTriggerMasterEditApproval();
        stopWatch.lap("tryTriggerMasterEditApproval");

        handleQuoteValueBeforeCreate();
        stopWatch.lap("handleQuoteValueBeforeCreate");

        doSaveData();
        stopWatch.lap("doSaveData");

        //设置写库标记
        writeDB = true;

        //将结果写入result中。
        return buildResult(objectData, detailObjectData);
    }

    @Override
    protected Result buildValidateResult() {
        if (Objects.nonNull(uniqueRuleDuplicateResult) && arg.enableUniqueCheckResult()) {
            DuplicateDataVerificationResult duplicateDataVerificationResult = DuplicateDataVerificationResult.fromUniqueRule(uniqueRule, objectDescribe, uniqueRuleDuplicateResult);
            return Result.builder()
                    .writeDB(false)
                    .duplicateDataVerificationResult(duplicateDataVerificationResult)
                    .build();
        }
        if (triggerMasterApproval) {
            IObjectDescribe masterDescribe = getRealMasterDescribe();
            IObjectData realMasterData = getRealMasterObjectData(objectData);
            IObjectData masterData = new ObjectData();
            masterData.setId(realMasterData.getId());
            masterData.setDescribeApiName(realMasterData.getDescribeApiName());
            masterData.setName(realMasterData.getName());
            MasterApprovalResult masterApprovalResult = MasterApprovalResult.builder()
                    .masterDisplayName(masterDescribe.getDisplayName())
                    .masterData(ObjectDataDocument.of(masterData))
                    .build();

            //终端老版本先把审批结果存到redis，在详情页提示出来
            if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_680)) {
                serviceFacade.saveMasterDataApprovalResult(objectData, masterApprovalResult);
            }

            //Web端和新版终端返回主对象的data和描述的displayName
            return Result.builder()
                    .triggerApproval(true)
                    .masterApprovalResult(masterApprovalResult)
                    .objectData(ObjectDataDocument.of(objectData))
                    .build();
        }
        return super.buildValidateResult();
    }

    protected void tryTriggerMasterEditApproval() {
        if (!needTriggerApprovalFlow()) {
            return;
        }
        tryTriggerMasterApproval(ApprovalFlowTriggerType.UPDATE, ObjectAction.CREATE, objectData, null,
                objectDescribes, arg.getFreeApprovalDef());
        recordLifeStatusModifyLog();
        //同步更新从对象的生命状态
        updateDetailObjectDataLifeStatus();
        if (isMasterEditApprovalStartSuccess()) {
            this.triggerMasterApproval = true;
            throw new AcceptableValidateException(buildValidateResult());
        }
    }

    protected Result buildResult(IObjectData objectData, Map<String, List<IObjectData>> detailObjectData) {
        return Result.builder()
                .objectData(ObjectDataDocument.of(objectData))
                .details(ObjectDataDocument.ofMap(detailObjectData))
                .relatedDataList(RelatedDataDocument.fromMap(relatedObjectData))
                .isDuplicate(Boolean.FALSE)
                .writeDB(writeDB)
                .triggerApproval(isApprovalFlowStartSuccess(objectData.getId()))
                .build();
    }

    protected void doSaveData() {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap(objectDescribes);
        describeMap.putAll(relatedDescribes);
        //正式更新对象和从对象
        SaveMasterAndDetailData.Arg saveArg = SaveMasterAndDetailData.Arg.builder()
                .idempotentKey(buildIdempotentKey())
                .masterObjectData(objectData)
                .detailObjectData(detailObjectData)
                .relatedObjectData(relatedObjectData)
                .objectDescribes(describeMap)
                .actionType("addAction")
                .enableRealTimeCalculateDataAuth(arg.enableRealTimeCalculateDataAuth())
                .enableUniqueCheckResult(arg.enableUniqueCheckResult())
                .realTimeCalculateDetailAuth(arg.realTimeCalculateDetailAuth())
                .build();
        SaveMasterAndDetailData.Result saveResult = null;
        try {
            saveResult = serviceFacade.saveMasterAndDetailData(actionContext.getUser(), saveArg, convertRuleDataContainer);
        } catch (MetadataDataDuplicateBusinessException e) {
            log.warn("saveMasterAndDetailData fail with MetadataDataDuplicateBusinessException", e);
            MetadataDataDuplicateBusinessException.DuplicateMessage duplicateMessage = e.getDuplicateMessage();
            DuplicateDataVerificationResult duplicateDataVerificationResult = DuplicateDataVerificationResult.fromFieldDuplicateMessage(e.getMessage(), duplicateMessage);
            BaseObjectSaveAction.Result actionResult = BaseObjectSaveAction.Result.builder()
                    .writeDB(false)
                    .duplicateDataVerificationResult(duplicateDataVerificationResult)
                    .build();
            throw new AcceptableValidateException(actionResult);
        }
        //被幂等组件拦截抛异常提示
        if (saveResult == null) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.DO_NOT_SUBMIT_REPEATEDLY));
        }
        saveOriginalDataByChangeOrder(saveArg);
        objectData = saveResult.getMasterObjectData();
        detailObjectData = saveResult.getDetailObjectData();
        relatedObjectData = saveResult.getRelatedObjectData();
    }

    private void saveOriginalDataByChangeOrder(SaveMasterAndDetailData.Arg saveArg) {
        if (!ObjectDescribeExt.of(objectDescribe).isChangeOrderObject()) {
            return;
        }
        ObjectDataDocument originalData = arg.getOriginalData();
        if (Objects.isNull(originalData)) {
            return;
        }
        infraServiceFacade.saveChangeOrderOriginalData(actionContext.getUser(), saveArg,
                originalData.toObjectData(), ObjectDataDocument.ofDataMap(arg.getOriginalDetails()));
    }

    private String buildIdempotentKey() {
        String postId = actionContext.getPostId();
        if (Strings.isNullOrEmpty(postId)) {
            return null;
        }
        return Hashing.sha256().newHasher()
                .putString("saveMasterAndDetailData", StandardCharsets.UTF_8)
                .putString(postId, StandardCharsets.UTF_8)
                .putString(actionContext.getTenantId(), StandardCharsets.UTF_8)
                .putString(actionContext.getObjectApiName() + "/" + actionContext.getActionCode(), StandardCharsets.UTF_8)
                .hash().toString();
    }

    @Override
    protected BaseObjectSaveAction.Result after(A arg, BaseObjectSaveAction.Result result) {
        super.after(arg, result);
        stopWatch.lap("super.after");

        //批量处理记录审计日志
        recordLog();
        stopWatch.lap("recordLog");

        //触发审批流
        triggerApprovalFlow();
        stopWatch.lap("triggerApprovalFlow");
        // 触发相关对象的审批流
        triggerRelatedDataApprovalFlow();
        stopWatch.lap("triggerRelatedDataApprovalFlow");

        //触发工作流
        triggerWorkFlow();
        stopWatch.lap("triggerWorkFlow");

        //sendActionMq:http://wiki.firstshare.cn/pages/viewpage.action?pageId=37088911
        sendActionMq();
        stopWatch.lap("sendActionMq");

        processWithPartner(result, arg.ignoreSendingRemind());
        stopWatch.lap("processWithPartner");

        return result;
    }

    private void sendActionMq() {
        List<IObjectData> tempSourceDataList = ObjectDataExt.copyList(getAllObjectData());
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> sendActionMq(tempSourceDataList, ObjectAction.CREATE));
        parallelTask.run();
    }

    /**
     * 1.主从审批的白名单企业，主从同时新建的从，需要触发工作流（根据生命状态）
     * 2.灰度主从同时审批的企业，需要根据触发主对象的审批流的结果，来决定是否触发从对象的工作流
     */
    protected void triggerWorkFlow() {
        if (!needTriggerWorkFlow()) {
            return;
        }

        // 主从审批的白名单企业在主从一起新建时，即使触发了主的审批，也需要触发从对象的工作流
        //批量处理触发工作流以及处理过滤器异常的数据
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            List<IObjectData> dataListForCreateWorkFlow = getDataListForCreateWorkFlow();
            doStartCreateWorkFlow(dataListForCreateWorkFlow);
        });
        // 只有没有出发审批流程才考虑触法相关对象的工作流
        if (!needTriggerApprovalFlow()) {
            parallelTask.submit(() -> processRelatedObjectData((apiName, dataList) -> doStartCreateWorkFlow(dataList)));
        }
        parallelTask.run();
    }

    protected List<IObjectData> getDataListForCreateWorkFlow() {
        List<IObjectData> dataListForCreateWorkFlow = Lists.newArrayList();
        //主从白名单或没有触发主的审批的从对象需要触发工作流
        if (AppFrameworkConfig.isInMasterDetailApprovalWhiteList(actionContext.getTenantId()) || isApprovalNotExist()) {
            CollectionUtils.nullToEmpty(detailObjectData).forEach((k, v) -> dataListForCreateWorkFlow.addAll(v));
        }
        //本对象是normal状态或者主对象是in_change状态的从对象需要触发工作流
        if (ObjectDataExt.of(objectData).isNormal() || ObjectDataExt.of(objectData).isInChange()) {
            dataListForCreateWorkFlow.add(objectData);
        }
        //从对象，主是ineffective状态且没有触发审批，主和从都需要触发工作流
        List<IObjectData> relateDataForWorkFlow = getRelateDataForCreateWorkFlow();
        if (dataListForCreateWorkFlow.stream().anyMatch(x -> x.getDescribeApiName().equals(objectData.getDescribeApiName())
                && x.getId().equals(objectData.getId()))) {
            relateDataForWorkFlow.removeIf(x -> x.getDescribeApiName().equals(objectData.getDescribeApiName())
                    && x.getId().equals(objectData.getId()));
        }
        dataListForCreateWorkFlow.addAll(relateDataForWorkFlow);
        return dataListForCreateWorkFlow;
    }

    protected void triggerApprovalFlow() {
        //如果生命状态变了，说明已经触发了主的编辑审批
        if (isLifeStatusChanged()) {
            return;
        }

        if (!needTriggerApprovalFlow()) {
            return;
        }

        //批量处理触发审批流
        //主从一起创建的时候只触发主对象的审批流（6.3的需求）
        //单独新建从触发主的审批流(680)
        if (needTriggerMasterApproval()) {
            tryTriggerMasterApproval(ApprovalFlowTriggerType.CREATE, ObjectAction.CREATE, objectData, null,
                    objectDescribes, arg.getFreeApprovalDef());
        } else {
            //有从对象的话将创建时间存起来，审批通过回调的时候需要使用
            Long detailCreateTime = CollectionUtils.notEmpty(detailDescribeMap) ? objectData.getCreateTime() : null;
            Map<String, Map<String, Object>> callbackDataMap = buildCallbackDataForAddAction(objectData, detailCreateTime);

            //自定义对象和销售订单触发新建审批，直接透传objectData
            if (isStartApprovalWithData()) {
                startApprovalFlowWithObjectData(objectDescribe, objectData, ApprovalFlowTriggerType.CREATE,
                        Maps.newHashMap(), callbackDataMap, arg.getFreeApprovalDef());
            } else {
                startApprovalFlow(Lists.newArrayList(objectData), ApprovalFlowTriggerType.CREATE, Maps.newHashMap(),
                        callbackDataMap, arg.getFreeApprovalDef());
            }
        }
        recordLifeStatusModifyLog();
        //同步更新从对象的生命状态
        updateDetailObjectDataLifeStatus();
    }

    private void triggerRelatedDataApprovalFlow() {
        if (!needTriggerApprovalFlow()) {
            return;
        }
        processRelatedObjectData((apiName, dataList) -> {
            Map<String, Map<String, Object>> callbackDataMap = dataList.stream().reduce(Maps.newHashMap(), (result, data) -> {
                Map<String, Map<String, Object>> callbackData = buildCallbackDataForAddRelatedAction(data);
                result.putAll(callbackData);
                return result;
            }, (x, y) -> {
                x.putAll(y);
                return x;
            });
            serviceFacade.batchStartApprovalAsynchronous(ApprovalFlowTriggerType.CREATE, actionContext.getUser(), dataList, Maps.newHashMap(), callbackDataMap);
        });
    }

    private Map<String, Map<String, Object>> buildCallbackDataForAddRelatedAction(IObjectData objectData) {
        Map<String, Object> callbackData = Maps.newHashMap();
        callbackData.put(ExtraDataKeys.TRIGGER_FROM, OptionInfo.FROM_RELATED);
        Map<String, Map<String, Object>> callbackDataMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(callbackData)) {
            callbackDataMap.put(objectData.getId(), callbackData);
        }
        return callbackDataMap;
    }

    private boolean isStartApprovalWithData() {
        return AppFrameworkConfig.isStartAddApprovalWithData(actionContext.getTenantId(), objectDescribe.getApiName());
    }

    private void processWithPartner(Result result, boolean ignoreSendingRemind) {
        if (ignoreSendingRemind) {
            return;
        }
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            String dataId = result.getObjectData().getId();
            String partnerId = this.arg.getObjectData().toObjectData().get("partner_id", String.class);
            if (StringUtils.isNotEmpty(dataId) && StringUtils.isNotEmpty(partnerId)) {
                Tuple<Integer, Long> newOutInfo = changePartnerAndOwner(this.actionContext.getUser(),
                        this.objectDescribe.getApiName(), Sets.newHashSet(dataId), partnerId);
                OutInfoChangeModel model = OutInfoChangeModel
                        .builder()
                        .dataId(dataId)
                        .dataName(this.arg.getObjectData().toObjectData().getName())
                        .oldOutEI(0)
                        .displayName(this.objectDescribe.getDisplayName())
                        .oldOutUserId(0)
                        .newOutEI(newOutInfo.getKey())
                        .newOutUserId(newOutInfo.getValue().intValue())
                        .isPreDefineObj("package".equals(objectDescribe.getDefineType()))
                        .build();
                outInfoChangeModelList.add(model);

                partnerRemindOutUserService.remindOutUser(actionContext.getUser(), actionContext.getObjectApiName(), 92, outInfoChangeModelList);
            }
        });
        parallelTask.run();
    }

    /*-----------begin--------------*/
    public Tuple<Integer, Long> changePartnerAndOwner(User user, String apiName, Set<String> dataIds, String partnerId) {
        return this.changePartnerAndOwner(user, apiName, dataIds, partnerId, null);
    }

    public Tuple<Integer, Long> changePartnerAndOwner(User user, String apiName, Set<String> dataIds, String partnerId, Long outOwnerId) {
        if (StringUtils.isEmpty(partnerId)) {
            return new Tuple<>();
        }
        log.debug("PartnerService changePartnerAndOwner,user {},apiName {},dataIds {},partnerId {},outOwnerId {}", user, apiName, dataIds, partnerId, outOwnerId);
        Integer outTenantId = null;
        Map<String, RelationDownstreamResult> downstreamMap = infraServiceFacade.getRelationDownstreamInfo(user.getTenantId(), Sets.newHashSet(partnerId));
        RelationDownstreamResult downstream = downstreamMap.get(partnerId);
        if (downstream != null) {
            //设置外部企业和外部负责人
            outTenantId = downstream.getDownstreamOuterTenantId();
            if (Objects.isNull(outOwnerId)) {
                outOwnerId = downstream.getRelationOwnerOuterUid();
            }
        } else {
            outOwnerId = null;
        }
        return Tuple.of(outTenantId == null ? Integer.valueOf(0) : outTenantId, outOwnerId == null ? Long.valueOf(0L) : outOwnerId);
    }

    /**
     * 这里只处理主对象的外部负责人信息
     * <p>
     * 从对象会在 {@link AbstractStandardAddAction#modifyDetailObjectDataBeforeCreate(IObjectData, Map)} 方法中同步主对象的处理
     * <p>
     * 如果当前对象为从对象 {@link BaseObjectSaveAction#modifyOwnerOrTeamMemberBeforeCreate(IObjectData, IObjectDescribe)}
     * 方法会同步数据库中主对象的外部负责人信息
     */
    private void fillOutOwnerAndOutTeamMember() {
        List<IObjectData> objectDataList = Lists.newArrayList(objectData);
        // 补充外部负责人
        doChangeOutOwnerByPartnerIdOrAccountId(objectDescribe, objectDataList);
        // 补充外部相关团队
        infraServiceFacade.fillOutTeamMember(actionContext.getUser(), objectDescribe, objectDataList);
    }

    /**
     * 通过合作伙伴、客户回填外部负责人
     *
     * @param objectDescribe
     * @param objectDataList
     */
    private void doChangeOutOwnerByPartnerIdOrAccountId(IObjectDescribe objectDescribe, List<IObjectData> objectDataList) {
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            return;
        }
        // 下游创建数据、指定当前操作人为外部负责人
        if (actionContext.getUser().isOutUser()) {
            setOutOwnerAndOutTenantId(objectDataList);
            return;
        }
        // 通过外部负责人回填外部企业
        infraServiceFacade.syncOutTenantIdFromOutUser(actionContext.getUser(), objectDescribe, objectDataList);
        if (needFillOutOwner()) {
            infraServiceFacade.fillOutOwner(actionContext.getUser(), objectDescribe, objectDataList);
        }
    }

    protected boolean needFillOutOwner() {
        return BooleanUtils.isTrue(arg.getFillOutOwner());
    }

    @Deprecated
    protected void doChangePartnerAndOwner(IObjectData objectData) {
        // doChangePartnerAndOwner(Lists.newArrayList(objectData));
    }

    /**
     * 下游用户（游客身份不处理）创建数据设置负责人、外部负责人、外部企业值
     *
     * @param objectDataList
     */
    private void setOutOwnerAndOutTenantId(List<IObjectData> objectDataList) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        User outUser = actionContext.getUser();
        if (outUser.isOutGuestUser()) {
            //下游游客身份不赋值外部负责人和外部企业
            return;
        }

        Map<String, String> partnerIdMap = Maps.newHashMap();
        // 根据 data 获取 describe
        String describeApiName = objectDataList.get(0).getDescribeApiName();
        IObjectDescribe objectDescribe = Optional.ofNullable(objectDescribes.get(describeApiName))
                .orElseGet(() -> relatedDescribes.get(describeApiName));
        // 下游创建数据，通过外部负责人
        // 回填启用了关联外部数据权限的关联客户、联系人的数据
        for (IObjectReferenceField field : ObjectDescribeExt.of(objectDescribe).getSupportRelationOuterOwnerFields()) {
            String fieldName = field.getApiName();
            String id = infraServiceFacade.getUpstreamMapperObjectId(outUser, field.getTargetApiName());
            partnerIdMap.put(fieldName, id);
        }

        ObjectDataExt.OwnerPolicy policy = ObjectDataExt.OwnerPolicy.builder()
                .allowOutUserByArg(true)
                .outUserAssignOwner(ObjectDescribeExt.of(objectDescribe).getExpectEmployeeAllocateRuleByGray(actionContext.getUser(), true))
                .defaultDataOwnerId(() -> infraServiceFacade.getDefaultDataOwnerByUser(outUser).orElse(null))
                .setOutOwnerWhenEmpty(arg.fromImport() && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUTER_IMPORT_SUPPORT_OUT_OWNER_GRAY, outUser.getTenantId()))
                .build();
        objectDataList.forEach(data -> {
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            dataExt.setOutUser(outUser, policy);
            String outTenantId = outUser.getOutTenantId();
            String outUserId = dataExt.getOutOwnerId().orElseGet(outUser::getOutUserId);
            synchronizeOutTeamMemberOwner(dataExt, outTenantId, outUserId);
            partnerIdMap.forEach(dataExt::setIfAbsent);
        });
    }

    private void synchronizeOutTeamMemberOwner(ObjectDataExt dataExt, String outTenantId, String outUserId) {
        IObjectDescribe describe = objectDescribes.get(dataExt.getDescribeApiName());
        if (Objects.nonNull(describe) && ObjectDescribeExt.of(describe).isSlaveObject()) {
            return;
        }
        // 同步外部相关团队
        dataExt.synchronizeOutTeamMemberOwner(outTenantId, outUserId);
    }

    protected void calculateCountAndFormulaFields() {
        serviceFacade.calculateForAddAction(actionContext.getUser(), objectData, detailObjectData, objectDescribes,
                !arg.calculateDefaultValue(), true);
    }

    protected void recordLog() {
        List<IObjectData> allObjectData = getAllObjectDataCopy();
        if (arg.fromImport()) {
            logAsync(allObjectData, EventType.ADD, ActionType.IMPORT_ADD);
        } else {
            logAsync(allObjectData, EventType.ADD, ActionType.Add);
        }
        // 记录相关对象的修改记录
        logRelatedData();
    }

    private void logRelatedData() {
        if (CollectionUtils.empty(relatedObjectData)) {
            return;
        }
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        processRelatedObjectData((apiName, dataList) -> {
            List<IObjectData> copyList = ObjectDataExt.copyList(dataList);
            parallelTask.submit(() -> {
                // 修改记录标记为关联对象一起新建的
                copyList.forEach(data -> data.set(OptionInfo.CREATE_FROM, OptionInfo.FROM_RELATED));
                IObjectDescribe objectDescribe = relatedDescribes.get(apiName);
                serviceFacade.log(actionContext.getUser(), EventType.ADD, ActionType.Add, objectDescribe, copyList);
            });
        });
        parallelTask.run();
    }

    protected final List<IObjectData> getAllObjectDataCopy() {
        List<IObjectData> allObjectDataCopy = detailObjectData.values().stream().flatMap(x -> x.stream().map(y -> ObjectDataExt.of(y).copy()))
                .collect(Collectors.toList());
        allObjectDataCopy.add(ObjectDataExt.of(objectData).copy());
        fillSourceInfo(allObjectDataCopy);
        return allObjectDataCopy;
    }

    private void fillSourceInfo(List<IObjectData> allObjectDataCopy) {
        if (Objects.isNull(arg.getOptionInfo())) {
            return;
        }

        String from = arg.createFrom();
        CollectionUtils.nullToEmpty(allObjectDataCopy)
                .stream()
                .filter(Objects::nonNull)
                .forEach(a -> {
                    a.set("fromDraft", arg.getOptionInfo().isFromDraft());//从草稿保存的数据
                    a.set("fromClone", arg.getOptionInfo().isFromClone());//从复制保存的数据
                    a.set("fromMapping", arg.getOptionInfo().isFromMapping());//从映射保存的数据
                    a.set("fromTransform", arg.getOptionInfo().isFromTransform());//从转换按钮保存的数据
                    a.set("fromReferenceCreate", arg.getOptionInfo().isFromReferenceCreate());//从参照新建按钮保存的数据
                    a.set("fromId", arg.getOptionInfo().getFromId());//来源数据id
                    a.set("fromApiName", arg.getOptionInfo().getFromApiName());//来源数据apiName
                    // 记录数据创建来源
                    a.set(OptionInfo.CREATE_FROM, from);
                    //记录日志相关信息
                    a.set("extendsLogInfo", arg.getOptionInfo().getExtendsLogInfo());
                });

    }

    protected void modifyRelatedObjectDataBeforeCreate() {
        processRelatedObjectData((apiName, relatedDataList) -> {
            IObjectDescribe objectDescribe = relatedDescribes.get(apiName);
            relatedDataList.forEach(objectData -> {
                //在正式更新之前,修改objectData中的一些值,比如修改时间、业务类型、signIn字段的默认值等。
                modifySystemAndPackageFieldsOfObjectDataBeforeCreate(objectData, objectDescribe);
            });
            // 同步负责人,外部负责人,归属部门,归属组织
            syncRelatedObjectDataOwner(actionContext.getUser(), relatedDataList);
            //临时文件转正式
            serviceFacade.processData(objectDescribe, relatedDataList);
        });
    }

    private void syncRelatedObjectDataOwner(User user, List<IObjectData> relatedDataList) {
        if (CollectionUtils.empty(relatedDataList)) {
            return;
        }
        List<String> owner = objectData.getOwner();
        List<String> dataOwnDepartment = objectData.getDataOwnDepartment();
        List<String> dataOwnOrganization = objectData.getDataOwnOrganization();
        Optional<String> outOwnerIdOpt = ObjectDataExt.of(objectData).getOutOwnerId();
        String outTenantId = objectData.getOutTenantId();

        relatedDataList.forEach(objectData -> {
                    objectData.setOwner(owner);
                    objectData.setDataOwnDepartment(dataOwnDepartment);
                    objectData.setDataOwnOrganization(dataOwnOrganization);
                    outOwnerIdOpt.ifPresent(outOwnerId ->
                            ObjectDataExt.of(objectData).setOutTenantAndOutOwner(outTenantId, outOwnerId));
                    setDefaultTeamMember(objectData);
                    addCreatorToTeamMember(objectData, user);
                }
        );
    }

    /**
     * 同步主对象负责人
     * 同步主对象外部负责人
     * 同步主对象生命状态（灰度控制）
     * 同步主对象锁定状态（灰度控制）
     * 同步主对象归属部门
     * 同步主对象归属组织
     * <p>
     * 图片附件、临时转正式
     *
     * @param masterObjectData
     * @param objectDataDetail
     */
    //在主从一同新建时,对于从对象的处理
    protected void modifyDetailObjectDataBeforeCreate(IObjectData masterObjectData,
                                                      Map<String, List<IObjectData>> objectDataDetail) {

        if (CollectionUtils.empty(objectDataDetail)) {
            return;
        }

        //从主对象的json中拿到ownerId
        ObjectDataExt masterDataExt = ObjectDataExt.of(masterObjectData);
        Optional<String> masterOwner = masterDataExt.getOwnerId();
        Optional<String> masterOutOwnerId = masterDataExt.getOutOwnerId();

        objectDataDetail.forEach((apiName, objectDataList) -> {
            IObjectDescribe detailDescribe = this.objectDescribes.get(apiName);
            objectDataList.forEach(detail -> {
                //循环给从对象写入一些系统信息
                modifySystemAndPackageFieldsOfObjectDataBeforeCreate(detail, detailDescribe);

                //循环将masterOwner放到从对象中
                ObjectDataExt detailDataExt = ObjectDataExt.of(detail);
                masterOwner.ifPresent(detailDataExt::setOwnerId);
                // 补充下游外部负责人信息
                masterOutOwnerId.ifPresent(outOwnerId -> {
                    detailDataExt.setOutTenantAndOutOwner(masterDataExt.getOutTenantId(), outOwnerId);
                    // 同步合作伙伴
                    if (ObjectDescribeExt.of(detailDescribe).isPRMEnabled()) {
                        detailDataExt.setPartnerId(masterDataExt.getPartnerId());
                    }
                });

                //灰度企业从对象生命状态默认和主保持一致，其他企业默认为normal
                if (ObjectDescribeExt.of(detailDescribe).isSlaveObjectCreateWithMasterAndInGrayList()) {
                    detailDataExt.setLifeStatus(masterDataExt.getLifeStatus());
                    detailDataExt.setLockStatus(masterDataExt.getLockStatus());
                } else {
                    detailDataExt.setLifeStatus(ObjectLifeStatus.NORMAL);
                }
                // 主从同时新建，从对象使用主的归属部门
                detail.setDataOwnDepartment(masterObjectData.getDataOwnDepartment());
                // 主从同时新建，从对象使用主对象的归属组织
                detail.setDataOwnOrganization(masterObjectData.getDataOwnOrganization());
                if (ObjectDescribeExt.of(detailDescribe).isExistOutDataOwnDepartment()) {
                    detailDataExt.setOutDataOwnDepartmentId(masterDataExt.getOutDataOwnDepartmentId());
                }
                if (ObjectDescribeExt.of(detailDescribe).isExistOutDataOwnOrganization()) {
                    detailDataExt.setOutDataOwnOrganizationId(masterDataExt.getOutDataOwnOrganizationId());
                }
                syncDetailDataPartner(masterDataExt.getPartnerId(), detailDescribe, detail);
            });
            //临时文件转正式
            serviceFacade.processData(detailDescribe, objectDataList);
        });

    }

    @Override
    protected String getButtonApiName() {
        if (AppFrameworkConfig.isAddEditUIActionGray(actionContext.getTenantId(), actionContext.getObjectApiName())) {
            return ObjectAction.CREATE_SAVE.getButtonApiName();
        }
        return ButtonExt.DEFAULT_ADD_BUTTON_API_NAME;
    }

    @Override
    protected final void replaceDetailDataMergeResult(MergeStateContainer sourceMergeStateContainer) {
        detailObjectData = getDetails(sourceMergeStateContainer);
        if (log.isInfoEnabled()) {
            log.info("detailDataMergeResult detailObjectData:{}", ObjectDataDocument.ofMap(detailObjectData));
        }
    }

    @NotNull
    private Map<String, List<IObjectData>> getDetails(MergeStateContainer sourceMergeStateContainer) {
        Map<String, List<IObjectData>> result = Maps.newHashMap();
        List<IObjectData> dataList = sourceMergeStateContainer.detailsToAdd();
        if (CollectionUtils.empty(dataList)) {
            return result;
        }
        for (IObjectData data : dataList) {
            IObjectDescribe describe = objectDescribes.get(data.getDescribeApiName());
            if (Objects.isNull(describe)) {
                continue;
            }
            result.computeIfAbsent(data.getDescribeApiName(), it -> Lists.newArrayList()).add(data);
        }
        result.forEach((apiName, details) -> addMasterDetailFieldIntoDetailDataList(objectData.getId(), objectDescribes.get(apiName), details));
        return result;
    }

    @Override
    protected final MergeStateContainer getSourceMergeStateContainer() {
        List<IObjectData> objectDataList = detailObjectData.values().stream().flatMap(Collection::stream).collect(Collectors.toList());
        return MergeStateContainer.of(objectDataList, Collections.emptyList(), Collections.emptyList());
    }

    @Override
    protected void setDefaultForChangeOrder(IObjectData objectData, IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        if (describeExt.isSlaveObject()) {
            return;
        }
        String originalDescribeApiName = describeExt.getOriginalDescribeApiName();
        if (!ChangeOrderConfig.changeOrderDescribeGray(actionContext.getTenantId(), originalDescribeApiName)) {
            return;
        }
        if (Strings.isNullOrEmpty(getChangeRuleName())) {
            return;
        }
        User user = actionContext.getUser();
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        objectDataExt.setDataOwnerIfAbsent(user);
        if (user.isOutUser()) {
            objectDataExt.set(ObjectDataExt.CHANGED_BY, Lists.newArrayList(user.getOutUserId()));
        } else {
            objectDataExt.set(ObjectDataExt.CHANGED_BY, Lists.newArrayList(user.getUserId()));
        }
        objectDataExt.set(ObjectDataExt.CHANGED_TIME, System.currentTimeMillis());
        objectDataExt.setChangeOrderEffectiveStatus(ChangeOrderEffectiveStatus.INEFFECTIVE);
    }

    @Override
    protected Map<String, List<SaveMasterAndDetailData.RelatedObjectData>> getPostObjectRelatedData() {
        return relatedObjectData;
    }

    protected void setDataOwnDeptIds(IObjectData objectData, List<String> deptIds) {
        if (CollectionUtils.notEmpty(objectData.getDataOwnDepartment())) {
            return;
        }
        objectData.setDataOwnDepartment(deptIds);
    }

    protected void setDataOwnOrgIds(IObjectData objectData, List<String> orgIds) {
        if (CollectionUtils.notEmpty(objectData.getDataOwnOrganization())) {
            return;
        }
        objectData.setDataOwnOrganization(orgIds);
    }

    private void modifyDataOwnOrgAndDept(IObjectData objectData, IObjectDescribe describe) {
        // 新建时归属组织（部门）不为空，使用用户填写的归属组织（部门）
        if (!needFillOrgOrDept(objectData)) {
            return;
        }
        // 从对象取关联的主对象的归属组织（部门）
        User user = actionContext.getUser();
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        if (describeExt.isSlaveObject()) {
            describeExt.getMasterDetailFieldDescribe()
                    .filter(field -> !Strings.isNullOrEmpty(objectData.get(field.getApiName(), String.class)))
                    .map(field -> serviceFacade.findObjectData(user, objectData.get(field.getApiName(), String.class), field.getTargetApiName()))
                    .ifPresent(data -> {
                        setDataOwnDeptIds(objectData, data.getDataOwnDepartment());
                        if (describeExt.isOpenOrganization()) {
                            setDataOwnOrgIds(objectData, data.getDataOwnOrganization());
                        }
                    });
            return;
        }
        // 负责人不为空，取负责人的主属部门和主属组织
        if (CollectionUtils.empty(objectData.getOwner())) {
            return;
        }

        // 灰度企业，下游新建数据。
        // 归属部门、归属组织使用下游企业对应的 客户、合作伙伴数据上的部门和组织
        boolean fillData = serviceFacade.fillDataOwnDeptAndOrgByOutUser(user, describeExt, Lists.newArrayList(objectData));
        if (fillData) {
            if (CollectionUtils.empty(objectData.getDataOwnDepartment())) {
                String fieldLabel = describeExt.getFieldLabelByName(IObjectData.DATA_OWN_DEPARTMENT);
                throw new ValidateException(I18N.text(I18NKey.AREA_NOT_NULL, fieldLabel));
            }
            if (describeExt.isOpenOrganization() && CollectionUtils.empty(objectData.getDataOwnOrganization())) {
                String fieldLabel = describeExt.getFieldLabelByName(IObjectData.DATA_OWN_ORGANIZATION);
                throw new ValidateException(I18N.text(I18NKey.AREA_NOT_NULL, fieldLabel));
            }
            return;
        }

        ObjectDataExt.of(objectData).getOwnerId()
                .ifPresent(ownerId -> {
                    OrganizationInfo organizationInfo = serviceFacade.findMainOrgAndDeptByUserId(actionContext.getTenantId(), user.getUserId(), Lists.newArrayList(ownerId));
                    fillDept(objectData, describe, organizationInfo, ownerId);
                    fillOrg(objectData, describe, organizationInfo, ownerId);
                });
    }

    private boolean needFillOrgOrDept(IObjectData objectData) {
        return CollectionUtils.empty(objectData.getDataOwnOrganization())
                || CollectionUtils.empty(objectData.getDataOwnDepartment());
    }

    private void fillOrg(IObjectData objectData, IObjectDescribe describe, OrganizationInfo organizationInfo, String ownerId) {
        if (!ObjectDescribeExt.of(describe).isOpenOrganization() || CollectionUtils.notEmpty(objectData.getDataOwnOrganization())) {
            return;
        }
        if (SUPPER_ADMIN_USER_ID.equals(ownerId)) {
            objectData.setDataOwnOrganization(Collections.singletonList(COMPANY_ID));
            return;
        }
        ObjectDataExt.of(objectData).setDataOwnOrgByDeptInfo(organizationInfo.getMainOrg(ownerId));
    }

    private void fillDept(IObjectData objectData, IObjectDescribe describe, OrganizationInfo organizationInfo, String ownerId) {
        if (CollectionUtils.notEmpty(objectData.getDataOwnDepartment())
                || UdobjGrayConfig.isAllow(UdobjGrayConfigKey.NOT_NEED_FILL_DATA_OWN_DEPT_OBJ, describe.getApiName())) {
            return;
        }
        if (SUPPER_ADMIN_USER_ID.equals(ownerId)) {
            objectData.setDataOwnDepartment(Collections.singletonList(COMPANY_ID));
            return;
        }
        ObjectDataExt.of(objectData).setDataOwnDepartmentId(organizationInfo.getMainDeptId(ownerId));
    }
}
