package com.facishare.paas.appframework.core.model.plugin;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.stereotype.Component;

/**
 * Created by zhouwr on 2022/4/14.
 */
@Component
public class PluginBeanPostProcessor implements BeanPostProcessor {

    @Autowired
    private PluginManager pluginManager;

    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof Plugin) {
            pluginManager.register((Plugin) bean);
        }
        return bean;
    }
}
