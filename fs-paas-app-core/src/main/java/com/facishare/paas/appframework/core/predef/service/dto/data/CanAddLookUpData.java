package com.facishare.paas.appframework.core.predef.service.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

public interface CanAddLookUpData {
    @Data
    class Arg {

        /**
         * 当前对象ApiName
         */
        @JSONField(name = "M1")
        private String describeApiName;

        /**
         * 当前对象数据Id
         */
        @JSONField(name = "M2")
        private String dataId;

        /**
         * 要新建的对象ApiName
         */
        @JSONField(name = "M3")
        private String targetDescribeApiName;


        /**
         * LookUp字段的FieldName
         */
        @JSONField(name = "M4")
        private String lookupFieldName;
    }

    @Builder
    @Data
    class Result {
        /**
         * 是否支持新建
         */
        @JSONField(name = "M1")
        private boolean canAdd;

        /**
         * 如果不支持新建时候的提示语
         */
        @JSONField(name = "M2")
        private String helpText;
    }
}
