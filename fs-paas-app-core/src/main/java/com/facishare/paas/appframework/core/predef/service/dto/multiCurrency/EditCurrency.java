package com.facishare.paas.appframework.core.predef.service.dto.multiCurrency;

import com.facishare.paas.appframework.metadata.MtCurrency;
import lombok.Builder;
import lombok.Data;

public interface EditCurrency {

    @Data
    class Arg {
        private String id;
        private String currencyPrefix;
        private String currencySuffix;
        private String currencySymbol;
        private String currencyUnit;
        private String currencyLabel;
        private String currencyType;

        public MtCurrency to() {
            return MtCurrency.builder()
                    .id(id)
                    .currencyLabel(currencyLabel)
                    .currencyPrefix(currencyPrefix)
                    .currencySuffix(currencySuffix)
                    .currencySymbol(currencySymbol)
                    .currencyUnit(currencyUnit)
                    .currencyType(currencyType)
                    .build();
        }
    }

    @Data
    @Builder
    class Result {

    }
}
