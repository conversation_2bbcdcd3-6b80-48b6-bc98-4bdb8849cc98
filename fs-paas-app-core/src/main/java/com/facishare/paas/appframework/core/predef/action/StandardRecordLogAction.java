package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class StandardRecordLogAction extends PreDefineAction<StandardRecordLogAction.Arg, StandardRecordLogAction.Result> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }


    @Override
    protected Result doAct(Arg arg) {
        List<IObjectData> dataList = ObjectDataDocument.ofDataList(arg.getAfterData());
        if (CollectionUtils.empty(dataList)) {
            return new Result();
        }
        if (ActionType.Add.getId().equals(arg.getActionType())) {
            recordAddLog(dataList);
        }
        if (ActionType.Modify.getId().equals(arg.getActionType())) {
            List<IObjectData> dbDataList = ObjectDataDocument.ofDataList(arg.getBeforeData());
            if (CollectionUtils.empty(dbDataList)) {
                return new Result();
            }
            Map<String, IObjectData> dbDataMap = dbDataList.stream().collect(Collectors.toMap(DBRecord::getId, x -> x, (x, y) -> x));
            recordEditLog(dataList, dbDataMap);
        }
        return new Result();
    }

    private void recordEditLog(List<IObjectData> dataList, Map<String, IObjectData> dbDataMap) {
        ParallelUtils.createBackgroundTask().submit(() -> {
            serviceFacade.updateImportLog(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, objectDescribe, dataList, dbDataMap);
        }).run();
    }

    private void recordAddLog(List<IObjectData> dataList) {
        ParallelUtils.createBackgroundTask().submit(() -> {
            serviceFacade.log(actionContext.getUser(), EventType.ADD, ActionType.Add, objectDescribe, dataList);
        }).run();
    }

    @Data
    public static class Arg {
        private List<ObjectDataDocument> beforeData;
        private List<ObjectDataDocument> afterData;
        private String actionType;
    }


    @Data
    public static class Result {
    }
}
