package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import io.protostuff.Tag;
import lombok.Builder;
import lombok.Data;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface FindLayout {
    @Data
    class Arg {
        @JSONField(name = "M2")
        private String objectDescribeApiName;

        @JSONField(name = "M3")
        private String apiName;
    }

    @Data
    @Builder
    class Result {
        @Tag(1)
        @JSONField(name = "M1")
        private LayoutDocument layout;

        @Tag(2)
        @JSONField(name = "M2")
        private ObjectDescribeDocument objectDescribeDraft;
        @Tag(2)
        @JSONField(name = "M3")
        private ObjectDescribeDocument objectDescribe;
    }
}
