package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.ObjectDataNotFoundException;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.DetailResource;
import com.facishare.paas.appframework.metadata.filter.ObjectDataFilter;
import com.facishare.paas.appframework.metadata.repository.model.MtConvertRule;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.ObjectMappingRuleDetailInfo;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;


/**
 * Created by zhaooju on 2023/4/3
 */
// todo 改造成handler
public abstract class AbstractConvertAction extends AbstractStandardAction<AbstractConvertAction.Arg, AbstractConvertAction.Result> {
    private MtConvertRule masterConvertRule;
    private List<MtConvertRule> convertRuleList;
    private IObjectDescribe sourceObjectDescribe;
    private List<IObjectDescribe> sourceDetailDescribes;
    private List<IObjectData> sourceObjectDataList;
    private List<Tuple<IObjectData, Map<String, List<IObjectData>>>> masterDetailDataTupleList;
    private List<IObjectMappingRuleInfo> mappingRuleInfoList;

    @Override
    protected void init() {
        IActionContext context = ActionContextExt.of(actionContext.getUser()).getContext();
        dataList = serviceFacade.findObjectDataByIdsExcludeInvalid(context,
                getDataPrivilegeIds(arg),
                arg.getSourceApiName());
        if (CollectionUtils.empty(dataList)) {
            throw new ObjectDataNotFoundException(I18N.text(I18NKey.DATA_NOT_USED));
        }
        sourceObjectDescribe = serviceFacade.findObjectWithoutCopy(actionContext.getTenantId(), arg.getSourceApiName());
        sourceObjectDataList = CollectionUtils.sortByGivenOrder(dataList, arg.getSourceIds(), IObjectData::getId);
        Set<String> sourceDetailApiNames = Sets.newHashSet();
        Set<String> objectApiNames = Sets.newHashSet();
        convertRuleList.forEach(rule -> {
            objectApiNames.add(rule.getTargetObjectDescribeApiName());
            objectApiNames.add(rule.getSourceObjectDescribeApiName());
            if (StringUtils.equals(rule.getSourceObjectDescribeApiName(), arg.getSourceApiName())) {
                return;
            }
            sourceDetailApiNames.add(rule.getSourceObjectDescribeApiName());
        });
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjectsWithoutCopy(actionContext.getTenantId(), objectApiNames);
        convertRuleList.removeIf(rule -> !describeMap.containsKey(rule.getSourceObjectDescribeApiName()));
        sourceDetailDescribes = Lists.newArrayList();
        describeMap.forEach((apiName, describe) -> {
            if (sourceDetailApiNames.contains(apiName)) {
                sourceDetailDescribes.add(describe);
            }
        });
        describeMap.put(sourceObjectDescribe.getApiName(), sourceObjectDescribe);
        infraServiceFacade.doValidateRuleFieldConfig(convertRuleList, describeMap);
    }

    @Override
    protected void doDataPrivilegeCheck() {
        if (skipBaseValidate()) {
            return;
        }
        List<String> funcPrivilegeCodes = getFuncPrivilegeCodes();
        if (CollectionUtils.empty(funcPrivilegeCodes)) {
            return;
        }
        serviceFacade.doDataPrivilegeCheck(actionContext.getUser(), dataList, sourceObjectDescribe,
                funcPrivilegeCodes.get(0));
    }

    private void initDetailObjectData() {
        //获取从对象数据
        this.masterDetailDataTupleList = Lists.newArrayList();
        if (arg.isSpecifiedDetails()) {
            processSpecifiedDetails();
        } else {
            processUnspecifiedDetails();
        }
    }

    private void processSpecifiedDetails() {
        Map<String, IObjectData> sourceObjectDataMap = sourceObjectDataList.stream().collect(Collectors.toMap(IObjectData::getId, Function.identity()));
        IActionContext context = ActionContextExt.of(actionContext.getUser()).getContext();
        List<String> sourceIds = sourceObjectDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
        if (CollectionUtils.empty(sourceDetailDescribes)) {
            sourceObjectDataMap.forEach((id, objectData) -> masterDetailDataTupleList.add(Tuple.of(objectData, Maps.newHashMap())));
        } else {
            List<SourceDetail> specifiedSourceDetails = CollectionUtils.sortByGivenOrder(CollectionUtils.nullToEmpty(arg.getSpecifiedSourceDetails()), sourceIds, SourceDetail::getMasterId);
            specifiedSourceDetails.forEach(sourceDetail -> {
                String masterId = sourceDetail.getMasterId();
                if (!sourceObjectDataMap.containsKey(masterId)) {
                    return;
                }
                Map<String, List<String>> details = sourceDetail.getDetails();
                Map<String, List<IObjectData>> objectDataDetails = Maps.newHashMap();
                CollectionUtils.nullToEmpty(details).forEach((apiName, ids) -> {
                    List<IObjectData> detailData = serviceFacade.findObjectDataByIdsExcludeInvalid(context, ids, apiName);
                    objectDataDetails.put(apiName, detailData);
                });
                masterDetailDataTupleList.add(Tuple.of(sourceObjectDataMap.get(masterId), objectDataDetails));
            });
        }
    }

    private void processUnspecifiedDetails() {
        Map<String, MtConvertRule> detailConvertRuleMap = convertRuleList.stream()
                .filter(rule -> !StringUtils.equals(rule.getApiName(), masterConvertRule.getApiName()))
                .collect(Collectors.toMap(MtConvertRule::getSourceObjectDescribeApiName, Function.identity(), (x, y) -> x));
        for (IObjectData objectData : sourceObjectDataList) {
            if (CollectionUtils.empty(sourceDetailDescribes)) {
                masterDetailDataTupleList.add(Tuple.of(objectData, Maps.newHashMap()));
            } else {
                Map<String, List<IObjectData>> objectDataDetails = Maps.newHashMap();
                sourceDetailDescribes.forEach(detail -> {
                    if (!detailConvertRuleMap.containsKey(detail.getApiName())) {
                        return;
                    }
                    Optional<MasterDetailFieldDescribe> masterField = ObjectDescribeExt.of(detail).getMasterDetailFieldDescribe();
                    if (!masterField.isPresent() || !Objects.equals(masterField.get().getIsCreateWhenMasterCreate(), Boolean.TRUE)) {
                        return;
                    }
                    SearchTemplateQuery searchTemplateQuery = serviceFacade.buildDetailSearchTemplateQuery(actionContext.getUser(), ObjectDescribeExt.of(detail), objectData);
                    List<IObjectData> detailObjectDataList = serviceFacade.findBySearchQuery(actionContext.getUser(), detail.getApiName(), searchTemplateQuery).getData();
                    Map<String, IObjectMappingRuleInfo> mappingRuleInfoMap = findMappingRuleInfoList().stream()
                            .filter(rule -> !StringUtils.equals(rule.getRuleApiName(), masterConvertRule.getApiName()))
                            .collect(Collectors.toMap(IObjectMappingRuleInfo::getSourceApiName, Function.identity(), (x, y) -> x));
                    List<IObjectData> detailObjectData = filterDetailObjectData(detail, detailObjectDataList, objectData, detailConvertRuleMap, mappingRuleInfoMap);
                    objectDataDetails.put(detail.getApiName(), detailObjectData);
                });
                masterDetailDataTupleList.add(Tuple.of(objectData, objectDataDetails));
            }
        }
    }

    private List<IObjectData> filterDetailObjectData(IObjectDescribe detail, List<IObjectData> detailObjectDataList, IObjectData objectData, Map<String, MtConvertRule> detailConvertRuleMap, Map<String, IObjectMappingRuleInfo> mappingRuleInfoMap) {
        MtConvertRule detailConvertRule = detailConvertRuleMap.get(detail.getApiName());
        String whereType = detailConvertRule.getWhereType();
        SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.of(detailConvertRule.getWheres());
        // 函数条件
        processQueryByFunction(whereType, detail, objectData, queryExt);
        String closeFieldApiName = detailConvertRule.getCloseFieldApiName();
        if (masterConvertRule.closeAfterAllDetailConvert() && detailConvertRule.needCloseSourceOrder()
                && ObjectDescribeExt.of(detail).getActiveFieldDescribeSilently(closeFieldApiName).isPresent()) {
            detailObjectDataList.removeIf(x -> Objects.equals(x.get(closeFieldApiName), detailConvertRule.convertCloseFieldValue(detail, masterConvertRule.getName())));
        }
        removeByRuleRecordTypeConfig(detailObjectDataList, mappingRuleInfoMap.get(detail.getApiName()));
        // 字段条件
        ObjectDataFilter dataFilter = ObjectDataFilter.builder()
                .describeExt(ObjectDescribeExt.of(detail))
                .queryExt(queryExt)
                .filterLabel(detailConvertRule.getName())
                .build();
        return dataFilter.doFilter(detailObjectDataList);
    }

    private void removeByRuleRecordTypeConfig(List<IObjectData> detailObjectDataList, IObjectMappingRuleInfo mappingRuleInfo) {
        if (Objects.isNull(mappingRuleInfo) || CollectionUtils.empty(mappingRuleInfo.getFieldMapping())) {
            return;
        }
        mappingRuleInfo.getFieldMapping().stream()
                .filter(fieldMap -> StringUtils.equals(fieldMap.getSourceFieldName(), MultiRecordType.RECORD_TYPE))
                .findFirst()
                .ifPresent(x -> {
                    List<String> recordTypeList = CollectionUtils.nullToEmpty(x.getOptionMapping()).stream()
                            .map(IObjectMappingRuleEnumInfo::getSourceEnumCode)
                            .collect(Collectors.toList());
                    detailObjectDataList.removeIf(data -> !recordTypeList.contains(data.getRecordType()));
                });
    }

    private void removeMaskFieldValue(User user, IObjectData objectData, Map<String, List<IObjectData>> details,
                                      IObjectDescribe describe, List<IObjectDescribe> detailDescribes) {
        Map<String, IObjectDescribe> describeMap = Maps.newHashMap();
        describeMap.put(describe.getApiName(), describe);
        detailDescribes.forEach(x -> describeMap.put(x.getApiName(), x));
        serviceFacade.removeMaskFieldValue(user, objectData, details, describeMap);
    }

    @Override
    protected void before(Arg arg) {
        initConvertRules();
        stopWatch.lap("initConvertRules");
        validate(arg);
        stopWatch.lap("validate");
        super.before(arg);
        validateRuleCondition();
        stopWatch.lap("validateRuleCondition");
    }

    private void validate(Arg arg) {
        List<String> sourceIds = arg.getSourceIds();
        int maxNum = FieldManyMaxConfig.getObjectReferenceManyMaxLimit(actionContext.getTenantId(), masterConvertRule.getTargetObjectDescribeApiName());
        if (arg.isDoublePull()) {
            if (masterConvertRule.isSimplyScene()) {
                if (sourceIds.size() != 1) {
                    throw new ValidateException(I18NExt.text(I18NKey.SIMPLE_SCENE_ONLY_SELECT_ONE_SOURCE_DATA));
                }
                String id = ObjectDataExt.of(arg.getObjectData()).get(masterConvertRule.getAssociatedFieldApiName(), String.class);
                if (StringUtils.isNotEmpty(id) && !Objects.equals(sourceIds.get(0), id)) {
                    throw new ValidateException(I18NExt.text(I18NKey.SIMPLE_SCENE_ONLY_SELECT_LOOKUP_SOURCE_DATA));
                }
            } else if (masterConvertRule.isAdvancedScene()) {
                List<String> ids = CollectionUtils.nullToEmpty(ObjectDataExt.of(arg.getObjectData()).get(masterConvertRule.getAssociatedFieldApiName(), List.class));
                if ((sourceIds.size() + ids.size()) > maxNum) {
                    throw new ValidateException(I18NExt.text(I18NKey.CONVERT_RULE_SOURCE_IDS_TOO_MUCH, maxNum - ids.size()));
                }
            } else {
                throw new ValidateException(I18NExt.text(I18NKey.CONVERT_RULE_SCENE_TYPE_ERROR));
            }
        } else {
            if (sourceIds.size() > maxNum) {
                throw new ValidateException(I18NExt.text(I18NKey.CONVERT_RULE_SOURCE_IDS_TOO_MUCH, maxNum));
            }
        }
    }


    private void initConvertRules() {
        convertRuleList = infraServiceFacade.findConvertRuleInInternalObjByApiName(actionContext.getUser(), arg.getRuleApiName(), true);
        if (CollectionUtils.empty(convertRuleList)) {
            throw new ValidateException(I18NExt.text(I18NKey.CONVERT_RULE_DISABLE_OR_DELETE));
        }
        masterConvertRule = convertRuleList.stream()
                .filter(a -> StringUtils.isEmpty(a.getMasterConvertRuleApiName()))
                .findFirst().orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.CONVERT_RULE_DISABLE_OR_DELETE)));
        sourceObjectDescribe = serviceFacade.findObjectWithoutCopy(actionContext.getTenantId(), arg.getSourceApiName());
    }


    @Override
    protected Map<String, Object> getArgs() {
        Map<String, Object> args = super.getArgs();
        args.put("convertArgs", arg);
        return args;
    }

    @Override
    protected Result doAct(Arg arg) {
        initDetailObjectData();
        stopWatch.lap("initDetailObjectData");
        //不映射掩码字段
        List<MasterSlaveDataModel> masterSlaveDataModelList = Lists.newArrayList();
        if (masterConvertRule.isSimplyScene()) {
            oneToOne(masterSlaveDataModelList, masterDetailDataTupleList, Lists::newArrayList);
        } else if (masterConvertRule.isAdvancedScene()) {
            if (masterConvertRule.isManyToOne()) {
                manyToOne(masterSlaveDataModelList);
            } else if (masterConvertRule.isGroupingByRule()) {
                splitOrdersByFields(arg, masterSlaveDataModelList);
            }
        } else {
            throw new ValidateException(I18NExt.text(I18NKey.CONVERT_RULE_SCENE_TYPE_ERROR));
        }
        stopWatch.lap("doConvert end");
        Result result = Result.builder()
                .masterSlaveDataList(MasterSlaveDataDocument.ofList(masterSlaveDataModelList))
                .build();
        if (BooleanUtils.isTrue(arg.getIncludeRuleConfig())) {
            result.setMappingRuleInfoList(MappingRuleDocument.fromList(mappingRuleInfoList));
        }
        return result;
    }

    private void splitOrdersByFields(Arg arg, List<MasterSlaveDataModel> masterSlaveDataModelList) {
        if (arg.isDoublePull()) {
            throw new ValidateException(I18NExt.text(I18NKey.CONVERT_RULE_DOUBLE_PULL_NOT_SUPPORT_GROUPING_BY_RULE));
        }
        MtConvertRule.CombinedFields combinedByFields = masterConvertRule.getCombinedByFields();
        if (Objects.isNull(combinedByFields)) {
            manyToOne(masterSlaveDataModelList);
        } else {
            List<MtConvertRule.Fields> fields = combinedByFields.getFields();
            String apiName = sourceObjectDescribe.getApiName();
            List<String> masterCombinedFields = CollectionUtils.nullToEmpty(fields).stream()
                    .filter(field -> StringUtils.equals(apiName, field.getObjectApiName()))
                    .map(MtConvertRule.Fields::getFieldApiName)
                    .collect(Collectors.toList());
            List<String> detailCombinedFields = Lists.newArrayList();
            Optional<MtConvertRule> detailConvertRule = convertRuleList.stream()
                    .filter(rule -> !StringUtils.equals(rule.getApiName(), masterConvertRule.getApiName()))
                    .findFirst();
            detailConvertRule.ifPresent(rule -> {
                List<String> fieldApiNames = CollectionUtils.nullToEmpty(fields).stream()
                        .filter(field -> StringUtils.equals(rule.getSourceObjectDescribeApiName(), field.getObjectApiName()))
                        .map(MtConvertRule.Fields::getFieldApiName)
                        .collect(Collectors.toList());
                detailCombinedFields.addAll(fieldApiNames);
            });
            if (CollectionUtils.empty(masterCombinedFields) && CollectionUtils.empty(detailCombinedFields)) {
                manyToOne(masterSlaveDataModelList);
            } else {
                splitOrdersByFields(masterSlaveDataModelList, masterCombinedFields, detailCombinedFields, detailConvertRule.orElse(null));
            }
        }
    }

    private void splitOrdersByFields(List<MasterSlaveDataModel> masterSlaveDataModelList, List<String> masterCombinedFields, List<String> detailCombinedFields, MtConvertRule detailConvertRule) {
        List<Tuple<IObjectData, Map<String, List<IObjectData>>>> sourceDetailObjectDataList = Lists.newArrayList();
        Map<String, List<String>> fromMasterIdsMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(masterCombinedFields)) {
            List<Function<IObjectData, Object>> groupByFunctions = Lists.newArrayList();
            masterCombinedFields.forEach(field -> groupByFunctions.add(x -> x.get(field)));
            Map<List<Object>, List<IObjectData>> sourceObjectDataListGrouped = sourceObjectDataList.stream()
                    .collect(Collectors.groupingBy(p -> groupByFunctions.stream().map(function -> function.apply(p)).collect(Collectors.toList())));
            sourceObjectDataListGrouped.forEach((groupKey, sourceDataList) -> {
                Map<String, List<IObjectData>> sourceDetailObjectData = Maps.newHashMap();
                List<String> masterIds = Lists.newArrayList();
                sourceDataList.forEach(sourceObjectData -> {
                    masterDetailDataTupleList.stream().filter(tuple -> Objects.equals(sourceObjectData.getId(), tuple.getKey().getId()))
                            .findFirst()
                            .ifPresent(tuple -> {
                                Map<String, List<IObjectData>> details = tuple.getValue();
                                details.forEach((detailApiName, detailDataList) -> sourceDetailObjectData.computeIfAbsent(detailApiName, k -> Lists.newArrayList()).addAll(CollectionUtils.nullToEmpty(detailDataList)));
                            });
                    masterIds.add(sourceObjectData.getId());
                });
                fromMasterIdsMap.put(sourceDataList.get(0).getId(), masterIds);
                if (CollectionUtils.empty(detailCombinedFields)) {
                    sourceDetailObjectDataList.add(Tuple.of(sourceDataList.get(0), sourceDetailObjectData));
                } else {
                    if (Objects.isNull(detailConvertRule)) {
                        return;
                    }
                    String detailDescribeApiName = detailConvertRule.getSourceObjectDescribeApiName();
                    Map<List<Object>, List<IObjectData>> sourceDetailObjectDataListGrouped = detailsGroupByFields(detailCombinedFields, sourceDetailObjectData, detailDescribeApiName);
                    if (CollectionUtils.empty(sourceDetailObjectDataListGrouped)) {
                        sourceDetailObjectDataList.add(Tuple.of(sourceDataList.get(0), sourceDetailObjectData));
                    } else {
                        sourceDetailObjectDataListGrouped.forEach((detailKey, detailValue) -> {
                            Map<String, List<IObjectData>> detailDataMap = Maps.newHashMap();
                            detailValue.forEach(detailObjectData -> detailDataMap.computeIfAbsent(detailDescribeApiName, k -> Lists.newArrayList()).add(detailObjectData));
                            sourceDetailObjectDataList.add(Tuple.of(sourceDataList.get(0), detailDataMap));
                        });
                    }
                }
            });
        } else if (CollectionUtils.notEmpty(detailCombinedFields)) {
            if (Objects.isNull(detailConvertRule)) {
                return;
            }
            String detailDescribeApiName = detailConvertRule.getSourceObjectDescribeApiName();
            Map<String, List<IObjectData>> sourceDetailObjectData = Maps.newHashMap();
            masterDetailDataTupleList.forEach(tuple ->
                    CollectionUtils.nullToEmpty(tuple.getValue()).forEach((detailApiName, detailDataList) ->
                            sourceDetailObjectData.computeIfAbsent(detailApiName, k -> Lists.newArrayList()).addAll(CollectionUtils.nullToEmpty(detailDataList))));
            Map<String, IObjectData> masterObjectDataMap = sourceObjectDataList.stream().collect(Collectors.toMap(DBRecord::getId, x -> x));
            List<String> fromMasterIds = Lists.newArrayList(masterObjectDataMap.keySet());
            Map<List<Object>, List<IObjectData>> sourceDetailObjectDataListGrouped = detailsGroupByFields(detailCombinedFields, sourceDetailObjectData, detailDescribeApiName);
            sourceDetailObjectDataListGrouped.forEach((detailKey, detailValue) -> {
                Map<String, List<IObjectData>> detailDataMap = Maps.newHashMap();
                Set<String> masterIdSet = Sets.newHashSet();
                IObjectDescribe detailDescribe = sourceDetailDescribes.stream()
                        .filter(describe -> StringUtils.equals(describe.getApiName(), detailDescribeApiName))
                        .findFirst().orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));
                detailValue.forEach(detailObjectData -> {
                    detailDataMap.computeIfAbsent(detailDescribeApiName, k -> Lists.newArrayList()).add(detailObjectData);
                    masterIdSet.add(detailObjectData.get(ObjectDescribeExt.of(detailDescribe).getMasterDetailFieldNameOrThrowException(sourceObjectDescribe.getApiName()), String.class));
                });
                List<String> masterIds = CollectionUtils.sortByGivenOrder(Lists.newArrayList(masterIdSet), fromMasterIds, x -> x);
                fromMasterIdsMap.put(masterIds.get(0), masterIds);
                sourceDetailObjectDataList.add(Tuple.of(masterObjectDataMap.get(masterIds.get(0)), detailDataMap));
            });
        }
        oneToOne(masterSlaveDataModelList, sourceDetailObjectDataList, fromMasterIdsMap::get);
    }

    private Map<List<Object>, List<IObjectData>> detailsGroupByFields(List<String> detailCombinedFields, Map<String, List<IObjectData>> sourceDetailObjectData, String detailDescribeApiName) {
        List<Function<IObjectData, Object>> detailGroupByFunctions = Lists.newArrayList();
        detailCombinedFields.forEach(field -> detailGroupByFunctions.add(x -> x.get(field)));
        return CollectionUtils.nullToEmpty(sourceDetailObjectData.get(detailDescribeApiName))
                .stream()
                .collect(Collectors.groupingBy(p -> detailGroupByFunctions.stream().map(function -> function.apply(p)).collect(Collectors.toList())));
    }

    private void oneToOne(List<MasterSlaveDataModel> masterSlaveDataModelList, List<Tuple<IObjectData, Map<String, List<IObjectData>>>> masterDetailDataTupleList, Function<String, List<String>> fromMasterIds) {
        Map<String, Integer> splitOrderMaxNumLimitMap = AppFrameworkConfig.getSplitOrderMaxNumLimit();
        Integer splitOrderMaxNumLimit = splitOrderMaxNumLimitMap.getOrDefault(actionContext.getTenantId(), 20);
        if (masterDetailDataTupleList.size() > splitOrderMaxNumLimit) {
            throw new ValidateException(I18NExt.text(I18NKey.CONVERT_RULE_SPLIT_ORDER_EXCEED_MAX_LIMIT, splitOrderMaxNumLimit));
        }
        masterDetailDataTupleList = CollectionUtils.sortByGivenOrder(masterDetailDataTupleList, arg.getSourceIds(), x -> x.getKey().getId());
        AtomicInteger count = new AtomicInteger();
        for (Tuple<IObjectData, Map<String, List<IObjectData>>> dataMapTuple : masterDetailDataTupleList) {
            IObjectData sourceObjectData = dataMapTuple.getKey();
            Map<String, List<IObjectData>> sourceDetailObjectData = dataMapTuple.getValue();
            List<String> sourceMasterIds = fromMasterIds.apply(sourceObjectData.getId());
            ObjectMappingService.MappingDataResult result = doConvert(sourceObjectData, sourceDetailObjectData, sourceMasterIds);
            stopWatch.lap("doConvert:" + count.incrementAndGet());
            IObjectData targetObjectData = result.getObjectData();
            Map<String, List<IObjectData>> targetDetails = result.getDetails();
            MasterSlaveDataModel masterSlaveDataModel = MasterSlaveDataModel.builder()
                    .fromMasterIds(sourceMasterIds)
                    .fromDetails(Lists.newArrayList(buildDetailResource(sourceObjectData, sourceDetailObjectData)))
                    .objectData(targetObjectData)
                    .details(targetDetails)
                    .build();
            masterSlaveDataModelList.add(masterSlaveDataModel);
        }
    }

    private void doublePullMergeDetails(Map<String, List<ObjectDataDocument>> details, Map<String, List<IObjectData>> targetDetails) {
        if (CollectionUtils.empty(details)) {
            return;
        }
        details.forEach((apiName, detailDataList) -> {
            List<IObjectData> detailData = CollectionUtils.nullToEmpty(detailDataList).stream()
                    .map(ObjectDataDocument::toObjectData).collect(Collectors.toList());
            if (CollectionUtils.empty(detailData)) {
                return;
            }
            detailData.addAll(CollectionUtils.nullToEmpty(targetDetails.get(apiName)));
            targetDetails.put(apiName, detailData);
        });
    }

    private void manyToOne(List<MasterSlaveDataModel> masterSlaveDataModelList) {
        List<String> masterIds = Lists.newArrayList();
        Map<String, List<IObjectData>> sourceDetailObjectData = Maps.newHashMap();
        MasterSlaveDataModel masterSlaveDataModel = MasterSlaveDataModel.builder().build();
        List<DetailResource> detailResourceList = Lists.newArrayList();
        masterDetailDataTupleList = CollectionUtils.sortByGivenOrder(masterDetailDataTupleList, arg.getSourceIds(), x -> x.getKey().getId());
        for (Tuple<IObjectData, Map<String, List<IObjectData>>> objectDataMapTuple : masterDetailDataTupleList) {
            IObjectData objectData = objectDataMapTuple.getKey();
            masterIds.add(objectData.getId());
            Map<String, List<IObjectData>> detailMap = objectDataMapTuple.getValue();
            detailMap.forEach((apiName, detailDataList) -> sourceDetailObjectData.computeIfAbsent(apiName, k -> Lists.newArrayList()).addAll(detailDataList));
            detailResourceList.add(buildDetailResource(objectData, detailMap));
        }
        ObjectMappingService.MappingDataResult result = doConvert(masterDetailDataTupleList.get(0).getKey(), sourceDetailObjectData, masterIds);
        stopWatch.lap("doConvert");
        masterSlaveDataModel.setFromMasterIds(masterIds);
        masterSlaveDataModel.setFromDetails(detailResourceList);
        masterSlaveDataModel.setObjectData(result.getObjectData());
        masterSlaveDataModel.setDetails(result.getDetails());
        masterSlaveDataModelList.add(masterSlaveDataModel);
    }

    private DetailResource buildDetailResource(IObjectData sourceObjectData, Map<String, List<IObjectData>> sourceDetailObjectData) {
        List<DetailResource.Detail> modelDetail = Lists.newArrayList();
        sourceDetailObjectData.forEach((apiName, detailDataList) -> {
            if (CollectionUtils.empty(detailDataList)) {
                return;
            }
            List<String> ids = detailDataList.stream().map(IObjectData::getId).collect(Collectors.toList());
            DetailResource.Detail detail = DetailResource.Detail.builder()
                    .apiName(apiName)
                    .ids(ids)
                    .build();
            modelDetail.add(detail);
        });
        return DetailResource.builder()
                .masterId(sourceObjectData.getId())
                .details(modelDetail)
                .build();
    }

    private ObjectMappingService.MappingDataResult doConvert(IObjectData sourceObjectData, Map<String, List<IObjectData>> sourceDetailObjectData, List<String> fromMasterIds) {
        removeMaskFieldValue(actionContext.getUser(), sourceObjectData, sourceDetailObjectData, sourceObjectDescribe, sourceDetailDescribes);
        ObjectMappingService.MappingDataArg mappingDataArg = new ObjectMappingService.MappingDataArg();
        mappingDataArg.setDetails(sourceDetailObjectData);
        mappingDataArg.setRuleApiName(masterConvertRule.getRuleApiName());
        mappingDataArg.setObjectData(sourceObjectData);
        List<IObjectMappingRuleInfo> mappingRuleInfos = findMappingRuleInfoList();
        addAssociatedFieldName2FieldMapping(mappingRuleInfos);
        return infraServiceFacade.mappingDataByRuleConfig(actionContext.getUser(), mappingDataArg, mappingRuleInfos, processConvertMappingResult(fromMasterIds));
    }

    private UnaryOperator<IObjectMappingParams> processConvertMappingResult(List<String> fromMasterIds) {
        return result -> {
            ObjectDataExt masterObjectData = arg.isDoublePull() ? ObjectDataExt.of(arg.getObjectData()) : ObjectDataExt.of(result.getObjectData());
            doublePullMergeObjectData(fromMasterIds, result.getObjectData(), masterObjectData);
            doublePullMergeDetails(arg.getDetails(), result.getDetailObjectData());
            result.setObjectData(masterObjectData.getObjectData());
            return result;
        };
    }

    private void doublePullMergeObjectData(List<String> fromMasterIds, IObjectData objectData, ObjectDataExt objectDataExt) {
        String associatedFieldApiName = masterConvertRule.getAssociatedFieldApiName();
        if (masterConvertRule.isAdvancedScene()) {
            if (arg.isDoublePull()) {
                List<String> ids = Lists.newArrayList();
                ids.addAll(CollectionUtils.nullToEmpty(objectDataExt.get(associatedFieldApiName, List.class)));
                fromMasterIds.stream().filter(masterId -> !ids.contains(masterId)).forEach(ids::add);
                objectDataExt.set(associatedFieldApiName, ids);
            } else {
                objectDataExt.set(associatedFieldApiName, fromMasterIds);
            }
        }
        if (masterConvertRule.isSimplyScene() && arg.isDoublePull()) {
            objectDataExt.set(associatedFieldApiName, objectData.get(associatedFieldApiName));
        }
    }

    private List<IObjectMappingRuleInfo> findMappingRuleInfoList() {
        if (Objects.nonNull(mappingRuleInfoList)) {
            return mappingRuleInfoList;
        }
        mappingRuleInfoList = infraServiceFacade.findConvertRuleByApiName(actionContext.getUser(), masterConvertRule.getRuleApiName());
        return mappingRuleInfoList;
    }

    private void addAssociatedFieldName2FieldMapping(List<IObjectMappingRuleInfo> objectMappingRuleInfos) {
        CollectionUtils.nullToEmpty(objectMappingRuleInfos).forEach(rule -> {
            List<IObjectMappingRuleDetailInfo> fieldMapping = rule.getFieldMapping();
            convertRuleList.stream()
                    .filter(x -> StringUtils.equals(x.getApiName(), rule.getRuleApiName()))
                    .findFirst()
                    .ifPresent(convertRule -> {
                        String associatedFieldApiName = convertRule.getAssociatedFieldApiName();
                        if (StringUtils.isNotEmpty(associatedFieldApiName)) {
                            IObjectMappingRuleDetailInfo ruleDetailInfo = new ObjectMappingRuleDetailInfo();
                            ruleDetailInfo.setSourceFieldName(IObjectData.ID);
                            ruleDetailInfo.setTargetFieldName(associatedFieldApiName);
                            fieldMapping.add(ruleDetailInfo);
                            rule.setFieldMapping(fieldMapping);
                        }
                    });
        });
    }

    private void validateRuleCondition() {
        List<String> noMatchIds = Lists.newArrayList();
        sourceObjectDataList.forEach(sourceObjectData -> {
            List<Wheres> wheres = masterConvertRule.toWheres(sourceObjectDescribe);
            IFilter filter = FilterExt.of(Operator.EQ, IObjectData.ID, sourceObjectData.getId()).getFilter();
            SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.ofWheresAndFilter(wheres, filter);
            if (masterConvertRule.needCloseSourceOrder()
                    && Objects.equals(sourceObjectData.get(masterConvertRule.getCloseFieldApiName()), masterConvertRule.convertCloseFieldValue(sourceObjectDescribe, masterConvertRule.getName()))) {
                noMatchIds.add(sourceObjectData.getId());
                return;
            }
            processQueryByFunction(masterConvertRule.getWhereType(), sourceObjectDescribe, sourceObjectData, queryExt);
            // 字段条件
            ObjectDataFilter dataFilter = ObjectDataFilter.builder()
                    .describeExt(ObjectDescribeExt.of(sourceObjectDescribe))
                    .queryExt(queryExt)
                    .filterLabel(masterConvertRule.getName())
                    .build();
            List<IObjectData> dataList = dataFilter.doFilter(Lists.newArrayList(sourceObjectData));
            if (CollectionUtils.empty(dataList)) {
                noMatchIds.add(sourceObjectData.getId());
            }
        });
        sourceObjectDataList.removeIf(x -> noMatchIds.contains(x.getId()));
        if (CollectionUtils.empty(sourceObjectDataList)) {
            block();
        }
    }

    private void processQueryByFunction(String whereType, IObjectDescribe sourceObjectDescribe, IObjectData sourceObjectData, SearchTemplateQueryExt queryExt) {
        // 函数条件
        if (!MtConvertRule.WHERE_TYPE_FUNCTION.equals(whereType)) {
            return;
        }
        serviceFacade.getFunctionLogicService().handleFiltersByValueType(actionContext.getUser(), sourceObjectDescribe.getApiName(), queryExt.toSearchTemplateQuery(), () -> {
            Tuple<IObjectData, Map<String, List<IObjectData>>> result = Tuple.of(sourceObjectData, Maps.newHashMap());
            infraServiceFacade.fillQuoteValueVirtualField(actionContext.getUser(), result.getKey(), result.getValue());
            return result;
        });
        stopWatch.lap("processQueryByFunction");
    }

    private void block() {
        if (StringUtils.isNotBlank(masterConvertRule.getMessage())) {
            throw new ValidateException(masterConvertRule.getMessage());
        }
        String sourceObjectDescribeApiName = masterConvertRule.getSourceObjectDescribeApiName();
        Map<String, String> objectDisplayName = serviceFacade.findDisplayNameByApiNames(actionContext.getTenantId(), Lists.newArrayList(sourceObjectDescribeApiName));
        throw new ValidateException(I18NExt.text(I18NKey.DATA_YOU_SELECTED_NOT_MATCH, objectDisplayName.get(sourceObjectDescribeApiName)));
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        if (StringUtils.equals(actionContext.getObjectApiName(), masterConvertRule.getTargetObjectDescribeApiName())) {
            return Lists.newArrayList(ObjectAction.REFERENCE_CREATE.getActionCode());
        }
        return Lists.newArrayList(ObjectAction.TRANSFORM.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getSourceIds();
    }

    @Override
    protected IObjectData getPreObjectData() {
        return sourceObjectDataList.get(0);
    }

    @Override
    protected IObjectData getPostObjectData() {
        return sourceObjectDataList.get(0);
    }

    @Override
    protected String getButtonApiName() {
        if (StringUtils.equals(actionContext.getObjectApiName(), masterConvertRule.getTargetObjectDescribeApiName())) {
            return ObjectAction.REFERENCE_CREATE.getButtonApiName();
        }
        if (StringUtils.equals(actionContext.getObjectApiName(), masterConvertRule.getSourceObjectDescribeApiName())) {
            return ObjectAction.TRANSFORM.getButtonApiName();
        }
        return null;
    }

    @Data
    public static class Arg {
        @Deprecated
        private String sourceId;
        private List<String> sourceIds;
        private String ruleApiName;
        private String sourceApiName;
        private ObjectDataDocument objectData;
        private Map<String, List<ObjectDataDocument>> details;
        private Boolean specifiedDetails;
        private List<SourceDetail> specifiedSourceDetails;
        private Boolean includeRuleConfig;

        public List<String> getSourceIds() {
            List<String> sourceIdList = CollectionUtils.nullToEmpty(this.sourceIds);
            if (StringUtils.isEmpty(this.sourceId)) {
                return sourceIdList;
            }
            return Lists.newArrayList(this.sourceId);
        }

        public boolean isSpecifiedDetails() {
            return Boolean.TRUE.equals(this.specifiedDetails);
        }

        public boolean isDoublePull() {
            return Objects.nonNull(this.objectData);
        }
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SourceDetail {
        private String masterId;
        private Map<String, List<String>> details;

    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private List<MasterSlaveDataDocument> masterSlaveDataList;
        private List<MappingRuleDocument> mappingRuleInfoList;
    }
}
