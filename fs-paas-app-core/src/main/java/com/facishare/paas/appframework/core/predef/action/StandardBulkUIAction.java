package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.button.action.ButtonExecutorContext;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.metadata.UdefActionExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;


@Slf4j
/**
 * create by zhaoju on 2020/06/12
 */
public class StandardBulkUIAction extends AbstractCustomButtonAction<StandardBulkUIAction.Arg> {

    @Override
    protected String getObjectDataId() {
        return null;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(arg.getButtonApiName());
    }

    @Override
    protected String getButtonApiName() {
        return arg.getButtonApiName();
    }

    @Override
    protected Map<String, Object> getArgs() {
        return CollectionUtils.nullToEmpty(arg.getArgs());
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        callPreFunction(arg);
    }

    private void callPreFunction(Arg arg) {
        ButtonExecutor.Arg executorArg = ButtonExecutor.Arg.builder()
                .buttonApiName(getButtonApiName())
                .describeApiName(actionContext.getObjectApiName())
                .dataIds(arg.getDataIds())
                .skipValidationFunction(arg.isSkipPreFunction())
                .triggerBatchFunc(true)
                .build();
        log.debug("callValidationFunction arg:{}", executorArg);
        validatedFunctionResult = infraServiceFacade.triggerValidationFunction(actionContext.getUser(), executorArg);
        log.debug("callValidationFunction result:{}", validatedFunctionResult);
        processValidatedFunctionResult();
    }

    @Override
    protected ButtonExecutor.Result startCustomButton() {
        ButtonExecutor.Arg buttonExecutorArg = ButtonExecutor.Arg.of(arg.getDataIds(), getArgs());
        ButtonExecutorContext buttonExecutorContext = ButtonExecutorContext.builder()
                .user(actionContext.getUser())
                .describe(objectDescribe)
                .button(udefButton)
                .stage(UdefActionExt.BULK)
                .build();
        return infraServiceFacade.startCustomButton(buttonExecutorArg, buttonExecutorContext);
    }
    @Override
    protected Set<String> getDataIds() {
        List<String> dataIds = CollectionUtils.nullToEmpty(arg.getDataIds());
        return Sets.newHashSet(dataIds);
    }

    @Override
    protected boolean skipValidate() {
        return true;
    }

    @Override
    protected void initObjectData() {
    }

    @Override
    protected boolean skipTriggerApprovalFlow() {
        return true;
    }

    @Override
    protected boolean skipLockValidate() {
        return false;
    }

    @Data
    public static class Arg {
        private boolean skipPreFunction = false;
        private String buttonApiName;
        private List<String> dataIds;
        private ObjectDataDocument args;
    }
}
