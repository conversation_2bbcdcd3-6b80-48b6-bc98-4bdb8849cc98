package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.dto.GetPrintTemplate;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.core.predef.action.StandardExportByPrintTemplateVerifyAction.Arg;
import com.facishare.paas.appframework.core.predef.action.StandardExportByPrintTemplateVerifyAction.Result;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Objects;

/**
 * 走打印的校验逻辑
 */
public class StandardExportByPrintTemplateVerifyAction extends PreDefineAction<Arg, Result> {


    @Override
    protected Result doAct(Arg arg) {

        //校验模板
        doVerify();
        //动作前验证
        doValidatePreAction();

        return Result.builder()
                .success(true)
                .build();
    }

    private void doVerify() {
        GetPrintTemplate.Arg body = GetPrintTemplate.Arg.builder()
                .templateId(this.arg.getPrintTemplateId())
                .build();
        GetPrintTemplate.Result result = infraServiceFacade.findPrintTemplate(actionContext.getUser(), body);
        if (Objects.isNull(result) || Objects.isNull(result.getPrintTemplateVO())) {
            log.warn("Export print document verify Error, tenantId:{}, userId:{}, result:{}.", actionContext.getTenantId(),
                    actionContext.getUser().getUserId(), result);
            throw new ValidateException(I18N.text(I18NKey.EXCEL_PRINT_TEMPLATE_NOT_EXIST));
        }
    }

    private void doValidatePreAction() {
        super.findDescribe();
        super.init();
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return arg.getDataIdList();
    }

    @Data
    public static class Arg {
        @JsonProperty("dataIdList")
        @SerializedName("dataIdList")
        private List<String> dataIdList;
        @JsonProperty("object_describe_api_name")
        @SerializedName("object_describe_api_name")
        private String objectDescribeApiName;
        @JsonProperty("print_template_id")
        @SerializedName("print_template_id")
        private String printTemplateId;
        @JsonProperty("bulk_export_type")
        @SerializedName("bulk_export_type")
        private String bulkExportType;
        @JsonProperty("result_processor")
        @SerializedName("result_processor")
        private String resultProcessor;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private boolean success;
        private String message;
    }
}
