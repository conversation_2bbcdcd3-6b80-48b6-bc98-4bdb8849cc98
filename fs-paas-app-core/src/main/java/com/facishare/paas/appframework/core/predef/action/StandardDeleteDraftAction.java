package com.facishare.paas.appframework.core.predef.action;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/11 2:51 下午
 */
public class StandardDeleteDraftAction extends AbstractStandardAction<StandardDeleteDraftAction.Arg,
        StandardDeleteDraftAction.Result> {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return null;
    }

    @Override
    protected void before(Arg arg) {
    }

    @Override
    protected Result doAct(Arg arg) {
        infraServiceFacade.deleteDraftByIds(actionContext.getTenantId(), arg.getDraftIdList());
        return Result.builder().success(true).build();
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Result {
        private boolean success;
    }

    @Data
    public static class Arg {
        @JSONField(name = "draft_id_list")
        @JsonProperty("draft_id_list")
        @SerializedName("draft_id_list")
        private List<String> draftIdList;
    }
}
