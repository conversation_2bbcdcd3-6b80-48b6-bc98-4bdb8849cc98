package com.facishare.paas.appframework.core.predef.service.dto.calculate;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface CalculateWithDrafts {

    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty(value = "object_data")
        @SerializedName("object_data")
        private ObjectDataDocument objectData;

        @JSONField(name = "M2")
        @JsonProperty(value = "details")
        @SerializedName("details")
        private Map<String, List<ObjectDataDocument>> detailDataMap;

        @JSONField(name = "skip_calculate_fields")
        @JsonProperty(value = "skip_calculate_fields")
        @SerializedName("skip_calculate_fields")
        private Map<String, List<String>> skipCalculateFields;

        @JSONField(name = "skip")
        @JsonProperty(value = "skip")
        @SerializedName("skip")
        private Boolean skipCalculateDVField;

        /**
         * 需要掩码加密的字段
         */
        private Map<String, List<String>> maskFieldApiNames;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Result {
        @JSONField(name = "M1")
        @JsonProperty(value = "object_data")
        @SerializedName("object_data")
        private ObjectDataDocument objectData;

        @JSONField(name = "M2")
        @JsonProperty(value = "details")
        @SerializedName("details")
        private Map<String, List<ObjectDataDocument>> detailDataMap;
    }

}
