package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface UpdateLayoutAndUpdateDescribe {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("layout_data")
        @SerializedName("layout_data")
        private String layoutData;

        @JSONField(name = "M2")
        @JsonProperty("draft_data")
        @SerializedName("draft_data")
        private String draftData;


        @JSONField(name = "M3")
        private boolean active;

        @JSONField(name = "M4")
        @JsonProperty("describe_data")
        @SerializedName("describe_data")
        private String describeData;

    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M1")
        private LayoutDocument layout;

        @JSONField(name = "M2")
        private ObjectDescribeDocument objectDescribeDraft;

        @JSONField(name = "M3")
        private ObjectDescribeDocument objectDescribe;
    }
}
