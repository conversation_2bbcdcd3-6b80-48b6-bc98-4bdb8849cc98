package com.facishare.paas.appframework.core.model.plugin;

/**
 * Created by zhouwr on 2022/4/14.
 */
public interface ActionPlugin<A extends Plugin.Arg, R extends Plugin.Result> extends Plugin {
    String INIT = "init";

    String BEFORE = "before";

    String AFTER = "after";

    String FINALLY_DO = "finallyDo";

    R init(PluginContext context, A arg);

    R before(PluginContext context, A arg);

    R after(PluginContext context, A arg);

    R finallyDo(PluginContext context, A arg);
}
