package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.rest.core.annotation.*;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021/10/18.
 */
@RestResource(
        value = "NCRM",
        desc = "编辑数据领域插件RPC代理类", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface EditActionDomainPluginProxy {
    @POST(desc = "编辑数据领域插件rest接口")
    EditActionDomainPlugin.RestResult post(@ServiceURLParam String url, @Body EditActionDomainPlugin.Arg arg, @HeaderMap Map<String, String> header);
}
