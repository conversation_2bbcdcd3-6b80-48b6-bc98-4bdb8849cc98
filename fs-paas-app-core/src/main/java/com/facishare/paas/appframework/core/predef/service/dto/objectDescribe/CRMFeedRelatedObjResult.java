package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.ImmutableList;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.crm.openapi.Utils.ACCOUNT_API_NAME;
import static com.facishare.crm.openapi.Utils.CONTACT_API_NAME;
import static com.facishare.crm.openapi.Utils.LEADS_API_NAME;
import static com.facishare.crm.openapi.Utils.OPPORTUNITY_API_NAME;

/**
 * create by z<PERSON><PERSON> on 2019/11/21
 */
@Data
@AllArgsConstructor(staticName = "of")
public class CRMFeedRelatedObjResult {
    private static final List<String> CRM_FEED_RELATED_OBJ_API_NAME = ImmutableList.of(LEADS_API_NAME,
            ACCOUNT_API_NAME, CONTACT_API_NAME, OPPORTUNITY_API_NAME);

    private List<CRMFeedRelated> crmFeedRelated;

    public static CRMFeedRelatedObjResult fromDescribes(List<IObjectDescribe> describes) {
        Map<String, IObjectDescribe> describeMap = describes.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, x -> x));
        List<CRMFeedRelated> relatedList = CRM_FEED_RELATED_OBJ_API_NAME.stream()
                .map(describeMap::get)
                .filter(Objects::nonNull)
                .map(CRMFeedRelated::of)
                .collect(Collectors.toList());
        return of(relatedList);
    }

    @Data
    @Builder
    public static class CRMFeedRelated {
        @JsonProperty("value")
        private String apiName;
        @JsonProperty("name")
        private String displayName;

        public static CRMFeedRelated of(IObjectDescribe describe) {
            return builder().apiName(describe.getApiName()).displayName(describe.getDisplayName()).build();
        }
    }
}
