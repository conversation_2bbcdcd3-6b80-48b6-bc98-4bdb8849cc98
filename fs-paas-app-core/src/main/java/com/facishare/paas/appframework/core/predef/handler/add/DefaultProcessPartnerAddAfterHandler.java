package com.facishare.paas.appframework.core.predef.handler.add;

import com.facishare.paas.appframework.common.util.ParallelUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.OutInfoChangeModel;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.service.PartnerRemindOutUserService;
import com.facishare.paas.appframework.privilege.EnterpriseRelationServiceImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by zhouwr on 2023/1/30.
 */
@Component
@HandlerProvider(name = "defaultProcessPartnerAddAfterHandler")
public class DefaultProcessPartnerAddAfterHandler implements AddActionHandler {

    @Autowired
    private EnterpriseRelationServiceImpl enterpriseRelationService;
    @Autowired
    private PartnerRemindOutUserService partnerRemindOutUserService;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        processWithPartner(context, arg);
        return new Result();
    }

    private void processWithPartner(HandlerContext context, Arg arg) {
        boolean ignoreSendingRemind = arg.getInterfaceArg().ignoreSendingRemind();
        if (ignoreSendingRemind) {
            return;
        }
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createBackgroundTask();
        parallelTask.submit(() -> {
            IObjectData objectData = arg.objectData();
            IObjectDescribe objectDescribe = arg.getObjectDescribe();
            String dataId = arg.objectData().getId();
            String partnerId = objectData.get("partner_id", String.class);
            if (StringUtils.isNotEmpty(dataId) && StringUtils.isNotEmpty(partnerId)) {
                Tuple<Integer, Long> newOutInfo = changePartnerAndOwner(context.getUser(), partnerId, null);
                OutInfoChangeModel model = OutInfoChangeModel
                        .builder()
                        .dataId(dataId)
                        .dataName(objectData.getName())
                        .oldOutEI(0)
                        .displayName(objectDescribe.getDisplayName())
                        .oldOutUserId(0)
                        .newOutEI(newOutInfo.getKey())
                        .newOutUserId(newOutInfo.getValue().intValue())
                        .isPreDefineObj("package".equals(objectDescribe.getDefineType()) ? true : false)
                        .build();
                List<OutInfoChangeModel> outInfoChangeModelList = Lists.newArrayList(model);
                partnerRemindOutUserService.remindOutUser(context.getUser(), arg.getObjectApiName(), 92, outInfoChangeModelList);
            }
        });
        parallelTask.run();
    }

    private Tuple<Integer, Long> changePartnerAndOwner(User user, String partnerId, Long outOwnerId) {
        if (StringUtils.isEmpty(partnerId)) {
            return new Tuple<>();
        }
        Integer outTenantId = null;
        Map<String, RelationDownstreamResult> downstreamMap = enterpriseRelationService.getRelationDownstreamInfo(user.getTenantId(), Sets.newHashSet(partnerId));
        RelationDownstreamResult downstream = downstreamMap.get(partnerId);
        if (downstream != null) {
            //设置外部企业和外部负责人
            outTenantId = downstream.getDownstreamOuterTenantId();
            if (Objects.isNull(outOwnerId)) {
                outOwnerId = downstream.getRelationOwnerOuterUid();
            }
        } else {
            outOwnerId = null;
        }
        return Tuple.of(outTenantId == null ? Integer.valueOf(0) : outTenantId, outOwnerId == null ? Long.valueOf(0L) : outOwnerId);
    }
}
