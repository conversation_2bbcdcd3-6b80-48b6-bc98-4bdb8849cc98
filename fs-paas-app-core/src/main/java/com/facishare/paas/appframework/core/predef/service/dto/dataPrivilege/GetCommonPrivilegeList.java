package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.privilege.dto.ObjectDataCommonPrivilegeInfo;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface GetCommonPrivilegeList {
    @Data
    class Arg {

    }

    @Builder
    @Data
    class Result {
        @JSONField(name = "M1")
        @Getter(onMethod_ = {@JsonProperty("ObjectDataPermissionInfos")})
        private List<ObjectDataCommonPrivilegeInfo> ObjectDataPermissionInfos;
        @JSONField(name = "M2")
        private List<ObjectDescribeDocument> objectDescribeList;
    }
}
