package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.appframework.metadata.CallBackActionParams;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <A> 前端传过来的原始 arg
 * <T> 转换后可以单条回调的 arg
 * <p>
 * create by z<PERSON><PERSON> on 2019/10/12
 */
public abstract class AbstractStandardAsyncBulkAction<A, T> extends PreDefineAction<A, AbstractStandardAsyncBulkAction.Result> {

    @Override
    protected List<String> getDataPrivilegeIds(A arg) {
        return null;
    }

    @Override
    protected Result doAct(A arg) {
        return submitJob(arg);
    }

    protected final Result submitJob(A arg) {
        CallBackActionParams callBackActionParams = getCallBackActionParams(arg);
        String jobId = submitBulkActionJob(callBackActionParams);
        return Result.of(jobId);
    }

    private String submitBulkActionJob(CallBackActionParams callBackActionParams) {
        return serviceFacade.submitBulkActionJob(actionContext.getUser(), callBackActionParams, actionContext.getObjectApiName());
    }

    protected CallBackActionParams<T> getCallBackActionParams(A arg) {
        return CallBackActionParams.<T>builder()
                .action(getActionCode())
                .buttonApiName(getButtonApiName())
                .actionParamMap(getActionParamMap())
                .lang(actionContext.getLang().getValue())
                .appId(actionContext.getAppId())
                .upstreamOwnerId(actionContext.getUpstreamOwnerId())
                .outTenantId(actionContext.getUser().getOutTenantId())
                .outUserId(actionContext.getUser().getOutUserId())
                .outIdentityType(actionContext.getOutIdentityType())
                .skipMessage(skipSendCRMMessage())
                .originalRequestSource(Optional.ofNullable(actionContext.getRequestSource()).map(Enum::name).orElse(null))
                .build();
    }

    /**
     * @return true 回调完成后,不发送 CRM 提醒
     */
    protected boolean skipSendCRMMessage() {
        return false;
    }

    private Map<String, T> getActionParamMap() {
        List<T> buttonParams = getButtonParams();
        if (CollectionUtils.empty(buttonParams)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_ERROR));
        }
        if (buttonParams.size() > 400) {
            throw new ValidateException(I18N.text(I18NKey.REACHED_MAX_LIMIT_SIZE));
        }
        // id 重复时使用后一个覆盖前一个
        return buttonParams.stream()
                .collect(Collectors.toMap(this::getDataIdByParam, x -> x, (x, y) -> y));
    }

    /**
     * 获取转换后 arg 的数据id
     *
     * @param param 转换后的 arg
     * @return 数据id
     */
    protected abstract String getDataIdByParam(T param);

    /**
     * 将前端传过来的 arg，转换为可以单条回调的 arg
     *
     * @return 转换后 arg 的列表
     */
    protected abstract List<T> getButtonParams();

    protected abstract String getButtonApiName();

    protected abstract String getActionCode();

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor(staticName = "of")
    public static class Result {
        private String jobId;
    }
}
