package com.facishare.paas.appframework.core.predef.facade;

import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.model.*;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.ExtraDataKeys;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDataFormatter;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.ObjectLifeStatus;
import com.facishare.paas.appframework.metadata.cache.CacheKeys;
import com.facishare.paas.appframework.metadata.cache.RedissonService;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.facishare.paas.appframework.metadata.mask.MaskFieldLogicService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/5/16.
 */
@Slf4j
@Service("approvalFlowServiceFacade")
public class ApprovalFlowServiceFacadeImpl implements ApprovalFlowServiceFacade {

    @Autowired
    private ServiceFacade serviceFacade;
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private MaskFieldLogicService maskFieldLogicService;
    @Autowired
    private RedissonService redissonService;

    @Override
    public IObjectData getRealMasterObjectData(User user, IObjectDescribe objectDescribe, IObjectData objectData) {
        MasterDetailFieldDescribe masterDetail = ObjectDescribeExt.of(objectDescribe).getMasterDetailFieldDescribe().orElse(null);
        if (masterDetail == null) {
            return null;
        }
        CacheContext cacheContext = CacheContext.getContext();
        String masterId = objectData.get(masterDetail.getApiName(), String.class);
        String cacheKey = REAL_MASTER_OBJECT_DATA + "|" + masterId;
        IObjectData realMasterData = cacheContext.getCache(cacheKey);
        if (Objects.isNull(realMasterData)) {
            realMasterData = serviceFacade.findObjectData(user, masterId, masterDetail.getTargetApiName());
            cacheContext.setCache(cacheKey, realMasterData);
        }
        return realMasterData;
    }

    @Override
    public Map<String, ApprovalFlowStartResult> tryTriggerMasterApproval(User user,
                                                                         ApprovalFlowTriggerType triggerType,
                                                                         ObjectAction action,
                                                                         IObjectData objectData,
                                                                         Map<String, Object> updateData,
                                                                         Map<String, IObjectDescribe> describeMap,
                                                                         Map<String, Object> freeApprovalDef,
                                                                         Map<String, Object> extraCallbackData,
                                                                         Map<String, List<String>> maskFieldApiNameMap) {
        IObjectDescribe objectDescribe = describeMap.get(objectData.getDescribeApiName());
        if (!needTriggerMasterApproval(objectDescribe)) {
            return Maps.newHashMap();
        }
        IObjectData realMasterData = getRealMasterObjectData(user, objectDescribe, objectData);
        Map<String, ApprovalFlowStartResult> startApprovalFlowResult = Maps.newHashMap();
        //normal状态触发编辑审批，ineffective状态触发新建审批
        if (triggerType == ApprovalFlowTriggerType.UPDATE && ObjectDataExt.of(realMasterData).isNormal()) {
            Map<String, Map<String, Object>> detailChangeMap = buildDetailChangeMap(action, objectDescribe, objectData, updateData);
            //计算从对象变更后主对象上的计算字段和统计字段的值
            Map<String, Object> data = computeMasterCalculateFields(user, realMasterData, describeMap, detailChangeMap);
            startApprovalFlowResult = startApprovalFlow(user, ApprovalFlowTriggerType.UPDATE, realMasterData,
                    data, Maps.newHashMap(), detailChangeMap, describeMap, freeApprovalDef, extraCallbackData, maskFieldApiNameMap);
            startApprovalFlowResult.put(objectData.getId(), startApprovalFlowResult.get(realMasterData.getId()));
            if (action != ObjectAction.CREATE) {
                updateDataStatus(user, objectData, realMasterData);
            }
        } else if (triggerType == ApprovalFlowTriggerType.CREATE && ObjectDataExt.of(realMasterData).isIneffective()) {
            Map<String, Map<String, Object>> callbackDataMap = buildCallbackDataForAddAction(user.getTenantId(),
                    realMasterData, objectData.getCreateTime(), extraCallbackData, action.getActionCode());
            startApprovalFlowResult = startApprovalFlow(user, Lists.newArrayList(realMasterData), ApprovalFlowTriggerType.CREATE,
                    Maps.newHashMap(), callbackDataMap, freeApprovalDef);
            startApprovalFlowResult.put(objectData.getId(), startApprovalFlowResult.get(realMasterData.getId()));
            updateDataStatus(user, objectData, realMasterData);
        }
        //单独新建从和主的生命状态保持一致
        if (action == ObjectAction.CREATE) {
            ObjectDataExt.of(objectData).setLifeStatus(ObjectDataExt.of(realMasterData).getLifeStatus());
        }
        cacheApprovalTriggerResult(startApprovalFlowResult);
        return startApprovalFlowResult;
    }

    private Map<String, Map<String, Object>> buildDetailChangeMap(ObjectAction action, IObjectDescribe objectDescribe,
                                                                  IObjectData objectData, Map<String, Object> updateData) {
        Map<String, Map<String, Object>> detailChangeMap = Maps.newHashMap();
        detailChangeMap.put(objectDescribe.getApiName(), Maps.newHashMap());
        Object changeData = null;
        switch (action) {
            case CREATE:
                IObjectData addData = ObjectDataExt.of(objectData).copy();
                changeData = Lists.newArrayList(ObjectDataDocument.of(addData));
                break;
            case UPDATE:
                changeData = Maps.newHashMap();
                ((Map) changeData).put(objectData.getId(), ObjectDataDocument.of(updateData));
                break;
            case INVALID:
                changeData = Lists.newArrayList(objectData.getId());
                break;
            default:
                break;
        }
        if (changeData != null) {
            detailChangeMap.get(objectDescribe.getApiName()).put(action.getActionCode(), changeData);
        }
        return detailChangeMap;
    }

    private Map<String, Object> computeMasterCalculateFields(User user, IObjectData realMasterData,
                                                             Map<String, IObjectDescribe> describeMap,
                                                             Map<String, Map<String, Object>> detailChangeMap) {
        IObjectDescribe masterDescribe = serviceFacade.findObject(realMasterData.getTenantId(), realMasterData.getDescribeApiName());
        describeMap.put(masterDescribe.getApiName(), masterDescribe);
        ObjectDataSnapshot dataSnapshot = ObjectDataSnapshot.builder()
                .masterSnapshot(Maps.newHashMap())
                .detailSnapshot(detailChangeMap)
                .build();
        //计算变更之后的统计字段和计算字段
        IObjectData snapshotData = serviceFacade.calculateForSnapshot(user, masterDescribe, realMasterData, dataSnapshot);
        //跟数据库里的数据做一次diff，diff结果供审批流过滤器使用
        Map<String, Object> diffMap = ObjectDataExt.of(realMasterData).diff(snapshotData, masterDescribe);
        ObjectDataExt.of(diffMap).removeInvalidFieldForApproval(masterDescribe);
        return diffMap;
    }

    @Override
    public Map<String, ApprovalFlowStartResult> startApprovalFlow(User user,
                                                                  ApprovalFlowTriggerType triggerType,
                                                                  IObjectData masterData,
                                                                  Map<String, Object> data,
                                                                  Map<String, Object> callbackData,
                                                                  Map<String, Map<String, Object>> detailChangeMap,
                                                                  Map<String, IObjectDescribe> describeMap,
                                                                  Map<String, Object> freeApprovalDef,
                                                                  Map<String, Object> extraCallbackData,
                                                                  Map<String, List<String>> maskFieldApiNameMap) {
        cacheApprovalTriggerType(triggerType);
        //给lookup字段填充__r和__l，用于审批流展示
        if (CollectionUtils.notEmpty(data)) {
            List<IObjectData> updateDataList = Lists.newArrayList(ObjectDataExt.of(data).getObjectData());
            fillExtendFieldInfo(user, describeMap.get(masterData.getDescribeApiName()), updateDataList,
                    maskFieldApiNameMap);
        }

        Map<String, Object> extraData = Maps.newHashMap();
        if (CollectionUtils.notEmpty(detailChangeMap)) {
            //填充lookup字段、人员字段、部门字段、国家省市区的__r、__l字段，用于审批流展示
            detailChangeMap.forEach((k, v) -> {
                List<IObjectData> addDetails = ObjectDataDocument.ofDataList((List) v.get(ObjectAction.CREATE.getActionCode()));
                fillExtendFieldInfo(user, describeMap.get(k), addDetails, maskFieldApiNameMap);
                //编辑审批中新建的从的生命状态设置为变更中
                if (ApprovalFlowTriggerType.UPDATE.equals(triggerType)) {
                    addDetails.forEach(x -> ObjectDataExt.of(x).setLifeStatus(ObjectLifeStatus.IN_CHANGE));
                }

                Map<String, ObjectDataDocument> updateMap = (Map<String, ObjectDataDocument>) v.get(ObjectAction.UPDATE.getActionCode());
                if (CollectionUtils.notEmpty(updateMap)) {
                    List<IObjectData> updateDetailList = Lists.newArrayList(updateMap.values()).stream()
                            .map(x -> x.toObjectData()).collect(Collectors.toList());
                    fillExtendFieldInfo(user, describeMap.get(k), updateDetailList, maskFieldApiNameMap);
                }
            });

            extraData.put(ExtraDataKeys.DETAIL_CHANGE, detailChangeMap);
        }
        addExtraInfo2CallbackData(callbackData, extraCallbackData);
        ApprovalFlowStartResult flowStartResult = serviceFacade.startApprovalFlow(user, masterData.getDescribeApiName(), masterData.getId(),
                triggerType, data, callbackData, null, extraData, freeApprovalDef);
        Map<String, ApprovalFlowStartResult> startApprovalFlowResult = Maps.newHashMap();
        startApprovalFlowResult.put(masterData.getId(), flowStartResult);

        //2.根据触发结果修改生命周期和锁定状态
        List<String> fieldsProjection = modifyObjectDataWhenStartApprovalFlowByResultMap(triggerType, Lists.newArrayList(masterData), startApprovalFlowResult);
        bulkUpdateObjectDataListInApproval(user, Lists.newArrayList(masterData), fieldsProjection);

        //灰度企业成功触发了编辑审批，记录数据快照
        if (triggerType == ApprovalFlowTriggerType.UPDATE && flowStartResult == ApprovalFlowStartResult.SUCCESS) {
            saveDataSnapshot(user, masterData, callbackData, detailChangeMap);
        }
        cacheApprovalTriggerResult(startApprovalFlowResult);

        return startApprovalFlowResult;
    }

    private void cacheApprovalTriggerType(ApprovalFlowTriggerType triggerType) {
        CacheContext.getContext().setCache(APPROVAL_TRIGGER_TYPE, triggerType);
    }

    private void cacheApprovalTriggerAsync() {
        CacheContext.getContext().setCache(APPROVAL_TRIGGER_ASYNC, true);
    }

    private void cacheApprovalTriggerResult(Map<String, ApprovalFlowStartResult> triggerResult) {
        CacheContext.getContext().setCache(APPROVAL_TRIGGER_RESULT, triggerResult);
    }

    private void fillExtendFieldInfo(User user, IObjectDescribe describe, List<IObjectData> dataList, Map<String, List<String>> maskFieldApiNameMap) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        //补充国家省市区的__r
        serviceFacade.fillCountryAreaLabel(describe, dataList, user);
        List<IObjectData> synchronizedDataList = ObjectDataExt.synchronize(dataList);
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        parallelTask.submit(() -> {
            //添加lookup字段的主属性__r
            serviceFacade.fillObjectDataWithRefObject(describe, synchronizedDataList, user);
        });
        parallelTask.submit(() -> {
            //人员字段的__r，__l
            serviceFacade.fillUserInfo(describe, synchronizedDataList, user);
        });
        parallelTask.submit(() -> {
            //部门字段的__r，__l
            serviceFacade.fillDepartmentInfo(describe, synchronizedDataList, user);
        });
        parallelTask.submit(() -> {
            // 公共对对象可见范围的__l
            serviceFacade.fillDataVisibilityRange(user, describe, synchronizedDataList);
        });

        List<String> maskFieldApiNames = CollectionUtils.empty(maskFieldApiNameMap) ? null : maskFieldApiNameMap.get(describe.getApiName());
        if (CollectionUtils.notEmpty(maskFieldApiNames)) {
            parallelTask.submit(() -> {
                // 加密掩码字段
                maskFieldLogicService.fillMaskFieldValue(user, describe, maskFieldApiNames, synchronizedDataList);
            });
        }

        try {
            parallelTask.await(5, TimeUnit.SECONDS);
        } catch (Exception e) {
            log.error("fillExtraFieldInfo error,tenantId:{},objectApiName:{},dataNum:{}", user.getTenantId(), describe.getApiName(),
                    dataList.size(), e);
        }
    }

    private void addExtraInfo2CallbackData(Map<String, Object> callbackData, Map<String, Object> extraCallbackData) {
        if (CollectionUtils.notEmpty(extraCallbackData)) {
            callbackData.putAll(extraCallbackData);
        }
    }

    private List<String> modifyObjectDataWhenStartApprovalFlowByResultMap(ApprovalFlowTriggerType type,
                                                                          List<IObjectData> objectDataList,
                                                                          Map<String, ApprovalFlowStartResult> resultMap) {
        Set<String> fieldsProjection = Sets.newHashSet();
        for (IObjectData objectData : objectDataList) {
            //根据触发审批流的结果来更新objectData中的生命周期字段
            fieldsProjection.addAll(ObjectDataExt.of(objectData).modifyObjectDataWhenStartApprovalFlow(type,
                    resultMap.get(objectData.getId())));
        }
        return Lists.newArrayList(fieldsProjection);
    }

    private void bulkUpdateObjectDataListInApproval(User user, List<IObjectData> objectDataList, List<String> fieldsProjection) {
        if (grayPreMatchApproval()) {
            return;
        }
        if (CollectionUtils.notEmpty(fieldsProjection)) {
            objectDataList.stream().collect(Collectors.groupingBy(IObjectData::getDescribeApiName))
                    .forEach((x, y) -> serviceFacade.batchUpdateByFields(user, y, fieldsProjection));
        }
    }

    private void saveDataSnapshot(User user, IObjectData objectData, Map<String, Object> callbackData,
                                  Map<String, Map<String, Object>> detailChangeMap) {
        if (grayPreMatchApproval()) {
            return;
        }
        ObjectDataSnapshot snapshot = ObjectDataSnapshot.builder()
                .masterSnapshot(callbackData)
                .detailSnapshot(detailChangeMap)
                .biz(RequestContext.Biz.ApprovalFlow.getCode())
                .bizId(RequestUtil.getOtherBizId())
                .subBizId(RequestUtil.getBizId())
                .build();
        infraServiceFacade.createSnapshot(user, objectData.getDescribeApiName(), objectData.getId(), snapshot);
    }

    private void updateDataStatus(User user, IObjectData objectData, IObjectData realMasterData) {
        ObjectDataExt.of(objectData).setLifeStatus(ObjectDataExt.of(realMasterData).getLifeStatus());
        if (grayPreMatchApproval()) {
            return;
        }
        serviceFacade.batchUpdateByFields(user, Lists.newArrayList(objectData),
                Lists.newArrayList(ObjectLifeStatus.LIFE_STATUS_API_NAME));
    }

    private boolean grayPreMatchApproval() {
        return Optional.ofNullable(RequestContextManager.getContext())
                .map(it -> it.<Boolean>getAttribute(RequestContext.Attributes.PRE_MATCH_APPROVAL))
                .orElse(false);
    }

    @Override
    public Map<String, Map<String, Object>> buildCallbackDataForAddAction(String tenantId,
                                                                          IObjectData objectData,
                                                                          Long detailCreateTime,
                                                                          Map<String, Object> extraCallbackData,
                                                                          String actionCode) {
        Map<String, Object> callbackData = Maps.newHashMap();
        //记录是新建还是编辑操作触发的审批
        if (!Strings.isNullOrEmpty(actionCode)) {
            callbackData.put(ExtraDataKeys.TRIGGER_ACTION_CODE, actionCode);
        }
        if (AppFrameworkConfig.isInMasterDetailApprovalGrayList(tenantId) && Objects.nonNull(detailCreateTime)) {
            callbackData.put(ExtraDataKeys.DETAIL_CREATE_TIME, String.valueOf(detailCreateTime));
        }
        //合并自定义的回调参数
        addExtraInfo2CallbackData(callbackData, extraCallbackData);
        Map<String, Map<String, Object>> callbackDataMap = Maps.newHashMap();
        if (CollectionUtils.notEmpty(callbackData)) {
            callbackDataMap.put(objectData.getId(), callbackData);
        }
        return callbackDataMap;
    }

    @Override
    public Map<String, ApprovalFlowStartResult> startApprovalFlow(User user,
                                                                  List<IObjectData> objectDataList,
                                                                  ApprovalFlowTriggerType approvalFlowTriggerType,
                                                                  Map<String, Map<String, Object>> dataMap,
                                                                  Map<String, Map<String, Object>> callbackDataMap,
                                                                  Map<String, Object> freeApprovalDef) {
        if (CollectionUtils.empty(objectDataList)) {
            return Maps.newHashMap();
        }
        cacheApprovalTriggerType(approvalFlowTriggerType);

        //1.触发审批流
        Map<String, ApprovalFlowStartResult> startApprovalFlowResult = serviceFacade.batchStartApproval(
                approvalFlowTriggerType, user, objectDataList, dataMap, callbackDataMap, freeApprovalDef);

        //2.根据触发结果修改生命周期和锁定状态
        List<String> fieldsProjection = modifyObjectDataWhenStartApprovalFlowByResultMap(approvalFlowTriggerType, objectDataList, startApprovalFlowResult);

        //3.批量更新。 fieldsProject是因为防止不必要的数据也被再次更新。
        List<IObjectData> dataToUpdate = objectDataList.stream().filter(x -> !ApprovalFlowStartResult.ALREADY_EXIST.equals(startApprovalFlowResult.get(x.getId())))
                .collect(Collectors.toList());
        bulkUpdateObjectDataListInApproval(user, dataToUpdate, fieldsProjection);
        cacheApprovalTriggerResult(startApprovalFlowResult);

        return startApprovalFlowResult;
    }

    @Override
    public Map<String, ApprovalFlowStartResult> startApprovalFlowWithObjectData(User user,
                                                                                IObjectDescribe describe,
                                                                                IObjectData objectData,
                                                                                ApprovalFlowTriggerType approvalFlowTriggerType,
                                                                                Map<String, Map<String, Object>> dataMap,
                                                                                Map<String, Map<String, Object>> callbackDataMap,
                                                                                Map<String, Object> freeApprovalDef) {
        cacheApprovalTriggerType(approvalFlowTriggerType);

        //使用拷贝的数据格式化之后触发审批流
        IObjectData cpData = ObjectDataExt.of(objectData).copy();
        ObjectDataFormatter.builder()
                .excludeQuoteField(true)
                .describe(describe)
                .dataList(Lists.newArrayList(cpData))
                .build()
                .format();
        ApprovalFlowStartResult result = serviceFacade.startApprovalWithObjectData(user, ObjectDataExt.of(cpData).toMap(),
                approvalFlowTriggerType.getTriggerTypeCode(), dataMap, callbackDataMap, freeApprovalDef);
        Map<String, ApprovalFlowStartResult> startApprovalFlowResult = Maps.newHashMap();
        startApprovalFlowResult.put(objectData.getId(), result);
        cacheApprovalTriggerResult(startApprovalFlowResult);

        //2.根据触发结果修改生命周期和锁定状态
        List<String> fieldsProjection = modifyObjectDataWhenStartApprovalFlowByResultMap(approvalFlowTriggerType,
                Lists.newArrayList(objectData), startApprovalFlowResult);

        //3.批量更新。 fieldsProject是因为防止不必要的数据也被再次更新。
        List<IObjectData> dataToUpdate = Lists.newArrayList(objectData).stream()
                .filter(x -> !ApprovalFlowStartResult.ALREADY_EXIST.equals(startApprovalFlowResult.get(x.getId())))
                .collect(Collectors.toList());
        bulkUpdateObjectDataListInApproval(user, dataToUpdate, fieldsProjection);

        return startApprovalFlowResult;
    }

    @Override
    public boolean isApprovalNotExist() {
        Map<String, ApprovalFlowStartResult> startApprovalFlowResult = CacheContext.getContext().getCache(APPROVAL_TRIGGER_RESULT);
        return CollectionUtils.empty(startApprovalFlowResult) ||
                startApprovalFlowResult.containsValue(ApprovalFlowStartResult.APPROVAL_NOT_EXIST);
    }

    @Override
    public ApprovalFlowTriggerType getApprovalFlowTriggerType() {
        return CacheContext.getContext().getCache(APPROVAL_TRIGGER_TYPE);
    }

    @Override
    public boolean needTriggerMasterApproval(IObjectDescribe objectDescribe) {
        //OpenAPI仍使用从的审批
        //没有灰度的企业仍使用从的审批
        //只有主从一起新建的从使用主的审批
        return ObjectDescribeExt.of(objectDescribe).needCheckMasterStatus();
    }

    @Override
    public void startApprovalFlowAsynchronous(User user,
                                              List<IObjectData> objectDataList,
                                              ApprovalFlowTriggerType approvalFlowTriggerType,
                                              Map<String, Map<String, Object>> dataMap,
                                              Map<String, Map<String, Object>> callbackDataMap) {
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        cacheApprovalTriggerType(approvalFlowTriggerType);
        cacheApprovalTriggerAsync();
        serviceFacade.batchStartApprovalAsynchronous(approvalFlowTriggerType, user, objectDataList, dataMap, callbackDataMap);
    }

    @Override
    public void updateDetailObjectDataLifeStatus(String actionCode,
                                                 User user,
                                                 ObjectLifeStatus lastLifeStatus,
                                                 IObjectData objectData,
                                                 Map<String, List<IObjectData>> detailObjectData,
                                                 Map<String, IObjectDescribe> detailDescribeMap,
                                                 IObjectData realMasterData,
                                                 boolean triggerApprovalFlowSuccess) {
        if (AppFrameworkConfig.isInMasterDetailApprovalWhiteList(user.getTenantId())) {
            return;
        }
        if (!isLifeStatusChanged(lastLifeStatus, objectData)) {
            return;
        }
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SYNC_UPDATE_DETAIL_OBJECT_DATA_LIFE_STATUS, user.getTenantId())) {
            return;
        }
        try {
            if (CollectionUtils.notEmpty(detailDescribeMap)) {
                String lockKey = CacheKeys.detailLifeStatusUpdateLockKey(user.getTenantId(), objectData.getDescribeApiName(), objectData.getId());
                RLock lock = redissonService.tryLock(0, 30, TimeUnit.SECONDS, lockKey);
                if (Objects.isNull(lock)) {
                    log.warn("try lock failed:{}", lockKey);
                    return;
                }
                try {
                    List<IObjectDescribe> noDataDetailDescribes = Lists.newArrayList();
                    ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
                    detailDescribeMap.forEach((detailApiName, detailDescribe) -> {
                        if (!ObjectDescribeExt.of(detailDescribe).isCreateWithMaster()) {
                            return;
                        }
                        if (!detailObjectData.containsKey(detailApiName)) {
                            noDataDetailDescribes.add(detailDescribe);
                            return;
                        }
                        //拷贝一下从对象数据，防止主流程出现ConcurrentModificationException
                        List<IObjectData> cpDetailObjectDataList = ObjectDataExt.copyList(detailObjectData.get(detailApiName));
                        parallelTask.submit(() ->
                        {
                            serviceFacade.updateDetailObjectDataLifeStatusWithDetailData(user, objectData, cpDetailObjectDataList);
                            log.info("process one detail,tenantId:{},masterApiName:{},masterDataId:{},lifeStatus:{},detailApiName:{},detailDataNum:{}",
                                    user.getTenantId(), objectData.getDescribeApiName(), objectData.getId(), ObjectDataExt.of(objectData).getLifeStatusText(),
                                    detailDescribe.getApiName(), CollectionUtils.size(cpDetailObjectDataList));
                        });
                    });
                    try {
                        parallelTask.await(3000, TimeUnit.MILLISECONDS);
                    } catch (Exception e) {
                        log.warn("parallel updateDetailObjectDataLifeStatus failed,actionCode:{},tenantId:{},masterApiName:{},masterDataId:{},lastLifeStatus:{},lifeStatus:{}  ",
                                actionCode, user.getTenantId(), objectData.getDescribeApiName(), objectData.getId(),
                                lastLifeStatus.getCode(), ObjectDataExt.of(objectData).getLifeStatusText(), e);
                    }
                    if (CollectionUtils.notEmpty(noDataDetailDescribes) && StandardAction.Edit.name().equals(actionCode)) {
                        serviceFacade.updateDetailObjectDataLifeStatusWithDetailDescribe(user, objectData, noDataDetailDescribes);
                    }
                } finally {
                    redissonService.unlock(lock);
                }
            } else if (triggerApprovalFlowSuccess && Objects.nonNull(realMasterData)) {
                serviceFacade.updateDetailObjectDataLifeStatusByMasterData(user, realMasterData, realMasterData.getDescribeApiName());
            }
        } catch (Exception e) {
            log.error("updateDetailObjectDataLifeStatus failed,actionCode:{},tenantId:{},apiName:{},dataId:{},lastLifeStatus:{},lifeStatus:{} ",
                    actionCode, user.getTenantId(), objectData.getDescribeApiName(), objectData.getId(),
                    lastLifeStatus.getCode(), ObjectDataExt.of(objectData).getLifeStatusText(), e);
        }
    }

    private boolean isLifeStatusChanged(ObjectLifeStatus lastLifeStatus, IObjectData objectData) {
        return ObjectDataExt.of(objectData).getLifeStatus() != lastLifeStatus;
    }

}
