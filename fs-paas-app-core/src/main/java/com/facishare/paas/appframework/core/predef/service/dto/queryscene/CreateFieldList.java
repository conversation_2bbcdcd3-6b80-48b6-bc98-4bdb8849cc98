package com.facishare.paas.appframework.core.predef.service.dto.queryscene;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.Data;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2019/06/24
 */
public interface CreateFieldList {

    @Data
    @JsonNaming(SnakeCaseStrategy.class)
    class Arg {
        private List<ObjectDataDocument> fieldList;
        private String objectDescribeApiName;
        private String sceneId;
        private String sceneApiName;
        private String extendAttribute;
        private String sceneType;
    }

    @Data
    class Result {
        private boolean success;

        public static Result success() {
            Result result = new Result();
            result.setSuccess(true);
            return result;
        }
    }
}
