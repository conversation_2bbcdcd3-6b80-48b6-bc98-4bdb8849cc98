package com.facishare.paas.appframework.core.predef.action.uievent;

import com.google.common.collect.Lists;

import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2019-08-05 18:10
 */
public class SimpleUIEventProcess implements UIEventProcess {

    private Processor basic;

    private List<Processor> processors = Lists.newArrayList();

    public SimpleUIEventProcess() {
    }

    @Override
    public void setBasic(Processor basic) {
        this.basic = basic;
    }

    @Override
    public void addProcessor(Processor processor) {
        processors.add(processor);
    }

    @Override
    public Processor getBasic() {
        return basic;
    }

    @Override
    public void invoke(ProcessRequest processRequest) {
        Objects.requireNonNull(processRequest);
        new SimpleUIProcessorContext().invokeNext(processRequest);
    }

    @Override
    public void addOrderedProcessors(Processor... processors) {
        for (Processor processor : processors) {
            addProcessor(processor);
        }

//        for (int i = processors.length - 1; i >= 0; i--) {
//            if (i == processors.length - 1) {
//                setBasic(processors[i]);
//            } else {
//                addProcessor(processors[i]);
//            }
//        }
    }


    private class SimpleUIProcessorContext implements ProcessorContext {
        // 是否停止接下来的处理
        private boolean stop = false;

        @Override
        public void invokeNext(ProcessRequest processRequest) {
            for (Processor processor : processors) {
                processor.invoke(processRequest ,this);
                if (isStop()) {
                    break;
                }
            }
        }

        @Override
        public boolean isStop() {
            return stop;
        }

        @Override
        public void setStop() {
            stop = true;
        }
    }

}
