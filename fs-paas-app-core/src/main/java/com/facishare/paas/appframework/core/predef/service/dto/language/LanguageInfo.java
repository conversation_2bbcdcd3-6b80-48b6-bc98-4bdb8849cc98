package com.facishare.paas.appframework.core.predef.service.dto.language;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LanguageInfo {
    @JSONField(name = "api_name")
    @JsonProperty(value = "api_name")
    private String apiName;
    @JSONField(name = "is_package")
    @JsonProperty(value = "is_package")
    private boolean sfaObject;
    @JSONField(name = "label")
    @JsonProperty(value = "label")
    private String label;
    @JSONField(name = "define_type")
    @JsonProperty(value = "define_type")
    private String defineType;
    @JSONField(name = "type")
    @JsonProperty(value = "type")
    private String type;
    @JSONField(name = "zh_CN")
    @JsonProperty(value = "zh_CN")
    private String simpleChinese;
    @JSONField(name = "en_US")
    @JsonProperty(value = "en_US")
    private String english;
    @JSONField(name = "zh_TW")
    @JsonProperty(value = "zh_TW")
    private String traditional;
    @JSONField(name = "pre_zh_CN")
    @JsonProperty(value = "pre_zh_CN")
    private String preSimpleChinese;
    @JSONField(name = "pre_en_US")
    @JsonProperty(value = "pre_en_US")
    private String preEnglish;
    @JSONField(name = "pre_zh_TW")
    @JsonProperty(value = "pre_zh_TW")
    private String preTraditional;
    @JSONField(name = "config")
    @JsonProperty(value = "config")
    private Map<String, Object> config;
}
