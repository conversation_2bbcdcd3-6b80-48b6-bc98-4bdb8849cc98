package com.facishare.paas.appframework.core.predef.service.dto.queryscene;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

public interface DeleteFilterFields {

    @Data
    class Arg {
        @JsonProperty("object_describe_api_name")
        @SerializedName("object_describe_api_name")
        private String describeApiName;

        @JsonProperty("scene_id")
        @SerializedName("scene_id")
        private String SceneId;

        @JsonProperty("scene_type")
        @SerializedName("scene_type")
        private String sceneType;

        @JsonProperty("scene_api_name")
        @SerializedName("scene_api_name")
        private String sceneApiName;

        @JsonProperty("what_api_name")
        @SerializedName("what_api_name")
        private String whatApiName;

        @JsonProperty("session_key")
        @SerializedName("session_key")
        private String sessionKey;

        @JsonProperty("extend_attribute")
        @SerializedName("extend_attribute")
        private String extendAttribute;
    }

    @Builder
    @Data
    class Result {
        private boolean success;
        private String message;
    }
}
