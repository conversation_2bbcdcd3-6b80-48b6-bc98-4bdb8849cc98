package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.FundAccountBaseService;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.domain.CancelEntryActionDomainPlugin;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.TransactionCallbackWithoutResult;
import org.springframework.transaction.support.TransactionTemplate;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

public class StandardCancelEntryAction extends AbstractStandardAction<CustomButtonAction.Arg, CustomButtonAction.Result> {
    protected FundAccountBaseService fundAccountBaseService = SpringUtil.getContext().getBean(FundAccountBaseService.class);
    private PlatformTransactionManager tm = (PlatformTransactionManager) SpringUtil.getContext().getBean("paasMetadataTransactionManager");
    protected IObjectDescribe newCustomerAccountDescribe;
    protected IObjectDescribe accountTransactionFlowDescribe;
    protected IObjectData objectData;
    protected IObjectData dbObjectData;
    private RLock cancelEntryLock;

    private IObjectData fundAccountData;
    private String customerFieldName;
    private String enterAccountAmountFieldName;
    private final Map<String, Object> updateDataFieldMap = Maps.newHashMap();

    private final String fundAccountFieldName = FundAccountBaseService.Const.FUND_ACCOUNT;

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList(ObjectAction.CANCEL_ENTRY.getActionCode());
    }

    @Override
    protected List<String> getDataPrivilegeIds(CustomButtonAction.Arg arg) {
        String dataId = arg.getObjectDataId();
        return StringUtils.isEmpty(dataId) ? Lists.newArrayList() : Lists.newArrayList(dataId);
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CANCEL_ENTRY.getButtonApiName();
    }

    @Override
    protected void findDescribe() {
        Map<String, IObjectDescribe> describeMap = serviceFacade.findObjects(actionContext.getTenantId(), Lists.newArrayList(actionContext.getObjectApiName(), FundAccountBaseService.Const.NEW_CUSTOMER_ACCOUNT_OBJ, FundAccountBaseService.Const.ACCOUNT_TRANSACTION_FLOW_OBJ));
        objectDescribe = describeMap.get(actionContext.getObjectApiName());
        newCustomerAccountDescribe = describeMap.get(FundAccountBaseService.Const.NEW_CUSTOMER_ACCOUNT_OBJ);
        accountTransactionFlowDescribe = describeMap.get(FundAccountBaseService.Const.ACCOUNT_TRANSACTION_FLOW_OBJ);
    }

    @Override
    protected void before(CustomButtonAction.Arg arg) {
        super.before(arg);
        objectData = dataList.get(0);
        dbObjectData = ObjectDataExt.of(objectData).copy();

        //取消入账锁
        String key = String.format("EnterCancel_%s_%s_%s", actionContext.getTenantId(), objectData.getDescribeApiName(), objectData.getId());
        cancelEntryLock = infraServiceFacade.tryLock(0L, 10, TimeUnit.SECONDS, key);
        if (Objects.isNull(cancelEntryLock)) {
            log.warn("duplicate cancel entry key:{}", key);
            throw new ValidateException(I18N.text(FundAccountBaseService.Const.UN_ENTRY_NOT_CANCEL, objectData.getName()));
        }
        FundAccountBaseService.checkLifeStatus(objectData);

        updateDataFieldMap.put(FundAccountBaseService.Const.ENTER_INTO_ACCOUNT, false);
        updateDataFieldMap.put(fundAccountFieldName, null);

        String fundAccountId = objectData.get(fundAccountFieldName, String.class);
        this.fundAccountData = serviceFacade.findObjectData(actionContext.getUser(), fundAccountId, "FundAccountObj");

        FundAccountBaseService.FundAuthConfigModel accessAuthModel = fundAccountBaseService.getAccessAuthInfo(actionContext.getUser(), actionContext.getObjectApiName());
        this.customerFieldName = accessAuthModel.getCustomerFieldName();
        this.enterAccountAmountFieldName = accessAuthModel.getEnterAccountAmountFieldName();
    }

    @Override
    protected CancelEntryActionDomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return CancelEntryActionDomainPlugin.Arg.builder().dbObjectData(ObjectDataDocument.of(this.dbObjectData))
                .fundAccountData(ObjectDataDocument.of(this.fundAccountData)).customerFieldName(this.customerFieldName).enterAccountAmountFieldName(this.enterAccountAmountFieldName).build();
    }

    @Override
    protected IObjectData getPreObjectData() {
        return this.objectData;
    }

    @Override
    protected IObjectData getPostObjectData() {
        return this.objectData;
    }

    protected boolean isCreditFundAccount() {
        String accessModule = this.fundAccountData.get(FundAccountBaseService.Const.FIELD_ACCESS_MODULE, String.class);
        return "3".equals(accessModule);
    }

    @Override
    protected CustomButtonAction.Result doAct(CustomButtonAction.Arg arg) {
        CustomButtonAction.Result result = new CustomButtonAction.Result();
        String objectApiName = actionContext.getObjectApiName();
        String objectDataId = arg.getObjectDataId();
        List<IObjectData> accountTransactionFlowList = cancelEntryValidate(objectApiName, objectDataId);
        String fundAccountId = objectData.get(fundAccountFieldName, String.class);
        String customerId = objectData.get(customerFieldName, String.class);
        //更新
        updateDataFieldMap.forEach(objectData::set);

        if (FundAccountBaseService.isReplenishmentQuantity(fundAccountData)) {
            serviceFacade.updateObjectData(actionContext.getUser(), objectData);
        } else {
            if (CollectionUtils.size(accountTransactionFlowList) != 1) {
                log.warn("user:{},objectApiName:{},objectDataId:{},relateIncomeFlow not one:{}", actionContext.getUser(), objectApiName, objectDataId, accountTransactionFlowList);
                throw new ValidateException(I18N.text(FundAccountBaseService.Const.UN_ENTRY_NOT_CANCEL, objectData.getName()));
            }
            IObjectData accountTransactionFlowData = accountTransactionFlowList.get(0);
            BigDecimal revenueAmount = accountTransactionFlowData.get(FundAccountBaseService.Const.REVENUE_AMOUNT, BigDecimal.class, BigDecimal.ZERO);
            IObjectData dbAccountTransactionFlowData = ObjectDataExt.of(accountTransactionFlowData).copy();

            IObjectData customerAccountData = fundAccountBaseService.getOrCreateNewCustomerAccountData(actionContext.getUser(), fundAccountId, customerId);
            BigDecimal accountBalance = customerAccountData.get(FundAccountBaseService.Const.ACCOUNT_BALANCE, BigDecimal.class, BigDecimal.ZERO);

            Map<String, Object> flowUpdateFieldMap = Maps.newHashMap();
            flowUpdateFieldMap.put(FundAccountBaseService.Const.ENTRY_STATUS, FundAccountBaseService.Const.ENTRY_STATUS_CANCEL);
            flowUpdateFieldMap.put(FundAccountBaseService.Const.ACCOUNT_BALANCE, accountBalance.subtract(revenueAmount));
            flowUpdateFieldMap.forEach(accountTransactionFlowData::set);

            boolean accountCheckRuleEnable = fundAccountBaseService.accountCheckRuleEnable(actionContext.getUser());
            Map<String, Map<String, Object>> customerAccountUpdateColumnMap = FundAccountBaseService.buildCustomerUpdateMapAndCheck(customerAccountData, revenueAmount.negate(),
                    isCreditFundAccount(), accountCheckRuleEnable, fundAccountBaseService.enableCustomerAccountExceed(actionContext.getUser()));
            List<String> customerAccountUpdateFields = Lists.newArrayList(customerAccountUpdateColumnMap.getOrDefault(customerAccountData.getId(), Maps.newHashMap()).keySet());

            List<IObjectData> updateNewCustomerAccountDataList = Lists.newArrayList();
            TransactionTemplate template = new TransactionTemplate(tm);

            template.execute(new TransactionCallbackWithoutResult() {
                @Override
                protected void doInTransactionWithoutResult(TransactionStatus status) {
                    List<IObjectData> updateNewCustomerResult = serviceFacade.batchUpdateByFields(actionContext.getUser(), Lists.newArrayList(customerAccountData), customerAccountUpdateFields, customerAccountUpdateColumnMap);
                    updateNewCustomerAccountDataList.addAll(updateNewCustomerResult);
                    serviceFacade.updateObjectData(actionContext.getUser(), objectData);
                    serviceFacade.updateObjectData(actionContext.getUser(), accountTransactionFlowData);
                }
            });
            if (!updateNewCustomerAccountDataList.isEmpty()) {
                Map<String, Object> newCustomerAccountUpdateFieldMap = com.google.common.collect.Maps.newHashMap();
                IObjectData updateNewCustomerAccountData = updateNewCustomerAccountDataList.get(0);
                customerAccountUpdateFields.forEach(k -> newCustomerAccountUpdateFieldMap.put(k, updateNewCustomerAccountData.get(k)));
                serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, newCustomerAccountDescribe, updateNewCustomerAccountData, newCustomerAccountUpdateFieldMap, customerAccountData);
            }
            serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, accountTransactionFlowDescribe, accountTransactionFlowData, flowUpdateFieldMap, dbAccountTransactionFlowData);
        }
        //记录日志
        serviceFacade.log(actionContext.getUser(), EventType.MODIFY, ActionType.Modify, objectDescribe, objectData, updateDataFieldMap, dbObjectData);
        return result;
    }

    private List<IObjectData> cancelEntryValidate(String objectApiName, String objectDataId) {
        String enterIntoAccountFieldName = FundAccountBaseService.Const.ENTER_INTO_ACCOUNT;
        boolean enterIntoAccount = objectData.get(enterIntoAccountFieldName, Boolean.class, Boolean.FALSE);
        if (!enterIntoAccount) {
            throw new ValidateException(I18N.text(FundAccountBaseService.Const.UN_ENTRY_NOT_CANCEL, objectData.getName()));
        }

        List<IFilter> filters = Lists.newArrayList();
        FundAccountBaseService.fillFilterEq(filters, FundAccountBaseService.Const.RELATE_RECORD_OBJECT, objectApiName);
        FundAccountBaseService.fillFilterEq(filters, FundAccountBaseService.Const.RELATE_RECORD_DATA_ID, objectDataId);
        FundAccountBaseService.fillFilterEq(filters, FundAccountBaseService.Const.ENTRY_STATUS, FundAccountBaseService.Const.ENTRY_STATUS_ENTERED);
        FundAccountBaseService.fillFilterEq(filters, "record_type", FundAccountBaseService.Const.FLOW_INCOME_RECORD_TYPE);
        FundAccountBaseService.fillFilterIn(filters, FundAccountBaseService.Const.REVENUE_TYPE, Lists.newArrayList(FundAccountBaseService.getRevenueType(objectApiName), "charge_off"));
        List<IObjectData> accountTransactionFlowList = list(FundAccountBaseService.Const.ACCOUNT_TRANSACTION_FLOW_OBJ, filters);
        //红冲过，禁止取消入账
        if (accountTransactionFlowList.stream().anyMatch(x -> "charge_off".equals(x.get(FundAccountBaseService.Const.REVENUE_TYPE, String.class)))) {
            throw new ValidateException(I18N.text(FundAccountBaseService.Const.CHARGE_OFF_NOT_CANCEL_ENTRY));
        }
        return accountTransactionFlowList;
    }

    private List<IObjectData> list(String objectApiName, List<IFilter> filterList) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setFilters(filterList);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setLimit(10);
        return serviceFacade.findBySearchQuery(actionContext.getUser(), objectApiName, searchTemplateQuery).getData();
    }

    @Override
    protected void finallyDo() {
        if (Objects.nonNull(cancelEntryLock)) {
            infraServiceFacade.unlock(cancelEntryLock);
        }
        super.finallyDo();
    }
}
