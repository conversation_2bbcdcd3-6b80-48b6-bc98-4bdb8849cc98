package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/9/24
 */
public interface FindWhatListLayoutByRoleCodes {
    @Data
    class Arg {
        private String objectDescribeApiName;
        private String whatApiName;
        //布局适用端(web或mobile)
        private String layoutAgentType;

        private Set<String> roleCodes;
        private String whatDataId;

    }

    @Data
    @Builder
    class Result {
        private List<RecordTypeLayoutStructure> layoutInfos;
    }

    @Data
    @Builder
    class RecordTypeLayoutStructure {
        private String roleCode;
        private List<LayoutDocument> abstractLayoutList;
        private Map<String, String> recordLayoutMapping;
        private DocumentBaseEntity flowTaskLayoutComponent;
    }
}
