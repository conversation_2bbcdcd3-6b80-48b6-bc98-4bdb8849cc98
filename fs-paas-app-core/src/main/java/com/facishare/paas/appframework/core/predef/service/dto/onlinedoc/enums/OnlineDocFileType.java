package com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums;

import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR> create by liy on 2024/6/19
 */
@Getter
@AllArgsConstructor
public enum OnlineDocFileType {

    FOLDER("folder", "文件夹"),
    FILE("file", "普通文件"),
    UNKNOWN("unknown", "未知类型"),
    ;

    private final String type;
    private final String name;
    private final static Map<String, OnlineDocFileType> TYPE_MAP = Maps.newHashMap();

    static {
        for (OnlineDocFileType item : OnlineDocFileType.values()) {
            TYPE_MAP.put(item.getType(), item);
        }
    }

    /**
     * 获取枚举
     */
    public static OnlineDocFileType of(String type) {
        return TYPE_MAP.get(type);
    }

    /**
     * 检查是否在枚举范围内
     */
    public static boolean in(String type) {
        return Objects.nonNull(TYPE_MAP.get(type));
    }
}
