package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.core.model.PreDefineAction;
import com.facishare.paas.common.util.UdobjConstants;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public class BaseGdprProjectRequestAction extends PreDefineAction<BaseGdprProjectRequestAction.Arg, BaseGdprProjectRequestAction.Result> {

    @Override
    protected Result doAct(Arg arg) {
        return null;
    }

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return null;
    }

    @Override
    protected List<String> getDataPrivilegeIds(Arg arg) {
        return Lists.newArrayList(arg.getDataId());
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    static class Arg {
        private String apiName;
        private String dataId;
        private String id;
        //默认是不处理从对象
        private String detailObjStrategy = UdobjConstants.LOCK_STRATEGY.CASCADE_DETAIL_OBJ.getValue();
        private String lockRuleApiName = UdobjConstants.LOCK_RULE_DEFAULT_API_NAME;
    }

    @Data
    @Builder
    @NoArgsConstructor
    static class Result {
    }
}
