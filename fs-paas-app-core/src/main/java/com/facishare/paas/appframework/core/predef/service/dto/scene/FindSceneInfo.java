package com.facishare.paas.appframework.core.predef.service.dto.scene;

import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.fasterxml.jackson.annotation.JsonAlias;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.databind.PropertyNamingStrategy.SnakeCaseStrategy;
import com.fasterxml.jackson.databind.annotation.JsonNaming;
import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * create by z<PERSON><PERSON> on 2019/05/22
 */
public interface FindSceneInfo {
    @Data
    @JsonNaming(SnakeCaseStrategy.class)
    class Arg {
        private String describeApiName;
        private String sceneApiName;
        private String extendAttribute;
        @JsonAlias("appId")
        private String appId;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        IScene scene;
    }
}
