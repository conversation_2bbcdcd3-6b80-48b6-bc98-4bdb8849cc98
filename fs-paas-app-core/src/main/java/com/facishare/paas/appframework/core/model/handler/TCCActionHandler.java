package com.facishare.paas.appframework.core.model.handler;

import com.fxiaoke.transaction.tcc.api.context.BranchTransactionalContext;

/**
 * 分布式业务处理器，支持分布式事务
 * Created by zhouwr on 2023/1/6.
 */
public interface TCCActionHandler<A extends Handler.Arg, R extends Handler.Result> extends Handler<A, R> {

    String COMMIT = "commit";
    String ROLLBACK = "rollback";

    /**
     * 业务处理器的执行方法
     *
     * @param context 接口上下文
     * @param arg     接口参数
     * @return 业务处理结果
     */
//    @TccTransactional(name = "handle", confirmMethod = "commit", cancelMethod = "rollback")
    R handle(HandlerContext context, A arg);

    /**
     * 分布式业务处理器的提交方法
     *
     * @param branchTransactionalContext 分布式事务上下文
     * @param context                    接口上下文
     * @param arg                        接口参数
     * @return 是否执行成功
     */
    boolean commit(BranchTransactionalContext branchTransactionalContext, HandlerContext context, A arg);

    /**
     * 分布式业务处理器的回滚方法
     *
     * @param branchTransactionalContext 分布式事务上下文
     * @param context                    接口上下文
     * @param arg                        接口参数
     * @return 是否执行成功
     */
    boolean rollback(BranchTransactionalContext branchTransactionalContext, HandlerContext context, A arg);
}
