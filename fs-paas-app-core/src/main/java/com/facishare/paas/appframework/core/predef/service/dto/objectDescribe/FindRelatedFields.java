package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.facishare.paas.appframework.core.model.ObjectFieldDescribeDocument;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import lombok.Data;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/10/12
 */
public interface FindRelatedFields {

    @Data
    class Arg {
        private String targetApiName;
    }

    @Data
    class Result {
        private List<ObjectFieldDescribeDocument> relatedFields;

        public static Result of(List<IFieldDescribe> fieldDescribes) {
            return new Result(ObjectFieldDescribeDocument.ofList(fieldDescribes));
        }

        private Result(List<ObjectFieldDescribeDocument> relatedFields) {
            this.relatedFields = relatedFields;
        }
    }
}
