package com.facishare.paas.appframework.core.predef.action.uievent;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.FunctionException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.function.dto.FuncBizExtendParam;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.UIEventType;
import com.facishare.paas.appframework.metadata.dto.RecordTypeMatchInfo;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.IObjectRelationMatch;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.fxiaoke.functions.model.Remind;
import com.fxiaoke.functions.ui.UIEvent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.facishare.paas.appframework.core.predef.action.StandardTriggerEventAction.*;

/**
 * 函数处理器
 *
 * <AUTHOR>
 * @date 2019-08-05 18:15
 */
@Slf4j
public class FunctionProcessor extends AbstractProcessor {
    private static final String TRIGGER_FIELD_API_NAME = "triggerFieldAPIName";
    private static final String OCR_DATA_RESULT = "ocrDataResult";
    private static final String DELETED_DETAILS = "deletedDetails";
    private static final String BIZ_INFO = "bizInfo";

    public FunctionProcessor(ActionContainer container) {
        super(container);
    }

    /**
     * 调用invoke之前，必须先设置event和actionContext
     */
    @Override
    public void invoke(UIEventProcess.ProcessRequest request, ProcessorContext context) {
        StopWatch stopWatch = StopWatch.create(getClass().getSimpleName());
        // 获取函数
        IUdefFunction function = container.getServiceFacade().getFunctionLogicService().findUDefFunction(requestContext.getUser(),
                event.getFuncApiName(),
                event.getDescribeApiName());
        stopWatch.lap("findUDefFunction");
        if (Objects.isNull(function) || !function.isActive()) {
            log.warn("UI event bounding function {} is forbidden or deleted", event.getFuncApiName());
            return;
        }

        //UI事件触发的字段通过action参数传入函数
        Map<String, Object> triggerParam = new HashMap<>();
        triggerParam.put(TRIGGER_FIELD_API_NAME, request.getTriggerFieldAPIName());
        triggerParam.put(OCR_DATA_RESULT, request.getOcrDataResult());
        triggerParam.put(DELETED_DETAILS, request.getDeletedDetails());
        triggerParam.put(BIZ_INFO, request.getBizInfo());

        infraServiceFacade.fillQuoteValueVirtualField(requestContext.getUser(), request.getMasterData(), request.getDetailDataMap());
        stopWatch.lap("fillQuoteValueVirtualField");
        // 调用函数
        FuncBizExtendParam.Arg funcBizExtendParam = FuncBizExtendParam.Arg.builder()
                .actionPage(request.getTriggerPage())
                .apiName("UIEvent")
                .actionStage(UIEventType.of(event.getType()).getCode(event.getTriggers()))
                .build();
        RunResult runResult = serviceFacade.getFunctionLogicService().executeUDefFunction(requestContext.getUser(), function, Maps.newHashMap(), request.getMasterData(), request.getDetailDataMap(), null, triggerParam, funcBizExtendParam);
        stopWatch.lap("executeUDefFunction");
        if (!runResult.isSuccess()) {
            log.warn(runResult.getErrorInfo());
            throw new FunctionException(I18N.text(I18NKey.FUNCTION_EXECUTION_ERROR) + runResult.getErrorInfo(), AppFrameworkErrorCode.FUNCTION_USER_BIZ_ERROR.getCode());
        }
        // request重新赋值
        UIEvent uiEventResult = getEventResult(runResult.getFunctionResult());
        setRemind(uiEventResult.getRemind(), request);
        request.setObjectAttribute(uiEventResult.getObjectAttribute());
        request.setFieldAttribute(uiEventResult.getFieldAttribute());
        request.setDetailFieldAttribute(uiEventResult.getDetailFieldAttribute());
        request.setDetailRecordType(uiEventResult.getDetailRecordType());
        request.setOptionAttribute(uiEventResult.getOptionAttribute());
        request.setDetailButton(uiEventResult.getDetailButton());
        Map detailRowFieldAttribute = CollectionUtils.nullToEmpty(uiEventResult.getExtendAttributes()).get(UIEvent.DETAIL_ROW_FIELD_ATTRIBUTE);
        request.setDetailRowFieldAttribute(detailRowFieldAttribute);
        //是否计算从对象的默认值和计算字段
        request.setDoCalculate(needDoCalculate(function, uiEventResult));

        if (Remind.class.getSimpleName().equals(function.getReturnType())) {
            request.setRemindEvent(true);
            context.setStop();
            stopWatch.logSlow(500);
            return;
        }

        // 处理函数返回的masterData
        Map<String, Object> masterDataMap = uiEventResult.getObjectData();
        dealFunctionReturnedData(masterDataMap, request.getMasterData());
        request.setMasterData(ObjectDataExt.of(masterDataMap)); // set master data
        // 处理函数返回的detailData
        @SuppressWarnings("unchecked") Map<String, List<Map<String, Object>>> detailDataMap
                = uiEventResult.getDetails();

        //过滤无效的从对象
        if (CollectionUtils.notEmpty(detailDataMap)) {
            Sets.newHashSet(detailDataMap.keySet()).stream()
                    .filter(x -> CollectionUtils.empty(detailObjectDescribeMap) || !detailObjectDescribeMap.containsKey(x))
                    .forEach(detailDataMap::remove);
        }
        // 有效的业务类型，过滤掉新增数据中无效的业务类型
        filterInvalidRecordTypeDataFromDetails(request, detailDataMap, findValidRecordType(detailDataMap));
        stopWatch.lap("filterInvalidRecordTypeDataFromDetails");

        // 修正字段值，如：""转为null等
        correctFieldValue(request);
        stopWatch.lap("correctFieldValue");

        validateObjectData(request);
        stopWatch.lap("validateObjectData");
        // 处理选项值隐藏 不下发隐藏的选项值
        processOptionHidden(request);
        stopWatch.lap("processOptionHidden");
        stopWatch.logSlow(500);
    }

    private boolean needDoCalculate(IUdefFunction function, UIEvent uiEventResult) {
        Boolean doCalculate = uiEventResult.getDoCalculate();
        if (Objects.nonNull(doCalculate)) {
            return doCalculate;
        }
        return AppFrameworkConfig.afterUiEventDoCalculateDetailsLastTime(function.getCreateTime());
    }

    private void validateObjectData(UIEventProcess.ProcessRequest request) {
        //校验主对象
        serviceFacade.validateDataType(objectDescribe, Lists.newArrayList(request.getMasterData()), requestContext.getUser());
        //校验从对象
        if (CollectionUtils.notEmpty(detailObjectDescribeMap)) {
            CollectionUtils.nullToEmpty(request.getDetailDataMap()).entrySet()
                    .stream()
                    .filter(entry -> detailObjectDescribeMap.containsKey(entry.getKey()))
                    .forEach(entry -> serviceFacade.validateDataType(detailObjectDescribeMap.get(entry.getKey()),
                            Lists.newArrayList(entry.getValue()), requestContext.getUser()));
        }
    }

    private void correctFieldValue(UIEventProcess.ProcessRequest request) {
        ObjectDataExt.correctValue(requestContext.getUser(), Lists.newArrayList(request.getMasterData()), objectDescribe);
        if (CollectionUtils.notEmpty(detailObjectDescribeMap)) {
            CollectionUtils.nullToEmpty(request.getDetailDataMap()).forEach((apiName, detailDataList) -> {
                if (!detailObjectDescribeMap.containsKey(apiName) || Objects.isNull(detailObjectDescribeMap.get(apiName))) {
                    return;
                }
                ObjectDataExt.correctValue(requestContext.getUser(), detailDataList, detailObjectDescribeMap.get(apiName));
                // 删除新增数据中的id字段
                CollectionUtils.nullToEmpty(detailDataList).forEach(data -> {
                    if (UIEventUtils.isNewDetail(data)) {
                        ObjectDataExt.of(data).remove(IObjectData.ID);
                    }
                });
            });
        }
    }


    private void processOptionHidden(UIEventProcess.ProcessRequest request) {
        Map<String, Map<String, Object>> optionAttribute = request.getOptionAttribute();
        IObjectData masterData = request.getMasterData();
        Map<String, List<IObjectData>> detailData = request.getDetailDataMap();
        Map<String, IObjectDescribe> detailDescribe = container.getDetailDescribe();
        if (!CollectionUtils.empty(optionAttribute)) {
            optionAttribute.forEach((apiName, optionFieldConfig) -> {
                // 主对象隐藏选项值
                if (apiName.equals(objectDescribe.getApiName())) {
                    hiddenOptionValue(objectDescribe, masterData, optionFieldConfig);
                }
                // 从对象隐藏选项值
                if (CollectionUtils.nullToEmpty(detailData.keySet()).contains(apiName)) {
                    IObjectDescribe objectDescribe = detailDescribe.get(apiName);
                    for (IObjectData objectData : detailData.get(apiName)) {
                        hiddenOptionValue(objectDescribe, objectData, optionFieldConfig);
                    }
                }
            });
        }
    }

    private void hiddenOptionValue(IObjectDescribe objectDescribe, IObjectData objectData, Map<String, Object> optionFieldConfig) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        // 选项值UI事件，通过函数隐藏或显示的选项字段描述
        List<SelectOne> selectOnes = CollectionUtils.nullToEmpty(describeExt.getSelectOneFields()).stream()
                .filter(selectOne -> CollectionUtils.notEmpty(optionFieldConfig) && optionFieldConfig.containsKey(selectOne.getApiName()))
                .collect(Collectors.toList());
        CollectionUtils.nullToEmpty(selectOnes).forEach(selectOneField -> {
            List<ISelectOption> selectOptions = selectOneField.getSelectOptions();
            Map<String, Object> optionValueConfig = (Map<String, Object>) optionFieldConfig.get(selectOneField.getApiName());
            if (CollectionUtils.notEmpty(optionValueConfig)) {
                CollectionUtils.nullToEmpty(selectOptions).forEach(selectOption -> {
                    String optionValue = selectOption.getValue();
                    Map<String, Boolean> hiddenConfig = (Map<String, Boolean>) optionValueConfig.get(optionValue);
                    if (CollectionUtils.notEmpty(hiddenConfig) && BooleanUtils.isTrue(hiddenConfig.get("hidden"))) {
                        if (IFieldType.SELECT_ONE.equals(selectOneField.getType()) && Objects.nonNull(objectData.get(selectOneField.getApiName()))
                                && StringUtils.equals((String) objectData.get(selectOneField.getApiName()), optionValue)) {
                            objectData.set(selectOneField.getApiName(), null);
                        } else if (IFieldType.SELECT_MANY.equals(selectOneField.getType()) && CollectionUtils.notEmpty(objectData.get(selectOneField.getApiName(), List.class))) {
                            List<String> selectManyValue = objectData.get(selectOneField.getApiName(), List.class);
                            List<String> filteredSelectValue = CollectionUtils.nullToEmpty(selectManyValue).stream()
                                    .filter(it -> !StringUtils.equals(optionValue, it))
                                    .collect(Collectors.toList());
                            objectData.set(selectOneField.getApiName(), filteredSelectValue);
                        }
                    }
                });
            }
        });
    }

    private void filterInvalidRecordTypeDataFromDetails(UIEventProcess.ProcessRequest request, Map<String, List<Map<String, Object>>> detailDataMap, Map<String, List<IRecordTypeOption>> validRecordType) {
        Map<String, List<IObjectData>> details = Maps.newHashMap();
        CollectionUtils.nullToEmpty(detailDataMap).forEach((apiName, detailList) -> {
            List<IObjectData> list = Lists.newArrayList();
            Iterator<Map<String, Object>> it = detailList.iterator();
            while (it.hasNext()) {
                Object dataObj = it.next();
                Map<String, Object> data = convertFuncReturnedObj(dataObj);
                if (checkDetailValidity(data, validRecordType, apiName)) {
                    // 补充object_describe_api_name字段，防止被用户篡改
                    data.put(ObjectDataExt.OBJECT_API_NAME, apiName);
                    ObjectDataExt.of(data).setDescribeApiName(apiName);
                    list.add(ObjectDataExt.of(data));
                }

            }
            details.put(apiName, list);
        });
        request.setDetailDataMap(details);  // set detail data
    }

    /*
     * 检查从数据有效性
     * 只能用于从对象，编辑均认为有效
     */
    private boolean checkDetailValidity(Map<String, Object> detailData,
                                        Map<String, List<IRecordTypeOption>> validRecordType,
                                        String apiName) {
        boolean result = true;
        if (UIEventUtils.isNewDetail(detailData)) {
            if (Objects.isNull(detailData.get(IFieldType.RECORD_TYPE))
                    || !checkValidRecordType((String) detailData.get(IFieldType.RECORD_TYPE),
                    validRecordType.get(apiName))) {
                result = false;
            }
        }
        return result;
    }

    /*
     * 转换函数返回的数据，有可能是Map或者是IObjectData
     */
    private Map<String, Object> convertFuncReturnedObj(Object dataObj) {
        if (dataObj instanceof IObjectData) {   // 兼容函数返回数据可能是IObjectData
            return ObjectDataExt.of((IObjectData) dataObj).toMap();
        }
        return (Map<String, Object>) dataObj;
    }

    /**
     * 检查业务类型是否有效
     */
    private boolean checkValidRecordType(String recordType, List<IRecordTypeOption> iRecordTypeOptions) {
        if (CollectionUtils.empty(iRecordTypeOptions)) {
            return false;
        }
        return iRecordTypeOptions.stream().anyMatch(option -> option.getApiName().equals(recordType));
    }

    /**
     * 获取各从对象有效的业务类型
     *
     * @param detailDataMap
     */
    private Map<String, List<IRecordTypeOption>> findValidRecordType(Map<String, List<Map<String, Object>>> detailDataMap) {
        // 查询角色分配的有效业务类型
        Map<String, List<IRecordTypeOption>> validRecordTypeMap = serviceFacade.findValidRecordTypeListMap(
                new ArrayList<String>(detailDataMap.keySet()), requestContext.getUser());
        // 查询主从匹配的业务类型
        List<IObjectRelationMatch> matchRecordTypes = serviceFacade.findMatchRecordTypeRelation(
                requestContext.getTenantId(), objectDescribe.getApiName(),
                container.getMasterData().getRecordType(), null);
        if (CollectionUtils.empty(matchRecordTypes)) {
            // 匹配为空，说明不需要过滤
            return validRecordTypeMap;
        }
        // 从分配的业务类型中，过滤掉不匹配的业务类型
        validRecordTypeMap.forEach((detailApiName, recordTypeList) -> {
            matchRecordTypes.stream().filter(match -> detailApiName.equals(match.getTargetApiName()))
                    .flatMap(this::recordInfo).forEach(recordInfo -> recordTypeList.removeIf(type -> checkFilter(type, recordInfo)));

        });
        return validRecordTypeMap;
    }

    private boolean checkFilter(IRecordTypeOption type, RecordTypeMatchInfo.RecordInfo recordInfo) {
        return type.getApiName().equals(recordInfo.getApiName()) && !recordInfo.isMatch();
    }

    private Stream<RecordTypeMatchInfo.RecordInfo> recordInfo(IObjectRelationMatch match) {
        List<RecordTypeMatchInfo.RecordInfo> recordInfos = JSONArray.parseArray(
                match.getTargetValue(), RecordTypeMatchInfo.RecordInfo.class);
        return recordInfos.stream();
    }

    /**
     * 处理函数返回的masterData的map
     * 该masterDataMap一定不为null，但可能为空
     * 该masterData可能返回全量字段，
     * 该masterData可能只返回用户函数指定的字段，
     * 该masterData可能返回无效字段
     * 将函数返回的masterDataMap与传入前的masterData进行合并，保证全量字段
     *
     * @param returnedDataMap 函数返回的数据
     * @param completeData    前端传入的全量数据
     */
    private void dealFunctionReturnedData(Map<String, Object> returnedDataMap, IObjectData completeData) {
        ObjectDataExt completeDataExt = ObjectDataExt.of(completeData);
        // 防止用户在函数中修改apiName;
        completeDataExt.set(IObjectData.DESCRIBE_API_NAME, completeData.get(IObjectData.DESCRIBE_API_NAME));
        // returnedDataMap不包含的字段以completeData为准
//        completeDataExt.toMap().forEach((apiName, value) -> {
//            if (returnedDataMap.containsKey(apiName)) {
//                return;
//            }
//            returnedDataMap.put(apiName, value);
//        });
    }


    private UIEvent getEventResult(Object object) {
        return JSONObject.parseObject(JSONObject.toJSONString(object, SerializerFeature.WriteMapNullValue,
                SerializerFeature.DisableCircularReferenceDetect, SerializerFeature.WriteBigDecimalAsPlain), UIEvent.class);
    }


    private void setRemind(Remind remind, UIEventProcess.ProcessRequest request) {
        if (ObjectUtils.isEmpty(remind) || (StringUtils.isBlank(remind.getContent())
                && CollectionUtils.empty(remind.getContentMap()))) {
            return;
        }

        Map<String, Object> remindMap = Maps.newHashMap();
        remindMap.put(REMIND_TYPE, remind.getType());
        remindMap.put(REMIND_CONTENT, remind.getContent());
        if (!CollectionUtils.empty(remind.getContentMap())) {
            remindMap.put(REMIND_CONTENT_MAP, remind.getContentMap());
        }
        request.setRemind(remindMap);
    }

}
