package com.facishare.paas.appframework.core.predef.action;

import com.facishare.paas.appframework.common.util.ObjectAction;

import java.util.List;
import java.util.stream.Collectors;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/10/12
 */
public class StandardAsyncBulkChangePartnerOwnerAction extends AbstractStandardAsyncBulkAction<StandardChangePartnerOwnerAction.Arg, StandardChangePartnerOwnerAction.Arg> {

    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return StandardAction.ChangePartnerOwner.getFunPrivilegeCodes();
    }

    @Override
    protected String getDataIdByParam(StandardChangePartnerOwnerAction.Arg param) {
        return param.getDataIds().get(0);
    }

    @Override
    protected List<StandardChangePartnerOwnerAction.Arg> getButtonParams() {
        return arg.getDataIds().stream()
                .map(id -> StandardChangePartnerOwnerAction.Arg.of(id, arg.getOutTenantId(),
                        arg.getOwnerId(), arg.getOldOwnerStrategy(), arg.getOldOwnerTeamMemberPermissionType(),
                        arg.getRelateObjectApiNames(), arg.getIsUpdateOutDataOwnDepartment(),
                        arg.getIsUpdateOutDataOwnOrganization()))
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.CHANGE_PARTNER_OWNER.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.CHANGE_PARTNER_OWNER.getActionCode();
    }
}
