package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2019/01/09
 */
public interface BulkTemporaryPrivilege {

    @Data
    class Arg {
        private List<TemporaryPrivilege.Arg> privileges;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private DocumentBaseEntity errorMessage;
    }
}
