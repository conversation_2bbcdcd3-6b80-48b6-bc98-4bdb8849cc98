package com.facishare.paas.appframework.core.predef.handler.edit;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.AcceptableValidateNoRollbackException;
import com.facishare.paas.appframework.core.model.InfraServiceFacade;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.core.predef.facade.AuditLogServiceFacade;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.dto.ObjectDataSnapshot;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

import static com.facishare.paas.appframework.core.model.RequestContext.Biz.ApprovalFlow;

/**
 * Created by zhouwr on 2023/2/20.
 */
@Component
@HandlerProvider(name = "defaultSaveSnapshotEditBeforeHandler")
public class DefaultSaveSnapshotEditBeforeHandler implements EditActionHandler {
    @Autowired
    private InfraServiceFacade infraServiceFacade;
    @Autowired
    private AuditLogServiceFacade auditLogServiceFacade;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        //编辑审批中的数据，保存快照之后直接返回
        if (arg.useSnapshot()) {
            saveSnapshot(context, arg);
            //审批过程中编辑数据，记录一条hidden=true的修改记录
            recordEditLog(context, arg);
            //直接跳出action，忽略后边的逻辑
            throw new AcceptableValidateNoRollbackException(buildResult(arg));
        }
        return new Result();
    }

    private Result buildResult(Arg arg) {
        BaseObjectSaveAction.Result actionResult = BaseObjectSaveAction.Result.builder()
                .isDuplicate(false)
                .writeDB(false)
                .triggerApproval(false)
                .objectData(arg.getObjectData())
                .details(arg.getDetailObjectData())
                .relatedDataList(arg.getRelatedObjectData())
                .build();
        Result result = new Result();
        result.setInterfaceResult(actionResult);
        return result;
    }

    private void saveSnapshot(HandlerContext context, Arg arg) {
        Map<String, Object> masterSnapshot = Maps.newHashMap(arg.getDataDiffResult().getUpdatedFieldMap());
        if (CollectionUtils.notEmpty(arg.getExtraCallbackData())) {
            masterSnapshot.putAll(arg.getExtraCallbackData());
        }
        ObjectDataSnapshot snapshot = ObjectDataSnapshot.builder()
                .masterSnapshot(masterSnapshot)
                .detailSnapshot(arg.getDataDiffResult().getDetailChangeMap())
                .biz(ApprovalFlow.getCode())
                .bizId(arg.getInterfaceArg().getBizInfo().getOtherBizId())
                .subBizId(arg.getInterfaceArg().getBizInfo().getBizId())
                .build();
        infraServiceFacade.createSnapshot(context.getUser(), arg.getObjectApiName(), arg.objectData().getId(), snapshot);
    }

    private void recordEditLog(HandlerContext context, Arg arg) {
        RequestUtil.setModifyLogHidden();
        auditLogServiceFacade.recordEditLog(context.getUser(), arg, arg.masterAndDetailDescribes(), Maps.newHashMap());
    }
}
