package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.facishare.paas.appframework.core.model.IButtonDocument;
import com.facishare.paas.appframework.core.model.SceneDocument;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.metadata.ui.layout.IComponent;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface FindCustomBasicConfiguration {
    @Data
    class Arg {
        private String describeApiName;
        private String layoutType;

        private Boolean includeButtons = true;
        private Boolean includeFieldTypes = true;
    }

    @Data
    @Builder
    class Result {
        private List<Category> components;
        private List<IButtonDocument> buttons;
        private List<String> fieldTypes;
        private List<SceneDocument> scenes;
        private List<IButtonDocument> batchButtons;
        private List<IButtonDocument> normalButtons;
        private List<IButtonDocument> singleButtons;
        private List<IButtonDocument> buttonsAdd;
        private List<IButtonDocument> buttonsEdit;
        private Map<String, List<IButtonDocument>> detailObjButtonMap;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Category {
        private String label;
        private String define_type;
        private List<Map> children;

        public static <T extends IComponent> Category of(String defineType, List<T> children) {
            List<Map> childrenMap = children.stream().map(x -> ComponentExt.of(x).toMap()).collect(Collectors.toList());
            return builder().define_type(defineType).children(childrenMap).build();
        }
    }
}
