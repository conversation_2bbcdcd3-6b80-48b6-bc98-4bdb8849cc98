package com.facishare.paas.appframework.core.predef.service.dto.log;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.log.LogModuleGroupEnum;
import lombok.Data;

import java.util.List;

import static com.facishare.paas.appframework.log.LogModuleGroupEnum.*;

/**
 * Created by rensx on 2017/10/31.
 */
@Data
public class LogModuleGroup {

    @JSONField(name = "M1")
    private int groupId;

    @JSONField(name = "M2")
    private String groupName;

    @JSONField(name = "M3")
    private List<LogModule> logModuleList;

    public static List<LogModuleGroupEnum> getLogModuleGroupEnumsByVersion(String version) {
        if (LicenseConstants.Versions.VERSION_BASIC.equals(version)) {
            return getVersionBasic();

        }
        if (LicenseConstants.Versions.isStandardProUpVersion(version)) {
            //只有专业版，旗舰版，旗舰增强版 有自定义对象管理,自定义函数管理
            return getVersionStandardPro();
        }
        return getVersionStandard();

    }
}