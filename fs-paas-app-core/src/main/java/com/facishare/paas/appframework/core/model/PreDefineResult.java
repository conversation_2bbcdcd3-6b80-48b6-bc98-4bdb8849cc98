package com.facishare.paas.appframework.core.model;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/2/4
 */
@Data
public abstract class PreDefineResult {

    /**
     * 用于业务拓展的值
     */
    private final Map<String, Object> extraData = Maps.newHashMap();

    public void setExtraData(String key, Object value) {
        extraData.put(key, value);
    }

    public Object getExtraData(String key) {
        return extraData.get(key);
    }
}
