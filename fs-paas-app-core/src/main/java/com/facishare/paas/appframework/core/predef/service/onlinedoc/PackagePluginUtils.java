package com.facishare.paas.appframework.core.predef.service.onlinedoc;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.predef.service.dto.onlinedoc.enums.PackagePluginDefineType;

/**
 * <AUTHOR> create by liy on 2024/9/13
 */
public class PackagePluginUtils {

    public static String name2I18n(String pluginApiName, String defineType, String name) {
        if (PackagePluginDefineType.CUSTOM.getType().equals(defineType)) {
            return name;
        }
        return I18NExt.getOnlyTextOrDefault(String.format("fs_paas.package_plugin.name.%s", pluginApiName), pluginApiName);
    }
}
