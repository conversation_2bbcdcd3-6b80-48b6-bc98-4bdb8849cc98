package com.facishare.paas.appframework.core.predef.service.dto.layout;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface UpdateLayout {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("layout_data")
        @SerializedName("layout_data")
        private String layoutData;

        @JSONField(name = "skip_validate")
        @JsonProperty("skip_validate")
        @SerializedName("skip_validate")
        private Boolean skipValidate;

        @JSONField(name = "appId")
        @JsonProperty("appId")
        @SerializedName("appId")
        private String appId;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M1")
        private LayoutDocument layout;
    }
}
