package com.facishare.paas.appframework.core.predef.service.dto.validateRule;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface ValidateRule {
    @Data
    class Arg {
        @JSONField(name = "M1")
        ObjectDataDocument objectData;

        @J<PERSON>NField(name = "M2")
        String objectApiName;

        @JSONField(name = "M3")
        Map<String, List<ObjectDataDocument>> details = Maps.newHashMap();

        @JSONField(name = "M4")
        String option;
    }
    @Data
    class Result {
        @JSONField(name = "M1")
        private String message;
        @JSONField(name = "M2")
        private boolean success;
        @JSONField(name = "M3")
        private boolean isSave;
        @JSONField(name = "M4")
        private List<String> blockMessages;
        @JSONField(name = "M5")
        private List<String> nonBlockMessages;

    }
}
