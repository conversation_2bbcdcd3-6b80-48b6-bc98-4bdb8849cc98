package com.facishare.paas.appframework.core.predef.service.dto.convertRule;

import com.facishare.paas.appframework.core.model.MappingRuleDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Data;

import java.util.List;

public interface ConvertRule {
    @Data
    class Arg {
        @SerializedName("rule_list")
        @JsonProperty("rule_list")
        private List<MappingRuleDocument> ruleList;
    }
}
