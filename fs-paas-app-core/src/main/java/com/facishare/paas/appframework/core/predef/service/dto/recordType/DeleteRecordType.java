package com.facishare.paas.appframework.core.predef.service.dto.recordType;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

public interface DeleteRecordType {

    @Data
    class Arg{
        @JSONField(name = "M1")
        String describeApiName;
        @JSONField(name = "M2")
        String recordApiName;
    }

    @Data
    class Result{

        @JSONField(name = "M2")
        private boolean success;
    }
}
