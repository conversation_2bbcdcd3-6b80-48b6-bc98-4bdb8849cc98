package com.facishare.paas.appframework.core.predef.service.dto.gdpr;

import com.facishare.paas.appframework.metadata.repository.model.GdprProjectRequest;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface FindGdprProjectRequestData {

    @Data
    class Arg {
        private String apiName;
        private String dataId;
    }

    @Data
    @Builder
    class Result {
        private List<GdprProjectRequest> result;
    }
}
