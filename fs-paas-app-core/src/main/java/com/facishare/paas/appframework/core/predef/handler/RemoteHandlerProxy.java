package com.facishare.paas.appframework.core.predef.handler;

import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.rest.core.annotation.*;

import java.util.Map;

/**
 * Created by zhouwr on 2023/3/3.
 */
@RestResource(
        value = "NCRM",
        desc = "RemoteHandler的RPC代理类", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface RemoteHandlerProxy {
    @POST(value = "/{handlerApiName}/service/{method}")
    String post(@Body Handler.Arg arg, @HeaderMap Map<String, String> headers, @PathParam("handlerApiName") String handlerApiName,
                @PathParam("method") String method, @ServiceKeyParam String serviceKey);
}
