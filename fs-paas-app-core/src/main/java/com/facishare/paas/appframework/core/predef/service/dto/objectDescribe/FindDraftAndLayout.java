package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 18/1/17.
 */
public interface FindDraftAndLayout {
  @Data
  class Arg {

    @JSONField(name = "M1")
    String draft_apiname;

    @JSONField(name = "M2")
    private boolean include_layout;

    @JSONField(name = "M3")
    private String layout_apiname;

  }

  @Builder
  @Data
  class Result {
    @JSONField(name = "M1")
    Object layout;

    @JSONField(name = "M2")
    Map objectDescribeDraft;
  }
}
