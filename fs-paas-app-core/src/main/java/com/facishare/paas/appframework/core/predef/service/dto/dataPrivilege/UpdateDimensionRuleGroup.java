package com.facishare.paas.appframework.core.predef.service.dto.dataPrivilege;

import com.facishare.paas.appframework.privilege.dto.DimensionRuleGroupReceivePojo;
import com.facishare.paas.appframework.privilege.dto.DimensionRulePojo;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface UpdateDimensionRuleGroup {

  @Data
  class Arg {

    String ruleCode;

    String ruleParse;

    int permission;

    String remark;

    List<DimensionRulePojo> rules;

    List<DimensionRuleGroupReceivePojo> receives;

  }


  @Data
  @Builder
  class Result {

    boolean success;

    String ruleCode;

  }

}
