package com.facishare.paas.appframework.core.predef.action;


import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.stream.Collectors;

public class StandardAsyncBulkUpdateGdprAction extends AbstractStandardAsyncBulkAction<StandardUpdateGdprAction.Arg, StandardUpdateGdprAction.Arg> {
    @Override
    protected List<String> getFuncPrivilegeCodes() {
        return Lists.newArrayList();
    }

    @Override
    protected String getDataIdByParam(StandardUpdateGdprAction.Arg param) {
        return param.getDataIds().get(0);
    }

    @Override
    protected List<StandardUpdateGdprAction.Arg> getButtonParams() {
        if (CollectionUtils.notEmpty(arg.getArgs())) {
            return arg.getDataIds().stream()
                    .map(dataId -> StandardUpdateGdprAction.Arg.ofArg(objectDescribe.getApiName(), dataId, arg.getArgs()))
                    .collect(Collectors.toList());
        }
        return arg.getDataIds().stream()
                .map(dataId -> StandardUpdateGdprAction.Arg.of(arg.getApiName(), dataId, arg.getLegalBase()))
                .collect(Collectors.toList());
    }

    @Override
    protected String getButtonApiName() {
        return ObjectAction.UPDATE_GDPR.getButtonApiName();
    }

    @Override
    protected String getActionCode() {
        return ObjectAction.UPDATE_GDPR.getActionCode();
    }
}
