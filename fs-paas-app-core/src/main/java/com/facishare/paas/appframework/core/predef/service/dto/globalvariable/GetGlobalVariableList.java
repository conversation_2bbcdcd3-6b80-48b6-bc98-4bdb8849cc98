package com.facishare.paas.appframework.core.predef.service.dto.globalvariable;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.List;

/**
 * Created by liyiguang on 2017/10/11.
 */
public interface GetGlobalVariableList {

    @Data
    class Arg {
        @JSONField(name = "M1")
        private String label;

        @JSONField(name = "M2")
        private Boolean realTimeTrans;
    }

    @Data
    class Result {
        @JSONField(name = "M2")
        private boolean success;

        @JSONField(name = "M8")
        private List globalVariableList;
    }

}
