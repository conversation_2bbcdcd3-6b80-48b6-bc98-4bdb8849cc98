package com.facishare.paas.appframework.core.predef.handler;

import com.facishare.paas.appframework.core.model.handler.Handler;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.controller.StandardController;
import com.facishare.paas.appframework.core.predef.handler.add.AddActionHandler;
import com.facishare.paas.appframework.core.predef.handler.describelayout.DescribeLayoutHandler;
import com.facishare.paas.appframework.core.predef.handler.detail.DetailHandler;
import com.facishare.paas.appframework.core.predef.handler.edit.EditActionHandler;
import com.facishare.paas.appframework.core.predef.handler.flowcompleted.FlowCompletedActionHandler;
import com.facishare.paas.appframework.core.predef.handler.flowstartcallback.FlowStartCallbackHandler;
import com.facishare.paas.appframework.core.predef.handler.incrementupdate.IncrementUpdateHandler;
import com.facishare.paas.appframework.core.predef.handler.invalid.InvalidActionHandler;
import com.facishare.paas.appframework.core.predef.handler.list.ListHandler;
import com.facishare.paas.appframework.core.predef.handler.list.RelatedListHandler;
import com.facishare.paas.appframework.core.predef.handler.listheader.ListHeaderHandler;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * Created by zhouwr on 2023/4/21.
 */
public class HandlerResultTypeMappings {

    private HandlerResultTypeMappings() {

    }

    private static final Map<String, Class<? extends Handler.Result>> RESULT_TYPE_MAPPINGS = Maps.newHashMap();

    static {
        registerHandlerResultType(StandardAction.Add.name(), AddActionHandler.Result.class);
        registerHandlerResultType(StandardAction.Edit.name(), EditActionHandler.Result.class);
        registerHandlerResultType(StandardAction.Invalid.name(), InvalidActionHandler.Result.class);
        registerHandlerResultType(StandardAction.BulkInvalid.name(), InvalidActionHandler.Result.class);
        registerHandlerResultType(StandardAction.FlowCompleted.name(), FlowCompletedActionHandler.Result.class);
        registerHandlerResultType(StandardAction.FlowStartCallback.name(), FlowStartCallbackHandler.Result.class);
        registerHandlerResultType(StandardAction.IncrementUpdate.name(), IncrementUpdateHandler.Result.class);

        registerHandlerResultType(StandardController.ListHeader.name(), ListHeaderHandler.Result.class);
        registerHandlerResultType(StandardController.List.name(), ListHandler.Result.class);
        registerHandlerResultType(StandardController.RelatedList.name(), RelatedListHandler.Result.class);
        registerHandlerResultType(StandardController.DescribeLayout.name(), DescribeLayoutHandler.Result.class);
        registerHandlerResultType(StandardController.Detail.name(), DetailHandler.Result.class);
        registerHandlerResultType(StandardController.WebDetail.name(), DetailHandler.Result.class);
    }

    public static Class<? extends Handler.Result> getHandlerResultType(String interfaceCode) {
        return RESULT_TYPE_MAPPINGS.get(interfaceCode);
    }

    public static void registerHandlerResultType(String interfaceCode, Class<? extends Handler.Result> resultType) {
        RESULT_TYPE_MAPPINGS.put(interfaceCode, resultType);
    }

}
