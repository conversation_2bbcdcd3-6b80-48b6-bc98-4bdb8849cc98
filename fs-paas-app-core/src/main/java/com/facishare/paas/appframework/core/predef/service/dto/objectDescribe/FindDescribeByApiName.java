package com.facishare.paas.appframework.core.predef.service.dto.objectDescribe;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.IButtonDocument;
import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2017/11/2
 */
public interface FindDescribeByApiName {
    @Data
    class Arg {
        @JSONField(name = "M1")
        @JsonProperty("describe_apiname")
        @SerializedName("describe_apiname")
        String describeApiName;

        @JSONField(name = "M2")
        @JsonProperty("include_layout")
        @SerializedName("include_layout")
        private boolean includeLayout;

        @JSONField(name = "M3")
        @JsonProperty("layout_type")
        @SerializedName("layout_type")
        private String layoutType;

        @JSONField(name = "M4")
        @JsonProperty("include_related_list")
        @SerializedName("include_related_list")
        private boolean includeRelatedList;

        @JsonProperty("include_buttons")
        @SerializedName("include_buttons")
        private boolean includeButtons;

        @JsonProperty("get_label_direct")
        @SerializedName("get_label_direct")
        private boolean getLabelDirect;

        @JsonProperty("fill_quote_field_option")
        @SerializedName("fill_quote_field_option")
        private boolean fillQuoteFieldOption;

        @JsonProperty("include_describe_extra")
        @SerializedName("include_describe_extra")
        private boolean includeDescribeExtra;
        @JsonProperty("include_fields_extra")
        @SerializedName("include_fields_extra")
        private boolean includeFieldsExtra;

        private String appId;
    }

    @Data
    @Builder
    class Result {
        @JSONField(name = "M4")
        private ObjectDescribeDocument objectDescribe;

        @JSONField(name = "M5")
        private LayoutDocument layout;

        @JSONField(name = "M11")
        private List<Map> relatedList;

        @JsonProperty("buttons")
        @SerializedName("buttons")
        private List<IButtonDocument> buttons;

        private ObjectDescribeDocument objectDescribeExt;

        private ObjectDescribeDocument describeExtra;

        private List<Map<String, Object>> fieldsExtra;
    }

}
