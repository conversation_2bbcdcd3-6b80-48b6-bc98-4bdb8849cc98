package com.facishare.paas.appframework.core.predef.handler.invalid;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.AcceptableValidateNoRollbackException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.handler.HandlerContext;
import com.facishare.paas.appframework.core.model.handler.HandlerProvider;
import com.facishare.paas.appframework.core.predef.action.StandardAction;
import com.facishare.paas.appframework.core.predef.action.StandardBulkInvalidAction;
import com.facishare.paas.appframework.core.predef.action.StandardInvalidAction;
import com.facishare.paas.appframework.core.predef.facade.ApprovalFlowServiceFacade;
import com.facishare.paas.appframework.flow.ApprovalFlowStartResult;
import com.facishare.paas.appframework.flow.ApprovalFlowTriggerType;
import com.facishare.paas.appframework.flow.exception.StartApprovalFlowException;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/5/16.
 */
@Component
@HandlerProvider(name = "defaultTriggerApprovalFlowInvalidBeforeHandler")
public class DefaultTriggerApprovalFlowInvalidBeforeHandler implements InvalidActionHandler {

    @Autowired
    private ApprovalFlowServiceFacade approvalFlowServiceFacade;

    @Override
    public Result handle(HandlerContext context, Arg arg) {
        List<ObjectDataDocument> needInvalidDataList = arg.getNeedInvalidDataList();
        IObjectDescribe objectDescribe = arg.getObjectDescribe();
        if (context.skipApprovalFlow() || CollectionUtils.empty(needInvalidDataList) || objectDescribe.isPublicObject()) {
            return new Result();
        }
        Result result;
        if (CollectionUtils.size(needInvalidDataList) > 1) {
            result = triggerApprovalFlowAsync(context, arg);
        } else {
            result = triggerApprovalFlowSync(context, arg);
        }
        //所有数据触发审批，直接跳出主流程
        if (CollectionUtils.size(result.getFilterIdList()) == CollectionUtils.size(arg.getObjectDataList())) {
            throw new AcceptableValidateNoRollbackException(buildInterfaceResult(context));
        }
        return result;
    }

    private Object buildInterfaceResult(HandlerContext context) {
        if (StandardAction.Invalid.name().equals(context.getInterfaceCode())) {
            return new StandardInvalidAction.Result();
        }
        return new StandardBulkInvalidAction.Result();
    }

    private Result triggerApprovalFlowAsync(HandlerContext context, Arg arg) {
        List<IObjectData> objectDataList = arg.needInvalidDataList();
        //不是未生效的数据
        List<IObjectData> effectiveDataList = objectDataList.stream().filter(x -> !ObjectDataExt.of(x).isIneffective()).collect(Collectors.toList());
        approvalFlowServiceFacade.startApprovalFlowAsynchronous(context.getUser(), effectiveDataList, ApprovalFlowTriggerType.INVALID,
                arg.getDataForApprovalFlow(), arg.getCallbackDataForApprovalFlow());
        return Result.builder().filterIdList(ObjectDataExt.getDataId(effectiveDataList)).build();
    }

    private Result triggerApprovalFlowSync(HandlerContext context, Arg arg) {
        List<IObjectData> objectDataList = arg.needInvalidDataList();
        //不是未生效的数据
        List<IObjectData> effectiveDataList = objectDataList.stream().filter(x -> !ObjectDataExt.of(x).isIneffective()).collect(Collectors.toList());
        Map<String, ApprovalFlowStartResult> resultMap = approvalFlowServiceFacade.startApprovalFlow(context.getUser(), effectiveDataList, ApprovalFlowTriggerType.INVALID,
                arg.getDataForApprovalFlow(), arg.getCallbackDataForApprovalFlow(), null);
        if (resultMap.containsValue(ApprovalFlowStartResult.FAILED)) {
            throw new StartApprovalFlowException(I18N.text(I18NKey.OBJECT_APPROVAL_FLOW_FAILED));
        }

        List<IObjectData> filterDataList = objectDataList.stream()
                .filter(x -> !ApprovalFlowStartResult.getNeedTriggerInvalidAfterActionEnum().contains(resultMap.getOrDefault(x.getId(),
                        ApprovalFlowStartResult.APPROVAL_NOT_EXIST)))
                .collect(Collectors.toList());
        List<IObjectData> failDataList = objectDataList.stream()
                .filter(x -> !ApprovalFlowStartResult.getNormalResultEnum().contains(resultMap.getOrDefault(x.getId(),
                        ApprovalFlowStartResult.APPROVAL_NOT_EXIST)))
                .collect(Collectors.toList());

        OpResult opResult = OpResult.builder().build();
        opResult.mergeFailedData(ObjectDataDocument.ofList(failDataList), I18N.text(I18NKey.OBJECT_APPROVAL_FLOW_FAILED));

        return Result.builder()
                .filterIdList(ObjectDataExt.getDataId(filterDataList))
                .opResult(opResult)
                .build();
    }
}
