package com.facishare.paas.appframework.core.predef.action;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.button.action.ActionExecutorContext;
import com.facishare.paas.appframework.button.action.ButtonExecutorContext;
import com.facishare.paas.appframework.button.action.ValidateFuncAction;
import com.facishare.paas.appframework.button.dto.ButtonExecutor;
import com.facishare.paas.appframework.common.service.dto.ImportView;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.AcceptableValidateException;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18N<PERSON>ey;
import com.facishare.paas.appframework.core.model.ImportLogMessage;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.predef.domain.ImportDataActionDomainPlugin;
import com.facishare.paas.appframework.core.predef.service.dto.button.FunctionAction;
import com.facishare.paas.appframework.core.util.ImportExportExt;
import com.facishare.paas.appframework.core.util.Lang;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.dto.batchDataExecuteTask;
import com.facishare.paas.appframework.log.dto.LogInfo;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.DuplicateSearchDataInfo;
import com.facishare.paas.appframework.metadata.dto.ImportReferenceMapping;
import com.facishare.paas.appframework.metadata.dto.ImportSetting;
import com.facishare.paas.appframework.metadata.dto.ImportTenantSetting;
import com.facishare.paas.appframework.metadata.dto.UniqueRuleSearchResult.DuplicateData;
import com.facishare.paas.appframework.metadata.dto.sfa.GetObjectByNames;
import com.facishare.paas.appframework.metadata.duplicated.DuplicatedSearchDataStoreService;
import com.facishare.paas.appframework.metadata.importobject.dataconvert.BatchConvertData;
import com.facishare.paas.appframework.metadata.importobject.dataconvert.BatchConvertResult;
import com.facishare.paas.appframework.metadata.importobject.dataconvert.ImportBatchFieldDataConverter;
import com.facishare.paas.appframework.metadata.options.SelectFieldDependenceLogicService;
import com.facishare.paas.appframework.metadata.options.bo.SelectFieldDependence;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.MasterDetailFieldDescribe;
import com.facishare.paas.metadata.impl.describe.RecordTypeFieldDescribe;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.springframework.dao.DuplicateKeyException;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
public abstract class BaseImportDataAction extends BaseImportAction {

    /**
     * 按ID匹配
     */
    public static final int MATCHING_TYPE_ID = 1;
    /**
     * 按主属性匹配
     */
    public static final int MATCHING_TYPE_NAME = 2;
    /**
     * 按唯一性规则匹配
     */
    public static final int MATCHING_TYPE_UNIQUE_RULE = 3;
    /**
     * 按特定字段匹配
     */
    public static final int MATCHING_TYPE_SPECIFIED_FIELD = 4;

    private final static List<String> NO_CHECK_DATA_PERMISSION_OBJECTS = Lists.newArrayList(
            Utils.PRICE_BOOK_API_NAME,
            Utils.PRICE_BOOK_PRODUCT_API_NAME,
            Utils.CUSTOMER_PAYMENT_API_NAME,
            Utils.GOAL_RULE_API_NAME,
            Utils.HIGHSEAS_API_NAME,
            Utils.LEADS_POOL_API_NAME
    );
    public static final String IMPORT_MARK = "_importMark";
    protected static String ROW_NO = "rowNo";
    protected List<ImportData> dataList;
    protected ObjectDescribeExt objectDescribeExt;
    protected List<IObjectData> actualList = Lists.newArrayList();
    protected List<IObjectData> validList = Lists.newArrayList();
    protected List<ImportError> allErrorList = Lists.newArrayList();
    protected SimpleDateFormat sdtf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
    protected SimpleDateFormat sdtf2 = new SimpleDateFormat("MM/dd/yy HH:mm");
    protected SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
    protected SimpleDateFormat sdf2 = new SimpleDateFormat("MM/dd/yy");
    protected SimpleDateFormat stf = new SimpleDateFormat("HH:mm");
    // 有效导入字段
    protected List<IFieldDescribe> validFieldDescribeList;
    private boolean isCurrentAdmin;
    private final Set<String> noConvertRefFields = Sets.newHashSet();
    private static final String GROUPBY_COUNT = "groupbycount";

    protected Map<Integer, ObjectDataDocument> argDocumentRowMap = Maps.newHashMap();

    private CRMRestService crmRestService;

    protected SelectFieldDependenceLogicService selectFieldDependenceLogicService;

    protected DuplicatedSearchDataStoreService duplicatedSearchDataStoreService;

    private Boolean relatedTeamEnabled = Boolean.TRUE;

    protected TeamMemberRoleService teamMemberRoleService;

    private boolean customTeamRoleGray;

    protected Map<String, String> teamMemberLabelMap = Maps.newHashMap();
    private final List<String> disabledTeamRoles = Lists.newArrayList();

    protected ImportTenantSetting.ImportMethod importMethod;
    protected ImportTenantSetting.FuzzyDuplicateImportStrategy fuzzyDuplicateImportStrategy;

    private CRMRestService getUpdateFieldAction() {
        if (crmRestService == null) {
            crmRestService = serviceFacade.getBean(CRMRestService.class);
        }
        return crmRestService;
    }

    @Override
    protected void before(Arg arg) {
        super.before(arg);
        initTenantSetting();
        initDescribeMap();
        selectFieldDependenceLogicService = serviceFacade.getBean(SelectFieldDependenceLogicService.class);
        duplicatedSearchDataStoreService = serviceFacade.getBean(DuplicatedSearchDataStoreService.class);
        teamMemberRoleService = serviceFacade.getBean(TeamMemberRoleService.class);
        objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
        relatedTeamEnabled = infraServiceFacade.findOptionalFeaturesSwitch(actionContext.getTenantId(), objectDescribe).getIsRelatedTeamEnabled();
        customTeamRoleGray = TeamMember.isTeamRoleGray(actionContext.getTenantId());
        initArgDocumentRowMap(arg);
        //将业务类型设置为必填
        objectDescribeExt.getFieldDescribeSilently(IObjectData.RECORD_TYPE).ifPresent(f -> f.setRequired(Boolean.TRUE));
        handelUniqueRuleField(uniqueRule, objectDescribe);
        initTeamRoleLabelMapping();
        //Label转换成apiName
        dataList = customConvertLabelToApiName(arg.getRows(), objectDescribeExt);
        stopWatch.lap("customConvertLabelToApiName");
        //当前用户是否是管理员
        isCurrentAdmin = serviceFacade.isAdmin(actionContext.getUser());
        stopWatch.lap("isAdmin");
        //按ID匹配，把dataList中关联、主从字段值替换为ID
        importLogMessageBuilder.start(ImportLogMessage.CONVERT_FIELDS);
        convertFields(dataList);
        stopWatch.lap("convertFields");
        importLogMessageBuilder.end(ImportLogMessage.CONVERT_FIELDS);
        importLogMessageBuilder.start(ImportLogMessage.CUSTOM_INIT);
        customInit(dataList);
        importLogMessageBuilder.end(ImportLogMessage.CUSTOM_INIT);
        //校验多语是否正常，默认值为空，多语不为空的情况
        validateMultiLang(dataList, objectDescribe);
        stopWatch.lap("validateMultiLang");
        //获取手机归属地
        getPhoneNumberInfo(dataList);
    }

    protected void validateMultiLang(List<ImportData> dataList, IObjectDescribe describe) {
        if (!AppFrameworkConfig.objectMultiLangGray(actionContext.getTenantId(), describe.getApiName())) {
            return;
        }
        Set<IFieldDescribe> enableMultiLangField = ObjectDescribeExt.of(describe).getEnableMultiLangField();

        for (ImportData importData : dataList) {
            IObjectData objectData = prepareData(importData);
            Set<String> errorFieldLabel = Sets.newHashSet();
            for (IFieldDescribe fieldDescribe : enableMultiLangField) {
                Map<String, Object> multiLangValue = ObjectDataExt.of(objectData).getMultiLangValue(actionContext.getTenantId(), describe.getApiName(), fieldDescribe);
                if (validateMultiLangValueBlank(multiLangValue) && ObjectDataExt.isValueEmpty(objectData.get(fieldDescribe.getApiName()))) {
                    errorFieldLabel.add(fieldDescribe.getLabel());
                }
            }
            if (CollectionUtils.notEmpty(errorFieldLabel)) {
                allErrorList.add(ImportError.builder().rowNo(importData.getRowNo())
                        .objectApiName(describe.getApiName())
                        .errorMessage(I18NExt.getOrDefault(I18NKey.IMPORT_DATA_LANGUAGE_ERROR, "填写了多语值的字段默认值不能为空:{0}", errorFieldLabel))// ignoreI18n
                        .build());
            }
        }
    }

    protected void setDefaultRecordType(List<ImportData> dataList, IObjectDescribe objectDescribe) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_RECORD_TYPE, actionContext.getTenantId())) {
            return;
        }
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        Optional<RecordTypeFieldDescribe> recordTypeField = ObjectDescribeExt.of(objectDescribe).getRecordTypeField();
        if (!recordTypeField.isPresent()) {
            return;
        }
        RecordTypeFieldDescribe recordTypeFieldDescribe = recordTypeField.get();
        List<IRecordTypeOption> recordTypeOptions = recordTypeFieldDescribe.getRecordTypeOptions();
        if (CollectionUtils.empty(recordTypeOptions) || recordTypeOptions.size() != 1) {
            return;
        }
        IRecordTypeOption recordTypeOption = recordTypeOptions.get(0);
        for (ImportData importData : dataList) {
            IObjectData data = importData.getData();
            if (!data.containsField(IFieldType.RECORD_TYPE)) {
                data.setRecordType(recordTypeOption.getApiName());
            }
        }
    }

    protected void initDescribeMap() {

    }

    private void initTenantSetting() {
        if (arg.getImportType() != IMPORT_TYPE_ADD) {
            importMethod = ImportTenantSetting.ImportMethod.NORMAL;
            return;
        }
        ImportTenantSetting importTenantSetting = infraServiceFacade.findImportTenantSetting(actionContext.getUser(), objectDescribe.getApiName());
        if (Objects.isNull(importTenantSetting)) {
            importMethod = ImportTenantSetting.ImportMethod.NORMAL;
            return;
        }
        ImportSetting insertImport = importTenantSetting.getInsertImport();
        importMethod = ImportTenantSetting.ImportMethod.of(insertImport.getImportMethod());
        //导入走新建逻辑 模糊查重策略
        fuzzyDuplicateImportStrategy = ImportTenantSetting.FuzzyDuplicateImportStrategy.of(insertImport.getFuzzyDuplicateStrategy());
    }

    private boolean isAddAction() {
        return ImportTenantSetting.ImportMethod.ADD_ACTION.equals(importMethod) && arg.getImportType() == IMPORT_TYPE_ADD;
    }


    protected void initTeamRoleLabelMapping() {
        if (TeamMember.isTeamMemberTypeExportGray(actionContext.getTenantId())) {
            List<IFieldDescribe> fieldDescribes = infraServiceFacade.generateTeamMemberField(actionContext.getUser(), objectDescribe.getApiName());
            for (IFieldDescribe fieldDescribe : fieldDescribes) {
                teamMemberLabelMap.put(fieldDescribe.getLabel(), fieldDescribe.getApiName());
            }
            return;
        }
        if (!customTeamRoleGray) {
            return;
        }
        List<TeamRoleInfo> teamRoleInfos = infraServiceFacade.findEnableTeamRoleInfosWithoutOwner(actionContext.getUser(), objectDescribe.getApiName());
        teamRoleInfos.forEach(teamRoleInfo -> {
            String roleReadOnlyLabel = String.join("-", teamRoleInfo.getRoleName(), I18NExt.text(I18NKey.constant_read_only));
            String roleReadOnlyValue = String.join("_", teamRoleInfo.getRoleType(), TeamMember.Permission.READONLY.getValue());
            teamMemberLabelMap.put(roleReadOnlyLabel, roleReadOnlyValue);
            String roleWriteLabel = String.join("-", teamRoleInfo.getRoleName(), I18NExt.text(I18NKey.constant_read_write));
            String roleWriteValue = String.join("_", teamRoleInfo.getRoleType(), TeamMember.Permission.READANDWRITE.getValue());
            teamMemberLabelMap.put(roleWriteLabel, roleWriteValue);

        });
    }

    private void initArgDocumentRowMap(Arg arg) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_VALIDATE_FUNC_ARG_DOCUMENT, actionContext.getTenantId())) {
            return;
        }
        if (isSupportFieldMapping()) {
            List<ObjectDataDocument> rows = arg.getRows();
            for (ObjectDataDocument row : rows) {
                ObjectDataDocument data = new ObjectDataDocument();
                Integer rowNo = Integer.valueOf(String.valueOf(row.get(ROW_NO)));
                for (Map.Entry<String, Object> entry : row.entrySet()) {
                    String label = arg.getHeaderExcelCols().stream()
                            .filter(x -> Objects.equals(x.getColIndex(), entry.getKey()))
                            .map(ImportView.ExcelCol::getColName)
                            .findFirst()
                            .orElse(entry.getKey());
                    data.put(label, entry.getValue());
                }
                argDocumentRowMap.put(rowNo, data);
            }
        } else {
            argDocumentRowMap = arg.getRows().stream()
                    .collect(Collectors.toMap(it -> Integer.valueOf(String.valueOf(it.get(ROW_NO))),
                            Function.identity(), (x, y) -> x));
        }
    }

    protected void handelUniqueRuleField(IUniqueRule uniqueRule, IObjectDescribe objectDescribe) {

    }

    protected void calculateFormula(List<IObjectData> validDataList) {

    }

    @Override
    protected Result doAct(Arg arg) {
        Result result = new Result();
        result.setSuccess(true);
        try {
            //ID匹配模式下，校验是否填写了ID
            validateNameID(dataList);
            // 补充归属部门和组织
            fillDataOwnDeptAndOrganization();
            //校验字段
            stopWatch.lap("validateField");
            validateField(objectDescribe, dataList);
            stopWatch.lap("validateField end");
            //校验Excel中重复数据
            validateUniqueDataInExcel();
            //校验数据库中重复数据
            validateUniqueDataInDB();
            //设置默认值
            importLogMessageBuilder.start(ImportLogMessage.CUSTOM_VALIDATE);
            customValidate(dataList);

            //通过领域插件定制导入数据
            customValidateDomainPlugin();
            importLogMessageBuilder.end(ImportLogMessage.CUSTOM_VALIDATE);

            // 处理外部负责人
            dealOuterOwner(dataList);
            importLogMessageBuilder.start(ImportLogMessage.CALL_VALIDATION_FUNCTION);
            executeFunction(result);
            importLogMessageBuilder.end(ImportLogMessage.CALL_VALIDATION_FUNCTION);
            if (isAddAction()) {
                importDataByAddAction();
            } else {
                validList = filterValidDataList(dataList, allErrorList);
                if (!CollectionUtils.empty(validList)) {
                    // 处理合作伙伴以及下游导入相关字段，回填out_tenant_id和out_owner
                    fillPartnerInfo(validList);
                    // 处理代理通外部来源字段
                    fillOutResource(validList);
                    customDefaultValue(validList);
                    // 补充外部归属部门和组织
                    fillDataOutOwnDeptAndOrganization();
                    stopWatch.lap("customAndValidate");
                    importLogMessageBuilder.start("importData");
                    //调用元数据批量导入服务
                    actualList = importData(validList);
                    stopWatch.lap("importData");
                    importLogMessageBuilder.end("importData");
                }
            }
            generateResult(result);
        } catch (AcceptableValidateException e) {
            throw e;
        } catch (AppBusinessException serviceException) {
            log.warn("AppBusinessException in ImportParamData of BulkImportDataService,tenantId:{}, arg:{}",
                    actionContext.getTenantId(),
                    arg, serviceException);
            result.setSuccess(false);
            result.setMessage(serviceException.getMessage());
        } catch (Exception e) {
            log.error("Unexpected Error in ImportParamData of BulkImportDataService,tenantId:{}, arg:{}",
                    actionContext.getTenantId(), arg, e);
            result.setSuccess(false);
            Throwable rootCause = ExceptionUtils.getRootCause(e);
            if (rootCause instanceof DuplicateKeyException) {
                result.setMessage(I18NExt.text(I18NKey.DO_NOT_INPUT_DUPLICATE_PRODUCT));
            } else {
                result.setMessage(I18NExt.text(I18NKey.UNKNOWN_EXCEPTION));
            }
        }

        return result;
    }

    protected void fillDataOutOwnDeptAndOrganization() {
    }

    protected void importDataByAddAction() {
    }

    private void executeFunction(Result result) {
        if (skipFunction()) {
            return;
        }
        if (getImportPreProcessing()) {
            // 预处理函数
            callPreProcessingFunction(dataList, allErrorList, result);
            stopWatch.lap("callPreProcessingFunction");
            // 将结果通过异常返回，阻断后续的操作
            throw new AcceptableValidateException(result);
        } else {
            // 前验证函数
            callValidationFunction(dataList, allErrorList);
            stopWatch.lap("callValidationFunction");
        }
    }

    protected boolean skipFunction() {
        return false;
    }

    @Override
    protected final List<String> getRecordTypes() {
        return CollectionUtils.nullToEmpty(dataList).stream()
                .map(x -> x.getData().getRecordType())
                .filter(x -> !Strings.isNullOrEmpty(x))
                .distinct()
                .collect(Collectors.toList());
    }

    @Override
    protected final ImportDataActionDomainPlugin.Arg buildDomainPluginArg(String method, List<String> recordTypeList) {
        return ImportDataActionDomainPlugin.Arg.builder()
                .dataList(ImportData.toPluginDtoList(dataList, recordTypeList))
                .build();
    }

    @Override
    protected final void processDomainPluginResult(String method, DomainPlugin.Arg pluginArg, DomainPlugin.Result pluginResult) {
        ImportDataActionDomainPlugin.Result domainPluginResult = (ImportDataActionDomainPlugin.Result) pluginResult;
        if (CollectionUtils.notEmpty(domainPluginResult.getErrorList())) {
            mergeErrorList(domainPluginResult.getErrorList());
        }
        if (CollectionUtils.notEmpty(domainPluginResult.getDataListToUpdate())) {
            domainPluginResult.getDataListToUpdate().forEach(x ->
                    dataList.stream()
                            .filter(y -> Objects.equals(y.getRowNo(), x.getRowNo()))
                            .forEach(y -> ObjectDataExt.of(y.getData()).putAll(x.getData())));
        }
    }

    private void customValidateDomainPlugin() {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        runDomainPlugin("customValidate", true, (domainPlugin, pluginArg) ->
                ((ImportDataActionDomainPlugin) domainPlugin).customValidate(actionContext, (ImportDataActionDomainPlugin.Arg) pluginArg));
    }

    protected boolean getImportPreProcessing() {
        return BooleanUtils.isTrue(arg.getImportPreProcessing());
    }

    private void callPreProcessingFunction(List<ImportData> importDataList, List<ImportError> allErrorList, Result result) {
        List<ImportData> validDataList = getValidDataList(importDataList, allErrorList);
        List<IObjectData> objectDataList = getDataList(validDataList);
        if (CollectionUtils.empty(objectDataList)) {
            return;
        }
        IUdefFunction function = findFunction();
        if (Objects.isNull(function)) {
            result.setHasImportPreProcessingFunction(false);
            return;
        }
        FunctionLogicService.FunctionServiceContext functionServiceContext = FunctionLogicService.FunctionServiceContext.builder()
                .function(function)
                .taskId(arg.getJobId())
                .user(actionContext.getUser())
                .finalBatch(BooleanUtils.isTrue(arg.getFinalBatch()))
                .unionImport(isUnionImport())
                .fileCode(arg.getFileCode())
                .build();
        batchDataExecuteTask.Result taskResult = serviceFacade.getFunctionLogicService().importPreProcessingFunction(functionServiceContext, objectDataList);
        ValidateFuncAction.ValidateResult validateResult = ValidateFuncAction.ValidateResult.from(taskResult);

        if (Objects.nonNull(validateResult) && BooleanUtils.isNotTrue(validateResult.getSuccess())) {
            result.setSuccess(false);
            result.setMessage(validateResult.getErrorMessage());
        }
    }

    protected Boolean isUnionImport() {
        return false;
    }

    protected void dealOuterOwner(List<ImportData> dataList) {
        // 根据已填写的外部负责人、回填外部企业信息
        syncOutTenantAndTeamMemberWithOutOwner(dataList);
        checkAndFillOutOwner(dataList);
        // 补充外部相关团队
        fillOutTeamMember(dataList);
    }

    private void syncOutTenantAndTeamMemberWithOutOwner(List<ImportData> dataList) {
        if (objectDescribeExt.isSlaveObject()) {
            return;
        }
        List<IObjectData> objectDataList = dataList.stream()
                .map(ImportData::getData)
                .collect(Collectors.toList());
        // 通过外部企业回填外部负责人信息
        infraServiceFacade.syncOutTenantIdFromOutUser(actionContext.getUser(), objectDescribe, objectDataList);
    }

    private void checkAndFillOutOwner(List<ImportData> dataList) {
        // 从对象不处理
        if (objectDescribeExt.isSlaveObject()) {
            return;
        }
        // 下游导入，不移除外部相关团队，也不校验外部负责人
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUTER_IMPORT_SUPPORT_OUT_OWNER_GRAY, actionContext.getTenantId()) && actionContext.getUser().isOutUser()) {
            return;
        }
        List<IObjectReferenceField> objectReferenceFields = objectDescribeExt.getSupportRelationOuterOwnerFields();
        if (CollectionUtils.empty(objectReferenceFields)) {
            return;
        }

        List<ImportData> toValidateOutOwner = Lists.newArrayList();
        List<ImportData> toFillOutOwner = Lists.newArrayList();
        for (ImportData importData : dataList) {
            IObjectData data = importData.getData();
            ObjectDataExt objectDataExt = ObjectDataExt.of(data);
            if (isFillOutOwner(objectDataExt)) {
                toFillOutOwner.add(importData);
            }
            if (isValidateOutOwner(objectDataExt)) {
                toValidateOutOwner.add(importData);
            }
        }
        // 移除外部相关团队
        removeOldOutTeamMember(dataList);
        // 校验外部负责人
        checkOutOwner(toValidateOutOwner, objectReferenceFields);
        // 回填外部负责人
        fillOutOwner(toFillOutOwner);
    }

    protected boolean isFillOutOwner(ObjectDataExt objectDataExt) {
        String outOwnerId = objectDataExt.getOutOwnerId().orElse(null);
        return Strings.isNullOrEmpty(outOwnerId);
    }

    protected boolean isValidateOutOwner(ObjectDataExt objectDataExt) {
        return !isFillOutOwner(objectDataExt);
    }

    /**
     * 外部负责人有值
     * 不开启校验
     * <p>
     * 开启校验
     * 客户或合作伙伴有值
     * 校验外部负责人是否为客户或合作伙伴对应的下游对接人
     * 客户、合作伙伴都无值
     *
     * @param toValidateOutOwner    带校验的数据
     * @param objectReferenceFields 参与校验的字段
     */
    protected void checkOutOwner(List<ImportData> toValidateOutOwner, List<IObjectReferenceField> objectReferenceFields) {
        List<ImportError> errorList;
        if (CollectionUtils.empty(toValidateOutOwner)) {
            return;
        }
        if (!needCheckOuterOwner()) {
            // 校验负责人是否为当前企业的对接人
            errorList = checkIsDownstreamContact(toValidateOutOwner);
            mergeErrorList(errorList);
            return;
        }
        Map<Integer, Boolean> referentFieldAllEmpty = Maps.newHashMap();
        for (IObjectReferenceField objectReferenceField : objectReferenceFields) {
            Map<String, Set<String>> dataIdUserIdMap = queryDataId2UserIdMap(objectReferenceField, toValidateOutOwner);
            // 校验通过的数据下一轮不需要校验
            toValidateOutOwner.removeIf(importData -> {
                IObjectData data = importData.getData();
                String referenceObjectId = data.get(objectReferenceField.getApiName(), String.class);
                // 关联的联系人、客户的是否都为空
                referentFieldAllEmpty.compute(importData.getRowNo(), (key, oldValue) -> {
                    if (Objects.isNull(oldValue)) {
                        return Strings.isNullOrEmpty(referenceObjectId);
                    }
                    return oldValue && Strings.isNullOrEmpty(referenceObjectId);
                });
                String outOwnerId = ObjectDataExt.of(data).getOutOwnerId().orElse(null);
                return dataIdUserIdMap.getOrDefault(referenceObjectId, Collections.emptySet()).contains(outOwnerId);
            });
        }
        errorList = toValidateOutOwner.stream()
                .filter(importData -> BooleanUtils.isNotTrue(referentFieldAllEmpty.get(importData.getRowNo())))
                .map(importData -> ImportError.builder()
                        .rowNo(importData.getRowNo())
                        .errorMessage(I18NExt.getOrDefault(I18NKey.EXTERNAL_OWNER_NON_DOWNSTREAM_CONTACT_PERSON, I18NKey.EXTERNAL_OWNER_NON_DOWNSTREAM_CONTACT_PERSON))
                        .objectApiName(objectDescribe.getApiName())
                        .build())
                .collect(Collectors.toList());
        mergeErrorList(errorList);
    }

    private List<ImportError> checkIsDownstreamContact(List<ImportData> toValidateOutOwner) {
        List<ImportError> errorList = Lists.newArrayList();

        Set<String> outUserIds = Sets.newHashSet();
        for (ImportData importData : toValidateOutOwner) {
            IObjectData data = importData.getData();
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            dataExt.getOutOwnerId().ifPresent(outUserIds::add);
        }
        if (CollectionUtils.empty(outUserIds)) {
            return errorList;
        }

        Map<String, Boolean> outUserMap = infraServiceFacade.isOuterUsersByTenantId(actionContext.getUser(), outUserIds);

        for (ImportData importData : toValidateOutOwner) {
            IObjectData data = importData.getData();
            ObjectDataExt dataExt = ObjectDataExt.of(data);
            dataExt.getOutOwnerId().ifPresent(it -> {
                if (BooleanUtils.isNotTrue(outUserMap.get(it))) {
                    ImportError importError = ImportError.builder()
                            .rowNo(importData.getRowNo())
                            .errorMessage(I18NExt.getOrDefault(I18NKey.OUTER_OWNER_NOT_DOWNSTREAM_PERSON, I18NKey.OUTER_OWNER_NOT_DOWNSTREAM_PERSON))
                            .objectApiName(objectDescribe.getApiName())
                            .build();
                    errorList.add(importError);
                }
            });
        }
        return errorList;
    }

    private Map<String, Set<String>> queryDataId2UserIdMap(IObjectReferenceField objectReferenceField, List<ImportData> toValidateOutOwner) {
        Set<String> referenceObjectIds = toValidateOutOwner.stream()
                .map(ImportData::getData)
                .map(it -> it.get(objectReferenceField.getApiName(), String.class))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toSet());
        if (CollectionUtils.empty(referenceObjectIds)) {
            return Maps.newHashMap();
        }
        return queryReferenceObjectId2UserIdMap(objectReferenceField.getTargetApiName(), referenceObjectIds);
    }

    /**
     * 客户联系人 id 和 下游对接人的映射关系
     *
     * @param targetApiName
     * @param referenceObjectIds
     * @return
     */
    private Map<String, Set<String>> queryReferenceObjectId2UserIdMap(String targetApiName, Set<String> referenceObjectIds) {
        Map<String, RelationDownstreamResult> resultMap = infraServiceFacade.getRelationDownstreamInfo(actionContext.getTenantId(),
                targetApiName, referenceObjectIds);
        List<String> outTenantIds = resultMap.values().stream()
                .map(RelationDownstreamResult::getDownstreamOuterTenantId)
                .map(String::valueOf)
                .distinct()
                .collect(Collectors.toList());

        List<User> users = infraServiceFacade.batchGetOutUsersByOutTenants(actionContext.getUser(), outTenantIds);
        Map<String, Set<String>> outTenantUserMap = users.stream().collect(Collectors.groupingBy(User::getOutTenantId,
                Collectors.mapping(User::getOutUserId, Collectors.toSet())));

        Map<String, Set<String>> result = Maps.newHashMap();
        resultMap.forEach((dataId, relationResult) -> {
            String outTenantId = String.valueOf(relationResult.getDownstreamOuterTenantId());
            result.put(dataId, CollectionUtils.nullToEmpty(outTenantUserMap.get(outTenantId)));
        });
        return result;
    }

    protected void fillOutOwner(List<ImportData> toFillOutOwner) {
        if (CollectionUtils.empty(toFillOutOwner)) {
            return;
        }
        List<IObjectData> objectDataList = toFillOutOwner.stream().map(ImportData::getData).collect(Collectors.toList());
        infraServiceFacade.fillOutOwner(actionContext.getUser(), objectDescribeExt, objectDataList);
    }

    protected void fillOutTeamMember(List<ImportData> dataList) {
    }

    protected void handleOutTeamMember(List<ImportData> dataList) {
        if (objectDescribeExt.isSlaveObject()) {
            return;
        }
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        List<IObjectData> objectDataList = dataList.stream().map(ImportData::getData).collect(Collectors.toList());
        infraServiceFacade.fillOutTeamMember(actionContext.getUser(), objectDescribeExt, objectDataList);
    }


    protected void removeOldOutTeamMember(List<ImportData> dataList) {
        if (!removeOutTeamMember()) {
            return;
        }
        for (ImportData importData : dataList) {
            ObjectDataExt objectDataExt = ObjectDataExt.of(importData.getData());
            objectDataExt.removeOutTeamMember();
        }
    }

    protected abstract boolean removeOutTeamMember();

    protected boolean needCheckOuterOwner() {
        return BooleanUtils.isTrue(arg.getCheckOutOwner());
    }

    private void callValidationFunction(List<ImportData> importDataList, List<ImportError> allErrorList) {
        List<ImportData> validDataList = getValidDataList(importDataList, allErrorList);
        if (CollectionUtils.empty(validDataList)) {
            return;
        }
        IUdefFunction function = findFunction();
        if (Objects.isNull(function)) {
            return;
        }
        List<ImportError> errorList = Lists.newArrayList();
        for (ImportData importData : validDataList) {
            IObjectData data = fillCustomInfo2FunctionData(importData);
            ObjectDataDocument argDocument = null;
            if (CollectionUtils.notEmpty(argDocumentRowMap)) {
                int rowNo = importData.getRowNo();
                argDocument = argDocumentRowMap.get(rowNo);
            }
            ButtonExecutor.Result result = callValidationFunctionOneData(data, function.getApiName(), argDocument);
            customProcessValidationFunctionResult(importData, result);
            if (result.isHasReturnValue() && Objects.nonNull(result.getReturnValue())) {
                errorList.add(new ImportError(importData.getRowNo(), result.getReturnValue().toString()));
            }
        }
        mergeErrorList(errorList);
    }

    // 业务用拓展函数执行结果
    protected void customProcessValidationFunctionResult(ImportData importData, ButtonExecutor.Result result) {

    }

    private List<IObjectData> getDataList(List<ImportData> importDataList) {
        List<IObjectData> result = Lists.newArrayList();
        for (ImportData importData : importDataList) {
            IObjectData data = fillCustomInfo2FunctionData(importData);
            result.add(data);
        }
        return result;
    }

    protected IObjectData fillCustomInfo2FunctionData(ImportData importData) {
        IObjectData data = ObjectDataExt.of(importData.getData()).getObjectData();
        data.set("_RowNo", importData.getRowNo());
        data.set("_TaskId", arg.getJobId());
        return data;
    }

    private ButtonExecutor.Result callValidationFunctionOneData(IObjectData objectData, String funcApiNam, ObjectDataDocument argDocument) {
        ButtonExecutor.Arg executorArg = ButtonExecutor.Arg.builder()
                .describeApiName(actionContext.getObjectApiName())
                .objectData(getValidationFunctionObjectData(objectData))
                .actionParams(argDocument)
                .build();
        ButtonExt buttonExt = ButtonExt.of(LayoutButtonExt.of(ObjectAction.BATCH_IMPORT.createButton()).toMap());
        ButtonExecutorContext executorContext = ButtonExecutorContext.builder()
                .describe(objectDescribe)
                .user(actionContext.getUser())
                .button(buttonExt.getButton())
                .build();
        ActionExecutorContext actionContext = ActionExecutorContext.of(executorContext, buildButtonAction(funcApiNam));
        log.debug("callValidationFunction arg:{}", executorArg);
        ButtonExecutor.Result validatedFunctionResult = validateFuncAction.invoke(executorArg, actionContext);
        log.debug("callValidationFunction result:{}", validatedFunctionResult);
        return validatedFunctionResult;
    }

    protected IObjectData getValidationFunctionObjectData(IObjectData objectData) {
        return objectData;
    }

    private IUdefAction buildButtonAction(String funcApiName) {
        return FunctionAction.ActionParameter.createByFuncApiName(funcApiName).toIUdefAction();
    }

    private List<ImportData> getValidDataList(List<ImportData> dataList, List<ImportError> allErrorList) {
        if (CollectionUtils.empty(allErrorList)) {
            return dataList;
        }
        Set<Integer> errorRowNo = allErrorList.stream().map(ImportError::getRowNo).collect(Collectors.toSet());
        return dataList.stream().filter(it -> !errorRowNo.contains(it.getRowNo())).collect(Collectors.toList());
    }

    protected abstract IUdefFunction findFunction();

    protected void fillDataOwnDeptAndOrganization() {

    }

    protected final void validDuplicateSearch() {
        if (!isOpenImportDuplicate()) {
            return;
        }
        if (CollectionUtils.empty(duplicatedSearchList)) {
            return;
        }
        // 为缺少id 的数据补充临时id
        List<IObjectData> objectDataList = dataList.stream().map(this::prepareData).collect(Collectors.toList());
        Tuple<List<IObjectData>, List<IObjectData>> noIdDataTuple = ObjectDataExt.fillDataId(objectDataList, null, objectDescribe);

        //给数据补充合作伙伴字段
        Map<String, Set<String>> fillFieldValueMap = fillFieldValue(objectDataList);
        try {
            // 校验查重规则规则(同一批)
            validDuplicateSearchInExcel(objectDataList);
            // 校验查重规则规则(数据库)
            validDuplicateSearchInDB(objectDataList);
        } finally {
            // 删除临时id
            ObjectDataExt.removeDataId(noIdDataTuple, objectDescribe);
            ObjectDataExt.removeTempFillId(objectDataList, fillFieldValueMap);
        }
    }

    protected abstract Map<String, Set<String>> fillFieldValue(List<IObjectData> objectDataList);


    protected boolean isSupportImport() {
        duplicatedSearchList.removeIf(x -> !(DuplicatedSearchExt.isSupportImport(x)
                && !DuplicatedSearchExt.of(x).machFuzzyRule()));
        return !CollectionUtils.empty(duplicatedSearchList);
    }

    private boolean isOpenImportDuplicate() {
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(actionContext.getTenantId(), objectDescribe.getApiName())) {
            duplicatedSearchList.removeIf(x -> !(DuplicatedSearchExt.isSupportImport(x)));
            return !CollectionUtils.empty(duplicatedSearchList);
        }
        return isSupportImport();
    }

    private void validDuplicateSearchInExcel(List<IObjectData> objectDataList) {
        // 加工特殊字段（lookup、单选 等）

        for (IDuplicatedSearch duplicatedSearch : duplicatedSearchList) {
            objectDataList = serviceFacade.processFieldValueForDuplicatedSearch(duplicatedSearch, objectDescribe, objectDataList, actionContext.getUser());
        }
        Map<String, IDuplicatedSearch> ruleApiNameDescribeMap = duplicatedSearchList.stream()
                .collect(Collectors.toMap(x -> getRuleMapKey(x.getDescribeApiName(), DuplicatedSearchExt.of(x).getRealRuleApiName()), t -> t, (x1, x2) -> x1));

        List<DuplicateSearchDataInfo> searchDataInfos = getDuplicateSearchDataInfos(objectDataList);
        if (CollectionUtils.empty(searchDataInfos)) {
            return;
        }

        Map<String, List<DuplicateSearchDataInfo>> searchDataInfoMap = Maps.newHashMap();
        searchDataInfos.forEach(x -> searchDataInfoMap.computeIfAbsent(x.getSourceDataId(), it -> Lists.newArrayList()).add(x));

        Map<String, Set<Integer>> dataIdRowNoMap = dataList.stream().collect(Collectors.groupingBy(it -> it.getData().getId(),
                Collectors.mapping(ImportData::getRowNo, Collectors.toSet())));
        for (ImportData importData : dataList) {
            List<ImportError> errorList = Lists.newArrayList();
            String id = importData.getData().getId();
            if (!searchDataInfoMap.containsKey(id)) {
                continue;
            }
            List<DuplicateSearchDataInfo> duplicateSearchDataInfos = searchDataInfoMap.get(id);
            duplicateSearchDataInfos.removeIf(x -> !x.isDuplicated());

            for (DuplicateSearchDataInfo duplicateSearchDataInfo : duplicateSearchDataInfos) {
                String rowLine = duplicateSearchDataInfo.getDataIds()
                        .stream().filter(x -> !StringUtils.equals(id, x))
                        .map(dataIdRowNoMap::get)
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        .map(String::valueOf)
                        .collect(Collectors.joining(","));
                IDuplicatedSearch duplicatedSearch = ruleApiNameDescribeMap.get(getRuleMapKey(duplicateSearchDataInfo.getApiName(), duplicateSearchDataInfo.getRuleApiName()));

                ImportError importError;
                if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(actionContext.getTenantId(), objectDescribe.getApiName())) {
                    importError = new ImportError(importData.getRowNo(),
                            I18NExt.getOrDefault(I18NKey.MULTI_DUPLICATE_DATA_IN_IMPORTED_DATA, I18NKey.MULTI_DUPLICATE_DATA_IN_IMPORTED_DATA, rowLine, duplicatedSearch.getName()));
                } else {
                    importError = new ImportError(importData.getRowNo(),
                            I18NExt.getOrDefault(I18NKey.DUPLICATE_DATA_IN_IMPORTED_DATA, I18NKey.DUPLICATE_DATA_IN_IMPORTED_DATA, rowLine));
                }

                errorList.add(importError);
            }
            mergeErrorList(errorList);
        }
    }

    private List<DuplicateSearchDataInfo> getDuplicateSearchDataInfos(List<IObjectData> objectDataList) {
        boolean useMultiRule = serviceFacade.useMultiRule(actionContext.getUser(), objectDescribe.getApiName());
        return duplicatedSearchDataStoreService.multiSaveAndDuplicateData(actionContext.getUser(), objectDescribe, duplicatedSearchList, objectDataList, useMultiRule);
    }


    private void validDuplicateSearchInDB(List<IObjectData> objectDataList) {
        // 调用查重接口
        List<DuplicateSearchDataInfo> searchDataInfoList = serviceFacade.searchDuplicateDataListByType(actionContext.getUser(),
                objectDataList, IDuplicatedSearch.Type.NEW, objectDescribe, duplicatedSearchList, serviceFacade.useMultiRule(actionContext.getUser(), objectDescribe.getApiName()));
        if (CollectionUtils.empty(searchDataInfoList)) {
            return;
        }

        Map<String, IDuplicatedSearch> ruleApiNameDescribeMap = duplicatedSearchList.stream()
                .collect(Collectors.toMap(x -> getRuleMapKey(x.getDescribeApiName(), DuplicatedSearchExt.of(x).getRealRuleApiName()), t -> t, (x1, x2) -> x1));

        // 重复数据的有提示信息
        Map<String, DuplicateSearchDataInfo> duplicateSearchDataInfoMap = searchDataInfoList.stream()
                .filter(it -> Objects.equals(it.getApiName(), objectDescribe.getApiName()))
                .filter(DuplicateSearchDataInfo::isDuplicated)
                .collect(Collectors.toMap(DuplicateSearchDataInfo::getSourceDataId, Function.identity(), DuplicateSearchDataInfo::merge));

        Set<String> idList = Sets.newHashSet();
        duplicateSearchDataInfoMap.values().forEach(x -> idList.addAll(x.getDataIds()));
        List<IObjectData> objectDataByIds = serviceFacade.findObjectDataByIds(actionContext.getTenantId(), Lists.newArrayList(idList), objectDescribe.getApiName());
        Map<String, String> idName = CollectionUtils.nullToEmpty(objectDataByIds).stream().collect(Collectors.toMap(DBRecord::getId, IObjectData::getName));

        List<ImportError> errorList = Lists.newArrayList();
        List<Map<String, Object>> logInfo = null;
        for (ImportData importData : dataList) {
            logInfo = Lists.newArrayList();
            String dataId = importData.getData().getId();
            DuplicateSearchDataInfo searchDataInfo = duplicateSearchDataInfoMap.get(dataId);
            if (Objects.nonNull(searchDataInfo)) {
                int rowNo = importData.getRowNo();
                ImportError importError;
                // 根据重复数据的id拿到name属性并提示
                List<String> names = idConvertToName(searchDataInfo.getDataIds(), idName);
                if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(actionContext.getTenantId(), objectDescribe.getApiName())) {
                    importError = new ImportError(rowNo, I18NExt.text(I18NKey.MULTI_DUPLICATE_DATA_IN_IMPORTED_DATA_SYSTEM,
                            names, ruleApiNameDescribeMap.get(getRuleMapKey(searchDataInfo.getApiName(), searchDataInfo.getRuleApiName())).getName()));
                } else {
                    importError = new ImportError(rowNo, I18NExt.text(I18NKey.DUPLICATE_DATA_IN_SYSTEM));
                }
                errorList.add(importError);
                Map<String, Object> info = Maps.newHashMap();
                info.put("rowNo", rowNo);
                info.put("sourceDataId", dataId);
                info.put("dataIds", searchDataInfo.getDataIds());
                info.put("ruleName", ruleApiNameDescribeMap.get(getRuleMapKey(searchDataInfo.getApiName(), searchDataInfo.getRuleApiName())).getName());
                logInfo.add(info);
            }
        }
        if (CollectionUtils.notEmpty(logInfo)) {
            log.info("validDuplicateSearchInDB ,logInfo:{}", JacksonUtils.toJson(logInfo));
        }
        mergeErrorList(errorList);
    }

    private String getRuleMapKey(String describeApiName, String ruleApiName) {
        if (StringUtils.isEmpty(ruleApiName)) {
            return describeApiName;
        }
        return describeApiName + ruleApiName;
    }

    private List<String> idConvertToName(LinkedHashSet<String> dataIds, Map<String, String> idName) {
        List<String> result = Lists.newArrayList();
        for (String dataId : dataIds) {
            String name = idName.get(dataId);
            if (StringUtils.isEmpty(name)) {
                result.add(dataId);
            } else {
                result.add(name + "(" + dataId + ")");
            }
        }
        return result;
    }

    protected abstract IObjectData prepareData(ImportData importData);

    protected void validUniquenessRule() {
        // 唯一性规则字段不能为空
        validUniquenessRuleFieldNoEmpty();
        // 校验唯一性规则(同一批)
        validUniquenessRuleInExcel();
        // 校验唯一性规则(数据库)
        validUniquenessRuleInDB();
    }

    protected void validUniquenessRuleFieldNoEmpty() {
        List<String> fieldNames = getRuleFieldName(uniqueRule);
        if (CollectionUtils.empty(fieldNames)) {
            return;
        }
        List<ImportError> errorList = dataList.stream().map(importData -> {
            boolean fieldAllIsEmpty = fieldNames.stream().allMatch(fieldName -> isNullOrEmpty(importData.getData(), fieldName));
            if (!fieldAllIsEmpty) {
                return null;
            }
            String errorMessage = fieldNames.stream()
                    .map(fieldName -> objectDescribeExt.getFieldLabelByName(fieldName))
                    .collect(Collectors.joining(","));
            if (fieldNames.size() == 1) {
                return new ImportError(importData.getRowNo(),
                        I18NExt.getOrDefault(I18NKey.UNIQUENESS_RULE_FIELDS_NOT_EMPTY, I18NKey.UNIQUENESS_RULE_FIELDS_NOT_EMPTY, errorMessage));
            }
            return new ImportError(importData.getRowNo(),
                    I18NExt.getOrDefault(I18NKey.UNIQUENESS_RULE_FIELDS_NOT_ALL_EMPTY, I18NKey.UNIQUENESS_RULE_FIELDS_NOT_ALL_EMPTY, errorMessage));
        }).filter(Objects::nonNull).collect(Collectors.toList());
        mergeErrorList(errorList);
    }

    private boolean isNullOrEmpty(IObjectData data, String fieldName) {
        String stringValue = getStringValue(data, fieldName);
        return Strings.isNullOrEmpty(stringValue);
    }

    protected abstract void validUniquenessRuleInDB();

    /**
     * 当前版本
     * 1. 一个对象只允许配一组唯一性规则
     * 2. 规则配置时可支持对象下选择1至5个字段 组合
     * 3. 选择字段均为精确匹配
     * 4. 如果是多个字段，均为"AND"的关系。例【客户】对象选择"客户名称"、"客户编号"两个字段，则唯一性规则为：当"客户名称"和"客户编号"完成相同时，系统判断为同一条记录
     */
    protected void validUniquenessRuleInExcel() {
        List<String> fieldNames = getRuleFieldName(uniqueRule);
        if (CollectionUtils.empty(fieldNames)) {
            return;
        }

        Map<String, List<ImportData>> uniqueMap = getUniqueMap(fieldNames);
        List<ImportError> errorList = uniqueMap.values().stream()
                .filter(this::isRepeat)
                .flatMap(objectDataList -> objectDataList.stream()
                        .map(importData -> new ImportError(importData.getRowNo(), getErrorMessage(getPrefix(fieldNames), objectDataList))))
                .collect(Collectors.toList());
        mergeErrorList(errorList);
    }

    private Map<String, List<ImportData>> getUniqueMap(List<String> fieldNames) {
        Map<String, List<ImportData>> resultMap = Maps.newHashMap();
        dataList.forEach(importData -> {
            String key = getUniqueKeyContainsEmpty(fieldNames, importData);
            if (!Strings.isNullOrEmpty(key)) {
                resultMap.computeIfAbsent(key, t -> Lists.newArrayList()).add(importData);
            }
        });
        return resultMap;
    }


    private boolean isRepeat(List<ImportData> importData) {
        return CollectionUtils.notEmpty(importData) && importData.size() > 1;
    }

    /**
     * 注意此方法会生成临时id用于查询唯一性规则
     * 更新导入时，需要处理生成的临时id
     *
     * @return
     */
    protected Map<String, List<String>> findDuplicateDataMapByUniquenessRule() {
        List<IObjectData> objectDataList = generatorTemporaryId();
        List<DuplicateData> duplicateData = serviceFacade.findDuplicateData(actionContext.getUser(), objectDataList, objectDescribe);

        return duplicateData.stream()
                .filter(x -> CollectionUtils.notEmpty(x.getDataIds()))
                .collect(Collectors.toMap(DuplicateData::getId, DuplicateData::getDataIds));
    }

    private List<IObjectData> generatorTemporaryId() {
        return dataList.stream()
                .map(importData -> {
                    IObjectData data = importData.getData();
                    data.setId(String.valueOf(importData.getRowNo()));
                    return data;
                }).collect(Collectors.toList());
    }

    private String getErrorMessage(String prefix, List<ImportData> importDataList) {
        return importDataList.stream()
                .map(importData -> String.valueOf(importData.getRowNo()))
                .collect(Collectors.joining(",", prefix, ""));
    }

    private String getPrefix(List<String> fieldNameList) {
        return I18NExt.getOrDefault(I18NKey.FIELD_VALUE_REPEAT, I18NKey.FIELD_VALUE_REPEAT,
                fieldNameList.stream().map(objectDescribeExt::getFieldLabelByName)
                        .collect(Collectors.joining(" and ")));
    }

    private String getUniqueKeyContainsEmpty(List<String> fieldNameList, ImportData importData) {
        List<String> uniqueDataList = ObjectDataDocument.of(importData.getData()).entrySet().stream()
                .filter(entry -> fieldNameList.contains(entry.getKey()))
                .map(entry -> Objects.isNull(entry.getValue()) ? "" : String.valueOf(entry.getValue()))
                .collect(Collectors.toList());
        if (StringUtils.isAllBlank(uniqueDataList.toArray(new String[0]))) {
            return null;
        }
        return String.join("\n", uniqueDataList);
    }

    protected List<String> getRuleFieldName(IUniqueRule uniqueRule) {
        if (!UniqueRuleExt.isEffectiveWhenImport(uniqueRule)) {
            return Collections.emptyList();
        }
        return UniqueRuleExt.of(uniqueRule).getRuleFieldName();
    }

    protected void convertFields(List<ImportData> dataList) {
        convertFields(dataList, objectDescribe);
    }

    protected void convertFields(List<ImportData> dataList, IObjectDescribe objectDescribe) {
        List<BatchConvertData> convertDataList = dataList.stream().map(ImportData::toBatchConvertData).collect(Collectors.toList());
        Set<String> fieldApiNames = getFieldApiNames(convertDataList);
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        List<IFieldDescribe> fieldDescribes = describeExt.filter(fieldDescribe -> fieldApiNames.contains(fieldDescribe.getApiName()));
        List<String> sortedFields = describeExt.getCountryAreaFields().stream()
                .sorted(Comparator.comparing(it -> ofType(it.getType())))
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toList());
        List<IFieldDescribe> fieldDescribeList = CollectionUtils.sortByGivenOrder(fieldDescribes, sortedFields, IFieldDescribe::getApiName);
        fieldDescribeList.forEach(fieldDescribe -> {
            ImportBatchFieldDataConverter converter = getFieldDataConverter(objectDescribe, sortedFields, fieldDescribe);
            if (converter == null) {
                return;
            }
            List<BatchConvertResult> convertResults = converter.convertFieldData(convertDataList, objectDescribe, fieldDescribe, buildConvertFieldContext());
            if (CollectionUtils.empty(convertResults)) {
                return;
            }
            // 拿到错误提示信息
            List<ImportError> errorList = convertResults.stream()
                    .filter(batchConvertResult -> Objects.nonNull(batchConvertResult.getConvertResult()))
                    .filter(batchConvertResult -> !batchConvertResult.getConvertResult().isSuccess())
                    .map(batchConvertResult -> ImportError.builder()
                            .rowNo(batchConvertResult.getNo())
                            .errorMessage(batchConvertResult.getConvertResult().getErrorMessage())
                            .objectApiName(describeExt.getApiName())
                            .build())
                    .collect(Collectors.toList());
            mergeErrorList(errorList);
        });
        //批量的做lookup和masterDetail对象的校验
        validateRelatedObject(arg.getMatchingType());
    }

    private ImportBatchFieldDataConverter getFieldDataConverter(IObjectDescribe objectDescribe, List<String> sortedFields, IFieldDescribe fieldDescribe) {
        ImportBatchFieldDataConverter converter;
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.IMPORT_SUPPORT_COUNTRY_AREA_FORM_DB, actionContext.getTenantId())
                && sortedFields.contains(fieldDescribe.getApiName())) {
            ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
            Optional<String> groupType = describeExt.getGroupFields().stream()
                    .filter(x -> describeExt.getGroupFieldList(x.getApiName()).stream()
                            .anyMatch(y -> Objects.equals(y.getApiName(), fieldDescribe.getApiName())))
                    .map(x -> ((GroupField) x).getGroupType())
                    .findFirst();
            if (groupType.isPresent()) {
                converter = infraServiceFacade.getBatchFieldDataConverter(groupType.get());
            } else {
                log.warn("import skip field convert,tenantId:{},fieldApiName:{}", actionContext.getTenantId(), fieldDescribe.getApiName());
                return null;
            }
        } else {
            converter = infraServiceFacade.getBatchFieldDataConverter(fieldDescribe.getType());
        }
        return converter;
    }

    protected ImportBatchFieldDataConverter.DataConverterContext buildConvertFieldContext() {
        return ImportBatchFieldDataConverter.DataConverterContext.builder()
                .user(actionContext.getUser())
                .build();
    }

    // 处理国家省市区字段的顺序，保证按顺序处理
    private int ofType(String type) {
        if (IFieldType.COUNTRY.equals(type)) {
            return 1;
        }
        if (IFieldType.PROVINCE.equals(type)) {
            return 2;
        }
        if (IFieldType.CITY.equals(type)) {
            return 3;
        }
        if (IFieldType.DISTRICT.equals(type)) {
            return 4;
        }
        if (IFieldType.TOWN.equals(type)) {
            return 5;
        }
        if (IFieldType.VILLAGE.equals(type)) {
            return 6;
        }
        return 0;
    }


    private Set<String> getFieldApiNames(List<BatchConvertData> convertDataList) {
        return convertDataList.stream()
                .map(BatchConvertData::getObjectData)
                .map(ObjectDataExt::of)
                .map(x -> x.toMap())
                .flatMap(x -> x.keySet().stream())
                .collect(Collectors.toSet());
    }

    /**
     * 校验数据ID
     */
    protected void validateNameID(List<ImportData> dataList) {
    }

    protected void fillPartnerInfo(List<IObjectData> validList) {
    }

    protected void fillOutResource(List<IObjectData> validList) {
    }

    protected abstract void customInit(List<ImportData> dataList);

    protected abstract void customValidate(List<ImportData> list);

    @Override
    protected Result after(Arg arg, Result result) {
        Result after = super.after(arg, result);
        updateDuplicateSearchData(actualList);
        customAfterImport(actualList);
        return after;
    }

    protected void updateDuplicateSearchData(List<IObjectData> actualList) {
    }

    protected void customAfterImport(List<IObjectData> actualList) {
        // 新建导入触发审批流
        startImportApprovalFlow();

        //触发工作流
        Boolean isWorkFlowEnable = arg.getIsWorkFlowEnabled();
        if (null != isWorkFlowEnable && isWorkFlowEnable) {
            startImportWorkFlow(actualList);
        }
        recordImportDataLog(actualList);
        stopWatch.lap("recordImportDataLog");
    }

    /**
     * 触发审批流
     */
    protected abstract void startImportApprovalFlow();

    /**
     * 记录日志
     */
    protected abstract void recordImportDataLog(List<IObjectData> actualList);

    /**
     * 触发工作流
     */
    protected abstract void startImportWorkFlow(List<IObjectData> actualList);

    protected abstract void customDefaultValue(List<IObjectData> list);

    protected abstract List<IObjectData> importData(List<IObjectData> list);

    /**
     * @param data     数据
     * @param describe 描述
     * @return
     */
    protected boolean checkUniqueInDB(IObjectData data, IObjectDescribe describe) {
        return true;
    }

    /**
     * 检查导入数据在数据库的唯一性
     */
    protected abstract void validateUniqueDataInDB();

    /**
     * 检查Excel中数据是否有重复行
     */
    protected void validateUniqueDataInExcel() {

        Map<String, Map<String, List<ImportData>>> uniqueMap = buildUniqueMap();

        if (CollectionUtils.empty(uniqueMap)) {
            return;
        }

        List<ImportError> errorList = checkUnique(uniqueMap);

        mergeErrorList(errorList);
    }

    private List<ImportError> checkUnique(Map<String, Map<String, List<ImportData>>> uniqueMap) {
        List<ImportError> errorList = Lists.newArrayList();
        List<IFieldDescribe> fieldDescribes = objectDescribeExt.getFieldDescribes();
        // 补充临时补充多语字段的描述
        ImportExportExt.supportMultiLangField(actionContext.getTenantId(), fieldDescribes, objectDescribeExt.getApiName());
        Map<String, IFieldDescribe> fieldDescribeMap = fieldDescribes.stream()
                .collect(Collectors.toMap(IFieldDescribe::getApiName, Function.identity()));
        Set<Map.Entry<String, Map<String, List<ImportData>>>> entrySet = uniqueMap.entrySet();
        for (Map.Entry<String, Map<String, List<ImportData>>> entry : entrySet) {
            Map<String, List<ImportData>> valueMap = entry.getValue();
            if (CollectionUtils.empty(valueMap)) {
                continue;
            }

            Set<Map.Entry<String, List<ImportData>>> valueEntrySet = valueMap.entrySet();
            for (Map.Entry<String, List<ImportData>> valueEntry : valueEntrySet) {
                List<ImportData> list = valueEntry.getValue();
                if (CollectionUtils.empty(list) || list.size() <= 1) {
                    continue;
                }

                //存在重复，在errorList中加入，在validList中去掉
                StringBuilder lineNo = new StringBuilder();
                for (ImportData importData : list) {
                    lineNo.append(importData.getRowNo()).append(",");
                }
                IFieldDescribe fieldDescribe = fieldDescribeMap.get(entry.getKey());
                String message = I18NExt.getOrDefault(I18NKey.ROW_VALUE_DUPLICATE, I18NKey.ROW_VALUE_DUPLICATE, lineNo.toString(), fieldDescribe.getLabel());
                for (ImportData importData : list) {
                    errorList.add(new ImportError(importData.getRowNo(), message));
                }
            }
        }
        return errorList;
    }

    private Map<String, Map<String, List<ImportData>>> buildUniqueMap() {
        Map<String, Map<String, List<ImportData>>> uniqueMap = Maps.newHashMap();
        if (CollectionUtils.empty(dataList)) {
            return uniqueMap;
        }
        for (ImportData importData : dataList) {
            Set<Map.Entry<String, Object>> entries = ObjectDataDocument.of(importData.getData()).entrySet();
            entries.forEach(entry -> {
                Optional<IFieldDescribe> fieldDescribeSilently = objectDescribeExt.getFieldDescribeSilently(entry.getKey());
                fieldDescribeSilently.ifPresent(fieldDescribe -> {
                    // 处理多语字段
                    if (BooleanUtils.isTrue(fieldDescribe.getEnableMultiLang())) {
                        Map<String, Object> multiLangValue = ObjectDataExt.of(importData.getData()).getMultiLangValue(actionContext.getTenantId(), objectDescribeExt.getApiName(), fieldDescribe);
                        multiLangValue.forEach((key, value) -> {
                            String fieldValue = ObjectDataExt.formatValueInImport(value);
                            if (isCheckExcelUniqueField(fieldDescribe, fieldValue)) {
                                add2UniqueMap(uniqueMap, importData, key, fieldValue);
                            }
                        });
                    }
                    String fieldValue = getStringValue(importData.getData(), fieldDescribe);
                    if (isCheckExcelUniqueField(fieldDescribe, fieldValue)) {
                        add2UniqueMap(uniqueMap, importData, fieldDescribe.getApiName(), fieldValue);
                    }
                });
            });
        }
        return uniqueMap;
    }

    private void add2UniqueMap(Map<String, Map<String, List<ImportData>>> uniqueMap, ImportData importData, String fieldApiName, String fieldValue) {
        //添加到uniqueMap，判断在当前批次是否有重复的
        if (uniqueMap.containsKey(fieldApiName)) {
            Map<String, List<ImportData>> valueMap = uniqueMap.get(fieldApiName);
            if (valueMap.containsKey(fieldValue)) {
                valueMap.get(fieldValue).add(importData);
            } else {
                valueMap.put(fieldValue, Lists.newArrayList(importData));
            }
        } else {
            Map<String, List<ImportData>> valueMap = Maps.newHashMap();
            valueMap.put(fieldValue, Lists.newArrayList(importData));
            uniqueMap.put(fieldApiName, valueMap);
        }
    }

    protected boolean isCheckExcelUniqueField(IFieldDescribe fieldDescribe, String fieldValue) {
        return BooleanUtils.isTrue(fieldDescribe.isUnique()) && StringUtils.isNotBlank(fieldValue);
    }

    protected void batchHandleObjectReference(List<ImportError> errorList, Map<IFieldDescribe, List<String>> defObjMap) {
        if (CollectionUtils.empty(defObjMap)) {
            return;
        }
        batchHandleObjectReference(errorList, objectDescribe, defObjMap, dataList);
    }

    private void batchHandleObjectReference(List<ImportError> errorList, IObjectDescribe objectDescribe,
                                            Map<IFieldDescribe, List<String>> defObjMap, List<ImportData> dataList) {
        if (CollectionUtils.empty(defObjMap)) {
            return;
        }

        Set<Map.Entry<IFieldDescribe, List<String>>> entries = defObjMap.entrySet();
        for (Map.Entry<IFieldDescribe, List<String>> entry : entries) {
            IFieldDescribe field = entry.getKey();
            String targetApiName;
            String targetFieldApiName = IObjectData.NAME; // 默认使用主属性
            List<String> nameList;
            if (Objects.equals(IFieldType.OBJECT_REFERENCE_MANY, field.getType())) {
                targetApiName = ObjectReferenceWrapper.of(field).getTargetApiName();
                nameList = entry.getValue().stream()
                        .filter(StringUtils::isNotBlank)
                        .flatMap(name -> Arrays.stream(name.split("\\|")))
                        .collect(Collectors.toList());
            } else if (Objects.equals(IFieldType.OBJECT_REFERENCE, field.getType())) {
                targetApiName = ObjectReferenceWrapper.of(field).getTargetApiName();
                nameList = entry.getValue();
            } else {
                MasterDetailFieldDescribe fieldDescribe = (MasterDetailFieldDescribe) field;
                targetApiName = fieldDescribe.getTargetApiName();
                nameList = entry.getValue();
            }
            IObjectDescribe targetDescribe = serviceFacade.findObject(actionContext.getTenantId(), targetApiName);
            List<IObjectData> referenceDataList;
            if (ObjectDescribeExt.isSFANotStandardObject(targetApiName)) {
                referenceDataList = getRefCRMObjData(nameList, targetApiName);
            } else {
                if (importReferenceFieldMappingSwitch) {
                    targetFieldApiName = importReferenceMapping.getReferenceFieldMapping().stream()
                            .filter(x -> Objects.equals(x.getObjectApiName(), objectDescribe.getApiName()))
                            .filter(x -> Objects.equals(x.getObjectReferenceFieldApiName(), field.getApiName()))
                            .filter(x -> Objects.equals(x.getTargetObjectApiName(), targetApiName))
                            .map(ImportReferenceMapping.ReferenceFieldMapping::getSpecifiedUniqueFieldApiName)
                            .findFirst()
                            .orElse(null);
                    Optional<IFieldDescribe> fieldDescribeSilently = ObjectDescribeExt.of(targetDescribe).getActiveFieldDescribeSilently(targetFieldApiName);
                    if (!fieldDescribeSilently.isPresent()) {
                        for (ImportData importData : dataList) {
                            errorList.add(ImportError.builder()
                                    .rowNo(importData.getRowNo())
                                    .errorMessage(I18NExt.text(I18NKey.REFERENCE_TARGET_FIELD_NOT_EXIST, field.getLabel()))
                                    .objectApiName(field.getDescribeApiName())
                                    .build());
                        }
                        continue;
                    }
                    // 唯一性id的唯一性不进行校验，都可以选择
                    if (!fieldDescribeSilently.get().isUnique() && !(IObjectData.ID.equals(fieldDescribeSilently.get().getApiName()))) {
                        for (ImportData importData : dataList) {
                            errorList.add(ImportError.builder()
                                    .rowNo(importData.getRowNo())
                                    .errorMessage(I18NExt.text(I18NKey.REFERENCE_TARGET_FIELD_NOT_UNIQUE, field.getLabel()))
                                    .objectApiName(field.getDescribeApiName())
                                    .build());
                        }
                        continue;
                    }
                }
                if (targetDescribe.getFieldDescribe(targetFieldApiName).isUnique() || IObjectData.ID.equals(targetFieldApiName)) {
                    referenceDataList = getRefDefObjData(nameList, targetApiName, targetFieldApiName);
                } else {
                    referenceDataList = getRefDefObjDataByAggregateSearchQuery(nameList, targetApiName);
                }
            }
            //校验
            checkAndFillReference(referenceDataList, field, errorList, dataList, targetFieldApiName);
        }
    }

    /**
     * @param referenceDataList  查找关联的对象的数据集合
     * @param field              查找关联字段
     * @param dataList           导入的数据集合
     * @param targetFieldApiName 关联对象上的唯一性目标字段
     */
    private void checkAndFillReference(List<IObjectData> referenceDataList, IFieldDescribe field,
                                       List<ImportError> errorList, List<ImportData> dataList, String targetFieldApiName) {
        //根据服务返回结果配置errorList和objectData的name替换成id
        boolean hasResult = !CollectionUtils.empty(referenceDataList);
        String label = field.getLabel();
        for (ImportData importData : dataList) {
            if (field.getType().equals(IFieldType.OBJECT_REFERENCE_MANY)) {
                handleObjectReferenceManny(referenceDataList, field, errorList, label, importData, targetFieldApiName);
            } else {
                handleLookUpImportData(referenceDataList, field, errorList, hasResult, label, importData, targetFieldApiName);
            }
        }
    }

    /**
     * @param referenceDataList include real data and aggregate count(if count>1)，
     */
    private void handleObjectReferenceManny(List<IObjectData> referenceDataList, IFieldDescribe fieldDescribe,
                                            List<ImportError> errorList, String label, ImportData importData, String targetFieldApiName) {
        String name = getStringValue(importData.getData(), fieldDescribe);
        if (Strings.isNullOrEmpty(name)) {
            return;
        }
        Set<String> refNameSet = Sets.newHashSet(Arrays.asList(name.split("\\|")));
        if (CollectionUtils.empty(refNameSet)) {
            return;
        }
        List<IObjectData> duplicatedDataList = Lists.newArrayList();
        List<String> matchedDataIds = Lists.newArrayList();
        // 最终使用的目标字段名
        String finalFieldName = StringUtils.defaultIfBlank(targetFieldApiName, IObjectData.NAME);
        for (IObjectData data : CollectionUtils.nullToEmpty(referenceDataList)) {
            String value = ObjectDataExt.of(data).getStringValue(finalFieldName);
            if (StringUtils.isBlank(value)) {
                continue;
            }
            if (refNameSet.contains(value)) {
                if (Objects.nonNull(data.get(GROUPBY_COUNT)) && (int) data.get(GROUPBY_COUNT) > 1) {
                    duplicatedDataList.add(data);
                    break;
                } else {
                    matchedDataIds.add(data.getId());
                }
            }
        }
        if (CollectionUtils.notEmpty(duplicatedDataList)) {
            errorList.add(ImportError.builder()
                    .rowNo(importData.getRowNo())
                    .errorMessage(I18NExt.getOrDefault(I18NKey.REFERENCE_NAME_DUPLICATE, "字段{0}关联数据主属性重复，请使用唯一性ID方式导入。", label))// ignoreI18n
                    .objectApiName(fieldDescribe.getDescribeApiName())
                    .build());
        } else if (CollectionUtils.empty(matchedDataIds)) {
            //没有权限
            errorList.add(ImportError.builder()
                    .rowNo(importData.getRowNo())
                    .errorMessage(I18NExt.getOrDefault(I18NKey.RELATED_OBJECT_DATA_DELETE_OR_NO_PRIVILEGE, "{0}关联的对象数据不存在或没有权限！", label))// ignoreI18n
                    .objectApiName(fieldDescribe.getDescribeApiName())
                    .build());
        } else {
            checkObjectReferenceManyMaxNum(importData, fieldDescribe, matchedDataIds, errorList);
        }
    }

    private void checkObjectReferenceManyMaxNum(ImportData importData, IFieldDescribe fieldDescribe, List<String> matchedDataIds, List<ImportError> errorList) {
        int maxNum = FieldManyMaxConfig.getObjectReferenceManyMaxLimit(actionContext.getTenantId(), fieldDescribe.getDescribeApiName());
        if (matchedDataIds.size() > maxNum) {
            errorList.add(ImportError.builder()
                    .rowNo(importData.getRowNo())
                    .errorMessage(I18NExt.getOrDefault(I18NKey.OBJECT_REFERENCE_MANY_FIELD_BEYOND_MAX_LIMIT, "查找关联多选字段数量超出最大限制{0}:{1}", fieldDescribe.getLabel()))// ignoreI18n
                    .objectApiName(fieldDescribe.getDescribeApiName())
                    .build());
        } else {
            importData.getData().set(fieldDescribe.getApiName(), matchedDataIds);
        }
    }

    private void handleLookUpImportData(List<IObjectData> referenceDataList, IFieldDescribe fieldDescribe,
                                        List<ImportError> errorList, boolean hasResult, String label,
                                        ImportData importData, String targetFieldApiName) {
        String name = getStringValue(importData.getData(), fieldDescribe);
        if (Strings.isNullOrEmpty(name)) {
            return;
        }
        IObjectData objectData = importData.getData();
        // 最终使用的目标字段名
        String finalFieldName = StringUtils.defaultIfBlank(targetFieldApiName, IObjectData.NAME);
        // 替换Name为ID， 校验
        IObjectData matchedData = null;
        if (hasResult) {
            matchedData = referenceDataList.stream()
                    .filter(data -> name.equals(data.get(finalFieldName))) // 使用目标字段名
                    .findFirst().orElse(null);
        }
        if (Objects.nonNull(matchedData)) {
            if (Objects.nonNull(matchedData.get(GROUPBY_COUNT)) && (int) matchedData.get(GROUPBY_COUNT) > 1) {
                // 关联对象数据重复
                errorList.add(ImportError.builder()
                        .rowNo(importData.getRowNo())
                        .errorMessage(I18NExt.getOrDefault(I18NKey.REFERENCE_NAME_DUPLICATE, "字段{0}关联数据主属性重复，请使用唯一性ID方式导入。", label))// ignoreI18n
                        .objectApiName(fieldDescribe.getDescribeApiName())
                        .build());
            } else {
                objectData.set(fieldDescribe.getApiName(), matchedData.getId());
                if (Objects.equals(IFieldType.MASTER_DETAIL, fieldDescribe.getType())) {
                    syncMasterDetailFields(importData, objectData, matchedData, (MasterDetail) fieldDescribe, errorList);
                }
            }
        } else {
            //没有权限
            errorList.add(ImportError.builder()
                    .rowNo(importData.getRowNo())
                    .errorMessage(I18NExt.getOrDefault(I18NKey.RELATED_OBJECT_DATA_DELETE_OR_NO_PRIVILEGE, "{0}关联的对象数据不存在或没有权限！", label))// ignoreI18n
                    .objectApiName(fieldDescribe.getDescribeApiName())
                    .build());
        }
    }

    /**
     * 根据ID匹配，不需要查重
     */
    protected void batchHandleObjectReferenceById(List<ImportError> errorList, Map<IFieldDescribe, List<String>> defObjMap, String tenantId) {
        batchHandleObjectReferenceById(errorList, defObjMap, tenantId, dataList);
    }

    /**
     * 根据ID匹配，不需要查重
     */
    private void batchHandleObjectReferenceById(List<ImportError> errorList, Map<IFieldDescribe, List<String>> defObjMap, String tenantId, List<ImportData> dataList) {
        if (CollectionUtils.empty(defObjMap)) {
            return;
        }
        Set<Map.Entry<IFieldDescribe, List<String>>> entries = defObjMap.entrySet();
        for (Map.Entry<IFieldDescribe, List<String>> entry : entries) {
            IFieldDescribe fieldDescribe = entry.getKey();
            String targetApiName;
            List<String> idValueList;
            if (IFieldType.OBJECT_REFERENCE_MANY.equals(fieldDescribe.getType())) {
                targetApiName = ObjectReferenceWrapper.of(fieldDescribe).getTargetApiName();
                idValueList = CollectionUtils.nullToEmpty(entry.getValue()).stream()
                        .filter(StringUtils::isNotBlank)
                        .flatMap(name -> Arrays.stream(name.split("\\|")))
                        .collect(Collectors.toList());
            } else if (IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())) {
                targetApiName = ObjectReferenceWrapper.of(fieldDescribe).getTargetApiName();
                idValueList = entry.getValue();
            } else {
                targetApiName = ((MasterDetailFieldDescribe) fieldDescribe).getTargetApiName();
                idValueList = entry.getValue();
            }
            List<IObjectData> referenceDataList = serviceFacade.findObjectDataByIdsIgnoreFormula(tenantId, idValueList, targetApiName);
            //将主对象的数据存起来用于校验
            Map<String, IObjectData> masterDataMap = Maps.newHashMap();
            if (IFieldType.MASTER_DETAIL.equals(fieldDescribe.getType())) {
                CollectionUtils.nullToEmpty(referenceDataList).forEach(x -> masterDataMap.put(x.getId(), x));
            }
            //根据服务返回结果配置errorList和objectData的name替换成id, 过滤掉作废或删除的数据
            List<String> idList = referenceDataList.stream().filter(data -> !data.isDeleted()).map(IObjectData::getId).collect(Collectors.toList());
            boolean hasResult = CollectionUtils.notEmpty(idList);
            for (ImportData importData : dataList) {
                IObjectData objectData = importData.getData();
                List<String> ids = getReferenceIds(objectData, fieldDescribe);
                if (CollectionUtils.empty(ids)) {
                    continue;
                }
                List<String> noneMatchValue = ids.stream()
                        .filter(it -> !idList.contains(it))
                        .distinct()
                        .collect(Collectors.toList());
                // 校验
                if (!hasResult || CollectionUtils.notEmpty(noneMatchValue)) {
                    //没有权限
                    errorList.add(ImportError.builder()
                            .rowNo(importData.getRowNo())
                            .errorMessage(I18NExt.getOrDefault(I18NKey.RELATED_OBJECT_DATA_DELETE_OR_NO_PRIVILEGE, "{0}关联的对象数据不存在或没有权限！", fieldDescribe.getLabel()))// ignoreI18n
                            .objectApiName(fieldDescribe.getDescribeApiName())
                            .build());
                } else if (IFieldType.OBJECT_REFERENCE_MANY.equals(fieldDescribe.getType())) {
                    checkObjectReferenceManyMaxNum(importData, fieldDescribe, ids, errorList);
                } else if (Objects.equals(IFieldType.MASTER_DETAIL, fieldDescribe.getType())) {
                    IObjectData masterData = masterDataMap.get(ids.get(0));
                    // 同步主对象部分属性到从对象
                    syncMasterDetailFields(importData, objectData, masterData, (MasterDetail) fieldDescribe, errorList);
                }
            }
        }
    }

    private List<String> getReferenceIds(IObjectData objectData, IFieldDescribe fieldDescribe) {
        String id = getStringValue(objectData, fieldDescribe);
        if (Strings.isNullOrEmpty(id)) {
            return Collections.emptyList();
        }
        if (IFieldType.OBJECT_REFERENCE_MANY.equals(fieldDescribe.getType())) {
            return Lists.newArrayList(id.split("\\|"));
        }
        return Lists.newArrayList(id);
    }

    private void syncMasterDetailFields(ImportData importData, IObjectData objectData, IObjectData masterData,
                                        MasterDetail masterDetailField, List<ImportError> errorList) {
        if (Objects.isNull(masterData)) {
            return;
        }
        objectData.setDataOwnDepartment(masterData.getDataOwnDepartment());
        if (objectDescribeExt.isOpenOrganization()) {
            objectData.setDataOwnOrganization(masterData.getDataOwnOrganization());
        }
        if (objectDescribeExt.isExistOutDataOwnDepartment()) {
            ObjectDataExt.of(objectData).setOutDataOwnDepartmentId(ObjectDataExt.of(masterData).getOutDataOwnDepartmentId());
        }
        if (objectDescribeExt.isExistOutDataOwnOrganization()) {
            ObjectDataExt.of(objectData).setOutDataOwnOrganizationId(ObjectDataExt.of(masterData).getOutDataOwnOrganizationId());
        }
        //校验主对象是否已锁定
        if (AppFrameworkConfig.isInMasterDetailApprovalGrayList(actionContext.getTenantId())
                && !Boolean.FALSE.equals((masterDetailField).getIsCreateWhenMasterCreate())) {
            ObjectDataExt masterDataExt = ObjectDataExt.of(masterData);
            ObjectDataExt.of(objectData).setLifeStatus(masterDataExt.getLifeStatus());
            ObjectDataExt.of(objectData).setLockStatus(masterDataExt.getLockStatus());
            if (masterDataExt.isLock() || masterDataExt.isInChange()) {
                errorList.add(ImportError.builder()
                        .rowNo(importData.getRowNo())
                        .errorMessage(I18NExt.text(I18NKey.CANNOT_IMPORT_BECAUSE_OF_MASTER_LOCKED))
                        .objectApiName(masterDetailField.getDescribeApiName())
                        .build());
            }
        }
    }

    private List<IObjectData> getRefCRMObjData(List<String> nameList, String targetApiName) {
        if (CollectionUtils.empty(nameList)) {
            return null;
        }

        try {
            // "联系人"对象查询数据时不过滤重复数据的参数
            RequestContextManager.getContext().setAttribute(GetObjectByNames.NEED_REPEATED_DATA, Boolean.TRUE);
            List<GetObjectByNames.NameIDPojo> objectDataList = getUpdateFieldAction().getObjectByNames(
                    actionContext.getUser(), targetApiName, nameList);
            if (CollectionUtils.empty(objectDataList)) {
                return null;
            }

            List<IObjectData> result = Lists.newArrayList();
            for (GetObjectByNames.NameIDPojo data : objectDataList) {
                if (data == null || Strings.isNullOrEmpty(data.getObjectName())) {
                    log.warn("NameIdPojo is invalid，data is null?{}", data == null);
                    if (data != null) {
                        log.info("NameIdPojo, objectName:{}, objectId:{}, hasPermission:{}", data.getObjectName(), data.getObjectID(), data.getHasPermission());
                    }
                    continue;
                }
                Map<String, Object> map = Maps.newHashMap();
                map.put(IObjectData.ID, data.getObjectID());
                map.put(IObjectData.NAME, data.getObjectName());
                map.put(IObjectData.OWNER, data.getOwnerID());
                result.add(ObjectDataExt.of(map).getObjectData());

            }
            return result;
        } catch (Exception e) {
            log.error("Error in getCRMObjData in bulkImport", e);
            return null;
        }
    }

    private boolean checkDataPermission(String apiName) {
        return !isCurrentAdmin && !NO_CHECK_DATA_PERMISSION_OBJECTS.contains(apiName);
    }

    /*
     * 根据主属性name聚合查询数据
     */
    private List<IObjectData> getRefDefObjDataByAggregateSearchQuery(List<String> nameList, String targetApiName) {
        if (CollectionUtils.empty(nameList)) {
            return null;
        }
        List<IObjectData> result = Lists.newArrayList();
        // 构造SearchTemplateQuery
        SearchTemplateQuery query = createSearchQuery(nameList.size(), targetApiName, nameList, IObjectData.NAME);
        query.setSearchSource("db");
        // 先聚合查询，返回结果的IObject中，只包含name和count
        List<IObjectData> dataList1 = serviceFacade.findByAggregateSearchQuery(actionContext.getUser(),
                query, targetApiName, ObjectDataExt.NAME);
        if (CollectionUtils.empty(dataList1)) {
            return null;
        }
        // count == 1 的数据筛选出来等待第二次查询详细数据信息
        List<String> names = dataList1.stream().filter(
                        data -> Objects.nonNull(data.get(GROUPBY_COUNT)) && (int) data.get(GROUPBY_COUNT) == 1)
                .map(IObjectData::getName).collect(Collectors.toList());
        // count == 1 的数据不为空
        if (CollectionUtils.notEmpty(names)) {
            SearchTemplateQuery query2 = createSearchQuery(names.size(), targetApiName, names, IObjectData.NAME);
            QueryResult<IObjectData> dataList2 = serviceFacade.findBySearchQuery(actionContext.getUser(), targetApiName, query2);
            if (CollectionUtils.notEmpty(dataList2.getData())) {
                result.addAll(dataList2.getData());
            }
        }
        // 遍历聚合结果，count > 1 的数据说明主属性重复
        List<IObjectData> duplicatedDataList = dataList1.stream().filter(d -> Objects.nonNull(d.get(GROUPBY_COUNT)) &&
                (int) d.get(GROUPBY_COUNT) > 1).collect(Collectors.toList());
        if (CollectionUtils.notEmpty(duplicatedDataList)) {
            result.addAll(duplicatedDataList);
        }
        return result;
    }

    private List<IObjectData> getRefDefObjData(List<String> nameList, String targetApiName, String fieldApiName) {
        if (CollectionUtils.empty(nameList)) {
            return null;
        }
        SearchTemplateQuery query = createSearchQuery(nameList.size() * 3, targetApiName, nameList, fieldApiName);
        QueryResult<IObjectData> searchResult = serviceFacade.findBySearchQuery(actionContext.getUser(), targetApiName, query);
        if (null == searchResult || CollectionUtils.empty(searchResult.getData())) {
            return null;
        }

        return searchResult.getData();
    }

    // 构造search query
    private SearchTemplateQuery createSearchQuery(int limit, String apiName, List<String> nameList, String fieldApiName) {
        SearchTemplateQuery query = new SearchTemplateQuery();
        query.setLimit(limit);
        query.setOffset(0);
        query.setNeedReturnCountNum(Boolean.FALSE);
        if (checkDataPermission(apiName)) {
            IObjectDescribe describe = serviceFacade.findObject(actionContext.getTenantId(), apiName);
            serviceFacade.handleDataRightsParameter(describe, query, actionContext.getUser());
        }

        List<IFilter> filters = Lists.newArrayList();
        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.IS_DELETED);
        filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
        filter.setFieldValueType("string");
        filter.setOperator(Operator.EQ);
        filter.setIndexName(IObjectData.IS_DELETED);
        filter.setFieldValues(Collections.singletonList("0"));
        filters.add(filter);

        filter = new Filter();
        filter.setFieldName(IObjectData.DESCRIBE_API_NAME);
        filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
        filter.setFieldValueType("string");
        filter.setOperator(Operator.EQ);
        filter.setIndexName(IObjectData.DESCRIBE_API_NAME);
        filter.setFieldValues(Collections.singletonList(apiName));
        filters.add(filter);

        if (IObjectData.NAME.equals(fieldApiName)) {
            filter = new Filter();
            filter.setFieldName(IObjectData.NAME);
            filter.setFieldDefineType(IObjectDescribe.DEFINE_TYPE_SYSTEM);
            filter.setFieldValueType("string");
            filter.setOperator(Operator.IN);
            filter.setIndexName(IObjectData.NAME);
            filter.setFieldValues(nameList);
            filters.add(filter);
        } else {
            IFilter customFilter = new Filter();
            customFilter.setFieldName(fieldApiName);
            customFilter.setOperator(Operator.IN);
            customFilter.setFieldValues(nameList);
            filters.add(customFilter);
        }

        query.setFilters(filters);
        return query;
    }

    protected List<String> getNameList(List<ImportData> dataList, String apiName) {
        if (Strings.isNullOrEmpty(apiName) || CollectionUtils.empty(dataList)) {
            return null;
        }

        List<String> result = Lists.newArrayList();
        for (ImportData data : dataList) {
            // 关联或者主从关系字段的值，不去除首尾特殊符号
            String value = ObjectDataExt.of(data.getData()).getStringValueInImportForReference(apiName);
            if (Strings.isNullOrEmpty(value) || result.contains(value)) {
                continue;
            }
            result.add(value);
        }
        return result;
    }


    private String convertAndValidateFieldValue(ImportData data, IObjectDescribe describe) {
        //根据field类型校验
        StringBuilder stringBuilder = new StringBuilder();
        data.getData().setDescribeApiName(describe.getApiName());
        data.getData().setTenantId(actionContext.getTenantId());
        Map<String, Object> doc = ObjectDataExt.of(data.getData()).toMap();
        List<String> fields = Lists.newArrayList(doc.keySet());
        try {
            for (String fieldApiName : fields) {
                IFieldDescribe fieldDescribe = describe.getFieldDescribe(fieldApiName);
                String errorMessage = doConvertAndValidateFieldValue(data, fieldDescribe);
                if (!Strings.isNullOrEmpty(errorMessage)) {
                    stringBuilder.append(errorMessage).append("\n");
                }
            }
            //检验级联单选的选项合法性
            String error = _checkCascadedSelectOne(data.getData(), describe);
            if (!Strings.isNullOrEmpty(error)) {
                stringBuilder.append(error).append("\n");
            }
            // 日期范围字段校验
            List<String> validateResults = ObjectDataExt.of(data.getData()).validateDateRangeField(describe);
            validateResults.forEach(msg -> stringBuilder.append(msg).append("\n"));
        } catch (Exception e) {
            log.error("Exception in ValidateData in BulkImportServiceImpl", e);
            stringBuilder.append(I18NExt.text(I18NKey.UNKNOWN_ERROR)).append("\n");
            return stringBuilder.toString();
        }

        return stringBuilder.toString();
    }

    private String _checkCascadedSelectOne(IObjectData data, IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        List<IFieldDescribe> fieldDescribes = describeExt.stream()
                .map(FieldDescribeExt::of)
                .filter(FieldDescribeExt::isCascadeChildField)
                .map(FieldDescribeExt::<IFieldDescribe>getFieldDescribe)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(fieldDescribes)) {
            return null;
        }

        StringJoiner joiner = new StringJoiner("\n");
        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            SelectOne selectOne = (SelectOne) fieldDescribe;
            String parentApiName = selectOne.getCascadeParentApiName();
            if (Strings.isNullOrEmpty(parentApiName)) {
                continue;
            }
            //数据上没有父级字段，不进行校验
            if (!ObjectDataExt.of(data).containsField(parentApiName)) {
                continue;
            }

            // 添加对业务类型的级联校验
            boolean parentFieldExist = describeExt.getFieldDescribeSilently(parentApiName)
                    .map(FieldDescribeExt::of)
                    .map(FieldDescribeExt::isCascadeParentField)
                    .orElse(false);
            // 父字段不存在,忽略校验
            if (!parentFieldExist) {
                continue;
            }
            List<String> currentValues = getSelectValues(data, selectOne);
            if (CollectionUtils.empty(currentValues)) {
                continue;
            }
            String parentValue = getParentSelectOneValue(data, parentApiName);
            // 字选项有值,父选项不存在
            if (Strings.isNullOrEmpty(parentValue)) {
                joiner.add(I18NExt.getOrDefault(I18NKey.OPTION_FALL_SHORT_SELECT_ONT_RULE, I18NKey.OPTION_FALL_SHORT_SELECT_ONT_RULE, selectOne.getLabel()));
                continue;
            }
            // 父子选项都有值,校验选项关系是否正确
            Optional<SelectFieldDependence> selectFieldDependence = selectFieldDependenceLogicService.find(actionContext.getUser(),
                    describe.getApiName(), parentApiName, selectOne.getApiName());
            if (!selectFieldDependence.isPresent()) {
                continue;
            }
            boolean success = selectFieldDependence.map(it -> it.isChildFields(parentValue, currentValues)).orElse(false);
            if (success) {
                continue;
            }
            joiner.add(I18NExt.getOrDefault(I18NKey.OPTION_FALL_SHORT_SELECT_ONT_RULE, I18NKey.OPTION_FALL_SHORT_SELECT_ONT_RULE, selectOne.getLabel()));
        }
        return joiner.toString();
    }

    private List<String> getSelectValues(IObjectData data, SelectOne selectOne) {
        Object value = data.get(selectOne.getApiName());
        if (value instanceof List) {
            return (List) value;
        }
        if (value == null) {
            return Collections.emptyList();
        }
        String str = String.valueOf(value);
        if (Strings.isNullOrEmpty(str)) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(str);
    }


    protected abstract String getParentSelectOneValue(IObjectData data, String parentApiName);

    private String doConvertAndValidateFieldValue(ImportData data, IFieldDescribe fieldDescribe) {
        if (Objects.isNull(fieldDescribe)) {
            return null;
        }
        String valueStr = getStringValue(data.getData(), fieldDescribe);
        if (fieldDescribe.isRequired() && Strings.isNullOrEmpty(valueStr)) {
            if (MATCHING_TYPE_ID == arg.getMatchingType()
                    && (IFieldType.OBJECT_REFERENCE.equals(fieldDescribe.getType())
                    || IFieldType.MASTER_DETAIL.equals(fieldDescribe.getType())
                    || IFieldType.OBJECT_REFERENCE_MANY.equals(fieldDescribe.getType()))) {
                //如果是根据唯一性ID校验且为关联或主从字段，这里不进行校验，避免文案重复
                return null;
            } else {
                return checkWithMustFillField(fieldDescribe);
            }
        }
        return null;
    }

    /**
     * 新建导入必填项不填写直接返回错误信息
     * 更新导入必填项不填写，返回信息取决于是否更新空内容
     * fieldDescribe字段对象的值为空且必填
     *
     * @return string or null
     */
    protected String checkWithMustFillField(IFieldDescribe fieldDescribe) {
        if (!fieldDescribe.isRequired()) {
            return null;
        }
        String label = fieldDescribe.getLabel();
        return I18NExt.getOrDefault(I18NKey.PLEASE_INPUT, I18NKey.PLEASE_INPUT, label);
    }

    protected String getStringValue(IObjectData data, IFieldDescribe fieldDescribe) {
        if (Objects.isNull(fieldDescribe)) {
            return "";
        }
        return getStringValue(data, fieldDescribe.getApiName());
    }

    protected String getStringValue(IObjectData data, String apiName) {
        return ObjectDataExt.of(data).getStringValueInImport(apiName);
    }

    protected List<IObjectData> filterValidDataList(List<ImportData> dataList, List<ImportError> allErrorList) {
        List<IObjectData> result = Lists.newArrayList();
        for (ImportData importData : dataList) {
            boolean hasError = false;
            for (ImportError importError : allErrorList) {
                if (Objects.equals(importData.getRowNo(), importError.getRowNo())) {
                    hasError = true;
                    break;
                }
            }
            if (!hasError) {
                result.add(importData.getData());
            }
        }
        return result;
    }

    protected void generateResult(Result result) {
        ImportResultValue resultValue = new ImportResultValue();
        if (!CollectionUtils.empty(actualList)) {
            log.info("Import data size:{}", actualList.size());
            resultValue.setImportSucceedCount(actualList.size());
        }
        //所有的错误的数据结果
        resultValue.setRowErrorList(allErrorList);
        result.setValue(resultValue);
    }

    protected void validateField(IObjectDescribe objectDescribe, List<ImportData> dataList) {
        List<ImportError> errorList = Lists.newArrayList();
        for (ImportData importData : dataList) {
            String errorMessage = convertAndValidateFieldValue(importData, objectDescribe);
            if (Strings.isNullOrEmpty(errorMessage)) {
                continue;
            }

            ImportError importError = new ImportError();
            importError.setRowNo(importData.getRowNo());
            importError.setErrorMessage(errorMessage);
            importError.setObjectApiName(objectDescribe.getApiName());
            errorList.add(importError);
        }
        mergeErrorList(errorList);
    }

    protected void mergeErrorList(List<ImportError> errorList) {
        for (ImportError newError : errorList) {
            boolean isExist = false;
            for (ImportError error : allErrorList) {
                if (Objects.equals(error.getRowNo(), newError.getRowNo())) {
                    isExist = true;
                    error.setErrorMessage(String.format("%s\n%s", error.getErrorMessage(), newError.getErrorMessage()));
                    break;
                }
            }
            if (!isExist) {
                allErrorList.add(newError);
            }
        }
    }

    protected void validateRelatedObject(Integer matchingType) {
        validateRelatedObject(matchingType, objectDescribe, dataList);
    }

    protected void validateRelatedObject(Integer matchingType, IObjectDescribe objectDescribe, List<ImportData> dataList) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribe);
        Map<IFieldDescribe, List<String>> defObjMap = getFieldDefObjMap(describeExt, dataList);
        validateRelatedObject(matchingType, objectDescribe, defObjMap, dataList);
    }

    private void validateRelatedObject(Integer matchingType, IObjectDescribe objectDescribe,
                                       Map<IFieldDescribe, List<String>> defObjMap, List<ImportData> dataList) {
        List<ImportError> errorList = Lists.newArrayList();
        if (importReferenceFieldMappingSwitch) {
            batchHandleObjectReference(errorList, objectDescribe, defObjMap, dataList);
        } else {
            if (MATCHING_TYPE_ID == matchingType) {
                if (isAddAction()) {
                    validReferenceID(errorList, defObjMap, dataList);
                    batchHandleObjectReferenceById(errorList, defObjMap, actionContext.getTenantId(), dataList);
                } else {
                    // 按照ID匹配时
                    // 关联字段和主从字段必填时，匹配ID必须填写ID
                    validReferenceID(errorList, defObjMap);
                    // 使用ID匹配，无需调用转id服务，只需要检验该id是否存在
                    batchHandleObjectReferenceById(errorList, defObjMap, actionContext.getTenantId());
                }
            } else {
                if (isAddAction()) {
                    // 按照Name或者其它方式导入
                    // 调用name转id服务
                    batchHandleObjectReference(errorList, objectDescribe, defObjMap, dataList);
                } else {
                    // 按照Name或者其它方式导入
                    // 调用name转id服务
                    batchHandleObjectReference(errorList, defObjMap);
                }
            }
        }
        mergeErrorList(errorList);
    }

    /**
     * 检查关联字段ID是否填写
     */
    protected void validReferenceID(List<ImportError> errorList, Map<IFieldDescribe, List<String>> defObjMap) {
        validReferenceID(errorList, defObjMap, dataList);
    }

    private void validReferenceID(List<ImportError> errorList, Map<IFieldDescribe, List<String>> defObjMap, List<ImportData> dataList) {
        if (CollectionUtils.empty(defObjMap)) {
            return;
        }
        Set<Map.Entry<IFieldDescribe, List<String>>> entries = defObjMap.entrySet();
        for (Map.Entry<IFieldDescribe, List<String>> entry : entries) {
            IFieldDescribe fieldDescribe = entry.getKey();
            //按id导入客户主数据字段不做校验（inherit_type==2）
            if (!fieldDescribe.isRequired() || Objects.equals(fieldDescribe.getInheritType(), 2)) {
                //非必填字段不必校验
                continue;
            }
            dataList.forEach(data -> {
                String val = getStringValue(data.getData(), fieldDescribe.getApiName());
                if (shouldProcessEmptyValue(val)) {
                    //没有填写关联对象的数据ID
                    errorList.add(ImportError.builder()
                            .rowNo(data.getRowNo())
                            .errorMessage(I18NExt.getOrDefault(I18NKey.DATA_ID_MUST_FILL, "字段{0}的唯一性ID没有填写。", fieldDescribe.getLabel()))// ignoreI18n
                            .objectApiName(fieldDescribe.getDescribeApiName())
                            .build());
                }
            });
        }
    }

    private boolean shouldProcessEmptyValue(String val) {
        if (Strings.isNullOrEmpty(val)) {
            return Objects.equals(arg.getImportType(), IMPORT_TYPE_ADD) || BooleanUtils.isTrue(arg.getIsEmptyValueToUpdate());
        }
        return false;
    }

    /**
     * @return key:查找关联字段
     * value:导入的EXCEL数据的内容
     */
    protected Map<IFieldDescribe, List<String>> getFieldDefObjMap(ObjectDescribeExt objectDescribeExt, List<ImportData> dataList) {
        Map<IFieldDescribe, List<String>> defObjMap = Maps.newHashMap();
        //组织数据
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(objectDescribeExt);
        describeExt.getFieldDescribes().stream()
                .filter(a -> (Objects.equals(IFieldType.OBJECT_REFERENCE, a.getType())
                        || Objects.equals(IFieldType.OBJECT_REFERENCE_MANY, a.getType())
                        || Objects.equals(IFieldType.MASTER_DETAIL, a.getType()))
                        && !noConvertRefFields.contains(a.getApiName())
                        && a.isActive())
                .forEach(fieldDescribe -> {
                    List<String> nameList = getNameList(dataList, fieldDescribe.getApiName());
                    defObjMap.put(fieldDescribe, nameList);
                });
        return defObjMap;
    }

    /**
     * 参数中的data的key是label，需要转换成apiName
     */
    protected List<ImportData> customConvertLabelToApiName(List<ObjectDataDocument> sourceDataList, IObjectDescribe describe) {
        if (isSupportFieldMapping()) {
            return customConvertColIndexToApiName(sourceDataList, describe);
        }
        if (CollectionUtils.empty(sourceDataList)) {
            return Collections.emptyList();
        }
        List<ImportData> resultList = Lists.newArrayList();
        for (ObjectDataDocument sourceData : sourceDataList) {
            ImportData data = convertLabelToApiName(sourceData, describe);
            if (CollectionUtils.notEmpty(ObjectDataExt.of(data.getData()).toMap())) {
                resultList.add(data);
            }
        }
        return resultList;
    }

    private List<ImportData> customConvertColIndexToApiName(List<ObjectDataDocument> sourceDataList, IObjectDescribe describe) {
        if (CollectionUtils.empty(sourceDataList)) {
            return Lists.newArrayList();
        }
        List<ImportData> result = Lists.newArrayList();
        for (ObjectDataDocument sourceData : sourceDataList) {
            ImportData data = customConvertColIndex2ApiName(sourceData, describe);
            if (Objects.isNull(data)) {
                continue;
            }
            if (CollectionUtils.notEmpty(ObjectDataExt.of(data.getData()).toMap())) {
                result.add(data);
            }
        }

        return result;
    }

    private ImportData customConvertColIndex2ApiName(ObjectDataDocument sourceData, IObjectDescribe describe) {
        if (CollectionUtils.empty(fieldMappings)) {
            return null;
        }
        ImportData resultData = new ImportData();
        IObjectData data = new ObjectData();
        List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(describe).getActiveFieldDescribes();
        ImportExportExt.supportMultiLangField(describe.getTenantId(), fieldDescribes, describe.getApiName());
        Set<Map.Entry<String, Object>> entries = sourceData.entrySet();
        for (Map.Entry<String, Object> entry : entries) {
            String colIndex = entry.getKey();
            if (ROW_NO.equalsIgnoreCase(colIndex)) {
                resultData.setRowNo(Integer.parseInt(String.valueOf(entry.getValue())));
                continue;
            }
            // 联合导入:增加[关联标识]虚拟字段
            if (addMarkLabelField(data, entry)) {
                continue;
            }
            String apiName = CollectionUtils.nullToEmpty(fieldMappings.get(describe.getApiName())).stream()
                    .filter(x -> Objects.equals(x.getColIndex(), colIndex))
                    .map(FieldMapping::getApiName)
                    .findFirst().orElse(null);
            if (StringUtils.isBlank(apiName)) {
                continue;
            }
            String fieldApiName = fieldDescribes.stream()
                    .filter(x -> Objects.equals(x.getApiName(), apiName))
                    .map(x -> {
                        if (Objects.nonNull(x.get(ImportExportExt.SOURCE_FIELD_API_NAME))) {
                            return x.get(ImportExportExt.SOURCE_FIELD_API_NAME, String.class);
                        }
                        return x.getApiName();
                    })
                    .findFirst()
                    .orElse(apiName);
            //过滤字段功能权限,【关联标识（必填）和主对象ID(请勿编辑此列)不过功能权限】
            if (isAddAction()) {
                if (!ImportExportExt.NOT_FILTER_IMPORT_FIELDS.contains(fieldApiName) && customFilterHeader(fieldApiName, describe)) {
                    continue;
                }
            } else {
                if (!ImportExportExt.NOT_FILTER_IMPORT_FIELDS.contains(fieldApiName) && customFilterHeader(fieldApiName)) {
                    continue;
                }
            }
            String value = Objects.isNull(entry.getValue()) ? "" : String.valueOf(entry.getValue()).trim();
            data.set(apiName, value);
        }
        //处理相关团队
        convertTeamMember(data, describe);
        // 处理支持多语的字段
        convertMultiLangFieldValue(data, fieldDescribes);
        resultData.setData(data);
        return resultData;
    }

    private ImportData convertLabelToApiName(ObjectDataDocument data, IObjectDescribe describe) {
        //参数中的data的key是label，需要转换成apiName
        if (null == data) {
            return null;
        }

        IObjectData result = new ObjectData();
        ImportData resultData = new ImportData();
        Set<Map.Entry<String, Object>> entrySet = data.entrySet();
        List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(describe).getActiveFieldDescribes();
        // 特殊处理多语字段
        ImportExportExt.supportMultiLangField(describe.getTenantId(), fieldDescribes, describe.getApiName());
        for (Map.Entry<String, Object> entry : entrySet) {
            String label = entry.getKey();
            if (ROW_NO.equalsIgnoreCase(label)) {
                resultData.setRowNo(Integer.parseInt(String.valueOf(entry.getValue())));
                continue;
            }
            // 联合导入：增加关联标识虚拟字段
            if (addMarkLabelField(result, entry)) {
                continue;
            }
            //处理相关团队
            //外部身份不处理相关团队
            if (teamMemberLabelMap.containsKey(label) || TeamMember.RoleWithPermission.contains(label)) {
                continue;
            }
            //将label转换成apiName
            IFieldDescribe fieldDescribe = getFieldApiName(label, fieldDescribes);

            String apiName = Objects.nonNull(fieldDescribe) ? fieldDescribe.getApiName() : null;
            // 字段是否支持导入需要用原始的apiName校验
            String sourceFieldApiName = Optional.ofNullable(fieldDescribe)
                    .map(it -> it.get("source_field_api_name", String.class))
                    .orElse(apiName);
            String value = entry.getValue() == null ? "" : String.valueOf(entry.getValue()).trim();
            // 定制需要过滤掉的字段
            if (isAddAction()) {
                if (customFilterHeader(sourceFieldApiName, describe)) {
                    continue;
                }
            } else {
                if (customFilterHeader(sourceFieldApiName)) {
                    continue;
                }
            }
            if (!Strings.isNullOrEmpty(apiName)) {
                result.set(apiName, value);
            }

            if (label.contains(NO_CONVERT_SYMBOL)) {
                noConvertRefFields.add(apiName);
            }
        }
        convertTeamMemberField(data, result);
        // 处理支持多语的字段
        convertMultiLangFieldValue(result, fieldDescribes);
        resultData.setData(result);
        return resultData;
    }

    private void convertTeamMemberField(ObjectDataDocument data, IObjectData result) {
        if (isSkipRelevantTeamProcess()) {
            return;
        }
        List<Map.Entry<String, Object>> teamMemberData = data.entrySet().stream()
                .filter(x -> teamMemberLabelMap.containsKey(x.getKey()) || TeamMember.RoleWithPermission.contains(x.getKey()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(teamMemberData)) {
            return;
        }
        //相关团队: 一个用户可以对应多个Role
        Map<String, List<TeamMember.RoleWithPermission>> userRoleMap = Maps.newHashMap();
        Map<String, List<String>> userTeamRoleMap = Maps.newHashMap();
        List<TeamMemberInfoPoJo> teamMemberInfoPoJos = Lists.newArrayList();
        boolean teamMemberTypeExportGray = TeamMember.isTeamMemberTypeExportGray(actionContext.getTenantId());
        for (Map.Entry<String, Object> teamMember : teamMemberData) {
            if (teamMemberTypeExportGray) {
                String teamMemberFieldApiName = teamMemberLabelMap.get(teamMember.getKey());
                Object value = teamMember.getValue();
                buildRelevantTeam(teamMemberFieldApiName, value, teamMemberInfoPoJos, result);
            } else {
                processRelevantTeam(teamMember, userRoleMap, userTeamRoleMap, result);
            }
        }
    }

    private void buildRelevantTeam(String teamMemberFieldApiName, Object teamMemberFieldValue, List<TeamMemberInfoPoJo> teamMemberInfoPoJos, IObjectData result) {
        if (StringUtils.isBlank(teamMemberFieldApiName)) {
            return;
        }
        String[] fieldApiName = teamMemberFieldApiName.split("_");
        if (fieldApiName.length != 3) {
            return;
        }
        if (!(teamMemberFieldValue instanceof String)) {
            return;
        }
        String value = (String) teamMemberFieldValue;
        if (StringUtils.isBlank(value)) {
            return;
        }
        String[] names = value.split("\\|");
        for (String name : names) {
            TeamMemberInfoPoJo teamMemberInfoPoJo = new TeamMemberInfoPoJo();
            teamMemberInfoPoJo.setTeamMemberType(fieldApiName[0]);
            teamMemberInfoPoJo.setTeamMemberRole(fieldApiName[1]);
            teamMemberInfoPoJo.setTeamMemberRoleList(Lists.newArrayList(fieldApiName[1]));
            teamMemberInfoPoJo.setTeamMemberPermissionType(fieldApiName[2]);
            teamMemberInfoPoJo.setTeamMemberName(name);
            teamMemberInfoPoJos.add(teamMemberInfoPoJo);
        }
        ObjectDataExt.of(result).setRelevantTeam(teamMemberInfoPoJos);
    }

    private void processRelevantTeam(Map.Entry<String, Object> entry,
                                     Map<String, List<TeamMember.RoleWithPermission>> userRoleMap,
                                     Map<String, List<String>> userTeamRoleMap,
                                     IObjectData result) {
        String label = entry.getKey();
        if (!customTeamRoleGray && TeamMember.RoleWithPermission.contains(label)) {
            //获取当前角色下所有用户名称
            Object obj = entry.getValue();
            String userNames = null;
            if (Objects.nonNull(obj)) {
                userNames = (String) obj;
            }
            if (!StringUtils.isEmpty(userNames)) {
                TeamMember.RoleWithPermission roleWithPermission = TeamMember.RoleWithPermission.getByLabel(label);
                //整理用户并去重
                Set<String> userNameSet = Sets.newHashSet(Arrays.asList(userNames.split("\\|")));
                //去除空字符串
                userNameSet.removeIf(StringUtils::isBlank);
                for (String userName : userNameSet) {
                    if (userRoleMap.containsKey(userName)) {
                        userRoleMap.get(userName).add(roleWithPermission);
                    } else {
                        userRoleMap.put(userName, Lists.newArrayList(roleWithPermission));
                    }
                }
            }
            result.set(ObjectDataExt.RELEVANT_TEAM, userRoleMap);
        }
        if (customTeamRoleGray && teamMemberLabelMap.containsKey(label)) {
            //获取当前角色下所有用户名称
            Object obj = entry.getValue();
            String userNames = null;
            if (Objects.nonNull(obj)) {
                userNames = (String) obj;
            }
            if (!StringUtils.isEmpty(userNames)) {
                String rolePermissionValue = teamMemberLabelMap.get(label);
                if (StringUtils.isEmpty(rolePermissionValue)) {
                    return;
                }
                //整理用户并去重
                Set<String> userNameSet = Sets.newHashSet(Arrays.asList(userNames.split("\\|")));
                //去除空字符串
                userNameSet.removeIf(StringUtils::isBlank);
                if (CollectionUtils.notEmpty(userNameSet) && disabledTeamRoles.contains(StringUtils.substringBefore(rolePermissionValue, "_"))) {
                    return;
                }
                for (String userName : userNameSet) {
                    if (userTeamRoleMap.containsKey(userName)) {
                        userTeamRoleMap.get(userName).add(rolePermissionValue);
                    } else {
                        userTeamRoleMap.put(userName, Lists.newArrayList(rolePermissionValue));
                    }
                }
            }
            result.set(ObjectDataExt.RELEVANT_TEAM, userTeamRoleMap);
        }
    }

    private void convertTeamMember(IObjectData data, IObjectDescribe describe) {
        if (CollectionUtils.empty(fieldMappings)) {
            return;
        }
        if (isSkipRelevantTeamProcess()) {
            return;
        }
        List<String> teamMemberFieldApiNames = fieldMappings.get(describe.getApiName()).stream()
                .filter(x -> Objects.equals(x.getImportFieldMark(), ImportExportExt.TEAM_MEMBER_MARK))
                .map(FieldMapping::getApiName)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(teamMemberFieldApiNames)) {
            return;
        }
        if (TeamMember.isTeamMemberTypeExportGray(actionContext.getTenantId())) {
            List<TeamMemberInfoPoJo> teamMemberInfoPoJos = Lists.newArrayList();
            for (String teamMemberFieldApiName : teamMemberFieldApiNames) {
                Object value = data.get(teamMemberFieldApiName);
                buildRelevantTeam(teamMemberFieldApiName, value, teamMemberInfoPoJos, data);
            }
            return;
        }


        Map<String, List<TeamMember.RoleWithPermission>> userRoleMap = Maps.newHashMap();
        Map<String, List<String>> userTeamRoleMap = Maps.newHashMap();
        for (String teamMemberFieldApiName : teamMemberFieldApiNames) {
            String roleAndPermission = StringUtils.substringAfter(teamMemberFieldApiName, ImportExportExt.TEAM_MEMBER_MARK + "_");
            TeamMember.RoleWithPermission roleWithPermission = TeamMember.RoleWithPermission.getByRoleAndPermission(roleAndPermission);
            if (!customTeamRoleGray && Objects.isNull(roleAndPermission)) {
                continue;
            }
            //获取当前角色下所有用户名称
            String userNames = data.get(teamMemberFieldApiName, String.class);
            if (StringUtils.isBlank(userNames)) {
                continue;
            }
            //整理用户并去重
            Set<String> userNameSet = Sets.newHashSet(Arrays.asList(userNames.split("\\|")));
            if (customTeamRoleGray && CollectionUtils.notEmpty(userNameSet)
                    && disabledTeamRoles.contains(StringUtils.substringBefore(roleAndPermission, "_"))) {
                continue;
            }
            //去除空字符串
            userNameSet.removeIf(StringUtils::isBlank);
            for (String userName : userNameSet) {
                if (customTeamRoleGray) {
                    if (userTeamRoleMap.containsKey(userName)) {
                        userTeamRoleMap.get(userName).add(roleAndPermission);
                    } else {
                        userTeamRoleMap.put(userName, Lists.newArrayList(roleAndPermission));
                    }
                } else {
                    if (userRoleMap.containsKey(userName)) {
                        userRoleMap.get(userName).add(roleWithPermission);
                    } else {
                        userRoleMap.put(userName, Lists.newArrayList(roleWithPermission));
                    }
                }
            }
        }
        data.set(ObjectDataExt.RELEVANT_TEAM, customTeamRoleGray ? userTeamRoleMap : userRoleMap);
    }

    private void convertMultiLangFieldValue(IObjectData objectData, List<IFieldDescribe> fieldDescribes) {
        if (!AppFrameworkConfig.objectMultiLangGray(actionContext.getTenantId(), objectDescribe.getApiName())) {
            return;
        }
        List<IFieldDescribe> enableMultiLangFields = fieldDescribes.stream()
                .filter(it -> BooleanUtils.isTrue(it.getEnableMultiLang()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(enableMultiLangFields)) {
            return;
        }
        ObjectDataExt objectDataExt = ObjectDataExt.of(objectData);
        List<Lang> values = Lang.values();
        for (IFieldDescribe fieldDescribe : enableMultiLangFields) {
            String fieldName = FieldDescribeExt.getMultiLangExtraFieldName(fieldDescribe.getApiName());
            List<Lang> langs = AppFrameworkConfig.multiLangSupport(values, fieldDescribe.getDescribeApiName(), fieldDescribe.getApiName());
            Map<String, Object> multiLangValue = Maps.newHashMap();
            // 模板中没有字段多语言信息
            boolean notContainsFields = true;
            for (Lang lang : langs) {
                String tempFieldName = FieldDescribeExt.getMultiLangTempFieldName(fieldDescribe.getApiName(), lang);
                // 不包含多语言字段
                if (!objectData.containsField(tempFieldName)) {
                    continue;
                }
                notContainsFields = false;
                Object value = objectData.get(tempFieldName);
                if (!ObjectDataExt.isValueEmpty(value) || updateMultiLangFieldEmptyValue()) {
                    multiLangValue.put(lang.getValue(), value);
                }
                objectDataExt.remove(tempFieldName);
            }
            // 模板中没有多语言字段,所有多语字段都为空且选择了为空不更新,则不需要 name__lang
            if (!(notContainsFields || (CollectionUtils.empty(multiLangValue) && !updateMultiLangFieldEmptyValue()))) {
                objectData.set(fieldName, multiLangValue);
            }
        }
    }

    private boolean validateMultiLangValueBlank(Map<String, Object> multiLangValue) {
        if (CollectionUtils.empty(multiLangValue)) {
            return false;
        }
        Set<Map.Entry<String, Object>> entrySet = multiLangValue.entrySet();
        return entrySet.stream().anyMatch(x -> !ObjectDataExt.isValueEmpty(x.getValue()));
    }

    protected boolean updateMultiLangFieldEmptyValue() {
        return true;
    }

    /**
     * 导入时，过滤掉指定字段
     *
     * @param apiName 字段 apiName
     */
    protected boolean customFilterHeader(String apiName) {
        return false;
    }

    protected boolean customFilterHeader(String fieldApiName, IObjectDescribe describe) {
        return false;
    }

    /**
     * @param result
     * @param entry
     * @return
     */
    protected boolean addMarkLabelField(IObjectData result, Map.Entry<String, Object> entry) {
        return false;
    }

    protected final Map<String, Object> getLogExtendsInfo() {
        Map<String, Object> map = Maps.newHashMap();
        map.put(LogInfo.TRIGGER_WORK_FLOW, BooleanUtils.isTrue(arg.getIsWorkFlowEnabled()));
        map.put(LogInfo.TRIGGER_APPROVAL_FLOW, BooleanUtils.isTrue(arg.getIsApprovalFlowEnabled()));
        return map;
    }

    public Boolean getRelatedTeamEnabled() {
        return relatedTeamEnabled;
    }

    private IFieldDescribe getFieldApiName(String key, List<IFieldDescribe> fieldDescribes) {
        if (Strings.isNullOrEmpty(key)) {
            return null;
        }

        for (IFieldDescribe fieldDescribe : fieldDescribes) {
            if (validTitles(key, fieldDescribe)) {
                return fieldDescribe;
            }
        }
        return null;
    }

    protected abstract List<IFieldDescribe> getValidImportFields();

    protected abstract void getPhoneNumberInfo(List<ImportData> dataList);

    private boolean isSkipRelevantTeamProcess() {
        return actionContext.getUser().isOutUser() || !relatedTeamEnabled;
    }

    @Data
    public static class ImportData {
        private int rowNo;
        private Boolean ownerChange;
        private IObjectData data;
        private String errorMessage;

        public static List<ImportDataActionDomainPlugin.ImportDataDto> toPluginDtoList(List<ImportData> importDataList, List<String> recordTypeList) {
            if (CollectionUtils.empty(importDataList)) {
                return Lists.newArrayList();
            }
            return importDataList.stream()
                    .filter(x -> CollectionUtils.empty(recordTypeList) || recordTypeList.contains(x.getData().getRecordType()))
                    .map(x -> ImportDataActionDomainPlugin.ImportDataDto.builder()
                            .rowNo(x.getRowNo())
                            .data(ObjectDataDocument.of(x.getData()))
                            .build())
                    .collect(Collectors.toList());
        }

        public boolean containsField(String fieldApiName) {
            return !Objects.isNull(data) && ObjectDataExt.of(data).toMap().containsKey(fieldApiName);
        }

        public List<String> getHeader() {
            return Objects.isNull(data) ? Lists.newArrayList() : Lists.newArrayList(ObjectDataExt.of(data).toMap().keySet());
        }

        public BatchConvertData toBatchConvertData() {
            return BatchConvertData.of(data, rowNo);
        }

        public boolean isOwnerChange() {
            return BooleanUtils.isTrue(this.ownerChange);
        }

    }
}
