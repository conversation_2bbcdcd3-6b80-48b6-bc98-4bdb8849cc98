package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.paas.appframework.core.model.LayoutDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.core.model.domain.ActionDomainPlugin;
import com.facishare.paas.appframework.core.model.domain.DomainPlugin;
import com.facishare.paas.appframework.core.rest.BaseAPIResult;
import lombok.*;
import lombok.experimental.SuperBuilder;

/**
 * Created by zhaooju on 2023/11/10
 */
public interface DesignerCreateLayoutActionDomainPlugin extends ActionDomainPlugin<DesignerCreateLayoutActionDomainPlugin.Arg, DesignerCreateLayoutActionDomainPlugin.Result> {

    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class Arg extends DomainPlugin.Arg {
        private LayoutDocument layoutData;
        private ObjectDescribeDocument describeData;
        private ObjectDescribeDocument describeExtra;
    }

    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class Result extends DomainPlugin.Result {
        private LayoutDocument layout;
        private ObjectDescribeDocument objectDescribe;
        private ObjectDescribeDocument describeExtra;
    }

    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class RestResult extends BaseAPIResult {
        private Result data;
    }

}
