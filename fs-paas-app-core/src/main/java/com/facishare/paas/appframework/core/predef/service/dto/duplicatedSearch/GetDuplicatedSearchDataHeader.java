package com.facishare.paas.appframework.core.predef.service.dto.duplicatedSearch;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Builder;
import lombok.Data;

import java.util.List;

public interface GetDuplicatedSearchDataHeader {
    @Data
    class Arg {
        @JSONField(name = "M1")
        private String object_api_name;
        @JSONField(name = "M2")
        private String type;
    }

    @Data
    @Builder
    class Result {
    }
}
