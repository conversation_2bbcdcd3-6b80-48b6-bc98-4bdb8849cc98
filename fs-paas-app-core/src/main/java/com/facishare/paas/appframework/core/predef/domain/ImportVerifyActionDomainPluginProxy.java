package com.facishare.paas.appframework.core.predef.domain;

import com.facishare.rest.core.annotation.*;

import java.util.Map;

/**
 * Created by zhouwr on 2022/1/18.
 */
@RestResource(
        value = "NCRM",
        desc = "导入校验领域插件RPC代理类", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface ImportVerifyActionDomainPluginProxy {
    @POST(desc = "导入校验领域插件rest接口")
    ImportVerifyActionDomainPlugin.RestResult post(@ServiceURLParam String url, @Body ImportVerifyActionDomainPlugin.Arg arg, @HeaderMap Map<String, String> header);
}
