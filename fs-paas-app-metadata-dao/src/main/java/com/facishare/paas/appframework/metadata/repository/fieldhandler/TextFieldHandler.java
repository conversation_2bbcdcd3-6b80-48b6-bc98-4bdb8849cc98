package com.facishare.paas.appframework.metadata.repository.fieldhandler;

import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.metadata.repository.converters.Converter;
import com.facishare.paas.appframework.metadata.repository.converters.Converters;

import java.util.Objects;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/12/27
 */
public class TextFieldHandler implements FieldHandler {
    @Override
    public Object parseFromObjectDataToEntity(Object objectDataValue, FieldInfo fieldInfo) {
        if (Objects.isNull(objectDataValue)) {
            return null;
        }
        if (fieldInfo.getObjectFieldType() == ObjectFieldType.JSON
                && !String.class.isAssignableFrom(fieldInfo.getFieldType().getRawType())) {
            if (!(objectDataValue instanceof String)) {
                objectDataValue = JacksonUtils.toJson(objectDataValue);
            }
        }
        Converter fieldConverter = fieldInfo.getFieldConverter();
        return fieldConverter.convert(objectDataValue);
    }

    @Override
    public Object parseFromEntityToObjectData(Object entityValue, FieldInfo fieldInfo) {
        ObjectFieldType objectFieldType = fieldInfo.getObjectFieldType();
        switch (objectFieldType) {
            case STRING:
                Converter<String> stringConverter = Converters.INSTANCE.getConverter(String.class);
                return stringConverter.convert(entityValue);
            case JSON:
                return entityValue;
            default:
                throw new IllegalArgumentException();
        }
    }
}
