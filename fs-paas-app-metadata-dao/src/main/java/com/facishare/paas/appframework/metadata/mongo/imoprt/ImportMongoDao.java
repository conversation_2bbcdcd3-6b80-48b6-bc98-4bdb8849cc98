package com.facishare.paas.appframework.metadata.mongo.imoprt;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.fxiaoke.api.IdGenerator;
import com.github.mongo.support.DatastoreExt;
import lombok.Setter;
import org.mongodb.morphia.query.Query;

import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * Created by zhaoju on 2022/3/14
 */
public class ImportMongoDao {
    @Setter
    protected DatastoreExt importDatastore;

    public void bulkCreate(User user, List<ImportInfo> importInfos) {
        if (CollectionUtils.empty(importInfos)) {
            return;
        }
        int offsetTime = 1000 * 60 * 60 * 24 * AppFrameworkConfig.getImportExpireTimeOffsetDate();
        for (ImportInfo importInfo : importInfos) {
            long currentTimeMillis = System.currentTimeMillis();
            importInfo.setCreateTime(new Date(currentTimeMillis));
            importInfo.setId(IdGenerator.get());
            importInfo.setTenantId(user.getTenantId());
            importInfo.setExpireTime(new Date(currentTimeMillis + offsetTime));
        }
        importDatastore.save(importInfos);
    }

    public List<ImportInfo> find(User user, String jobId, Collection<String> markLabel) {
        Query<ImportInfo> query = importDatastore.createQuery(ImportInfo.class);
        query.field(ImportInfo.JOB_ID).equal(jobId);
        query.field(IObjectData.TENANT_ID).equal(user.getTenantId());
        query.field(ImportInfo.MARK_LABEL).in(markLabel);

        return query.asList();
    }

    public void findAndConsumer(User user, String jobId, int limit, Consumer<List<ImportInfo>> consumer) {
        String idOffSet = null;
        while (true) {
            Query<ImportInfo> query = getQuery(jobId, user.getTenantId(), idOffSet, limit);
            List<ImportInfo> importInfos = query.asList();
            if (CollectionUtils.empty(importInfos)) {
                break;
            }
            consumer.accept(importInfos);
            int batchDataNum = importInfos.size();
            idOffSet = importInfos.get(batchDataNum - 1).getId();
        }
    }

    private Query<ImportInfo> getQuery(String jobId, String tenantId, String idOffSet, int limit) {
        Query<ImportInfo> query = importDatastore.createQuery(ImportInfo.class);
        query.field(ImportInfo.JOB_ID).equal(jobId);
        query.field(IObjectData.TENANT_ID).equal(tenantId);
        query.order("-" + IObjectData.ID);
        if (Objects.nonNull(idOffSet)) {
            query.field(IObjectData.ID).lessThan(idOffSet);
        }
        query.limit(limit);
        return query;
    }
}
