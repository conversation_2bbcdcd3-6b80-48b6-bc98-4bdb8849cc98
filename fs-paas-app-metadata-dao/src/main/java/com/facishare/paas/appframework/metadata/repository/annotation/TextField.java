package com.facishare.paas.appframework.metadata.repository.annotation;

import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.describe.IFieldType;

import java.lang.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/4.
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@FieldType(type = IFieldType.TEXT)
public @interface TextField {
    ObjectField field();

    int maxLength() default 200;

    String pattern() default "";

    ObjectFieldType fieldType() default ObjectFieldType.STRING;
}
