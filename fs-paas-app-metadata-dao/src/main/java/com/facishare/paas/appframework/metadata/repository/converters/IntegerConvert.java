package com.facishare.paas.appframework.metadata.repository.converters;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/04/20
 */
public class IntegerConvert implements Converter<Integer> {
    private static final IntegerConvert INTEGER_CONVERT = new IntegerConvert();

    public static ConverterFactory getConverterFactory() {
        return Converters.newFactory(Integer.class, getConvert());
    }

    private static IntegerConvert getConvert() {
        return INTEGER_CONVERT;
    }

    private IntegerConvert() {
    }

    @Override
    public Integer convert(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Integer) {
            return (Integer) value;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        return Integer.parseInt(value.toString());
    }
}
