package com.facishare.paas.appframework.metadata.repository.converters;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/04/20
 */
public class CharacterConvert implements Converter<Character> {
    private static final CharacterConvert CHARACTER_CONVERT = new CharacterConvert();

    public static ConverterFactory getConverterFactory() {
        return Converters.newFactory(Character.class, getConvert());
    }

    private static CharacterConvert getConvert() {
        return CHARACTER_CONVERT;
    }

    private CharacterConvert() {
    }

    @Override
    public Character convert(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Character) {
            return ((Character) value);
        }
        if (value instanceof String) {
            String str = (String) value;
            if (str.length() == 1) {
                return str.charAt(0);
            }
        }
        throw new RuntimeException("Expecting character, got: " + value);
    }
}
