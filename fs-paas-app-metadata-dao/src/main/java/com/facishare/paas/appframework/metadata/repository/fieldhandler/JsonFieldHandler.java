package com.facishare.paas.appframework.metadata.repository.fieldhandler;

import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.metadata.repository.converters.Converter;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/12/27
 */
public class J<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> implements FieldHandler {
    @Override
    public Object parseFromObjectDataToEntity(Object objectDataValue, FieldInfo fieldInfo) {
        Converter<Object> fieldConverter = fieldInfo.getFieldConverter();
        return fieldConverter.convert(objectDataValue);
    }

    @Override
    public Object parseFromEntityToObjectData(Object entityValue, FieldInfo fieldInfo) {
        ObjectFieldType objectFieldType = fieldInfo.getObjectFieldType();
        switch (objectFieldType) {
            case STRING:
                if (entityValue instanceof String) {
                    return entityValue;
                }
                return JacksonUtils.toJson(entityValue);
            case JSON:
                return JacksonUtils.toJson(entityValue);
            default:
                throw new IllegalArgumentException();
        }
    }
}
