package com.facishare.paas.appframework.metadata.repository.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2021/10/22.
 */
@Data
public class DomainPluginParam {

    public static final String IS_BINDING_OPTIONAL = "is_binding_optional";

    private Map<String, Object> triggerFields;
    private Map<String, String> fieldMapping;
    private List<DetailObj> details;
    private Map<String, Object> customParam;

    public DomainPluginParam copy() {
        DomainPluginParam result = new DomainPluginParam();
        result.setTriggerFields(Objects.isNull(this.triggerFields) ? null : Maps.newHashMap(this.triggerFields));
        result.setFieldMapping(Objects.isNull(this.fieldMapping) ? null : Maps.newHashMap(this.fieldMapping));
        result.setCustomParam(Objects.isNull(this.customParam) ? null : Maps.newHashMap(this.customParam));
        List<DetailObj> detailObjs = Objects.isNull(this.details) ? null : this.details.stream()
                .map(DetailObj::copy)
                .collect(Collectors.toList());
        result.setDetails(detailObjs);
        return result;
    }

    public List<String> masterFields(List<String> fieldKeys) {
        if (CollectionUtils.empty(fieldMapping) || CollectionUtils.empty(fieldKeys)) {
            return Lists.newArrayList();
        }
        return fieldKeys.stream().map(x -> fieldMapping.get(x)).filter(y -> !Strings.isNullOrEmpty(y)).collect(Collectors.toList());
    }

    @Data
    public static class DetailObj {
        private String objectApiName;
        private String detailKey;
        private Map<String, String> fieldMapping;
        private Map<String, Object> customParam;

        public DetailObj copy() {
            DetailObj result = new DetailObj();
            result.setObjectApiName(this.objectApiName);
            result.setDetailKey(this.detailKey);
            result.setFieldMapping(Objects.isNull(fieldMapping) ? null : Maps.newHashMap(fieldMapping));
            result.setCustomParam(Objects.isNull(customParam) ? null : Maps.newHashMap(customParam));
            return result;
        }
    }
}
