package com.facishare.paas.appframework.metadata.repository.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.repository.annotation.*;
import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.impl.describe.SelectOption;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2021/12/13
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = MtOptionSet.MT_OPTION_OBJ_API_NAME)
public class MtOptionSet extends BaseEntity {
    public static final String MT_OPTION_OBJ_API_NAME = "MtOptionObj";
    public static final String API_NAME = "api_name";
    public static final String LABEL = "label";
    public static final String LABEL_L = "label__lang";
    public static final String DESCRIPTION = "description";
    public static final String DESCRIPTION_L = "description__lang";
    public static final String DEFINE_TYPE = "define_type";

    @TextField(field = @ObjectField(apiName = API_NAME))
    @JsonProperty(API_NAME)
    @JSONField(name = API_NAME)
    private String apiName;

    @TextField(field = @ObjectField(apiName = MtOptionSet.LABEL))
    private String label;

    @LongTextField(field = @ObjectField(apiName = LABEL_L), fieldType = ObjectFieldType.JSON, maxLength = 2000)
    @JsonProperty(LABEL_L)
    private Map<String, String> labelLanguage;

    @LongTextField(field = @ObjectField(apiName = DESCRIPTION))
    private String description;

    @LongTextField(field = @ObjectField(apiName = DESCRIPTION_L), fieldType = ObjectFieldType.JSON, maxLength = 2000)
    @JsonProperty(DESCRIPTION_L)
    private Map<String, String> descriptionLanguage;

    @TrueOrFalseField(field = @ObjectField(apiName = "is_active", label = "is_active"))
    @JsonProperty("is_active")
    @JSONField(name = "is_active")
    private Boolean active;
    @TrueOrFalseField(field = @ObjectField(apiName = IObjectData.IS_DELETED, label = IObjectData.IS_DELETED))
    @JsonProperty(IObjectData.IS_DELETED)
    @JSONField(name = IObjectData.IS_DELETED)
    private Boolean deleted;
    @LongTextField(field = @ObjectField(apiName = "options"), fieldType = ObjectFieldType.JSON)
    private List<MtOption> options;
    @JsonProperty(DEFINE_TYPE)
    @JSONField(name = DEFINE_TYPE)
    @TextField(field = @ObjectField(apiName = DEFINE_TYPE))
    private String defineType;

    public static MtOptionSet fromJson(String json) {
        return JacksonUtils.fromJson(json, MtOptionSet.class);
    }

    public void synI18nFromDataMultiLang(String languageTag) {
        if (CollectionUtils.notEmpty(this.labelLanguage)) {
            this.label = this.labelLanguage.getOrDefault(languageTag, this.label);
        }
        if (CollectionUtils.notEmpty(this.descriptionLanguage)) {
            this.description = this.descriptionLanguage.getOrDefault(languageTag, this.description);
        }
    }

    public String toJson() {
        return JacksonUtils.toJson(this);
    }

    public MtOptionSet merge(MtOptionSet optionSet, boolean onlyMergeOptions) {
        if (!onlyMergeOptions) {
            setLabel(optionSet.getLabel());
            setDescription(optionSet.getDescription());
        }
        setOptions(optionSet.getOptions());
        return this;
    }

    /**
     * @param optionSet 待比较数据
     * @return true 相同 false 不相同
     */
    public boolean compareOptions(MtOptionSet optionSet) {
        if (equals(optionSet) || Objects.isNull(optionSet)) {
            return false;
        }

        if (!Objects.equals(getOptions().size(), optionSet.getOptions().size())) {
            return false;
        }
        Map<String, MtOption> optionMap = getOptions().stream()
                .collect(Collectors.toMap(MtOption::getValue, Function.identity()));
        for (MtOption option : optionSet.getOptions()) {
            MtOption mtOption = optionMap.get(option.getValue());
            if (Objects.isNull(mtOption)) {
                return false;
            }
            if (!Objects.equals(mtOption.getLabel(), option.getLabel())) {
                return false;
            }
        }
        return true;
    }

    public List<ISelectOption> toSelectOption() {
        return getOptions().stream()
                .map(MtOption::toSelectOption)
                .collect(Collectors.toList());
    }

    public boolean custom() {
        return DefineType.isCustom(defineType);
    }

    public boolean isPreset() {
        return Objects.equals(DefineType.PACKAGE.getValue(), defineType);
    }

    public enum DefineType {
        CUSTOM("custom", "自定义通用选项集"),   // ignoreI18n
        PACKAGE("package", "预置通用选项集");  // ignoreI18n

        @Getter
        private final String value;
        private final String description;

        DefineType(String value, String description) {
            this.value = value;
            this.description = description;
        }

        // 检查type是否是给定枚举之一
        public static void isValidDefineType(String type) {
            if (StringUtils.isBlank(type) || Arrays.stream(DefineType.values()).anyMatch(it -> Objects.equals(it.getValue(), type))) {
                return;
            }
            throw new ValidateException(I18NExt.text(I18NKey.OPTION_SET_DEFINE_TYPE_NO_EXIT));
        }

        public static boolean isCustom(String type) {
            return StringUtils.isBlank(type) || Objects.equals(CUSTOM.getValue(), type);
        }

    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor(staticName = "of")
    public static class MtOption {
        private String label;
        private String value;
        @JsonProperty("not_usable")
        @JSONField(name = "not_usable")
        private Boolean notUsable;
        @JsonProperty("font_color")
        @JSONField(name = "font_color")
        private String fontColor;

        /**
         * 其他选项（other）附带文本框是否必填
         */
        @JsonProperty("is_required")
        @JSONField(name = "is_required")
        private Boolean isRequired;

        public ISelectOption toSelectOption() {
            ISelectOption selectOption = new SelectOption();
            selectOption.setLabel(label);
            selectOption.setValue(value);
            if (Objects.nonNull(notUsable)) {
                selectOption.setNotUsable(notUsable);
            }
            if (!Strings.isNullOrEmpty(fontColor)) {
                selectOption.set("font_color", fontColor);
            }
            return selectOption;
        }
    }
}
