package com.facishare.paas.appframework.metadata.repository.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.metadata.repository.annotation.*;
import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by zhouwr on 2023/1/5.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Entity(apiName = HandlerDefinition.HANDLER_DEFINITION_API_NAME)
public class HandlerDefinition extends BaseEntity {
    public static final String HANDLER_DEFINITION_API_NAME = "HandlerDefinitionObj";

    public static final String DEFAULT_POST_ACTION_HANDLER = "defaultPostActionHandler";
    public static final String DEFAULT_TRIGGER_CREATE_APPROVAL_FLOW_ADD_AFTER_HANDLER = "defaultTriggerCreateApprovalFlowAddAfterHandler";
    public static final String DEFAULT_TRIGGER_UPDATE_APPROVAL_FLOW_EDIT_BEFORE_HANDLER = "defaultTriggerUpdateApprovalFlowEditBeforeHandler";
    public static final String DEFAULT_TRIGGER_CREATE_APPROVAL_FLOW_EDIT_AFTER_HANDLER = "defaultTriggerCreateApprovalFlowEditAfterHandler";
    public static final String DEFAULT_TRIGGER_APPROVAL_FLOW_INVALID_BEFORE_HANDLER = "defaultTriggerApprovalFlowInvalidBeforeHandler";

    public static final String PROVIDER_TYPE_SYSTEM = "system";
    public static final String PROVIDER_TYPE_TENANT_APL = "tenant_APL";
    public static final String PROVIDER_TYPE_TENANT_CUSTOM = "tenant_custom";

    public static final String COMMON = "common";
    public static final String PREDEFINE = "predefine";
    public static final String IS_ACTIVE = "is_active";
    public static final String API_NAME = "api_name";
    public static final String LABEL = "label";
    public static final String DESCRIPTION = "description";
    public static final String INTERFACE_CODE = "interface_code";
    public static final String HANDLER_TYPE = "handler_type";
    public static final String PROVIDER_TYPE = "provider_type";
    public static final String INDUSTRY_CODE = "industry_code";
    public static final String APL_API_NAME = "apl_api_name";
    public static final String REST_API_URL = "rest_api_url";
    public static final String SUPPORT_OBJECT_API_NAME = "support_object_api_name";
    public static final String PRIVILEGE_CONFIG = "privilege_config";
    public static final String MANAGEMENT_CONFIG = "management_config";
    public static final String DEFAULT_ORDER = "default_order";
    public static final String ENABLE_DISTRIBUTED_TRANSACTION = "enable_distributed_transaction";
    public static final String SUPPORT_ASYNC = "support_async";
    public static final String ENABLE_GRAY_CONFIG = "enable_gray_config";
    public static final String CONTINUE_AFTER_APPROVAL_SUCCESS = "continue_after_approval_success";

    public static final String BINDING_APP_IDS = "binding_app_ids";

    @TrueOrFalseField(field = @ObjectField(apiName = IObjectData.IS_DELETED, label = "是否删除"))
    private boolean deleted;

    @Builder.Default
    @TrueOrFalseField(field = @ObjectField(apiName = IS_ACTIVE, label = "是否启用"))
    private boolean active = true;

    @TextField(field = @ObjectField(apiName = API_NAME, label = "Handler的ApiName"))
    private String apiName;

    @TextField(field = @ObjectField(apiName = LABEL, label = "Handler的名称"))
    private String label;

    @TextField(field = @ObjectField(apiName = DESCRIPTION, label = "Handler的描述介绍"))
    private String description;

    @TextField(field = @ObjectField(apiName = INTERFACE_CODE, label = "Handler绑定的接口Code"))
    private String interfaceCode;

    @TextField(field = @ObjectField(apiName = HANDLER_TYPE, label = "Handler的类型"))
    private String handlerType;

    @TextField(field = @ObjectField(apiName = PROVIDER_TYPE, label = "Handler的提供者类型"))
    private String providerType;

    @TextField(field = @ObjectField(apiName = INDUSTRY_CODE, label = "行业Code"))
    private String industryCode;

    @TextField(field = @ObjectField(apiName = APL_API_NAME, label = "关联APL代码的ApiName"))
    private String aplApiName;

    @TextField(field = @ObjectField(apiName = REST_API_URL, label = "Handler 远程调用 Url"))
    private String restApiUrl;

    @TextField(field = @ObjectField(apiName = SUPPORT_OBJECT_API_NAME, label = "Handler适用的对象ApiName"))
    private String supportObjectApiName;

    @LongTextField(field = @ObjectField(apiName = PRIVILEGE_CONFIG, label = "Handler的权限控制"), fieldType = ObjectFieldType.JSON)
    private PrivilegeConfig privilegeConfig;

    @LongTextField(field = @ObjectField(apiName = MANAGEMENT_CONFIG, label = "Handler的后台管理配置"), fieldType = ObjectFieldType.JSON)
    private Map<String, Object> managementConfig;

    @NumberField(field = @ObjectField(apiName = DEFAULT_ORDER, label = "Handler的默认执行顺序"))
    private Integer defaultOrder;

    @TrueOrFalseField(field = @ObjectField(apiName = ENABLE_DISTRIBUTED_TRANSACTION, label = "Handler是否支持分布式事务"))
    private boolean enableDistributedTransaction;

    @TrueOrFalseField(field = @ObjectField(apiName = SUPPORT_ASYNC, label = "Handler是否支持异步执行"))
    private boolean supportAsync;

    @TrueOrFalseField(field = @ObjectField(apiName = ENABLE_GRAY_CONFIG, label = "Handler是否支持灰度配置"))
    private boolean enableGrayConfig;

    @TrueOrFalseField(field = @ObjectField(apiName = CONTINUE_AFTER_APPROVAL_SUCCESS, label = "成功触发审批流以后是否继续执行"))
    private Boolean continueAfterApprovalSuccess;

    @TagField(field = @ObjectField(apiName = BINDING_APP_IDS, label = "Handler所属应用"))
    private List<String> bindingAppIds;

    public static boolean isDefaultPostActionHandler(String handlerApiName) {
        return "defaultPostActionHandler".equals(handlerApiName);
    }

    public static boolean provideByTenant(String providerType) {
        return PROVIDER_TYPE_TENANT_APL.equals(providerType) || PROVIDER_TYPE_TENANT_CUSTOM.equals(providerType);
    }

    public boolean provideByTenant() {
        return provideByTenant(providerType);
    }

    public boolean continueAfterApprovalSuccess() {
        return Boolean.TRUE.equals(continueAfterApprovalSuccess);
    }

    public void mergeWithDbDefinition(HandlerDefinition dbDefinition) {
        setId(dbDefinition.getId());
        if (Objects.isNull(getPrivilegeConfig())) {
            setPrivilegeConfig(dbDefinition.getPrivilegeConfig());
        }
        if (Objects.isNull(getManagementConfig())) {
            setManagementConfig(dbDefinition.getManagementConfig());
        }
        if (Objects.isNull(getDefaultOrder())) {
            setDefaultOrder(dbDefinition.getDefaultOrder());
        }
        if (Objects.isNull(getLabel())) {
            setLabel(dbDefinition.getLabel());
        }
        if (Objects.isNull(getDescription())) {
            setDescription(dbDefinition.getDescription());
        }
        if (Objects.isNull(getInterfaceCode())) {
            setInterfaceCode(dbDefinition.getInterfaceCode());
        }
        if (Objects.isNull(getHandlerType())) {
            setHandlerType(dbDefinition.getHandlerType());
        }
        if (Objects.isNull(getProviderType())) {
            setProviderType(dbDefinition.getProviderType());
        }
        if (Objects.isNull(getSupportObjectApiName())) {
            setSupportObjectApiName(dbDefinition.getSupportObjectApiName());
        }
    }

    public String i18nLabel() {
        String i18nKey = String.format("paas.handler.%s.label", apiName);
        return I18NExt.getOrDefault(i18nKey, label);
    }

    public String i18nDescription() {
        String i18nKey = String.format("paas.handler.%s.description", apiName);
        return I18NExt.getOrDefault(i18nKey, description);
    }

    public void validate() {
        if (PROVIDER_TYPE_TENANT_APL.equals(providerType) && StringUtils.isBlank(aplApiName)) {
            throw new ValidateException("apl_api_name can not be blank when provider_type is tenant_APL");
        }
    }

    public boolean hiddenInManagement() {
        if (CollectionUtils.empty(managementConfig)) {
            return false;
        }
        return Boolean.TRUE.equals(managementConfig.get("hidden"));
    }

    public boolean defineByAppId(String appId) {
        if (CollectionUtils.empty(bindingAppIds)) {
            return true;
        }
        return bindingAppIds.contains(appId);
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class PrivilegeConfig {
        private List<BizConfig> bizConfigs;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    public static class BizConfig {
        private String key;
        private String value;
    }

}
