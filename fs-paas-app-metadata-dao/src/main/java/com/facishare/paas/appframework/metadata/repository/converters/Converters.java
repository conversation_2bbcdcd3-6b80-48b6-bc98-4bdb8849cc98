package com.facishare.paas.appframework.metadata.repository.converters;

import com.google.common.collect.Lists;
import com.google.common.reflect.TypeToken;
import com.google.gson.internal.Primitives;

import java.util.List;
import java.util.Objects;

/**
 * create by z<PERSON><PERSON> on 2021/04/20
 */
public enum Converters {
    INSTANCE(),
    ;

    private List<ConverterFactory> factories = Lists.newArrayList();

    Converters() {
        addConverters(BigDecimalConvert.getConverterFactory());
        addConverters(BooleanConvert.getConverterFactory());
        addConverters(ByteConvert.getConverterFactory());
        addConverters(CharacterConvert.getConverterFactory());
        addConverters(DoubleConvert.getConverterFactory());
        addConverters(FloatConvert.getConverterFactory());
        addConverters(IntegerConvert.getConverterFactory());
        addConverters(LongConvert.getConverterFactory());
        addConverters(ShortConvert.getConverterFactory());
        addConverters(StringConvert.getConverterFactory());
        addConverters(CollectionConvert.getConvertFactory());
        addConverters(MapConvert.getConvertFactory());
        addConverters(ReflectiveTypeConvert.getConvertFactory());
    }

    private void addConverters(ConverterFactory converterFactory) {
        factories.add(converterFactory);
    }

    @SuppressWarnings("unchecked")
    public <T> Converter<T> getConverter(Class<T> clazz) {
        return getConverter(TypeToken.of(clazz));
    }

    public <T> Converter<T> getConverter(TypeToken<T> typeToken) {
        for (ConverterFactory factory : factories) {
            Converter<T> converter = factory.create(typeToken);
            if (Objects.nonNull(converter)) {
                return converter;
            }
        }
        throw new IllegalArgumentException("cannot handle " + typeToken);
    }

    static <TT> ConverterFactory newFactory(Class<TT> clazz, Converter<TT> converter) {
        return new ConverterFactory() {
            @Override
            public <T> Converter<T> create(TypeToken<T> typeToken) {
                Class<? super T> rawType = typeToken.getRawType();
                if (Primitives.isPrimitive(rawType)) {
                    rawType = Primitives.wrap(rawType);
                }
                return rawType == clazz ? (Converter<T>) converter : null;
            }
        };
    }
}
