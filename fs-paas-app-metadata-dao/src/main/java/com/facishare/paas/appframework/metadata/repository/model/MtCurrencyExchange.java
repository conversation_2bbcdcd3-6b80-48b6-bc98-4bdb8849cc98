package com.facishare.paas.appframework.metadata.repository.model;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.metadata.repository.annotation.*;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang.StringUtils;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = MtCurrencyExchange.MT_CURRENCY_EXCHANGE_OBJ)
public class MtCurrencyExchange extends BaseEntity {
    public static final String MT_CURRENCY_EXCHANGE_OBJ = "MtCurrencyExchangeObj";

    //多货币多语key-pre
    public static final String MT_CURRENCY_PREFIX = "paas.multicurrency.mc_currency.";

    public static final String CURRENCY_FROM_CURRENCY_CODE = "from_currency_code";
    public static final String CURRENCY_TO_CURRENCY_CODE = "to_currency_code";


    @TextField(field = @ObjectField(apiName = "from_currency_code", label = "来源币种"))
    private String fromCurrencyCode;

    @TextField(field = @ObjectField(apiName = "to_currency_code", label = "目标币种"))
    private String toCurrencyCode;

    @NumberField(field = @ObjectField(apiName = "exchange_rate", label = "汇率"))
    private String exchangeRate;

    @TextField(field = @ObjectField(apiName = "exchange_rate_version", label = "汇率版本"))
    private String exchangeRateVersion;

    @TrueOrFalseField(field = @ObjectField(apiName = IObjectData.IS_DELETED, label = "is_deleted"))
    private boolean deleted;

    private String fromCurrencyCodeLabel;
    private String toCurrencyCodeLabel;

    public String getFromCurrencyCodeLabel() {
        return getI18NLabel(this.fromCurrencyCode);
    }

    public String getToCurrencyCodeLabel() {
        return getI18NLabel(this.toCurrencyCode);
    }

    public static String getI18NLabel(String code) {
        if (StringUtils.isBlank(code)) {
            return code;
        }
        String label = I18N.text(MT_CURRENCY_PREFIX + code);
        return StringUtils.isBlank(label) ? code : label;
    }
}
