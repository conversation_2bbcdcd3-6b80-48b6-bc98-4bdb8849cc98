package com.facishare.paas.appframework.metadata.repository.model;

import com.facishare.paas.appframework.metadata.repository.annotation.Entity;
import com.facishare.paas.appframework.metadata.repository.annotation.ObjectField;
import com.facishare.paas.appframework.metadata.repository.annotation.TextField;
import com.facishare.paas.appframework.metadata.repository.annotation.TrueOrFalseField;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

/**
 * create by z<PERSON><PERSON> on 2021/04/16
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = MtSwitch.MT_SWITCH_OBJ_API_NAME)
public class MtSwitch extends BaseEntity {
    public static final String MT_SWITCH_OBJ_API_NAME = "MtSwitchObj";

    public static final String SWITCH_TYPE = "switch_type";
    public static final String SWITCH_NAME = "switch_name";
    public static final String SWITCH_STATUS = "switch_status";
    public static final String BINDING_OBJECT_API_NAME = "binding_object_api_name";

    @TrueOrFalseField(field = @ObjectField(apiName = IObjectData.IS_DELETED, label = "is_deleted"))
    private boolean deleted;

    @TextField(field = @ObjectField(apiName = SWITCH_TYPE, label = "switch_type"))
    private String switchType;

    @TextField(field = @ObjectField(apiName = SWITCH_NAME, label = "switch_name"))
    private String switchName;

    @TrueOrFalseField(field = @ObjectField(apiName = SWITCH_STATUS, label = "switch_status"))
    private boolean switchStatus;

    @TextField(field = @ObjectField(apiName = BINDING_OBJECT_API_NAME, label = "binding_object_api_name"))
    private String bindingObjectApiName;
}
