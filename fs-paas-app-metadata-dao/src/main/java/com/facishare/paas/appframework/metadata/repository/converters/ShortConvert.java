package com.facishare.paas.appframework.metadata.repository.converters;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/04/20
 */
public class ShortConvert implements Converter<Short> {
    private static final ShortConvert SHORT_CONVERT = new ShortConvert();

    public static ConverterFactory getConverterFactory() {
        return Converters.newFactory(Short.class, getConvert());
    }

    private static ShortConvert getConvert() {
        return SHORT_CONVERT;
    }

    private ShortConvert() {
    }

    @Override
    public Short convert(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Short) {
            return ((Short) value);
        }
        if (value instanceof Number) {
            return ((Number) value).shortValue();
        }
        return Short.parseShort(value.toString());
    }
}
