package com.facishare.paas.appframework.metadata.repository.annotation;

import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.describe.IFieldType;

import java.lang.annotation.*;

@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@FieldType(type = IFieldType.DEPARTMENT)
public @interface DepartmentField {
    ObjectField field();

    boolean isSingle() default true;

    ObjectFieldType fieldType() default ObjectFieldType.ARRAY;
}
