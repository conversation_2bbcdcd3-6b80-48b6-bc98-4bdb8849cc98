package com.facishare.paas.appframework.metadata.repository.model;

import com.facishare.paas.appframework.metadata.repository.annotation.Entity;
import com.facishare.paas.appframework.metadata.repository.annotation.ObjectField;
import com.facishare.paas.appframework.metadata.repository.annotation.TextField;
import com.facishare.paas.appframework.metadata.repository.annotation.TrueOrFalseField;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = MtConvertTracker.MT_CONVERT_TRACKER_OBJ_API_NAME)
public class MtConvertTracker extends BaseEntity {
    public static final String MT_CONVERT_TRACKER_OBJ_API_NAME = "MtConvertTrackerObj";
    private static final String EVENT_ID = "event_id";
    private static final String SOURCE_API_NAME = "source_api_name";
    private static final String SOURCE_MASTER_API_NAME = "source_master_api_name";
    private static final String TARGET_API_NAME = "target_api_name";
    private static final String TARGET_MASTER_API_NAME = "target_master_api_name";
    private static final String SOURCE_ID = "source_id";
    private static final String SOURCE_MASTER_ID = "source_master_id";
    private static final String TARGET_ID = "target_id";
    private static final String TARGET_MASTER_ID = "target_master_id";
    private static final String RULE_API_NAME = "rule_api_name";



    @TrueOrFalseField(field = @ObjectField(apiName = IObjectData.IS_DELETED, label = "是否删除"))
    private boolean deleted;

    @TextField(field = @ObjectField(apiName = EVENT_ID, label = "事件id"))
    private String eventId;

    @TextField(field = @ObjectField(apiName = SOURCE_API_NAME, label = "源对象apiName"))
    private String sourceApiName;

    @TextField(field = @ObjectField(apiName = SOURCE_MASTER_API_NAME, label = "源主对象apiName"))
    private String sourceMasterApiName;

    @TextField(field = @ObjectField(apiName = TARGET_API_NAME, label = "目标对象apiName"))
    private String targetApiName;

    @TextField(field = @ObjectField(apiName = TARGET_MASTER_API_NAME, label = "目标主对象apiName"))
    private String targetMasterApiName;

    @TextField(field = @ObjectField(apiName = SOURCE_ID, label = "源数据id"))
    private String sourceId;

    @TextField(field = @ObjectField(apiName = TARGET_ID, label = "目标数据id"))
    private String targetId;

    @TextField(field = @ObjectField(apiName = SOURCE_MASTER_ID, label = "源主数据id"))
    private String sourceMasterId;

    @TextField(field = @ObjectField(apiName = TARGET_MASTER_ID, label = "目标主数据id"))
    private String targetMasterId;

    @TextField(field = @ObjectField(apiName = RULE_API_NAME, label = "规则apiName"))
    private String ruleApiName;

}
