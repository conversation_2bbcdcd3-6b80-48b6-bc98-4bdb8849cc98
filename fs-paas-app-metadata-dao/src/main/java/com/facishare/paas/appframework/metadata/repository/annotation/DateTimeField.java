package com.facishare.paas.appframework.metadata.repository.annotation;

import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.describe.IFieldType;

import java.lang.annotation.*;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 16/8/11.
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
@FieldType(type = IFieldType.DATE_TIME)
public @interface DateTimeField {
    ObjectField field();

    ObjectFieldType fieldType() default ObjectFieldType.LONG;

//  String timeZone() default "GMT+8";
//  String dateFormat() default "yyyy-MM-dd HH:mm:ss";
}
