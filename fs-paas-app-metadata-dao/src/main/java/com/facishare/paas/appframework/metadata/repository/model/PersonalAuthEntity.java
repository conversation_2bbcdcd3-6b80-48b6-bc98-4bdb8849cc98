package com.facishare.paas.appframework.metadata.repository.model;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.metadata.repository.annotation.*;
import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.IObjectData;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/3
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = "PersonalAuthObj")
public class PersonalAuthEntity extends BaseEntity {

    public static final String OBJECT_API_NAME = "PersonalAuthObj";
    public static final String PERSONAL_AUTH_FILED_EMPLOYEE_ID = "employee_id";
    public static final String PERSONAL_AUTH_FILED_APP_TYPE = "app_type";
    public static final String PERSONAL_AUTH_FILED_PLUGIN_API_NAME = "plugin_api_name";
    public static final String PERSONAL_AUTH_FILED_RUNTIME_DATA = "runtime_data";
    public static final String PERSONAL_AUTH_FILED_EXPIRED_TIME = "expired_time";

    @EmployeeField(field = @ObjectField(apiName = PERSONAL_AUTH_FILED_EMPLOYEE_ID))
    private String employeeId;

    @TextField(field = @ObjectField(apiName = PERSONAL_AUTH_FILED_APP_TYPE))
    private String appType;

    @TextField(field = @ObjectField(apiName = PERSONAL_AUTH_FILED_PLUGIN_API_NAME))
    private String pluginApiName;

    @LongTextField(field = @ObjectField(apiName = PERSONAL_AUTH_FILED_RUNTIME_DATA), fieldType = ObjectFieldType.JSON)
    private Map<String, Object> runtimeData;

    @DateTimeField(field = @ObjectField(apiName = PERSONAL_AUTH_FILED_EXPIRED_TIME, label = "过期时间"))
    protected long expiredTime;

    @TrueOrFalseField(field = @ObjectField(apiName = IObjectData.IS_DELETED, label = IObjectData.IS_DELETED))
    @JsonProperty(IObjectData.IS_DELETED)
    @JSONField(name = IObjectData.IS_DELETED)
    private boolean deleted;
}
