package com.facishare.paas.appframework.metadata.repository.model;

import com.alibaba.fastjson.annotation.JSONCreator;
import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.WheresExt;
import com.facishare.paas.appframework.metadata.repository.annotation.*;
import com.facishare.paas.appframework.metadata.repository.fieldhandler.ObjectFieldType;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.IRule;
import com.facishare.paas.metadata.impl.Rule;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import org.apache.commons.lang3.BooleanUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by zhaooju on 2023/3/29
 */
@Data
@ToString(callSuper = true)
@EqualsAndHashCode(callSuper = true)
@Entity(apiName = MtChangeOrderRule.MT_CHANGE_RULE_OBJ_API_NAME)
public class MtChangeOrderRule extends BaseEntity {
    public static final String MT_CHANGE_RULE_OBJ_API_NAME = "MtChangeRuleObj";
    public static final String ORIGINAL_DESCRIBE_API_NAME = "original_describe_api_name";
    public static final String API_NAME = "api_name";
    public static final String IS_ACTIVE = "is_active";

    @TextField(field = @ObjectField(apiName = "label"))
    private String label;
    @JsonProperty(API_NAME)
    @JSONField(name = API_NAME)
    @TextField(field = @ObjectField(apiName = API_NAME))
    private String apiName;
    @JsonProperty(ORIGINAL_DESCRIBE_API_NAME)
    @JSONField(name = ORIGINAL_DESCRIBE_API_NAME)
    @TextField(field = @ObjectField(apiName = ORIGINAL_DESCRIBE_API_NAME, label = "源单对象"))
    private String originalDescribeApiName;
    @JsonProperty("change_describe_api_name")
    @JSONField(name = "change_describe_api_name")
    @TextField(field = @ObjectField(apiName = "change_describe_api_name", label = "变更单对象"))
    private String changeDescribeApiName;
    @JsonProperty("effective_timing")
    @JSONField(name = "effective_timing")
    @SelectOneField(field = @ObjectField(apiName = "effective_timing", label = "生效时机"))
    private String effectiveTiming;
    @JsonProperty("calibration_timing")
    @JSONField(name = "calibration_timing")
    @SelectManyField(field = @ObjectField(apiName = "calibration_timing", label = "校验时机"))
    private List<String> calibrationTiming;
    @TrueOrFalseField(field = @ObjectField(apiName = IS_ACTIVE, label = "is_active"))
    @JsonProperty(IS_ACTIVE)
    @JSONField(name = IS_ACTIVE)
    private Boolean active;

    @JsonProperty("change_condition")
    @JSONField(name = "change_condition")
    @LongTextField(field = @ObjectField(apiName = "change_condition", label = "变更条件"), fieldType = ObjectFieldType.JSON)
    private ChangeCondition changeCondition;
    @JsonProperty("verify_condition")
    @JSONField(name = "verify_condition")
    @LongTextField(field = @ObjectField(apiName = "verify_condition", label = "校验条件"), fieldType = ObjectFieldType.JSON)
    private VerifyCondition verifyCondition;
    @JsonProperty("field_mapping")
    @JSONField(name = "field_mapping")
    @LongTextField(field = @ObjectField(apiName = "field_mapping", label = "字段映射"), fieldType = ObjectFieldType.JSON)
    private List<ObjectMapping> fieldMapping;


    public void merge(MtChangeOrderRule changeRule) {
        setLabel(changeRule.getLabel());
        setEffectiveTiming(changeRule.getEffectiveTiming());
        setCalibrationTiming(changeRule.getCalibrationTiming());
        setChangeCondition(changeRule.getChangeCondition());
        setVerifyCondition(changeRule.getVerifyCondition());
        setFieldMapping(changeRule.getFieldMapping());
    }

    public ObjectFieldMapper toObjectFieldMapper(IObjectDescribe... describes) {
        Map<String, IObjectDescribe> describeMap = Stream.of(describes).collect(Collectors.toMap(IObjectDescribe::getApiName, Function.identity()));
        return ObjectFieldMapper.of(fieldMapping, describeMap);
    }

    public List<CalibrationType> toCalibrationType() {
        return CollectionUtils.nullToEmpty(calibrationTiming).stream()
                .map(CalibrationType::of)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    public EffectiveType toEffectiveType() {
        return EffectiveType.of(effectiveTiming);
    }

    public IRule toValidateRule() {
        if (Objects.isNull(verifyCondition) || Strings.isNullOrEmpty(verifyCondition.getCondition())) {
            return null;
        }
        IRule rule = new Rule();
        rule.setApiName(getApiName());
        rule.setRuleName(getLabel());
        rule.setCondition(verifyCondition.getCondition());
        rule.setDefaultToZero(verifyCondition.getDefaultToZero());
        rule.setMessage(verifyCondition.getMessage());
        return rule;
    }

    @Data
    public static class ChangeCondition {
        public static final String WHERE_TYPE_FIELD = "field";
        public static final String WHERE_TYPE_FUNCTION = "function";

        private List<Map<String, Object>> wheres;
        @JsonProperty("function_api_name")
        @JSONField(name = "function_api_name")
        private String functionApiName;
        @JsonProperty("where_type")
        @JSONField(name = "where_type")
        private String whereType;
        private String message;

        public List<Wheres> toWheres(IObjectDescribe describe) {
            List<Wheres> result = WheresExt.castToWheresList(this.wheres);
            WheresExt.handleWheresByDescribe(result, describe);
            return result;
        }
    }

    @Data
    public static class VerifyCondition {
        private String condition;
        private String description;
        private String message;
        @JsonProperty("default_to_zero")
        @JSONField(name = "default_to_zero")
        private Boolean defaultToZero;
    }

    @Data
    public static class ObjectMapping {
        @JsonProperty("source_api_name")
        @JSONField(name = "source_api_name")
        private String sourceApiName;
        @JsonProperty("target_api_name")
        @JSONField(name = "target_api_name")
        private String targetApiName;
        @JsonProperty("field_mapping")
        @JSONField(name = "field_mapping")
        private List<FieldMapping> fieldMappings;
    }

    @Data
    public static class FieldMapping {
        @JsonProperty("source_field_api_name")
        @JSONField(name = "source_field_api_name")
        private final String sourceFieldApiName;
        @JsonProperty("target_field_api_name")
        @JSONField(name = "target_field_api_name")
        private final String targetFieldApiName;
        private final Boolean changeable;
        @JsonProperty("record_original_value")
        @JSONField(name = "record_original_value")
        private final Boolean recordOriginalValue;

        @JsonCreator
        @JSONCreator
        public FieldMapping(@JsonProperty("source_field_api_name") @JSONField(name = "source_field_api_name") String sourceFieldApiName,
                            @JsonProperty("target_field_api_name") @JSONField(name = "target_field_api_name") String targetFieldApiName,
                            @JsonProperty("changeable") @JSONField(name = "changeable") Boolean changeable,
                            @JsonProperty("record_original_value") @JSONField(name = "record_original_value") Boolean recordOriginalValue) {
            this.sourceFieldApiName = sourceFieldApiName;
            this.targetFieldApiName = targetFieldApiName;
            this.changeable = changeable;
            this.recordOriginalValue = recordOriginalValue;
        }
    }

    public enum CalibrationType {
        SUBMIT("0"), REVIEW("1"), EFFECTIVE("2"),
        ;

        @Getter
        private String type;

        CalibrationType(String type) {
            this.type = type;
        }

        private static final Map<String, CalibrationType> map;

        static {
            map = Arrays.stream(values()).collect(Collectors.toMap(CalibrationType::getType, Function.identity()));
        }

        public static CalibrationType of(String type) {
            return map.get(type);
        }
    }

    public enum EffectiveType {
        AUTO_EFFECT_AFTER_APPROVAL("0"),
        MANUAL_EFFECT("1"),
        ;
        @Getter
        private String type;

        EffectiveType(String type) {
            this.type = type;
        }

        private static final Map<String, EffectiveType> map;

        static {
            map = Arrays.stream(values()).collect(Collectors.toMap(EffectiveType::getType, Function.identity()));
        }

        public static EffectiveType of(String type) {
            return map.get(type);
        }
    }

    public static class ObjectFieldMapper {
        private final Table<String, String, FieldMapping> sourceTable;

        private ObjectFieldMapper(List<ObjectMapping> fieldMapping, Map<String, IObjectDescribe> describeMap) {
            Table<String, String, FieldMapping> table = HashBasedTable.create();
            for (ObjectMapping objectMapping : fieldMapping) {
                IObjectDescribe objectDescribe = describeMap.get(objectMapping.getSourceApiName());
                Set<String> groupFieldNames = Sets.newHashSet();
                if (Objects.nonNull(objectDescribe)) {
                    groupFieldNames = ObjectDescribeExt.of(objectDescribe).getGroupFields().stream()
                            .map(IFieldDescribe::getApiName)
                            .collect(Collectors.toSet());
                }
                // 处理组件中的字段
                for (FieldMapping mapping : objectMapping.getFieldMappings()) {
                    String sourceFieldApiName = mapping.getSourceFieldApiName();
                    if (groupFieldNames.contains(sourceFieldApiName)) {
                        List<IFieldDescribe> groupFieldList = ObjectDescribeExt.of(objectDescribe).getGroupFieldList(sourceFieldApiName);
                        for (IFieldDescribe fieldDescribe : groupFieldList) {
                            if (!table.contains(objectMapping.getSourceApiName(), fieldDescribe.getApiName())) {
                                FieldMapping newMapping = new FieldMapping(objectMapping.getSourceApiName(), fieldDescribe.getApiName(), mapping.getChangeable(), mapping.getRecordOriginalValue());
                                table.put(objectMapping.getSourceApiName(), fieldDescribe.getApiName(), newMapping);
                            }
                        }
                    }
                    table.put(objectMapping.getSourceApiName(), sourceFieldApiName, mapping);
                }
            }
            sourceTable = table;
        }

        private static ObjectFieldMapper of(List<ObjectMapping> fieldMapping, Map<String, IObjectDescribe> describeMap) {
            return new ObjectFieldMapper(fieldMapping, describeMap);
        }

        public boolean changeableBySource(String sourceApiName, String sourceFieldApiName) {
            return fieldMappingBySource(sourceApiName, sourceFieldApiName)
                    .map(it -> BooleanUtils.isTrue(it.getChangeable()))
                    .orElse(false);
        }

        public boolean recordOriginalValueBySource(String sourceApiName, String sourceFieldApiName) {
            return fieldMappingBySource(sourceApiName, sourceFieldApiName)
                    .map(it -> BooleanUtils.isTrue(it.getRecordOriginalValue()))
                    .orElse(false);
        }

        public Optional<FieldMapping> fieldMappingBySource(String sourceApiName, String sourceFieldApiName) {
            FieldMapping fieldMapping = sourceTable.get(sourceApiName, sourceFieldApiName);
            return Optional.ofNullable(fieldMapping);
        }
    }
}
