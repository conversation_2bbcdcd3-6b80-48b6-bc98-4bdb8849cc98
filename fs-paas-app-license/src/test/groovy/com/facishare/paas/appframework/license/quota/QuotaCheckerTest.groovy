package com.facishare.paas.appframework.license.quota

import com.facishare.paas.appframework.license.quota.checker.QuotaChecker
import spock.lang.Specification

class QuotaCheckerTest extends Specification {

    def "test QuotaInfo builder should create object with correct values"() {
        when:
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .tenantId("12345")
                .describeApiName("test_object__c")
                .fieldType("text")
                .actualCount(10)
                .paraKey("test_param")
                .build()

        then:
        quotaInfo.tenantId == "12345"
        quotaInfo.describeApiName == "test_object__c"
        quotaInfo.fieldType == "text"
        quotaInfo.actualCount == 10
        quotaInfo.paraKey == "test_param"
    }

    def "test QuotaInfo should be correctly initialized with no-args constructor"() {
        when:
        def quotaInfo = new QuotaChecker.QuotaInfo()

        then:
        quotaInfo.tenantId == null
        quotaInfo.describeApiName == null
        quotaInfo.fieldType == null
        quotaInfo.actualCount == 0
        quotaInfo.paraKey == null
    }

    def "test QuotaInfo should be correctly initialized with all-args constructor"() {
        when:
        def quotaInfo = new QuotaChecker.QuotaInfo(
                "12345",
                "test_object__c",
                "text",
                10,
                "test_param"
        )

        then:
        quotaInfo.tenantId == "12345"
        quotaInfo.describeApiName == "test_object__c"
        quotaInfo.fieldType == "text"
        quotaInfo.actualCount == 10
        quotaInfo.paraKey == "test_param"
    }

    def "test QuotaInfo setters should modify values correctly"() {
        given:
        def quotaInfo = new QuotaChecker.QuotaInfo()

        when:
        quotaInfo.setTenantId("12345")
        quotaInfo.setDescribeApiName("test_object__c")
        quotaInfo.setFieldType("text")
        quotaInfo.setActualCount(10)
        quotaInfo.setParaKey("test_param")

        then:
        quotaInfo.tenantId == "12345"
        quotaInfo.describeApiName == "test_object__c"
        quotaInfo.fieldType == "text"
        quotaInfo.actualCount == 10
        quotaInfo.paraKey == "test_param"
    }
} 