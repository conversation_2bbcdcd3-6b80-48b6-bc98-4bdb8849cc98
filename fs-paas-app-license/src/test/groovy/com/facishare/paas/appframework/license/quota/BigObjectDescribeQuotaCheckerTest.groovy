package com.facishare.paas.appframework.license.quota

import com.facishare.paas.appframework.license.quota.checker.BigObjectDescribeQuotaChecker
import com.facishare.paas.appframework.license.quota.checker.QuotaChecker
import com.facishare.paas.appframework.license.util.ModulePara
import com.google.common.collect.Maps
import spock.lang.Specification

class BigObjectDescribeQuotaCheckerTest extends Specification {

    def "test getMaxCount should return mapped value if available"() {
        given:
        def paraKey = ModulePara.CUSTOM_BIG_OBJECTS_LIMIT.getParaKey()
        def paraMap = [(paraKey): 20]
        def bigObjectDescribeQuotaChecker = BigObjectDescribeQuotaChecker.builder()
                .paraMap(paraMap)
                .build()
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .build()

        when:
        def result = bigObjectDescribeQuotaChecker.getMaxCount(quotaInfo)

        then:
        result == 20
    }

    def "test getMaxCount should return default value if not mapped"() {
        given:
        def paraMap = Maps.newHashMap()
        def bigObjectDescribeQuotaChecker = BigObjectDescribeQuotaChecker.builder()
                .paraMap(paraMap)
                .build()
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .tenantId("12345")
                .build()

        when:
        def result = bigObjectDescribeQuotaChecker.getMaxCount(quotaInfo)

        then:
        result == ModulePara.CUSTOM_BIG_OBJECTS_LIMIT.getDefaultValue("12345")
    }

    def "test check should return true when actualCount <= maxCount"() {
        given:
        def paraKey = ModulePara.CUSTOM_BIG_OBJECTS_LIMIT.getParaKey()
        def paraMap = [(paraKey): 20]
        def bigObjectDescribeQuotaChecker = BigObjectDescribeQuotaChecker.builder()
                .paraMap(paraMap)
                .build()
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(10)
                .build()

        when:
        def result = bigObjectDescribeQuotaChecker.check(quotaInfo)

        then:
        result == true
    }

    def "test check should return false when actualCount > maxCount"() {
        given:
        def paraKey = ModulePara.CUSTOM_BIG_OBJECTS_LIMIT.getParaKey()
        def paraMap = [(paraKey): 20]
        def bigObjectDescribeQuotaChecker = BigObjectDescribeQuotaChecker.builder()
                .paraMap(paraMap)
                .build()
        def quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(30)
                .build()

        when:
        def result = bigObjectDescribeQuotaChecker.check(quotaInfo)

        then:
        result == false
    }
} 