package com.facishare.paas.appframework.license.quota;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.license.dto.ModuleParaLicense;
import com.facishare.paas.appframework.license.exception.LicenseException;
import com.facishare.paas.appframework.license.quota.checker.*;
import com.facishare.paas.appframework.license.util.LicenseConstants;
import com.facishare.paas.appframework.license.util.ModulePara;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.license.util.ModulePara.REFERENCE_FIELD_LIMIT;

@Slf4j
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantLicenseInfo {
    private User user;
    private LicenseService licenseService;
    //key是moduleCode,value是对应校验器
    private Map<String, QuotaChecker> checkerMap;
    private Map<String, List<ModuleParaLicense>> moduleParaMap;
    private Map<ModulePara, Integer> quotaMap;
    Map<String, Set<String>> moduleMap;
    // 有配额的字段类型
    private final List<String> NEED_TO_CHECK_QUOTA_FIELD_TYPE = Lists.newArrayList(IFieldType.OBJECT_REFERENCE, IFieldType.OBJECT_REFERENCE_MANY,
            IFieldType.FILE_ATTACHMENT, IFieldType.FORMULA, IFieldType.AUTO_NUMBER, IFieldType.IMAGE,
            IFieldType.QUOTE, IFieldType.COUNT, IFieldType.HTML_RICH_TEXT, IFieldType.RICH_TEXT, IFieldType.BIG_TEXT);
    // 无法购买配额的字段类型
    private final List<String> WITHOUT_QUOTA_FIELD_TYPE = Lists.newArrayList("group|sign_in", "group|payment", IFieldType.MASTER_DETAIL, IFieldType.HTML_RICH_TEXT, IFieldType.RICH_TEXT
            , IFieldType.BIG_TEXT);

    public TenantLicenseInfo init(Set<String> apps) {
        quotaMap = Maps.newHashMap();
        //根据应用获取对应的所有module和para
        moduleMap = parseModuleBy(apps);
        //根据modulePara通过分版服务查下对应配额数值
        moduleParaMap = licenseService.batchGetModuleLicenses(user, moduleMap);
        initQuotaInfo();
        checkerMap = generateQuotaChecker();
        return this;
    }

    private void fillExtraInfo() {
        if (moduleMap.containsKey(ModulePara.Module.FIELD_INFO.getModuleCode())) {
            //增加固定数值配额
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SUPPORT_1500_FIELDS_EI, user.getTenantId())) {
                quotaMap.put(ModulePara.ALL_FIELD_LIMIT, 1500);
            } else if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SUPPORT_THOUSAND_FIELDS_EI, user.getTenantId())) {
                quotaMap.put(ModulePara.ALL_FIELD_LIMIT, 1000);
            } else if (AppFrameworkConfig.supportMoreFields(user.getTenantId())) {
                quotaMap.put(ModulePara.ALL_FIELD_LIMIT, 600);
            } else {
                quotaMap.put(ModulePara.ALL_FIELD_LIMIT, 500);
            }
            quotaMap.put(ModulePara.MASTER_DETAIL_FIELD_LIMIT, 1);
            quotaMap.put(ModulePara.SIGN_IN_FIELD_LIMIT, 1);
            quotaMap.put(ModulePara.PAY_FIELD_LIMIT, 1);
            quotaMap.put(ModulePara.WHAT_LIST_FIELD_LIMIT, 1);

        }
    }

    private void initQuotaInfo() {
        //使用moduleMap循环保证每个配额都能返回数值(分版服务可能某个配额没返回)，否则设计器会按没有配额限制处理
        moduleMap.forEach((k, v) -> {
            List<ModuleParaLicense> paraLicenses = moduleParaMap.getOrDefault(k, Lists.newArrayList());
            Map<String, Integer> map = paraLicenses
                    .stream()
                    .collect(Collectors.toMap(ModuleParaLicense::getParaKey, a -> parseParaValue(a.getParaValue())));
            v.stream().forEach(a -> quotaMap.put(ModulePara.parse(a), map.getOrDefault(a, ModulePara.parse(a).getDefaultValue(user.getTenantId()))));
        });
        fillExtraInfo();
    }

    private int parseParaValue(String paraValue) {
        return Strings.isNullOrEmpty(paraValue) ? 0 : Integer.parseInt(paraValue);
    }

    public Map<String, Map<String, Integer>> getQuotaMap() {
        Map<String, Map<String, Integer>> map = Maps.newHashMap();
        Set<Map.Entry<ModulePara, Integer>> entries = quotaMap.entrySet();
        for (Map.Entry<ModulePara, Integer> entry : entries) {
            String moduleBiz = entry.getKey().getModuleBiz();
            Map<String, Integer> tmpMap = map.getOrDefault(moduleBiz, Maps.newHashMap());
            tmpMap.put(entry.getKey().getParaKey(), entry.getValue());
            map.putIfAbsent(moduleBiz, tmpMap);
        }

        return map;
    }

    public int getModuleParaQuota(ModulePara para) {
        return quotaMap.getOrDefault(para, 0);
    }


    private Map<String, QuotaChecker> generateQuotaChecker() {
        return moduleParaMap.entrySet().stream()
                .collect(Collectors.toMap(Map.Entry::getKey, a -> buildCheckerByModuleCode(a.getKey(), a.getValue())));
    }

    private QuotaChecker buildCheckerByModuleCode(String moduleCode, List<ModuleParaLicense> moduleParaLicense) {
        Map<String, Integer> paramMap = moduleParaLicense
                .stream()
                .collect(Collectors.toMap(ModuleParaLicense::getParaKey, this::parseIntValue));
        switch (moduleCode) {
            case LicenseConstants.ModuleCode.OBJECT_MULTI_LAYOUT:
                return LayoutQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.UI_EVENT:
                return UIEventQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.OBJECT_MULTI_TYPE:
                return RecordTypeQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.FIELD_INFO:
                return FieldQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.CUSTOM_FUNCTION:
                return FunctionQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.CUSTOM_OBJECT:
                return DescribeQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.BIG_OBJECT_APP:
                return BigObjectDescribeQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.SOCIAL_OBJECT:
                return SocialObjectDescribeQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.CUSTOM_ROLES:
                return RoleQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.DATA_ROLES:
                return DataRoleQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.GRADE_INSTRUMENT:
                return ScoreQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.LAYOUT_RULES:
                return LayoutRuleQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.SMART_FORM:
                return SmartFormQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.CUSTOM_BUTTON:
                return CustomButtonQuotaChecker.builder()
                        .paraMap(paramMap)
                        .build();
            case LicenseConstants.ModuleCode.MASTER_DETAIL_OBJECT:
                return new MasterDetailObjectChecker(paramMap);
            case LicenseConstants.ModuleCode.MASTER_RELATED_OBJECT:
                return new MasterRelatedObjectChecker(paramMap);
            case LicenseConstants.ModuleCode.MASTER_RELATED_OBJECT_COMPONENT:
                return new MasterRelatedObjectComponentChecker(paramMap);
            default:
                log.warn("no exist checker,moduleCode:{},paramMap:{}", moduleCode, paramMap);
                return NoExistChecker.builder().build();
        }
    }

    private Integer parseIntValue(ModuleParaLicense a) {
        return parseParaValue(a.getParaValue());
    }

    private Map<String, Set<String>> parseModuleBy(Set<String> apps) {
        Set<String> modules = Sets.newHashSet();
        CollectionUtils.nullToEmpty(apps)
                .forEach(a -> modules.addAll(ModulePara.getBizModule().getOrDefault(a, Sets.newHashSet())));

        return modules.stream()
                .collect(Collectors.toMap(a -> a, a -> ModulePara.getModulePara().get(a)));
    }

    private QuotaChecker getCheckerBy(String moduleCode) {
        return checkerMap.getOrDefault(moduleCode, NoExistChecker.builder().build());
    }

    public void checkDescribeCount(int describeCount, boolean isBigObject, boolean isSocialObject) {
        if (isBigObject) {
            checkByModuleCode(describeCount, ModulePara.Module.BIG_OBJECT.getModuleCode());
        }
        if (isSocialObject) {
            checkByModuleCode(describeCount, ModulePara.Module.SOCIAL_OBJECT.getModuleCode());
        }
        if (!isBigObject && !isSocialObject) {
            checkByModuleCode(describeCount, ModulePara.Module.CUSTOM_OBJECT.getModuleCode());
        }
    }

    private void checkByModuleCode(int describeCount, String moduleCode) {
        QuotaChecker checker = getCheckerBy(moduleCode);
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(describeCount)
                .build();
        if (!checker.check(quotaInfo)) {
            throw new LicenseException(I18N.text(I18NKey.VERSION_QUOTA_INSUFFICIENT));
        }
    }

    public int usableDescribeCount(int describeCount) {
        QuotaChecker checker = getCheckerBy(ModulePara.Module.CUSTOM_OBJECT.getModuleCode());
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(describeCount)
                .build();
        return checker.getMaxCount(quotaInfo) - describeCount;
    }

    public int usableDescribeCount(int describeCount, boolean includeBigObject, boolean includeSocialObject, boolean onlyVisibleScope) {
        int bigObjectMaxCount = 0;
        if (includeBigObject) {
            bigObjectMaxCount = getMaxCount(ModulePara.Module.BIG_OBJECT.getModuleCode());
        }
        int socialObjectMaxCount = 0;
        if (includeSocialObject) {
            socialObjectMaxCount = getMaxCount(ModulePara.Module.SOCIAL_OBJECT.getModuleCode());
        }
        int customObjectMaxCount = 0;
        if (!onlyVisibleScope) {
            customObjectMaxCount = getMaxCount(ModulePara.Module.CUSTOM_OBJECT.getModuleCode());
        }
        return bigObjectMaxCount + socialObjectMaxCount + customObjectMaxCount - describeCount;
    }

    private int getMaxCount(String moduleCode) {
        QuotaChecker checker = getCheckerBy(moduleCode);
        return checker.getMaxCount(new QuotaChecker.QuotaInfo());
    }

    public void checkLayoutCount(int layoutCount, String describeApiName) {
        QuotaChecker checker = getCheckerBy(ModulePara.Module.OBJECT_MULTI_LAYOUT.getModuleCode());
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(layoutCount)
                .describeApiName(describeApiName)
                .build();
        if (!checker.check(quotaInfo)) {
            throw new LicenseException(I18N.text(I18NKey.SUPPORT_LAYOUT, checker.getMaxCount(quotaInfo)));
        }
    }

    public void checkRecordTypeCount(int recordTypeCount, String describeApiName) {
        QuotaChecker checker = getCheckerBy(ModulePara.Module.OBJECT_MULTI_TYPE.getModuleCode());
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(recordTypeCount)
                .describeApiName(describeApiName)
                .build();
        if (!checker.check(quotaInfo)) {
            throw new LicenseException(I18N.text(I18NKey.SUPPORT_SERVICE_TYPE, checker.getMaxCount(quotaInfo)));
        }
    }

    public void checkCustomButtonCountLimit(int customButtonCount, String describeApiName) {
        QuotaChecker checker = getCheckerBy(ModulePara.Module.CUSTOM_BUTTON.getModuleCode());
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(customButtonCount)
                .describeApiName(describeApiName)
                .build();
        if (!checker.check(quotaInfo)) {
            throw new LicenseException(I18N.text(I18NKey.CUSTOM_BUTTON_LIMIT, checker.getMaxCount(quotaInfo)));
        }
    }

    public void checkDataShareCount(int dataShareCount) {
        QuotaChecker checker = getCheckerBy(ModulePara.Module.DATA_ROLES.getModuleCode());
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(dataShareCount)
                .paraKey(ModulePara.DATA_SHARE_RULES_LIMIT.getParaKey())
                .build();
        if (!checker.check(quotaInfo)) {
            throw new LicenseException(I18N.text(I18NKey.SUPPORT_DATA_SHARE, checker.getMaxCount(quotaInfo)));
        }
    }

    public void checkConditionalDataShareCount(int conditionalRuleCount) {
        QuotaChecker checker = getCheckerBy(ModulePara.Module.DATA_ROLES.getModuleCode());
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(conditionalRuleCount)
                .paraKey(ModulePara.CONDITIONAL_SHARING_LIMIT.getParaKey())
                .build();
        if (!checker.check(quotaInfo)) {
            throw new LicenseException(I18N.text(I18NKey.DATA_SHARE_RULE_COUNT_MAX));
        }
    }

    public void checkEntityShareCount(int entityShareCount) {
        QuotaChecker checker = getCheckerBy(ModulePara.Module.DATA_ROLES.getModuleCode());
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo
          .builder()
          .actualCount(entityShareCount)
          .paraKey(ModulePara.DATA_SHARE_RULES_LIMIT.getParaKey())
          .build();
        if (entityShareCount > checker.getMaxCount(quotaInfo)) {
            throw new LicenseException(I18N.text(I18NKey.ENTITY_SHARE_COUNT_MAX));
        }
    }

    public void checkFunctionCountLimit(int functionCount) {
        checkByModuleCode(functionCount, ModulePara.CUSTOM_FUNCTION_LIMIT.getModuleCode());
    }

    public void checkLayoutRuleCount(int layoutRuleCount) {
        QuotaChecker checker = getCheckerBy(ModulePara.LAYOUT_RULES_LIMIT.getModuleCode());
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(layoutRuleCount)
                .paraKey(ModulePara.LAYOUT_RULES_LIMIT.getParaKey())
                .build();
        if (!checker.check(quotaInfo)) {
            throw new LicenseException(I18N.text(I18NKey.LAYOUT_RULE_UPPER_LIMIT, checker.getMaxCount(quotaInfo)));
        }
    }

    public void checkUpdateUIEventCount(int actualCount) {
        QuotaChecker checker = getCheckerBy(ModulePara.UI_UPDATE_EVENT_LIMIT.getModuleCode());
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(actualCount)
                .paraKey(ModulePara.UI_UPDATE_EVENT_LIMIT.getParaKey())
                .build();
        if (!checker.check(quotaInfo)) {
            throw new LicenseException(I18N.text(I18NKey.UI_EVENT_EXCEED, checker.getMaxCount(quotaInfo)));
        }
    }

    public void checkCheckUIEventCount(int actualCount) {
        QuotaChecker checker = getCheckerBy(ModulePara.UI_CHECK_EVENT_LIMIT.getModuleCode());
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                .actualCount(actualCount)
                .paraKey(ModulePara.UI_CHECK_EVENT_LIMIT.getParaKey())
                .build();
        if (!checker.check(quotaInfo)) {
            throw new LicenseException(I18N.text(I18NKey.UI_EVENT_EXCEED, checker.getMaxCount(quotaInfo)));
        }
    }

    public void checkDetailObjectCount(int actualCount, String detailApiName) {
        QuotaChecker checker = getCheckerBy(ModulePara.MASTER_DETAIL_OBJECT_LIMIT.getModuleCode());
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                .tenantId(user.getTenantId())
                .actualCount(actualCount)
                .paraKey(ModulePara.MASTER_DETAIL_OBJECT_LIMIT.getParaKey())
                .build();
        if (!checker.check(quotaInfo)) {
            int maxCount = checker.getMaxCount(quotaInfo);
            log.warn("创建主数据时，从数据数量为:{}个,超过{}的最大限制,tenantId:{},detailApiName:{}", actualCount,
                    maxCount, user.getTenantId(), detailApiName);
            throw new ValidateException(I18N.text(I18NKey.DETAIL_DATA_NUMBER_MORE, actualCount, maxCount));
        }
    }

    public void checkRelatedObjectCount(int actualCount, String relatedApiName) {
        QuotaChecker checker = getCheckerBy(ModulePara.MASTER_RELATED_OBJECT_LIMIT.getModuleCode());
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                .tenantId(user.getTenantId())
                .actualCount(actualCount)
                .paraKey(ModulePara.MASTER_RELATED_OBJECT_LIMIT.getParaKey())
                .build();
        if (!checker.check(quotaInfo)) {
            int maxCount = checker.getMaxCount(quotaInfo);
            log.warn("actualCount:{},maxCount:{},tenantId:{},detailApiName:{}", actualCount,
                    maxCount, user.getTenantId(), relatedApiName);
            throw new ValidateException(I18N.text(I18NKey.RELATED_DATA_NUMBER_MORE, actualCount, maxCount));
        }
    }

    public void validateRelatedListFormComponentCount(int actualCount, String apiName) {
        QuotaChecker checker = getCheckerBy(ModulePara.MASTER_RELATED_OBJECT_COMPONENT_LIMIT.getModuleCode());
        QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                .tenantId(user.getTenantId())
                .actualCount(actualCount)
                .paraKey(ModulePara.MASTER_RELATED_OBJECT_COMPONENT_LIMIT.getParaKey())
                .build();
        if (!checker.check(quotaInfo)) {
            int maxCount = checker.getMaxCount(quotaInfo);
            log.warn("actualCount:{},maxCount:{},tenantId:{},detailApiName:{}", actualCount,
                    maxCount, user.getTenantId(), apiName);
            throw new ValidateException(I18N.text(I18NKey.RELATED_DATA_NUMBER_MORE, actualCount, maxCount));
        }
    }

    private String getFieldCheckErrorKey(ModulePara para) {
        switch (para) {
            case ALL_FIELD_LIMIT:
                return I18NKey.CUSTOM_FIELD_BEYOND_MAX_LIMIT;
            case PICTURE_LIMIT:
                return I18NKey.IMAGE_FIELD_BEYOND_MAX_LIMIT;
            case ATTACHMENT_LIMIT:
                return I18NKey.ENCLOSURE_FIELD_BEYOND_MAX_LIMIT;
            case COMPUTED_FIELD_LIMIT:
                return I18NKey.FORMULA_FIELD_BEYOND_MAX_LIMIT;
            case REFERENCE_FIELD_LIMIT:
                return I18NKey.RELATION_FIELD_BEYOND_MAX_LIMIT;
            case INCREASE_NUMBER_LIMIT:
                return I18NKey.INCREASING_CODING_FIELD_BEYOND_MAX_LIMIT;
            case STATISTICAL_FIELD_LIMIT:
                return I18NKey.COUNT_FIELD_BEYOND_MAX_LIMIT;
            case QUOTE_FIELD_LIMIT:
                return I18NKey.QUOTE_FIELD_BEYOND_MAX_LIMIT;
            case HTML_RICH_TEXT_FIELD_LIMIT:
                return I18NKey.HTML_RICH_TEXT_FIELD_LIMIT;
            case BIG_TEXT_FIELD_LIMIT:
                return I18NKey.BIG_RICH_TEXT_FIELD_LIMIT;
            case WHAT_LIST_FIELD_LIMIT:
                return I18NKey.WHAT_LIST_FIELD_LIMIT;
            case RICH_TEXT_FIELD_LIMIT:
                return I18NKey.RICH_TEXT_FIELD_LIMIT;
        }
        return null;
    }

    private ModulePara getParaByFieldType(String fieldType) {
        switch (fieldType) {
            case IFieldType.OBJECT_REFERENCE:
            case IFieldType.OBJECT_REFERENCE_MANY:
                return REFERENCE_FIELD_LIMIT;
            case IFieldType.FILE_ATTACHMENT:
                return ModulePara.ATTACHMENT_LIMIT;
            case IFieldType.FORMULA:
                return ModulePara.COMPUTED_FIELD_LIMIT;
            case IFieldType.AUTO_NUMBER:
                return ModulePara.INCREASE_NUMBER_LIMIT;
            case IFieldType.IMAGE:
                return ModulePara.PICTURE_LIMIT;
            case IFieldType.QUOTE:
                return ModulePara.QUOTE_FIELD_LIMIT;
            case IFieldType.COUNT:
                return ModulePara.STATISTICAL_FIELD_LIMIT;
            case IFieldType.HTML_RICH_TEXT:
                return ModulePara.HTML_RICH_TEXT_FIELD_LIMIT;
            case IFieldType.RICH_TEXT:
                return ModulePara.RICH_TEXT_FIELD_LIMIT;
            case IFieldType.BIG_TEXT:
                return ModulePara.BIG_TEXT_FIELD_LIMIT;
            case IFieldType.WHAT_LIST_DATA:
                return ModulePara.WHAT_LIST_FIELD_LIMIT;
            case "all":
                return ModulePara.ALL_FIELD_LIMIT;
        }
        return ModulePara.EMPTY;
    }

    public void checkFieldCount(Map<String, Integer> fieldMap, IObjectDescribe describe) {
        getFieldCount(fieldMap, describe, true);
    }

    public Map<String, Integer> getFieldCount(Map<String, Integer> fieldMap, IObjectDescribe describe, boolean isCheck) {
        QuotaChecker checker = getCheckerBy(ModulePara.Module.FIELD_INFO.getModuleCode());
        Map<String, Integer> fieldLimit = Maps.newHashMap();
        fieldMap.forEach((key, value) -> {
            ModulePara para = getParaByFieldType(key);
            if ((para.isEmpty())) {
                return;
            }
            QuotaChecker.QuotaInfo quotaInfo = QuotaChecker.QuotaInfo.builder()
                    .actualCount(value)
                    .tenantId(user.getTenantId())
                    .describeApiName(Objects.nonNull(describe) ? describe.getApiName() : null)
                    .paraKey(para.getParaKey())
                    .build();
            if (isCheck && !checker.check(quotaInfo)) {
                throw new LicenseException(I18N.text(getFieldCheckErrorKey(para), checker.getMaxCount(quotaInfo)));
            }
            if (!isCheck) {
                fieldLimit.put(key, checker.getMaxCount(quotaInfo));
                resetRichTextLimit(describe, key, fieldLimit);
            }
        });
        return fieldLimit;
    }

    private static void resetRichTextLimit(IObjectDescribe describe, String key, Map<String, Integer> fieldLimit) {
        if (Objects.isNull(describe)) {
            return;
        }
        if (StringUtils.equalsAny(key, IFieldType.HTML_RICH_TEXT, IFieldType.RICH_TEXT) && ObjectDescribeExt.of(describe).isSlaveObject()) {
            fieldLimit.put(key, 0);
        }
    }

    public List<String> getNeedToCheckQuotaFieldType() {
        return NEED_TO_CHECK_QUOTA_FIELD_TYPE;
    }

    public List<String> getWithoutQuotaFieldType() {
        return WITHOUT_QUOTA_FIELD_TYPE;
    }

}
