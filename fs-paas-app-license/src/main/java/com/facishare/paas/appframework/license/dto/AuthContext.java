package com.facishare.paas.appframework.license.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 访问license服务请求认证上下文
 *
 * Created by liyiguang on 2017/8/16.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AuthContext {
    private String appId;
    private String tenantId;
    private String userId;
    private Map<String,String> properties;
    private Map<String,Object> objectProperties;
}
