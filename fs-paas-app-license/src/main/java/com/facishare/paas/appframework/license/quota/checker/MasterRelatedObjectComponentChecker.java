package com.facishare.paas.appframework.license.quota.checker;

import java.util.Map;

import static com.facishare.paas.appframework.license.util.ModulePara.MASTER_RELATED_OBJECT_COMPONENT_LIMIT;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/4/13
 */
public class MasterRelatedObjectComponent<PERSON><PERSON><PERSON> extends BaseQuotaChecker {
    public MasterRelatedObjectComponentChecker(Map<String, Integer> paraMap) {
        super(paraMap);
    }

    @Override
    public int getMaxCount(QuotaInfo quotaInfo) {
        return paraMap.getOrDefault(MASTER_RELATED_OBJECT_COMPONENT_LIMIT.getParaKey(), MASTER_RELATED_OBJECT_COMPONENT_LIMIT.getDefaultValue(quotaInfo.getTenantId()));
    }

    @Override
    public boolean check(QuotaInfo quotaInfo) {
        return getMaxCount(quotaInfo) >= quotaInfo.getActualCount();
    }
}
