package com.facishare.crm.privilege.service.impl;

import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.privilege.service.CommonGroupService;
import com.facishare.crm.privilege.swagger.ApiException;
import com.facishare.crm.privilege.swagger.api.InsertApi;
import com.facishare.crm.privilege.swagger.api.UpdateApi;
import com.facishare.crm.privilege.swagger.model.BaseResult;
import com.facishare.crm.privilege.swagger.model.GroupPojo;
import com.facishare.crm.privilege.swagger.model.PojoArg;
import com.facishare.crm.privilege.swagger.model.UserArg;
import com.facishare.crm.privilege.util.Constant;
import com.facishare.crm.privilege.util.SwaggerApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by luxin on 2017/9/29.
 */
@Slf4j
@Component
public class CommonGroupServiceImpl implements CommonGroupService {
  @Override
  public Object createGroup(String tenantId, String groupName, String description) throws ApiException, CrmCheckedException {
    InsertApi insertApi = SwaggerApiUtil.getInsertApi();

    PojoArg arg = new PojoArg();
    arg.setAppId(Constant.APP_ID);
    arg.setTenantId(tenantId);
    arg.setUserId(Constant.SYSTEM_OPT_USER_ID);
    arg.setGroupPojo(getGroupPojo(tenantId, groupName, description));
    BaseResult response = insertApi.insert(arg);

    if (!response.getSuccess()) {
      log.error("addUsers2IntelliFormRole userRoleApi.roleInfo error. arg :{},error msg :{}", arg, response.getErrMessage());
      throw new CrmCheckedException(response.getErrCode(), response.getErrMessage());
    }
    return response.getResult();
  }

  @Override
  public void updateGroupMembers(String tenantId, String groupId, List<String> userIds) throws ApiException, CrmCheckedException {
    UpdateApi groupUpdateApi = SwaggerApiUtil.getGroupUpdateApi();

    UserArg arg = new UserArg();
    arg.setAppId(Constant.APP_ID);
    arg.setTenantId(tenantId);
    arg.setUserId(Constant.SYSTEM_OPT_USER_ID);
    arg.setGroupId(groupId);
    arg.setUserIdList(userIds);

    BaseResult response = groupUpdateApi.updateGroupMem(arg);
    if (!response.getSuccess()) {
      log.error("addUsers2IntelliFormRole userRoleApi.roleInfo error. arg :{},error msg :{}", arg, response.getErrMessage());
      throw new CrmCheckedException(response.getErrCode(), response.getErrMessage());
    }
  }

  @NotNull
  private GroupPojo getGroupPojo(String tenantId, String groupName, String description) {
    GroupPojo groupPojo = new GroupPojo();
    groupPojo.setTenantId(tenantId);
    groupPojo.setAppId(Constant.APP_ID);
    groupPojo.setName(groupName);
    groupPojo.setDescription(description);
    groupPojo.setStatus(0);
    groupPojo.setType(0);
    return groupPojo;
  }
}
