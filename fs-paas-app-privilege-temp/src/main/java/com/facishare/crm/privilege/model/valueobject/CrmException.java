package com.facishare.crm.privilege.model.valueobject;

import com.facishare.crm.common.exception.CRMErrorCode;
import lombok.Data;

/**
 * Created with IntelliJ IDEA.
 * User: gaozl
 * Date: 16-12-13
 * Time: 下午7:49
 * To change this template use File | Settings | File Templates.
 */
@Data
public class CrmException extends RuntimeException {
  private int code = -1;//错误码
  private String message;//错误说明
  private boolean needReport = false;
  private boolean fromDotNet = false;
  private CRMErrorCode errorCode = null;

  public int getCode() {
    return this.code;
  }

  public String getMessage() {
    return this.message;
  }

  public CrmException(CRMErrorCode errorCode) {
    this.errorCode = errorCode;
    this.needReport = false;
    if (errorCode != null) {
      this.message = errorCode.getMessage();
    }
  }

  public CrmException(CRMErrorCode errorCode, boolean needReport) {
    this.errorCode = errorCode;
    this.needReport = needReport;
    if (errorCode != null) {
      this.message = errorCode.getMessage();
    }
  }

  public boolean isFromDotNet() {
    return fromDotNet;
  }

  public void setFromDotNet(boolean fromDotNet) {
    this.fromDotNet = fromDotNet;
  }

  public CrmException(int code) {
    this.code = code;
    this.message = "error code: [ " + code + " ]";
  }

  public CrmException(int code, boolean needReport) {
    this.code = code;
    this.message = "error code: [ " + code + " ]";
    this.needReport = needReport;
  }

  public CrmException(int code, String message) {
    this.code = code;
    this.message = message;
  }

  public CrmException(int code, String message, boolean needReport) {
    this.code = code;
    this.message = message;
    this.needReport = needReport;
  }
  public CrmException(int code, boolean fromDotNet,String message) {
    this.code = code;
    this.message = message;
    this.fromDotNet = fromDotNet;
  }

  public CrmException(int code, String message, Throwable cause) {
    this.code = code;
    this.message = message;
  }
  public CrmException(int code, String message, Throwable cause, boolean needReport) {
    this.code = code;
    this.message = message;
    this.needReport=needReport;
  }

  public CrmException(String message) {
    this.code = -1;
    this.message = message;
  }

  public CrmException(String message, boolean needReport) {
    this.code = -1;
    this.message = message;
    this.needReport = needReport;
  }

  public boolean isNeedReport() {
    return needReport;
  }

  public CrmException needReport() {
    this.needReport = true;
    return this;
  }

  public void setNeedReport(boolean needReport) {
    this.needReport = needReport;
  }

}
