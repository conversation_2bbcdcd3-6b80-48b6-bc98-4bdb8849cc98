package com.facishare.crm.privilege.controller.arg;

import com.facishare.crm.privilege.util.Constant;

/**
 * Created by lei on 12/5/16.
 */
public class GroupListArg {

    private Integer searchType; //0 按照组名搜索 1 按照用户名搜索
    private String keyword;
    private Integer pageNumber;
    private Integer pageSize;
    private Integer status;
    private String orderKey;
    private Boolean isAsc = true;

    public Integer getSearchType() {
        return searchType;
    }

    public void setSearchType(Integer searchType) {
        this.searchType = searchType;
    }

    public String getKeyword() {
        return keyword;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public Integer getPageNumber() {
        return pageNumber;
    }

    public void setPageNumber(Integer pageNumber) {
        if (null == pageNumber || pageNumber <= 0) {
            this.pageNumber = 1;
        } else {
            this.pageNumber = pageNumber;
        }
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        if (null == pageSize || pageSize <= 0 || pageSize > 1000) {
            this.pageSize = Constant.getPageSize();
        } else {
            this.pageSize = pageSize;
        }
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getStatus() {
        return status;
    }

    public void setOrderKey(String orderBy) {
        this.orderKey = orderBy;
    }

    public String getOrderKey() {
        return orderKey;
    }

    public void setIsAsc(Boolean isAsc) {
        this.isAsc = isAsc;
    }

    public Boolean getIsAsc() {
        return isAsc;
    }
}
