package com.facishare.crm.privilege.rest.proxy.privilege.model;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.rest.core.util.JsonUtil;
import lombok.Data;

import java.util.Map;

/**
 * 底层REST服务的BaseArg。
 * Type1类型:海波团队的REST基础参数。
 * Created by yusb on 17/4/13.
 */
@Data
public class BaseArgDataPrivilege {

    private BaseContextDataPrivilege context =new BaseContextDataPrivilege();

    public void setAuthContext(SessionContext cxt){
      context.setTenantId(cxt.getEId().toString());
      context.setAppId(DefObjConstants.PACKAGE_NAME_CRM);
      context.setUserId(cxt.getUserIdString());
    }

    public void setObjectProperties(Map<String,Object> objectProperties){
      this.context.setObjectProperties(objectProperties);
    }
    public void setProperties(Map<String,Object> properties){
      this.context.setProperties(properties);
    }

    @Override
    public String toString() {
      return JsonUtil.toJson(this);
    }
}
