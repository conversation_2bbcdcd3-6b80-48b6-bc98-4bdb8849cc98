package com.facishare.crm.privilege.privilegeobject;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.privilege.model.FunctionInfo;
import com.facishare.crm.privilege.model.ObjectPrivilegeInfo;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by lx on 24/06/2018.
 */

public abstract class AbstractPrivilegeObject {

    private int order;

    private ObjectPrivilegeInfo objectPrivilegeInfo;
    private List<String> funcCodes;
    protected static IChangeableConfig prop = ConfigFactory.getConfig("fs-crm-objects-pririlege-config");

    {
        objectPrivilegeInfo = JSONObject.parseObject(prop.get(getConfigKey()), ObjectPrivilegeInfo.class);
        funcCodes = Collections.unmodifiableList(objectPrivilegeInfo.getRoleFunctionInfos().stream()
                .map(FunctionInfo::getFunctionNumber).collect(Collectors.toList()));
    }


    public abstract String getApiName();


    public abstract String getAppId();

    public abstract int getOrder();


    public String getViewListFuncCode() {
        return objectPrivilegeInfo.getViewListFuncCode();
    }

    public ObjectPrivilegeInfo getObjectPrivilegeInfo() {
        return objectPrivilegeInfo;
    }

    public List<String> getFuncCodes() {
        return funcCodes;
    }

    public String getConfigKey() {
        return getAppId() + getApiName();
    }

}
