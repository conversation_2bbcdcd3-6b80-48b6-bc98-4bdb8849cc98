package com.facishare.crm.privilege.rest.proxy.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.metadata.DescribeLogicServiceImpl;
import com.facishare.paas.appframework.privilege.util.FunctionPrivillegeConfig;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.ObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.bson.Document;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.facishare.paas.metadata.api.describe.IObjectDescribe.VISIBLE_SCOPE_BI;

/**
 * Created by fengyq on 12/7/16.
 */
@Service
@Slf4j
public class MetadataService {

    @Autowired
    private DescribeLogicServiceImpl objectDescribeService;

    /**
     * @param <T> IObjectDescribeDraft 或者 IObjectDescribe
     * @return
     * @throws CrmCheckedException
     */
    public <T> List<T> findDescribeList(FindDescribeListArg arg, String tenantId) throws MetadataServiceException {
        List<IObjectDescribe> describeList;
        describeList = getDraftOrDescribeListByConditionsAndTenantId(arg.isIncludeSystemObj(), arg.isIncludeUnActived(), arg.isExcludeDetailObj(), tenantId, IObjectDescribe.class, arg.getPackageName(), arg.getIsAsc());
        List<IObjectDescribe> resultList = dealDescribeListByIsIncludeField(arg.isIncludeFieldDescribe(), describeList, IObjectDescribe.class);
        return (List<T>) resultList;

    }

    private <T> boolean checkDescribeNull(List<T> describeList) {
        //如果返回结果是空
        if (describeList == null || describeList.isEmpty()) {
            log.debug("Leaving findDescribeList(),objectDescribeDrafts are not found");
            return true;
        }
        return false;
    }

    private <T> List<T> dealDescribeListByIsIncludeField(Boolean isIncludeField, List<T> draftList, Class ClassZ) {
        if (checkDescribeNull(draftList)) {
            return Lists.newArrayList();
        }
        List<T> resultList = Lists.newArrayList();

        for (T t : draftList) {
            Map containerDocument = ((DocumentBasedBean) t).getContainerDocument();
            String jsonStr = JSON.toJSONString(containerDocument);
            if (!isIncludeField) {
                containerDocument.put("fields", Maps.newHashMap());
            }
            IObjectDescribe describe = new ObjectDescribe();
            describe.fromJsonString(jsonStr);
            resultList.add((T) describe);
        }

        return resultList;
    }


    private <T> List<T> getDraftOrDescribeListByConditionsAndTenantId(Boolean isIncludeSystem, Boolean isIncludeUnActived, Boolean isExcludeDetailObj, String tenantId, Class classZ, String packageName, Boolean isAsc) throws MetadataServiceException {
        StopWatch stopWatch = StopWatch.create("getDraftOrDescribeListByConditionsAndTenantId,tenantId:" + tenantId);
        List<T> describeList;
        Document example = new Document();
        Map<String, Object> map = Maps.newHashMap();
        //如果packageName不为空,那么就指定获取某package的describe
        if (!Strings.isNullOrEmpty(packageName)) {
            map.put(IObjectDescribe.PACKAGE, packageName);
        } else {
            map.put(IObjectDescribe.PACKAGE, "CRM");
        }
        //如果不需要返回系统/package对象,那就指定只返回custom对象。
        if (isIncludeSystem.equals(Boolean.FALSE)) {
            map.put(IObjectDescribe.DEFINE_TYPE, IObjectDescribe.DEFINE_TYPE_CUSTOM);
        }
        //不能用下面被注释掉的代码去查,因为会有bug。会把老版本is_active为true的describe查出来。
    /*//如果不需要返回禁用的对象,那么就指定只返回isActive=true的对象。
    if (isIncludeUnActived.equals(Boolean.FALSE)) {
      map.put(IObjectDescribe.IS_ACTIVE, Boolean.TRUE);
    }*/
        example.putAll(map);
        stopWatch.lap("getDraftOrDescribeListByConditionsAndTenantId step 1");

        describeList = (List<T>) objectDescribeService.findByExample(tenantId, example);

        stopWatch.lap("getDraftOrDescribeListByConditionsAndTenantId step 2");
        //如果不需要返回禁用的对象,那么就指定只返回isActive=true的对象。
        if (isIncludeUnActived.equals(Boolean.FALSE)) {
            List<T> toRemoveList = Lists.newArrayList();
            for (T objectDescribe : describeList) {
                if (((ObjectDescribe) objectDescribe).isActive() != null &&
                        ((ObjectDescribe) objectDescribe).isActive().equals(Boolean.FALSE)) {
                    toRemoveList.add(objectDescribe);
                }
            }
            describeList.removeAll(toRemoveList);
        }
        //把不需要返回的从对象干掉
        if (isExcludeDetailObj.equals(Boolean.TRUE)) {
            List<T> toRemoveList = Lists.newArrayList();
            for (T objectDescribe : describeList) {
                if (isMasterDetailObj((IObjectDescribe) objectDescribe)) {
                    toRemoveList.add(objectDescribe);
                }
            }
            describeList.removeAll(toRemoveList);
        }


        Iterator<T> iterator = describeList.iterator();
        while (iterator.hasNext()) {
            T next = iterator.next();
            ObjectDescribe describe = (ObjectDescribe) next;
            if (VISIBLE_SCOPE_BI.equals(describe.getVisibleScope())) {
                iterator.remove();
            }
        }

        List<ObjectDescribe> describes = (List<ObjectDescribe>) describeList;
        stopWatch.lap("getDraftOrDescribeListByConditionsAndTenantId step 3");

        if (isAsc != null) {
            //对于基本有序的列表,进行冒泡排序,效率最高。因为id是基本正序排列的,所以基本有序。
            for (int i = 0; i < describes.size(); i++) {
                for (int j = i; j < describes.size(); j++) {
                    if ((describes.get(i).getCreateTime() != null && describes.get(j).getCreateTime() != null) &&
                            (describes.get(i).getCreateTime() > describes.get(j).getCreateTime())) {
                        ObjectDescribe tmp = describes.get(i);
                        describes.set(i, describes.get(j));
                        describes.set(j, tmp);
                    }
                }
            }
            describeList = (List<T>) describes;
            if (isAsc.equals(Boolean.FALSE)) {
                //默认倒叙,倒叙
                Collections.reverse(describeList);
            }
        }
        stopWatch.lap("getDraftOrDescribeListByConditionsAndTenantId step 4");
        if (isIncludeSystem) {
            //还不支持的老对象,就过滤掉,665写迁移已处理完，多余逻辑移除,开启灰度走该逻辑
            if (!FunctionPrivillegeConfig.openGrayOld_master_detail_obj_api_names_enterpriseids(tenantId)) {
                List<T> toRemoveList = Lists.newArrayList();
                for (T objectDescribe : describeList) {
                    if (Utils.OLD_MASTER_DETAIL_OBJ_API_NAMES.contains(((ObjectDescribe) objectDescribe).getApiName())) {
                        toRemoveList.add(objectDescribe);
                    }
                }
                describeList.removeAll(toRemoveList);
            }
            //排序
            List<T> newOrderedDescribeList = Lists.newArrayList();
            List<T> toRemoveDescribeList = Lists.newArrayList();
            List<String> objectDescribeApiNames = describeList.stream().map(it -> ((ObjectDescribe) it).getApiName()).collect(Collectors.toList());
            for (String orderedObjApiName : AppFrameworkConfig.getObjectOrder()) {
                if (objectDescribeApiNames.contains(orderedObjApiName)) {
                    int index = objectDescribeApiNames.indexOf(orderedObjApiName);
                    newOrderedDescribeList.add(describeList.get(index));
                    toRemoveDescribeList.add(describeList.get(index));
                }
            }
            describeList.removeAll(toRemoveDescribeList);
            newOrderedDescribeList.addAll(describeList);
            describeList = newOrderedDescribeList;
        }
        stopWatch.lap("getDraftOrDescribeListByConditionsAndTenantId step 5");
        stopWatch.logSlow(1000);
        return describeList;
    }

    public List<IObjectData> getDataProjectionDataList(List<String> includeFields, List<IObjectData> dataList) {
        List<IObjectData> resultList = Lists.newArrayList();
        for (int i = 0; i < dataList.size(); i++) {
            IObjectData objectData = new ObjectData();
            objectData.set(IObjectData.ID, dataList.get(i).getId());
            objectData.set(IObjectData.DESCRIBE_API_NAME, dataList.get(i).getDescribeApiName());
            objectData.set(IObjectData.DESCRIBE_ID, dataList.get(i).getDescribeId());
            objectData.set(IObjectData.TENANT_ID, dataList.get(i).getTenantId());
            for (String field : includeFields) {
                objectData.set(field, dataList.get(i).get(field));
            }
            resultList.add(objectData);
        }

        return resultList;
    }


    public boolean isMasterDetailObj(IObjectDescribe objectDescribe) {
        if (objectDescribe == null || objectDescribe.getFieldDescribes() == null ||
                objectDescribe.getFieldDescribes().size() == 0) return false;
        for (IFieldDescribe fieldDescribe : objectDescribe.getFieldDescribes()) {
            if (IFieldType.MASTER_DETAIL.equals(fieldDescribe.getType())) {
                return true;
            }
        }
        return false;
    }

}