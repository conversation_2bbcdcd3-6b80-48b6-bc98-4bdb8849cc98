package com.facishare.crm.privilege.controller;

import com.facishare.crm.privilege.controller.arg.*;
import com.facishare.crm.privilege.service.UserService;
import com.facishare.crm.privilege.util.ResultUtil;
import com.facishare.fcp.model.BaseFcpServiceResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * Created by lei on 11/21/16.
 */
@Service
public class UserController extends BaseController implements IUserController {

    @Autowired
    private UserService userService;

    @Override
    public BaseFcpServiceResult batchAddUsersToRoles(BatchAddUsersToRolesArg arg) {
        return ResultUtil.convert(userService.batchAddUsersToRoles(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult updateUserRole(UpdateUserRoleArg arg) {
        return ResultUtil.convert(userService.updateUserRole(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult crmUserList(CrmUserListArg arg) {
        return ResultUtil.convert(userService.crmUserList(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult roleUsers(RoleUsersArg arg) {
        return ResultUtil.convert(userService.roleUsers(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult copyUserRoleToUsers(CopyUserRoleToUserArg arg) {
        return ResultUtil.convert(userService.copyUserRoleToUsers(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult removeCrmUser(RemoveCrmUserArg arg) {
        return ResultUtil.convert(userService.removeCrmUser(arg, getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult updateUsersMajorRole(UpdateUsersMajorRole arg) {
        return ResultUtil.convert(userService.updateUsersMajorRole(arg,getSessionContext()));
    }

    @Override
    public BaseFcpServiceResult updateUserMajorRoleRespectively(UpdateUserMajorRoleRespectivelyArg arg) {
        return ResultUtil.convert(userService.updateUserMajorRoleRespectively(arg,getSessionContext()));
    }

}
