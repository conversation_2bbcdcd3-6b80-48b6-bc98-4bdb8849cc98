package com.facishare.crm.privilege.rest.proxy.auth.model;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 底层REST服务的BaseContext。
 * Type1类型:海波团队REST接口的Context。
 * Created by yusb on 17/4/13.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseAuthContext {
  private String tenantId;
  private String appId;
  private String userId;

  private Map<String,Object> objectProperties;
  private Map<String,Object> properties;
}
