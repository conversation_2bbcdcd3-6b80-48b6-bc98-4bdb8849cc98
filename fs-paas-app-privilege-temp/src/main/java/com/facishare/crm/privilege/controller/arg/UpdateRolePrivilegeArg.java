package com.facishare.crm.privilege.controller.arg;

import lombok.Data;
import lombok.ToString;

import java.util.List;

/**
 * Created by luxin on 2016/11/21.
 */
@Data
@ToString
public class UpdateRolePrivilegeArg {
    //前端传的roleCode
    private String roleCode;
    //后端返回的roleCode
    private String returnedRoleCode;
    private Integer roleType;
    private List<String> funcCode;

    private List<String> updatedViewListFuncCode;

}