package com.facishare.crm.privilege.util.pb;

import io.protostuff.Tag;

/**
 * Created by lei on 11/30/16.
 */
public class QueryQuotaResult {
    //    required int32 code = 1; //200成功，其他失败
//    required string msg = 2; //OK成功
//    required int32 quota = 3; //配额数
    @Tag(1)
    private int code;
    @Tag(2)
    private String msg;
    @Tag(3)
    private int quota;

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public int getQuota() {
        return quota;
    }

    public void setQuota(int quota) {
        this.quota = quota;
    }

    @Override
    public String toString() {
        return "QueryQuotaResult{" +
                "code=" + code +
                ", msg='" + msg + '\'' +
                ", quota=" + quota +
                '}';
    }
}
