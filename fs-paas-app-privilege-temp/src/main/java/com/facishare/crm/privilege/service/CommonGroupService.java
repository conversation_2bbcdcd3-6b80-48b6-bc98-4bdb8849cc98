package com.facishare.crm.privilege.service;

import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.privilege.swagger.ApiException;
import com.facishare.crm.privilege.swagger.model.BaseResult;

import java.util.List;

/**
 * Created by luxin on 2017/9/29.
 */
public interface CommonGroupService {

  /**
   * 新建用户组
   *
   * @param tenantId
   * @param groupName
   * @param description
   * @return
   */
  Object createGroup(String tenantId, String groupName, String description) throws ApiException, CrmCheckedException;

  /**
   * 更新用户组的员工,将原来的员工都删除,将userIds全部加入到用户组
   *
   * @param tenantId
   * @param groupId
   * @param userIds
   */
  void updateGroupMembers(String tenantId, String groupId, List<String> userIds) throws ApiException, CrmCheckedException;

}
