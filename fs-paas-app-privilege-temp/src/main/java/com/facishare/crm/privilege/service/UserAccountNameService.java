package com.facishare.crm.privilege.service;

import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.privilege.model.valueobject.CrmContext;
import com.facishare.crm.privilege.swagger.ApiClient;
import com.facishare.crm.privilege.swagger.ApiException;
import com.facishare.crm.privilege.swagger.SwaggerClientUtil;
import com.facishare.crm.privilege.swagger.api.QueryApi;
import com.facishare.crm.privilege.swagger.model.IdListArg;
import com.facishare.crm.privilege.swagger.model.ListUserInfoResult;
import com.facishare.crm.privilege.swagger.model.UserInfo;
import com.facishare.crm.privilege.util.Constant;
import com.facishare.crm.valueobject.SessionContext;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IChangeableConfig;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Created by yusb on 17/4/5.
 */
@Service
@Slf4j
public class UserAccountNameService {

  private IChangeableConfig props;

  @PostConstruct
  private void init() {
    props = ConfigFactory.getConfig(Constant.getFS_CRM_PRIVILEGE());
    log.info("GroupService props {}", Arrays.toString(props.getLines().toArray()));
  }



  public String getUserName(String userId, CrmContext context) {
    ApiClient apiClient = SwaggerClientUtil.getApiClient(Constant.BASE_URL_ORG_KEY, props);
    QueryApi api = new QueryApi(apiClient);
    IdListArg idArg = new IdListArg();

    idArg.setIdList(Collections.singletonList(userId));
    idArg.setUserId(String.valueOf(context.getUserId()));
    idArg.setAppId(Constant.getAppId());
    idArg.setTenantId(String.valueOf(context.getTenantId()));
    try {
      ListUserInfoResult userInfos = api.getUserInfoByUserIds(idArg);
      List<UserInfo> uis = userInfos.getResult();
      if (uis.isEmpty()) return "";
      return uis.get(0).getName();
    } catch (ApiException e) {
      log.error(Constant.paas_error_log + " m={} p={}", "getUserInfoByUserIds", JSONObject.toJSON(idArg), e);
    }
    return "";
  }
}
