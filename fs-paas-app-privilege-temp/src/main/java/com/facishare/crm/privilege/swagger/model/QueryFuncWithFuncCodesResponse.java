/**
 * Facishare PAAS Auth
 * This is PAAS auth service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.model;

import java.util.Objects;
import com.facishare.crm.privilege.swagger.model.FunctionPojo;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.util.ArrayList;
import java.util.List;


/**
 * QueryFuncWithFuncCodesResponse
 */
@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2017-05-16T15:01:04.279+08:00")
public class QueryFuncWithFuncCodesResponse   {
  @SerializedName("errCode")
  private Integer errCode = null;

  @SerializedName("errMessage")
  private String errMessage = null;

  @SerializedName("result")
  private List<FunctionPojo> result = new ArrayList<FunctionPojo>();

  @SerializedName("success")
  private Boolean success = false;

  public QueryFuncWithFuncCodesResponse errCode(Integer errCode) {
    this.errCode = errCode;
    return this;
  }

   /**
   * Get errCode
   * @return errCode
  **/
  @ApiModelProperty(example = "null", value = "")
  public Integer getErrCode() {
    return errCode;
  }

  public void setErrCode(Integer errCode) {
    this.errCode = errCode;
  }

  public QueryFuncWithFuncCodesResponse errMessage(String errMessage) {
    this.errMessage = errMessage;
    return this;
  }

   /**
   * Get errMessage
   * @return errMessage
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getErrMessage() {
    return errMessage;
  }

  public void setErrMessage(String errMessage) {
    this.errMessage = errMessage;
  }

  public QueryFuncWithFuncCodesResponse result(List<FunctionPojo> result) {
    this.result = result;
    return this;
  }

  public QueryFuncWithFuncCodesResponse addResultItem(FunctionPojo resultItem) {
    this.result.add(resultItem);
    return this;
  }

   /**
   * Get result
   * @return result
  **/
  @ApiModelProperty(example = "null", value = "")
  public List<FunctionPojo> getResult() {
    return result;
  }

  public void setResult(List<FunctionPojo> result) {
    this.result = result;
  }

  public QueryFuncWithFuncCodesResponse success(Boolean success) {
    this.success = success;
    return this;
  }

   /**
   * Get success
   * @return success
  **/
  @ApiModelProperty(example = "null", value = "")
  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    QueryFuncWithFuncCodesResponse queryFuncWithFuncCodesResponse = (QueryFuncWithFuncCodesResponse) o;
    return Objects.equals(this.errCode, queryFuncWithFuncCodesResponse.errCode) &&
        Objects.equals(this.errMessage, queryFuncWithFuncCodesResponse.errMessage) &&
        Objects.equals(this.result, queryFuncWithFuncCodesResponse.result) &&
        Objects.equals(this.success, queryFuncWithFuncCodesResponse.success);
  }

  @Override
  public int hashCode() {
    return Objects.hash(errCode, errMessage, result, success);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class QueryFuncWithFuncCodesResponse {\n");
    
    sb.append("    errCode: ").append(toIndentedString(errCode)).append("\n");
    sb.append("    errMessage: ").append(toIndentedString(errMessage)).append("\n");
    sb.append("    result: ").append(toIndentedString(result)).append("\n");
    sb.append("    success: ").append(toIndentedString(success)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

