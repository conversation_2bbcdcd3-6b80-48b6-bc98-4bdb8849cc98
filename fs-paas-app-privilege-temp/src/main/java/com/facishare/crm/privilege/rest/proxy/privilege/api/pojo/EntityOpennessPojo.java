package com.facishare.crm.privilege.rest.proxy.privilege.api.pojo;

import lombok.Builder;
import lombok.Data;

/**
 * Created by yusb on 2017/4/24.
 */
@Data
@Builder
public class EntityOpennessPojo {
  private String id;
  private String entityId;
  private Integer permission;
  private Integer scope;
  private String appId;
  private String tenantId;
  private Boolean delFlag;
  private String creator;
  private Long createTime;
  private String modifier;
  private Long modifyTime;

  static final public Integer SCOPE_ALL = 0; //全公司
  static final public Integer SCOPE_DEPT = 1; //本部门
  static final public Integer SCOPE_PRIVATE = 2; //私有

  static final public Integer PERMISSION_READONLY = 1; //只读
  static final public Integer PERMISSION_READ_OR_WRITE = 2; //读写

}
