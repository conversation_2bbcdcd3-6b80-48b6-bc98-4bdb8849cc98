package com.facishare.crm.privilege.util;

import com.facishare.converter.EIEAConverter;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.dto.InternationalItem;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.util.AppIdUtil;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.ListUtils;

import java.util.List;

/**
 * Created by luxin on 2016/12/23.
 */
@Slf4j
public class AddRemindRecordUtil {

    private static CRMNotificationService notificationService = SpringUtil.getContext().getBean(CRMNotificationService.class);
    private static EIEAConverter eieaConverter = SpringUtil.getContext().getBean(EIEAConverter.class);

    public static String getDeleteDec() {
        return I18NKey.DELETED;
    }

    public static String getAddDec() {
        return I18NKey.INCREASED;
    }

    //发送crm通知
    public static void callRemindRecordMethod(SessionContext sessionContext, List<Integer> receiveUserIds, String roleName, String prefixDec) {

        User user = new User(sessionContext.getEId().toString(), String.valueOf(sessionContext.getUserId()));
        StringBuffer sb = new StringBuffer();
        sb.append(I18N.text(prefixDec));
        sb.append(roleName);
        sb.append(I18N.text(I18NKey.PRIVILEGE));
     //   final String content = sb.toString();
//        final CRMNotification crmNotification = CRMNotification.builder()
//                .content(content)
//                .remindRecordType(11)
//                .content2Id(user.getUserId())
//                .dataId(StringUtils.EMPTY)
//                .receiverIds(Sets.newHashSet(ListUtils.defaultIfNull(receiveUserIds, Lists.newArrayList())))
//                .build();
//        notificationService.sendCRMNotification(user, crmNotification);

        String operator = "{{E." + eieaConverter.enterpriseIdToAccount(sessionContext.getEId().intValue()) + "." + user.getUserId() + "}}";
        sb.append("操作人：");// ignoreI18n
        sb.append(operator);
        final String fullContent = sb.toString();
        final NewCrmNotification newCrmNotification = NewCrmNotification.builder()
                .senderId(user.getUserId())
                .fullContent(fullContent)
                .fullContentInfo(InternationalItem.builder()
                        .internationalKey(I18NKey.ADD_REMIND_RECORD_UTIL)
                        .internationalParameters(Lists.newArrayList(
                                I18NExt.getI18NKey(prefixDec),
                                roleName,
                                I18NExt.getI18NKey(I18NKey.PRIVILEGE),
                                operator))
                        .build())
                .remindSender(true)
                .type(11)
                .receiverIDs(Sets.newHashSet(ListUtils.defaultIfNull(receiveUserIds, Lists.newArrayList())))
                .appId(AppIdUtil.getAppId(user))
                .build();
        notificationService.sendNewCrmNotification(user, newCrmNotification);
    }

}
