/**
 * Facishare PAAS Auth
 * This is PAAS auth service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.model;

import java.util.Objects;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * RoleViewPojo
 */
@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2017-05-16T15:01:04.279+08:00")
public class RoleViewPojo   {
  @SerializedName("id")
  private String id = null;

  @SerializedName("tenantId")
  private String tenantId = null;

  @SerializedName("appId")
  private String appId = null;

  @SerializedName("roleCode")
  private String roleCode = null;

  @SerializedName("entityId")
  private String entityId = null;

  @SerializedName("viewId")
  private String viewId = null;

  @SerializedName("recordTypeId")
  private String recordTypeId = null;

  public RoleViewPojo id(String id) {
    this.id = id;
    return this;
  }

   /**
   * Get id
   * @return id
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getId() {
    return id;
  }

  public void setId(String id) {
    this.id = id;
  }

  public RoleViewPojo tenantId(String tenantId) {
    this.tenantId = tenantId;
    return this;
  }

   /**
   * Get tenantId
   * @return tenantId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getTenantId() {
    return tenantId;
  }

  public void setTenantId(String tenantId) {
    this.tenantId = tenantId;
  }

  public RoleViewPojo appId(String appId) {
    this.appId = appId;
    return this;
  }

   /**
   * Get appId
   * @return appId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getAppId() {
    return appId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }

  public RoleViewPojo roleCode(String roleCode) {
    this.roleCode = roleCode;
    return this;
  }

   /**
   * Get roleCode
   * @return roleCode
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getRoleCode() {
    return roleCode;
  }

  public void setRoleCode(String roleCode) {
    this.roleCode = roleCode;
  }

  public RoleViewPojo entityId(String entityId) {
    this.entityId = entityId;
    return this;
  }

   /**
   * Get entityId
   * @return entityId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getEntityId() {
    return entityId;
  }

  public void setEntityId(String entityId) {
    this.entityId = entityId;
  }

  public RoleViewPojo viewId(String viewId) {
    this.viewId = viewId;
    return this;
  }

   /**
   * Get viewId
   * @return viewId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getViewId() {
    return viewId;
  }

  public void setViewId(String viewId) {
    this.viewId = viewId;
  }

  public RoleViewPojo recordTypeId(String recordTypeId) {
    this.recordTypeId = recordTypeId;
    return this;
  }

   /**
   * Get recordTypeId
   * @return recordTypeId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getRecordTypeId() {
    return recordTypeId;
  }

  public void setRecordTypeId(String recordTypeId) {
    this.recordTypeId = recordTypeId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    RoleViewPojo roleViewPojo = (RoleViewPojo) o;
    return Objects.equals(this.id, roleViewPojo.id) &&
        Objects.equals(this.tenantId, roleViewPojo.tenantId) &&
        Objects.equals(this.appId, roleViewPojo.appId) &&
        Objects.equals(this.roleCode, roleViewPojo.roleCode) &&
        Objects.equals(this.entityId, roleViewPojo.entityId) &&
        Objects.equals(this.viewId, roleViewPojo.viewId) &&
        Objects.equals(this.recordTypeId, roleViewPojo.recordTypeId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(id, tenantId, appId, roleCode, entityId, viewId, recordTypeId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class RoleViewPojo {\n");
    
    sb.append("    id: ").append(toIndentedString(id)).append("\n");
    sb.append("    tenantId: ").append(toIndentedString(tenantId)).append("\n");
    sb.append("    appId: ").append(toIndentedString(appId)).append("\n");
    sb.append("    roleCode: ").append(toIndentedString(roleCode)).append("\n");
    sb.append("    entityId: ").append(toIndentedString(entityId)).append("\n");
    sb.append("    viewId: ").append(toIndentedString(viewId)).append("\n");
    sb.append("    recordTypeId: ").append(toIndentedString(recordTypeId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

