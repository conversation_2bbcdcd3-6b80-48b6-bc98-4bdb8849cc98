/**
 * Facishare PAAS Auth
 * This is PAAS auth service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.api;

import com.facishare.crm.privilege.swagger.ApiCallback;
import com.facishare.crm.privilege.swagger.ApiClient;
import com.facishare.crm.privilege.swagger.ApiException;
import com.facishare.crm.privilege.swagger.ApiResponse;
import com.facishare.crm.privilege.swagger.Configuration;
import com.facishare.crm.privilege.swagger.Pair;
import com.facishare.crm.privilege.swagger.ProgressRequestBody;
import com.facishare.crm.privilege.swagger.ProgressResponseBody;
import com.facishare.crm.privilege.swagger.model.BaseResult;
import com.facishare.crm.privilege.swagger.model.DelRoleFieldPermissArg;
import com.facishare.crm.privilege.swagger.model.FieldPermissResponse;
import com.facishare.crm.privilege.swagger.model.RoleFieldPermissionArg;
import com.facishare.crm.privilege.swagger.model.UpdateRoleFieldPermissionArg;
import com.facishare.crm.privilege.swagger.model.UserEntitysFieldPermissArg;
import com.facishare.crm.privilege.swagger.model.UserEntitysFieldPermissResponse;
import com.facishare.crm.privilege.swagger.model.UserFieldPermissionArg;
import com.google.gson.reflect.TypeToken;

import java.io.IOException;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class FieldPermissionApi {
    private ApiClient apiClient;

    public FieldPermissionApi() {
        this(Configuration.getDefaultApiClient());
    }

    public FieldPermissionApi(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return apiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.apiClient = apiClient;
    }

    /* Build call for delRoleFieldPermiss */
    private okhttp3.Call delRoleFieldPermissCall(DelRoleFieldPermissArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/delRoleFieldPermiss".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 删除角色字段权限
     * 删除角色字段权限
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult delRoleFieldPermiss(DelRoleFieldPermissArg body) throws ApiException {
        ApiResponse<BaseResult> resp = delRoleFieldPermissWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 删除角色字段权限
     * 删除角色字段权限
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> delRoleFieldPermissWithHttpInfo(DelRoleFieldPermissArg body) throws ApiException {
        okhttp3.Call call = delRoleFieldPermissCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 删除角色字段权限 (asynchronously)
     * 删除角色字段权限
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call delRoleFieldPermissAsync(DelRoleFieldPermissArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = delRoleFieldPermissCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for roleFieldPermiss */
    private okhttp3.Call roleFieldPermissCall(RoleFieldPermissionArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/roleFieldPermiss".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 角色字段权限查询
     * 角色字段权限查询
     * @param body  (optional)
     * @return FieldPermissResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public FieldPermissResponse roleFieldPermiss(RoleFieldPermissionArg body) throws ApiException {
        ApiResponse<FieldPermissResponse> resp = roleFieldPermissWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 角色字段权限查询
     * 角色字段权限查询
     * @param body  (optional)
     * @return ApiResponse&lt;FieldPermissResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<FieldPermissResponse> roleFieldPermissWithHttpInfo(RoleFieldPermissionArg body) throws ApiException {
        okhttp3.Call call = roleFieldPermissCall(body, null, null);
        Type localVarReturnType = new TypeToken<FieldPermissResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 角色字段权限查询 (asynchronously)
     * 角色字段权限查询
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call roleFieldPermissAsync(RoleFieldPermissionArg body, final ApiCallback<FieldPermissResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = roleFieldPermissCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<FieldPermissResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for updateFieldPermiss */
    private okhttp3.Call updateFieldPermissCall(UpdateRoleFieldPermissionArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/updateFieldPermiss".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 角色字段权限更新
     * 角色字段权限更新
     * @param body  (optional)
     * @return BaseResult
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public BaseResult updateFieldPermiss(UpdateRoleFieldPermissionArg body) throws ApiException {
        ApiResponse<BaseResult> resp = updateFieldPermissWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 角色字段权限更新
     * 角色字段权限更新
     * @param body  (optional)
     * @return ApiResponse&lt;BaseResult&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<BaseResult> updateFieldPermissWithHttpInfo(UpdateRoleFieldPermissionArg body) throws ApiException {
        okhttp3.Call call = updateFieldPermissCall(body, null, null);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 角色字段权限更新 (asynchronously)
     * 角色字段权限更新
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call updateFieldPermissAsync(UpdateRoleFieldPermissionArg body, final ApiCallback<BaseResult> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = updateFieldPermissCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<BaseResult>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for userEntitysFieldPermiss */
    private okhttp3.Call userEntitysFieldPermissCall(UserEntitysFieldPermissArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/userEntitysFieldPermiss".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 查询用户对多个对象的字段权限
     * 查询用户对多个对象的字段权限
     * @param body  (optional)
     * @return UserEntitysFieldPermissResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public UserEntitysFieldPermissResponse userEntitysFieldPermiss(UserEntitysFieldPermissArg body) throws ApiException {
        ApiResponse<UserEntitysFieldPermissResponse> resp = userEntitysFieldPermissWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 查询用户对多个对象的字段权限
     * 查询用户对多个对象的字段权限
     * @param body  (optional)
     * @return ApiResponse&lt;UserEntitysFieldPermissResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<UserEntitysFieldPermissResponse> userEntitysFieldPermissWithHttpInfo(UserEntitysFieldPermissArg body) throws ApiException {
        okhttp3.Call call = userEntitysFieldPermissCall(body, null, null);
        Type localVarReturnType = new TypeToken<UserEntitysFieldPermissResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 查询用户对多个对象的字段权限 (asynchronously)
     * 查询用户对多个对象的字段权限
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call userEntitysFieldPermissAsync(UserEntitysFieldPermissArg body, final ApiCallback<UserEntitysFieldPermissResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = userEntitysFieldPermissCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<UserEntitysFieldPermissResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
    /* Build call for userFieldPermiss */
    private okhttp3.Call userFieldPermissCall(UserFieldPermissionArg body, final ProgressResponseBody.ProgressListener progressListener, final ProgressRequestBody.ProgressRequestListener progressRequestListener) throws ApiException {
        Object localVarPostBody = body;
        

        // create path and map variables
        String localVarPath = "/userFieldPermiss".replaceAll("\\{format\\}","json");

        List<Pair> localVarQueryParams = new ArrayList<Pair>();

        Map<String, String> localVarHeaderParams = new HashMap<String, String>();

        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        ApiClientHeaderUtil.initHttpHeader(localVarHeaderParams, apiClient);

        if(progressListener != null) {
            apiClient.getHttpClient().networkInterceptors().add(new okhttp3.Interceptor() {
                @Override
                public okhttp3.Response intercept(Chain chain) throws IOException {
                    okhttp3.Response originalResponse = chain.proceed(chain.request());
                    return originalResponse.newBuilder()
                    .body(new ProgressResponseBody(originalResponse.body(), progressListener))
                    .build();
                }
            });
        }

        String[] localVarAuthNames = new String[] {  };
        return apiClient.buildCall(localVarPath, "POST", localVarQueryParams, localVarPostBody, localVarHeaderParams, localVarFormParams, localVarAuthNames, progressRequestListener);
    }

    /**
     * 用户字段权限查询
     * 用户字段权限查询
     * @param body  (optional)
     * @return FieldPermissResponse
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public FieldPermissResponse userFieldPermiss(UserFieldPermissionArg body) throws ApiException {
        ApiResponse<FieldPermissResponse> resp = userFieldPermissWithHttpInfo(body);
        return resp.getData();
    }

    /**
     * 用户字段权限查询
     * 用户字段权限查询
     * @param body  (optional)
     * @return ApiResponse&lt;FieldPermissResponse&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     */
    public ApiResponse<FieldPermissResponse> userFieldPermissWithHttpInfo(UserFieldPermissionArg body) throws ApiException {
        okhttp3.Call call = userFieldPermissCall(body, null, null);
        Type localVarReturnType = new TypeToken<FieldPermissResponse>(){}.getType();
        return apiClient.execute(call, localVarReturnType);
    }

    /**
     * 用户字段权限查询 (asynchronously)
     * 用户字段权限查询
     * @param body  (optional)
     * @param callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     */
    public okhttp3.Call userFieldPermissAsync(UserFieldPermissionArg body, final ApiCallback<FieldPermissResponse> callback) throws ApiException {

        ProgressResponseBody.ProgressListener progressListener = null;
        ProgressRequestBody.ProgressRequestListener progressRequestListener = null;

        if (callback != null) {
            progressListener = new ProgressResponseBody.ProgressListener() {
                @Override
                public void update(long bytesRead, long contentLength, boolean done) {
                    callback.onDownloadProgress(bytesRead, contentLength, done);
                }
            };

            progressRequestListener = new ProgressRequestBody.ProgressRequestListener() {
                @Override
                public void onRequestProgress(long bytesWritten, long contentLength, boolean done) {
                    callback.onUploadProgress(bytesWritten, contentLength, done);
                }
            };
        }

        okhttp3.Call call = userFieldPermissCall(body, progressListener, progressRequestListener);
        Type localVarReturnType = new TypeToken<FieldPermissResponse>(){}.getType();
        apiClient.executeAsync(call, localVarReturnType, callback);
        return call;
    }
}
