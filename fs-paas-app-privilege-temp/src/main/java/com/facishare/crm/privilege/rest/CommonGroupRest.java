package com.facishare.crm.privilege.rest;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.facishare.crm.common.exception.CRMErrorCode;
import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.privilege.service.CommonGroupService;
import com.facishare.crm.privilege.swagger.ApiException;
import com.facishare.crm.privilege.util.CommonRestConst;
import com.facishare.crm.privilege.util.ValidateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.Produces;
import javax.ws.rs.core.Response;
import java.util.List;

/**
 * Created by luxin on 2017/10/10.
 */
@Controller
@Path("/commongroup")
public class CommonGroupRest {

  @Autowired
  private CommonGroupService commonGroupService;


  @POST
  @Path("/creategroup")
  @Produces({"application/json"})
  public Response createGroup(String body) {
    JSONObject jsonObject = JSON.parseObject(body);
    String tenantId = jsonObject.getString(CommonRestConst.TENANT_ID_KEY);
    String groupName = jsonObject.getString(CommonRestConst.GROUP_NAME_KEY);
    String description = jsonObject.getString(CommonRestConst.DESCRIPTION_KEY);

    boolean isValidParam = ValidateUtil.isValidParam(tenantId, groupName);
    if (isValidParam) {
      try {
        Object groupId = commonGroupService.createGroup(tenantId, groupName, description);
        return ValidateUtil.getSuccessResponse(groupId);

      } catch (ApiException e) {
        return ValidateUtil.getResponse(CRMErrorCode.PAAS_ERROR.getCode(), e.getMessage());
      } catch (CrmCheckedException e) {
        return ValidateUtil.getResponse(e.getIntErrorCode(), e.getMessage());
      }
    } else {
      return ValidateUtil.getResponse(CRMErrorCode.PARAMETER_IS_WRONG.getCode(), CRMErrorCode.PARAMETER_IS_WRONG.getMessage());
    }

  }


  @POST
  @Path("/updategroupmenbers")
  @Produces({"application/json"})
  public Response updateGroupMembers(String body) {
    JSONObject jsonObject = JSON.parseObject(body);
    String tenantId = jsonObject.getString(CommonRestConst.TENANT_ID_KEY);
    String groupId = jsonObject.getString(CommonRestConst.GROUP_ID);
    List<String> userIds = jsonObject.getObject(CommonRestConst.USER_IDS_KEY, List.class);

    boolean isValidParam = ValidateUtil.isValidParam(userIds, tenantId, groupId);
    if (isValidParam) {
      try {
        commonGroupService.updateGroupMembers(tenantId, groupId, userIds);
        return ValidateUtil.getSuccessResponse();

      } catch (ApiException e) {
        return ValidateUtil.getResponse(CRMErrorCode.PAAS_ERROR.getCode(), e.getMessage());
      } catch (CrmCheckedException e) {
        return ValidateUtil.getResponse(e.getIntErrorCode(), e.getMessage());
      }
    } else {
      return ValidateUtil.getResponse(CRMErrorCode.PARAMETER_IS_WRONG.getCode(), CRMErrorCode.PARAMETER_IS_WRONG.getMessage());
    }

  }


}
