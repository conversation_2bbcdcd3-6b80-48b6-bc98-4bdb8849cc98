package com.facishare.crm.privilege.model;

import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2016/12/9.
 */
@Data
public class JsonMessage {
    List<TextMsg> textMsg;
    Map snapshot = Maps.newHashMap();
    String SaleDynamicID = "";

    @Data
    public static class TextMsg {
        final Integer type = 0;
        String text;
        static final String dataID = "";
        final Integer objectType = 0;
    }

    public TextMsg generateTextMsg(){
        return new TextMsg();
    }

    @Override
    public String toString() {
        return "JsonMessage{" +
                "textMsg=" + textMsg +
                ", snapshot='{" + snapshot + '\'' +
                "}, SaleDynamicID='" + SaleDynamicID + '\'' +
                '}';
    }
}
