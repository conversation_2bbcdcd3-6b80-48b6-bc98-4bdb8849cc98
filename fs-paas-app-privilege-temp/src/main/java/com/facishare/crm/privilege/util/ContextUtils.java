package com.facishare.crm.privilege.util;

import com.facishare.crm.privilege.swagger.model.AuthContext;
import com.facishare.crm.valueobject.SessionContext;

import static com.facishare.crm.userdefobj.DefObjConstants.SUPER_PRIVILEGE_USER_ID;

/**
 * Created by ysb on 2017/5/6.
 */
public class ContextUtils {

    public static SessionContext generateSessionContext(Long eid, Integer userId) {
        SessionContext sessionContext = new SessionContext();
        sessionContext.setUserId(userId);
        sessionContext.setEId(eid);
        return sessionContext;
    }

    /**
     * 获得authContext by tenantId
     *
     * @param tenantId
     * @return
     */
    public static AuthContext getAdminContext(String tenantId) {
        AuthContext auth = new AuthContext();
        auth.setAppId(Constant.APP_ID);
        auth.setTenantId(tenantId);
        //内部接口调用,人员赋值-1000
        auth.setUserId(SUPER_PRIVILEGE_USER_ID);
        return auth;
    }


    public static AuthContext getAuthContext(SessionContext sessionContext) {
        AuthContext auth = new AuthContext();
        auth.setAppId(sessionContext.getAppId() == null ? "CRM" : sessionContext.getAppId());
        auth.setTenantId(sessionContext.getEId().toString());
        //内部接口调用,人员赋值-1000
        auth.setUserId(sessionContext.getUserIdString());
        auth.setOuterTenantId(sessionContext.getOuterTenantId());
        auth.setOuterUserId(sessionContext.getOutUserId());
        return auth;
    }


}
