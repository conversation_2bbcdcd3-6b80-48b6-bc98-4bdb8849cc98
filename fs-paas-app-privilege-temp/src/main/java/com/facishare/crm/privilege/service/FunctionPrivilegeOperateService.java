package com.facishare.crm.privilege.service;

import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.crm.privilege.exception.PaasPrivilegeException;
import com.facishare.crm.privilege.swagger.ApiException;

import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2017/5/25.
 */
@Deprecated
public interface FunctionPrivilegeOperateService {

  /**
   * 把自定义对象的全部权限给动态表单角色
   *
   * @param tenantId 企业id
   * @param apiNames 自定义对象的apiName
   */
  void addFunc2IntelliFormRole(String tenantId, List<String> apiNames) throws CrmCheckedException, ApiException;


  /**
   * 把自定义对象的全部权限自定义角色
   *
   * @param tenantId 企业id
   * @param apiNames 自定义对象的apiName
   */
  void addUserDefinedFunc2UserDefinedRole(String tenantId, String roleCode, List<String> apiNames) throws CrmCheckedException, ApiException;


  /**
   * 给某个角色添加自定义对象的功能权限
   *
   * @param tenantId
   * @param roleCode 角色code
   * @param apiName 对象名称
   * @param funcCodes 增加的功能权限列表
   */
  void addUserDefinedFuncAccess2Role(String tenantId, String roleCode, String apiName, List<String> funcCodes) throws CrmCheckedException, ApiException;


  /**
   * 预置对象增加权限到角色
   *
   * @param tenantId  企业id
   * @param funcCodes 预置对象功能权限码列表
   */
  void addDefaultFunc2Role(String tenantId, String roleCode, List<String> funcCodes) throws CrmCheckedException, ApiException;


  /**
   * 智能表单角色批量加人
   *
   * @param tenantId 企业id
   * @param userIds  员工的id
   */
  void addUser2IntelliFormRole(String tenantId, List<String> userIds) throws CrmCheckedException, ApiException;


  /**
   * 智能表单角色批量删除员工
   *
   * @param tenantId 企业id
   * @param userIds  员工的id
   */
  void deleteUserFromIntelliFormRole(String tenantId, List<String> userIds) throws CrmCheckedException, ApiException;


  /**
   * 自定义角色批量删除员工
   *
   * @param ea       企业ea
   * @param tenantId 企业id
   * @param userIds  员工的id
   * @param roleCode 角色的唯一标识
   */
  void deleteUserFromUserDefinedRole(String ea, String tenantId, String roleCode, List<String> userIds) throws CrmCheckedException, ApiException;


  /**
   * 自定义角色批量加人
   *
   * @param ea       企业ea
   * @param tenantId 企业id
   * @param userIds  员工的id
   * @param roleCode 角色唯一标识
   */
  void addUser2Role(String ea, String tenantId, String roleCode, List<String> userIds) throws CrmCheckedException, ApiException;


  /**
   * 创建自定义角色
   *
   * @param tenantId    企业id
   * @param roleName    自定义角色的名字
   * @param description 自定义对象的描述
   * @return 自定义角色的roleCode
   */
  String createUserDefinedRole(String tenantId, String roleName, String description) throws CrmCheckedException, ApiException, PaasPrivilegeException;

  /**
   * 增加默认角色
   */
  String addRole(String tenantId, String roleCode, String roleName, String description, Integer roleType) throws ApiException, CrmCheckedException;


  /**
   * 查看智能表单角色是否已经存在
   *
   * @return true存在, false不存在
   */
  boolean existIntelliFormRole(String tenantId) throws ApiException, CrmCheckedException;

  /**
   * 查看智能表单角色是否已经存在
   *
   * @return true存在, false不存在
   */
  boolean existRole(String tenantId, String roleCode) throws ApiException, CrmCheckedException;

  /**
   * 获取角色下的员工
   *
   * @param tenantId 企业的id
   * @param roleCode 角色唯一标识
   * @return 角色下的员工id列表
   */
  List<String> getUserIdsByRole(String tenantId, String roleCode) throws ApiException, CrmCheckedException;

  /**
   * 批量根据roleCode分别获取角色下的员工
   */
  Map<String, List<String>> batchGetUserIdsByRoleCodes(String tenantId, List<String> roleCode) throws ApiException, CrmCheckedException;

  /**
   * 批量获取员工具有的角色
   */
  Map<String, List<String>> batchGetUsersRoleInfo(String tenantId, List<String> userIds, Integer roleFilterFlag) throws CrmCheckedException, ApiException;

  /**
   * 更新某个对象下的字段权限,对老对象和新对象都适用
   *
   * @param tenantId
   * @param roleCode
   * @param objectApiName
   * @param fieldName2Status
   * @return
   */
  void updateRoleObjectFieldsPrivilege(String tenantId, String roleCode, String objectApiName, Map<String, Integer> fieldName2Status) throws CrmCheckedException, ApiException;

  /**
   * 获取某个角色下的某个对象的字段权限,对老对象和新对象都适用
   *
   * @param tenantId
   * @param roleCode
   * @param objectApiName
   * @return
   */
  Map<String, Integer> getRoleObjectFieldsPrivilege(String tenantId, String roleCode, String objectApiName) throws CrmCheckedException, ApiException;
}
