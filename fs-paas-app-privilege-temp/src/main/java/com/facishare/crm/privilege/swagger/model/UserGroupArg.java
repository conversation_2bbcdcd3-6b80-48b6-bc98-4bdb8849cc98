package com.facishare.crm.privilege.swagger.model;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;

@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2017-07-25T12:04:51.098+08:00")
public class UserGroupArg {
  @SerializedName("tenantId")
  private String tenantId = null;

  @SerializedName("appId")
  private String appId = null;

  @SerializedName("userId")
  private String userId = null;

  @SerializedName("id")
  private String id = null;

  @SerializedName("status")
  private Integer status = null;

  @SerializedName("searchKey")
  private String searchKey = null;

  @SerializedName("pageInfo")
  private PageInfo pageInfo = null;

  @SerializedName("orderBy")
  private OrderInfo orderBy = null;

  public void setTenantId(String tenantId) {
    this.tenantId = tenantId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public void setId(String id) {
    this.id = id;
  }

  public void setStatus(Integer status) {
    this.status = status;
  }

  public void setSearchKey(String searchKey) {
    this.searchKey = searchKey;
  }

  public void setPageInfo(PageInfo pageInfo) {
    this.pageInfo = pageInfo;
  }

  public void setOrderBy(OrderInfo orderBy) {
    this.orderBy = orderBy;
  }

  @ApiModelProperty(example = "null", value = "")
  public String getTenantId() {
    return tenantId;
  }

  @ApiModelProperty(example = "null", value = "")
  public String getAppId() {
    return appId;
  }

  @ApiModelProperty(example = "null", value = "")
  public String getUserId() {
    return userId;
  }

  @ApiModelProperty(example = "null", value = "")
  public String getId() {
    return id;
  }

  @ApiModelProperty(example = "null", value = "")
  public Integer getStatus() {
    return status;
  }

  @ApiModelProperty(example = "null", value = "")
  public String getSearchKey() {
    return searchKey;
  }

  @ApiModelProperty(example = "null", value = "")
  public PageInfo getPageInfo() {
    return pageInfo;
  }

  @ApiModelProperty(example = "null", value = "")
  public OrderInfo getOrderBy() {
    return orderBy;
  }
}
