package com.facishare.crm.privilege.swagger.model;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;

import java.util.Objects;

@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2017-07-25T12:04:51.098+08:00")
public class MultiArg {
  @SerializedName("tenantId")
  private String tenantId = null;

  @SerializedName("appId")
  private String appId = null;

  @SerializedName("userId")
  private String userId = null;

  @SerializedName("groupId")
  private String groupId = null;

  public MultiArg tenantId(String tenantId) {
    this.tenantId = tenantId;
    return this;
  }

  /**
   * Get tenantId
   * @return tenantId
   **/
  @ApiModelProperty(example = "null", value = "")
  public String getTenantId() {
    return tenantId;
  }

  public void setTenantId(String tenantId) {
    this.tenantId = tenantId;
  }

  public MultiArg appId(String appId) {
    this.appId = appId;
    return this;
  }

  /**
   * Get appId
   * @return appId
   **/
  @ApiModelProperty(example = "null", value = "")
  public String getAppId() {
    return appId;
  }

  public void setAppId(String appId) {
    this.appId = appId;
  }

  public MultiArg userId(String userId) {
    this.userId = userId;
    return this;
  }

  /**
   * Get userId
   * @return userId
   **/
  @ApiModelProperty(example = "null", value = "")
  public String getUserId() {
    return userId;
  }

  public void setUserId(String userId) {
    this.userId = userId;
  }

  public MultiArg groupId(String groupId) {
    this.groupId = groupId;
    return this;
  }

  /**
   * Get id
   * @return id
   **/
  @ApiModelProperty(example = "null", value = "")
  public String getGroupId() {
    return groupId;
  }

  public void setGroupId(String groupId) {
    this.groupId = groupId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    MultiArg multiArg = (MultiArg) o;
    return Objects.equals(this.tenantId, multiArg.tenantId) &&
      Objects.equals(this.appId, multiArg.appId) &&
      Objects.equals(this.userId, multiArg.userId) &&
      Objects.equals(this.groupId, multiArg.groupId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(tenantId, appId, userId, groupId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class IdArg {\n");

    sb.append("    tenantId: ").append(toIndentedString(tenantId)).append("\n");
    sb.append("    appId: ").append(toIndentedString(appId)).append("\n");
    sb.append("    userId: ").append(toIndentedString(userId)).append("\n");
    sb.append("    groupId: ").append(toIndentedString(groupId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
