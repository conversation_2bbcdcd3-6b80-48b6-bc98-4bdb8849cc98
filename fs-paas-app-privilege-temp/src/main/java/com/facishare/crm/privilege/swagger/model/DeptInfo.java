/**
 * Facishare PAAS Organization
 * This is PAAS organization service project
 *
 * OpenAPI spec version: v1
 * 
 *
 * NOTE: This class is auto generated by the swagger code generator program.
 * https://github.com/swagger-api/swagger-codegen.git
 * Do not edit the class manually.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */


package com.facishare.crm.privilege.swagger.model;

import java.util.Objects;
import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;


/**
 * DeptInfo
 */
@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2017-07-25T12:04:51.098+08:00")
public class DeptInfo   {
  @SerializedName("deptId")
  private String deptId = null;

  @SerializedName("deptName")
  private String deptName = null;

  @SerializedName("leaderUserId")
  private String leaderUserId = null;

  @SerializedName("leaderName")
  private String leaderName = null;

  @SerializedName("parentId")
  private String parentId = null;

  public DeptInfo deptId(String deptId) {
    this.deptId = deptId;
    return this;
  }

   /**
   * Get deptId
   * @return deptId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getDeptId() {
    return deptId;
  }

  public void setDeptId(String deptId) {
    this.deptId = deptId;
  }

  public DeptInfo deptName(String deptName) {
    this.deptName = deptName;
    return this;
  }

   /**
   * Get deptName
   * @return deptName
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getDeptName() {
    return deptName;
  }

  public void setDeptName(String deptName) {
    this.deptName = deptName;
  }

  public DeptInfo leaderUserId(String leaderUserId) {
    this.leaderUserId = leaderUserId;
    return this;
  }

   /**
   * Get leaderUserId
   * @return leaderUserId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getLeaderUserId() {
    return leaderUserId;
  }

  public void setLeaderUserId(String leaderUserId) {
    this.leaderUserId = leaderUserId;
  }

  public DeptInfo leaderName(String leaderName) {
    this.leaderName = leaderName;
    return this;
  }

   /**
   * Get leaderName
   * @return leaderName
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getLeaderName() {
    return leaderName;
  }

  public void setLeaderName(String leaderName) {
    this.leaderName = leaderName;
  }

  public DeptInfo parentId(String parentId) {
    this.parentId = parentId;
    return this;
  }

   /**
   * Get parentId
   * @return parentId
  **/
  @ApiModelProperty(example = "null", value = "")
  public String getParentId() {
    return parentId;
  }

  public void setParentId(String parentId) {
    this.parentId = parentId;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    DeptInfo deptInfo = (DeptInfo) o;
    return Objects.equals(this.deptId, deptInfo.deptId) &&
        Objects.equals(this.deptName, deptInfo.deptName) &&
        Objects.equals(this.leaderUserId, deptInfo.leaderUserId) &&
        Objects.equals(this.leaderName, deptInfo.leaderName) &&
        Objects.equals(this.parentId, deptInfo.parentId);
  }

  @Override
  public int hashCode() {
    return Objects.hash(deptId, deptName, leaderUserId, leaderName, parentId);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class DeptInfo {\n");
    
    sb.append("    deptId: ").append(toIndentedString(deptId)).append("\n");
    sb.append("    deptName: ").append(toIndentedString(deptName)).append("\n");
    sb.append("    leaderUserId: ").append(toIndentedString(leaderUserId)).append("\n");
    sb.append("    leaderName: ").append(toIndentedString(leaderName)).append("\n");
    sb.append("    parentId: ").append(toIndentedString(parentId)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}

