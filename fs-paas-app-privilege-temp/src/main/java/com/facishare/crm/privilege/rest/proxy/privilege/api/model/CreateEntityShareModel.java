package com.facishare.crm.privilege.rest.proxy.privilege.api.model;

import com.facishare.crm.privilege.rest.proxy.privilege.api.pojo.EntitySharePojo;
import com.facishare.crm.privilege.rest.proxy.privilege.model.BaseArgDataPrivilege;
import com.facishare.crm.privilege.rest.proxy.privilege.model.BaseResultDataPrivilege;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by yusb on 2017/4/24.
 */
public class CreateEntityShareModel {

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Arg extends BaseArgDataPrivilege {
    private List<EntitySharePojo> shareList;
  }

  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Result extends BaseResultDataPrivilege {
    private List<String> result;
  }
}
