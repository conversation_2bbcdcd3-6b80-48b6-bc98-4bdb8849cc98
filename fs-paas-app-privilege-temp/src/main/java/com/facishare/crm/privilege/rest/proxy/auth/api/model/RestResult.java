package com.facishare.crm.privilege.rest.proxy.auth.api.model;

import com.facishare.crm.privilege.rest.proxy.auth.api.pojo.RoleInfoPojo;
import com.facishare.paas.auth.model.params.PageInfo;
import lombok.Data;

import java.util.List;

/**
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 17/4/25.
 */
@Data
public class RestResult {
  private List<RoleInfoPojo> roles;
  private PageInfo pageInfo;
}
