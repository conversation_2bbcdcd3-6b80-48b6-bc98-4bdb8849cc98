package com.facishare.crm.privilege.swagger.model;

import com.google.gson.annotations.SerializedName;
import io.swagger.annotations.ApiModelProperty;

import java.util.Map;
import java.util.Objects;

@javax.annotation.Generated(value = "class io.swagger.codegen.languages.JavaClientCodegen", date = "2024-09-24T12:34:56.789+08:00")
public class GroupMultiResult {
  @SerializedName("errCode")
  private Integer errCode = null;

  @SerializedName("errKey")
  private String errKey = null;

  @SerializedName("errMessage")
  private String errMessage = null;

  @SerializedName("errDescription")
  private String errDescription = null;

  @SerializedName("result")
  private Map<String, String> result = null;

  @SerializedName("success")
  private Boolean success = false;

  public GroupMultiResult errCode(Integer errCode) {
    this.errCode = errCode;
    return this;
  }

  /**
   * Get errCode
   * @return errCode
   **/
  @ApiModelProperty(example = "null", value = "")
  public Integer getErrCode() {
    return errCode;
  }

  public void setErrCode(Integer errCode) {
    this.errCode = errCode;
  }

  public GroupMultiResult errKey(String errKey) {
    this.errKey = errKey;
    return this;
  }

  /**
   * Get errKey
   * @return errKey
   **/
  @ApiModelProperty(example = "null", value = "")
  public String getErrKey() {
    return errKey;
  }

  public void setErrKey(String errKey) {
    this.errKey = errKey;
  }

  public GroupMultiResult errMessage(String errMessage) {
    this.errMessage = errMessage;
    return this;
  }

  /**
   * Get errMessage
   * @return errMessage
   **/
  @ApiModelProperty(example = "null", value = "")
  public String getErrMessage() {
    return errMessage;
  }

  public void setErrMessage(String errMessage) {
    this.errMessage = errMessage;
  }

  public GroupMultiResult errDescription(String errDescription) {
    this.errDescription = errDescription;
    return this;
  }

  /**
   * Get errDescription
   * @return errDescription
   **/
  @ApiModelProperty(example = "null", value = "")
  public String getErrDescription() {
    return errDescription;
  }

  public void setErrDescription(String errDescription) {
    this.errDescription = errDescription;
  }

  public GroupMultiResult result(Map<String, String> result) {
    this.result = result;
    return this;
  }

  /**
   * Get result
   * @return result
   **/
  @ApiModelProperty(example = "null", value = "")
  public Map<String, String> getResult() {
    return result;
  }

  public void setResult(Map<String, String> result) {
    this.result = result;
  }

  public GroupMultiResult success(Boolean success) {
    this.success = success;
    return this;
  }

  /**
   * Get success
   * @return success
   **/
  @ApiModelProperty(example = "null", value = "")
  public Boolean getSuccess() {
    return success;
  }

  public void setSuccess(Boolean success) {
    this.success = success;
  }


  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }
    GroupMultiResult groupMultiResult = (GroupMultiResult) o;
    return Objects.equals(this.errCode, groupMultiResult.errCode) &&
      Objects.equals(this.errKey, groupMultiResult.errKey) &&
      Objects.equals(this.errMessage, groupMultiResult.errMessage) &&
      Objects.equals(this.errDescription, groupMultiResult.errDescription) &&
      Objects.equals(this.result, groupMultiResult.result) &&
      Objects.equals(this.success, groupMultiResult.success);
  }

  @Override
  public int hashCode() {
    return Objects.hash(errCode, errKey, errMessage, errDescription, result, success);
  }

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class GroupPageResult {\n");

    sb.append("    errCode: ").append(toIndentedString(errCode)).append("\n");
    sb.append("    errKey: ").append(toIndentedString(errKey)).append("\n");
    sb.append("    errMessage: ").append(toIndentedString(errMessage)).append("\n");
    sb.append("    errDescription: ").append(toIndentedString(errDescription)).append("\n");
    sb.append("    result: ").append(toIndentedString(result)).append("\n");
    sb.append("    success: ").append(toIndentedString(success)).append("\n");
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
}
