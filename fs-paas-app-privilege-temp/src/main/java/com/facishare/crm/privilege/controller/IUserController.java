package com.facishare.crm.privilege.controller;

import com.facishare.crm.privilege.controller.arg.*;
import com.facishare.fcp.annotation.FcpMethod;
import com.facishare.fcp.annotation.FcpService;
import com.facishare.fcp.model.BaseFcpServiceResult;

@FcpService("userApi")
public interface IUserController {

//角色分配
//9.部门列表
//10.查询部门里员工
//11.员工角色修改(增加 修改 删除)
//12.角色增加员工
//13.批量移出员工角色
//14.查询员工角色
//15.CRM员工列表
//16.非CRM员工列表
//17. CRM 员工数量  显示344/500

  /**
   * 员工增加角色
   *
   * @return
   */
  @FcpMethod("batchAddUsersToRoles")
  BaseFcpServiceResult batchAddUsersToRoles(BatchAddUsersToRolesArg arg);

  /**
   * 更新员工角色
   *
   * @return
   */
  @FcpMethod("updateUserRole")
  BaseFcpServiceResult updateUserRole(UpdateUserRoleArg arg);


  /**
   * crm员工列表  根据参数判断返回是CRM员工 或者是非CRM员工
   *
   * @return
   */
  @FcpMethod("crmUserList")
  BaseFcpServiceResult crmUserList(CrmUserListArg arg);

  /**
   * 根据据色查询用户
   *
   * @param arg
   * @return
   */
  @FcpMethod("roleUsers")
  BaseFcpServiceResult roleUsers(RoleUsersArg arg);

  /**
   * 复制一个员工角色到另一个员工
   *
   * @param arg
   * @return
   */
  @FcpMethod("copyUserRoleToUsers")
  BaseFcpServiceResult copyUserRoleToUsers(CopyUserRoleToUserArg arg);

  /**
   * 从crm员工中移除
   *
   * @param arg
   * @return
   */
  @FcpMethod("removeCrmUser")
  BaseFcpServiceResult removeCrmUser(RemoveCrmUserArg arg);

  /**
   * 批量更新员工的主角色
   *
   * @param arg
   * @return
   */
  @FcpMethod("updateUsersMajorRole")
  BaseFcpServiceResult updateUsersMajorRole(UpdateUsersMajorRole arg);

  /**
   * 分别设置多个员工的主角色
   *
   * @param arg
   * @return
   */
  @FcpMethod("updateUserMajorRoleRespectively")
  BaseFcpServiceResult updateUserMajorRoleRespectively(UpdateUserMajorRoleRespectivelyArg arg);


}
