<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans" xmlns:context="http://www.springframework.org/schema/context"
       xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
       http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/context http://www.springframework.org/schema/context/spring-context.xsd">
    <!-- 向Spring容器注册基本服务 -->
    <context:annotation-config/>
    <!-- 指定配置扫描包路径 -->
    <context:component-scan
            base-package="com.facishare.paas.auth"/>




    <bean id="openService" class="com.facishare.crm.privilege.service.OpenService"/>
    <bean id="syncCrmVisibleService" class="com.facishare.crm.privilege.service.SyncCrmVisibleService"/>
    <bean id="dynaactionformService" class="com.facishare.crm.privilege.service.impl.FunctionPrivilegeOperateServiceImpl"/>
    <bean id="PushSessionService" class="com.facishare.crm.privilege.service.PushSessionService"/>


</beans>