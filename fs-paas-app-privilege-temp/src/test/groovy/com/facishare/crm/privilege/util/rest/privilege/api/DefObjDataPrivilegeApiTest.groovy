package com.facishare.crm.privilege.util.rest.privilege.api

import com.facishare.crm.privilege.BasePrivilegeTest
import com.facishare.crm.privilege.rest.proxy.privilege.api.DefObjDataPrivilegeApi
import com.facishare.crm.privilege.rest.proxy.privilege.api.model.*
import com.facishare.crm.privilege.rest.proxy.privilege.api.pojo.EntityOpennessPojo
import com.facishare.crm.privilege.rest.proxy.privilege.model.BasePageInfoDataPrivilege
import com.google.common.collect.Lists

import javax.annotation.Resource
/**
 * Created by yusb on 2017/5/2.
 */
class DefObjDataPrivilegeApiTest extends BasePrivilegeTest {

  @Resource
  DefObjDataPrivilegeApi defObjDataPrivilegeApi

  def "数据权限的CRUD测试通过 Create And Get And Update And Del "() {
    given:
    def createArg = new CreateBaseDataPrivilegeRulesModel.Arg()
    def delArg = new DelBaseDataPrivilegeRulesModel.Arg()
    def updateArg = new UpdateBaseDataPrivilegeRulesModel.Arg()
    def getArg = new GetBaseDataPrivilegeRulesModel.Arg()
    def createResult = new CreateBaseDataPrivilegeRulesModel.Result()
    def delResult = new DelBaseDataPrivilegeRulesModel.Result()
    def updateResult = new UpdateBaseDataPrivilegeRulesModel.Result()
    def getResult = new GetBaseDataPrivilegeRulesModel.Result()
    def getResultPojo = new EntityOpennessPojo()
    def pageInfo = new BasePageInfoDataPrivilege()
    pageInfo.setCurrentPage(1)
    pageInfo.setPageSize(10)

    //Create
    createArg.setAuthContext(ctx)
    createArg.setEntityOpenness(Lists.newArrayList(spockEntityOpennessPojo))
    when:
    createResult = defObjDataPrivilegeApi.createBaseDataPrivilegeRules(createArg)
    then:
    createResult.errCode == 0

    //Update
    updateArg.setAuthContext(ctx)
    spockEntityOpennessPojo.setScope(EntityOpennessPojo.SCOPE_PRIVATE)
    spockEntityOpennessPojo.setPermission(EntityOpennessPojo.PERMISSION_READONLY)
    updateArg.setEntityOpenness(Lists.newArrayList(spockEntityOpennessPojo))
    when:
    updateResult = defObjDataPrivilegeApi.updateBaseDataPrivilegeRules(updateArg)
    then:
    updateResult.errCode == 0

    //Get
    getArg.setAuthContext(ctx)
    getArg.setEntitys(Lists.newArrayList(SPOCK_DESCRIBE_API_NAME))
    getArg.setPermission(EntityOpennessPojo.PERMISSION_READONLY)
    getArg.setScope(EntityOpennessPojo.SCOPE_PRIVATE)
    getArg.setPage(pageInfo)
    when:
    getResult = defObjDataPrivilegeApi.getBaseDataPrivilegeRules(getArg)
    getResultPojo = getResult.result.content.get(0)
    then:
    getResult.result.page.total == 1
    then:
    getResultPojo.scope == EntityOpennessPojo.SCOPE_PRIVATE
    getResultPojo.permission == EntityOpennessPojo.PERMISSION_READONLY

    //Del
    delArg.setAuthContext(ctx)
    delArg.setEntitys(Lists.newArrayList(getResultPojo.entityId))
    when:
    delResult = defObjDataPrivilegeApi.delBaseDataPrivilegeRules(delArg)
    then:
    delResult.errCode == 0

    //Get
    when:
    getResult = defObjDataPrivilegeApi.getBaseDataPrivilegeRules(getArg)
    then:
    getResult.result.page.total == 0
  }

  def "临时删除数据"(){
    def delArg = new DelBaseDataPrivilegeRulesModel.Arg()
    def delResult = new DelBaseDataPrivilegeRulesModel.Result()
    //Del
    delArg.setAuthContext(ctx)
    delArg.setEntitys(Lists.newArrayList("object_spock_test__c","object_spock_test2__c","object_spock_test1__c"))
    when:
    delResult = defObjDataPrivilegeApi.delBaseDataPrivilegeRules(delArg)
    then:
    delResult.errCode == 0
  }


}
