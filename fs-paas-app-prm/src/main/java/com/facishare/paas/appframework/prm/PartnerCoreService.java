package com.facishare.paas.appframework.prm;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;

/**
 * 合作伙伴接口类
 *
 * <AUTHOR>
 */
public interface PartnerCoreService {
    /**
     * 获取合作伙伴id
     *
     * @param user 用户
     * @return 合作伙伴id
     */
    String getPartnerId(User user);

    /**
     * 获取合作伙伴id
     *
     * 上面的 PartnerCoreService#getPartnerId 通过调rest接口 com.fxiaoke.enterpriserelation2.service.AuthService#getExternalOuterUserInfo来查询，比较耗时，
     *
     * 这里直接查 EnterpriseRelationObj对象（biz_enterprise_relation）
     */
    String queryPartnerId(User user);

    /**
     * 外不负责人是不是该合作伙伴的对应企业的人员
     *
     * @param objectData 数据
     * @param tenantId   企业id
     * @return
     */
    boolean isPartnerOutOwner(IObjectData originalObjectData, String tenantId);

    /**
     * 判断 partner_id 字段下 relation_outer_data_privilege 属性是否为 outer_owner
     * 是则说明关联了外部属性，否则没关联
     * 该属性会决定了相关逻辑的走向
     *
     * @param objectDescribe 对象描述
     * @return
     */
    boolean isRelationPartnerAttribute(IObjectDescribe objectDescribe);

    /**
     * 判断是否是代理通人员
     * @param user 用户
     * @param outTenantId 外部企业id
     * @param outUserId 外部人员id
     * @return 判断结果
     */
    boolean isPrmEmployee(User user, String outTenantId, String outUserId);

    /**
     * 根据参数获取是 Prm 的外部企业
     * @param user 用户
     * @param outTenantList 所有需要判断的外部企业
     * @return prm 企业 集合
     */
    List<String> getPrmOutTenants(User user, List<Long> outTenantList);
}
