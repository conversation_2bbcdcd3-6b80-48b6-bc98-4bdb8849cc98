package com.facishare.paas.appframework.metadata

import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.ObjectReferenceManyFieldDescribe
import com.facishare.paas.metadata.api.search.Wheres
import com.facishare.paas.metadata.impl.describe.ObjectReferenceFieldDescribe
import spock.lang.Specification
import spock.lang.Unroll

class ObjectReferenceWrapperTest extends Specification {

    def "test of factory method with IObjectReferenceMany"() {
        given: "一个IObjectReferenceMany对象"
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe(["api_name": "test_field", "lable": "Test Field"])

        when: "使用of方法创建ObjectReferenceWrapper"
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        then: "应该创建ObjectReferenceMany实例"
        wrapper != null
        wrapper.getClass().getSimpleName() == "ObjectReferenceMany"
        wrapper.getType() == IFieldType.OBJECT_REFERENCE_MANY
        wrapper.getIFieldDescribe() == objectReferenceMany
    }

    def "test of factory method with IObjectReferenceField"() {
        given: "一个IObjectReferenceField对象"
        def objectReferenceField = new ObjectReferenceFieldDescribe(["api_name": "test_field", "label": "Test Field"])

        when: "使用of方法创建ObjectReferenceWrapper"
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        then: "应该创建ObjectReference实例"
        wrapper != null
        wrapper.getClass().getSimpleName() == "ObjectReference"
        wrapper.getType() == IFieldType.OBJECT_REFERENCE
        wrapper.getIFieldDescribe() == objectReferenceField
    }

    def "test of factory method with unsupported type"() {
        given: "一个不支持的IFieldDescribe对象"
        def fieldDescribe = Mock(IFieldDescribe) {
            getApiName() >> "test_field"
        }

        when: "使用of方法创建ObjectReferenceWrapper"
        def wrapper = ObjectReferenceWrapper.of(fieldDescribe)

        then: "应该返回null"
        wrapper == null
    }

    def "test ObjectReference getLookupRoles"() {
        given: "一个ObjectReference对象"
        def roles = ["role1", "role2"]
        def objectReferenceField = new ObjectReferenceFieldDescribe(["lookup_roles": roles])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "获取查找角色"
        def result = wrapper.getLookupRoles()

        then: "应该返回正确的角色列表"
        result == roles
    }

    def "test ObjectReference setLookupRoles"() {
        given: "一个ObjectReference对象"
        def roles = ["role1", "role2"]
        def objectReferenceField = new ObjectReferenceFieldDescribe(["lookup_roles": null])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "设置查找角色"
        wrapper.setLookupRoles(roles)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceField.setLookupRoles(roles)
    }

    def "test ObjectReference getWheres"() {
        given: "一个ObjectReference对象"
        def wheres = [["field": "value"]]
        def objectReferenceField = new ObjectReferenceFieldDescribe(["wheres": wheres])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "获取条件"
        def result = wrapper.getWheres()

        then: "应该返回正确的条件列表"
        result == wheres
    }

    def "test ObjectReference setWheres"() {
        given: "一个ObjectReference对象"
        def wheres = [["field": "value"]]
        def objectReferenceField = new ObjectReferenceFieldDescribe(["wheres": null])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "设置条件"
        wrapper.setWheres(wheres)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceField.setWheres(wheres)
    }

    def "test ObjectReference getRelatedWheres"() {
        given: "一个ObjectReference对象"
        def relationWheres = [["related_field": "value"]]
        def objectReferenceField = new ObjectReferenceFieldDescribe(["related_wheres": relationWheres])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        GroovyMock(ObjectDescribeExt, global: true)
        ObjectDescribeExt.getWheresBy(relationWheres) >> [Mock(Wheres)]

        when: "获取相关条件"
        def result = wrapper.getRelatedWheres()

        then: "应该返回Wheres列表"
        result.size() == 1
        result[0] instanceof Wheres
    }

    def "test ObjectReference setRelatedWheres"() {
        given: "一个ObjectReference对象"
        def relatedWheres = [["related_field": "value"]]
        def objectReferenceField = new ObjectReferenceFieldDescribe(["related_wheres": null])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "设置相关条件"
        wrapper.setRelatedWheres(relatedWheres)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceField.setRelationWheres(relatedWheres)
    }

    def "test ObjectReference getTargetApiName"() {
        given: "一个ObjectReference对象"
        def targetApiName = "target_object"
        def objectReferenceField = new ObjectReferenceFieldDescribe(["target_api_name": targetApiName])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "获取目标API名称"
        def result = wrapper.getTargetApiName()

        then: "应该返回正确的目标API名称"
        result == targetApiName
    }

    def "test ObjectReference getTargetRelatedListName"() {
        given: "一个ObjectReference对象"
        def listName = "related_list"
        def objectReferenceField = new ObjectReferenceFieldDescribe(["target_related_list_name": listName])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "获取目标相关列表名称"
        def result = wrapper.getTargetRelatedListName()

        then: "应该返回正确的列表名称"
        result == listName
    }

    def "test ObjectReference getTargetRelatedListLabel"() {
        given: "一个ObjectReference对象"
        def listLabel = "Related List"
        def objectReferenceField = new ObjectReferenceFieldDescribe(["target_related_list_name": listLabel])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "获取目标相关列表标签"
        def result = wrapper.getTargetRelatedListLabel()

        then: "应该返回正确的列表标签"
        result == listLabel
    }

    def "test ObjectReference getWhereType"() {
        given: "一个ObjectReference对象"
        def whereType = "CUSTOM"
        def objectReferenceField = new ObjectReferenceFieldDescribe(["where_type": whereType])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "获取条件类型"
        def result = wrapper.getWhereType()

        then: "应该返回正确的条件类型"
        result == whereType
    }

    def "test ObjectReference setWhereType"() {
        given: "一个ObjectReference对象"
        def whereType = "CUSTOM"
        def objectReferenceField = new ObjectReferenceFieldDescribe(["where_type": null])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "设置条件类型"
        wrapper.setWhereType(whereType)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceField.setWhereType(whereType)
    }

    def "test ObjectReference getRelatedWhereType"() {
        given: "一个ObjectReference对象"
        def relatedWhereType = "FUNCTION"
        def objectReferenceField = new ObjectReferenceFieldDescribe(["related_where_type": relatedWhereType])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "获取相关条件类型"
        def result = wrapper.getRelatedWhereType()

        then: "应该返回正确的相关条件类型"
        result == relatedWhereType
    }

    def "test ObjectReference setRelatedWhereType"() {
        given: "一个ObjectReference对象"
        def relatedWhereType = "FUNCTION"
        def objectReferenceField = new ObjectReferenceFieldDescribe(["related_where_type": null])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "设置相关条件类型"
        wrapper.setRelatedWhereType(relatedWhereType)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceField.setRelationWheresType(relatedWhereType)
    }

    def "test ObjectReference setTargetRelatedListName"() {
        given: "一个ObjectReference对象"
        def listName = "new_list"
        def objectReferenceField = new ObjectReferenceFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "设置目标相关列表名称"
        wrapper.setTargetRelatedListName(listName)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceField.setTargetRelatedListName(listName)
    }

    def "test ObjectReference setTargetRelatedListLabel"() {
        given: "一个ObjectReference对象"
        def listLabel = "New List"
        def objectReferenceField = new ObjectReferenceFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "设置目标相关列表标签"
        wrapper.setTargetRelatedListLabel(listLabel)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceField.setTargetRelatedListLabel(listLabel)
    }

    def "test ObjectReference setRelationOuterDataPrivilege"() {
        given: "一个ObjectReference对象"
        def privilege = "READ"
        def objectReferenceField = new ObjectReferenceFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "设置关系外部数据权限"
        wrapper.setRelationOuterDataPrivilege(privilege)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceField.setRelationOuterDataPrivilege(privilege)
    }

    def "test ObjectReference getRelationOuterDataPrivilege"() {
        given: "一个ObjectReference对象"
        def privilege = "READ"
        def objectReferenceField = new ObjectReferenceFieldDescribe(["relation_outer_data_privilege": privilege])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "获取关系外部数据权限"
        def result = wrapper.getRelationOuterDataPrivilege()

        then: "应该返回正确的权限"
        result == privilege
    }

    // ObjectReferenceMany 测试
    def "test ObjectReferenceMany getLookupRoles"() {
        given: "一个ObjectReferenceMany对象"
        def roles = ["role1", "role2"]
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe(["lookup_roles": roles])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "获取查找角色"
        def result = wrapper.getLookupRoles()

        then: "应该返回正确的角色列表"
        result == roles
    }

    def "test ObjectReferenceMany setLookupRoles"() {
        given: "一个ObjectReferenceMany对象"
        def roles = ["role1", "role2"]
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "设置查找角色"
        wrapper.setLookupRoles(roles)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceMany.setLookupRoles(roles)
    }

    def "test ObjectReferenceMany getWheres"() {
        given: "一个ObjectReferenceMany对象"
        def wheres = [["field": "value"]]
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe(["wheres": wheres])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "获取条件"
        def result = wrapper.getWheres()

        then: "应该返回正确的条件列表"
        result == wheres
    }

    def "test ObjectReferenceMany setWheres"() {
        given: "一个ObjectReferenceMany对象"
        def wheres = [["field": "value"]]
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "设置条件"
        wrapper.setWheres(wheres)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceMany.setWheres(wheres)
    }

    def "test ObjectReferenceMany getRelatedWheres"() {
        given: "一个ObjectReferenceMany对象"
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "获取相关条件"
        def result = wrapper.getRelatedWheres()

        then: "应该返回空列表"
        result.isEmpty()
    }

    def "test ObjectReferenceMany setRelatedWheres"() {
        given: "一个ObjectReferenceMany对象"
        def relatedWheres = [["related_field": "value"]]
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "设置相关条件"
        wrapper.setRelatedWheres(relatedWheres)

        then: "应该正常完成，不做任何操作"
        noExceptionThrown()
    }

    def "test ObjectReferenceMany getTargetApiName"() {
        given: "一个ObjectReferenceMany对象"
        def targetApiName = "target_object"
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe(["target_api_name": targetApiName])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "获取目标API名称"
        def result = wrapper.getTargetApiName()

        then: "应该返回正确的目标API名称"
        result == targetApiName
    }

    def "test ObjectReferenceMany getTargetRelatedListName"() {
        given: "一个ObjectReferenceMany对象"
        def listName = "related_list"
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe(["target_related_list_name": listName])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "获取目标相关列表名称"
        def result = wrapper.getTargetRelatedListName()

        then: "应该返回正确的列表名称"
        result == listName
    }

    def "test ObjectReferenceMany setTargetRelatedListName"() {
        given: "一个ObjectReferenceMany对象"
        def listName = "new_list"
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "设置目标相关列表名称"
        wrapper.setTargetRelatedListName(listName)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceMany.setTargetRelatedListName(listName)
    }

    def "test ObjectReferenceMany setTargetRelatedListLabel"() {
        given: "一个ObjectReferenceMany对象"
        def listLabel = "New List"
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "设置目标相关列表标签"
        wrapper.setTargetRelatedListLabel(listLabel)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceMany.setTargetRelatedListLabel(listLabel)
    }

    def "test ObjectReferenceMany setRelationOuterDataPrivilege"() {
        given: "一个ObjectReferenceMany对象"
        def privilege = "READ"
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "设置关系外部数据权限"
        wrapper.setRelationOuterDataPrivilege(privilege)

        then: "应该正常完成，不做任何操作"
        noExceptionThrown()
    }

    def "test ObjectReferenceMany getRelationOuterDataPrivilege"() {
        given: "一个ObjectReferenceMany对象"
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "获取关系外部数据权限"
        def result = wrapper.getRelationOuterDataPrivilege()

        then: "应该返回null"
        result == null
    }

    def "test ObjectReferenceMany getTargetRelatedListLabel"() {
        given: "一个ObjectReferenceMany对象"
        def listLabel = "Related List"
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe(["target_related_list_label": listLabel])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "获取目标相关列表标签"
        def result = wrapper.getTargetRelatedListLabel()

        then: "应该返回正确的列表标签"
        result == listLabel
    }

    def "test ObjectReferenceMany getWhereType"() {
        given: "一个ObjectReferenceMany对象"
        def whereType = "CUSTOM"
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe(["where_type": whereType])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "获取条件类型"
        def result = wrapper.getWhereType()

        then: "应该返回正确的条件类型"
        result == whereType
    }

    def "test ObjectReferenceMany setWhereType"() {
        given: "一个ObjectReferenceMany对象"
        def whereType = "CUSTOM"
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "设置条件类型"
        wrapper.setWhereType(whereType)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceMany.setWhereType(whereType)
    }

    def "test ObjectReferenceMany getRelatedWhereType"() {
        given: "一个ObjectReferenceMany对象"
        def relatedWhereType = "FUNCTION"
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe(["related_where_type": relatedWhereType])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "获取相关条件类型"
        def result = wrapper.getRelatedWhereType()

        then: "应该返回正确的相关条件类型"
        result == relatedWhereType
    }

    def "test ObjectReferenceMany setRelatedWhereType"() {
        given: "一个ObjectReferenceMany对象"
        def relatedWhereType = "FUNCTION"
        def objectReferenceMany = new ObjectReferenceManyFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceMany)

        when: "设置相关条件类型"
        wrapper.setRelatedWhereType(relatedWhereType)

        then: "应该调用底层对象的设置方法"
        1 * objectReferenceMany.set("related_where_type", relatedWhereType)
    }

    def "test getWheresBy"() {
        given: "一个ObjectReferenceWrapper"
        def wheres = [["field": "value"]]
        def objectReferenceField = new ObjectReferenceFieldDescribe(["wheres": wheres])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        def expectedWheres = [Mock(Wheres)]
        GroovyMock(ObjectDescribeExt, global: true)
        ObjectDescribeExt.getWheresBy(wheres) >> expectedWheres

        when: "获取Wheres对象"
        def result = wrapper.getWheresBy()

        then: "应该返回Wheres列表"
        result == expectedWheres
    }

    @Unroll
    def "test isFunctionWhereType - whereType: #whereType, relatedWhereType: #relatedWhereType, expected: #expected"() {
        given: "一个ObjectReferenceWrapper"
        def objectReferenceField = new ObjectReferenceFieldDescribe(["where_type": whereType, "related_where_type": relatedWhereType])
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        expect: "应该正确判断是否为函数条件类型"
        wrapper.isFunctionWhereType() == expected

        where:
        whereType                     | relatedWhereType              | expected
        FilterExt.FUNCTION_WHERE_TYPE | null                          | true
        null                          | FilterExt.FUNCTION_WHERE_TYPE | true
        FilterExt.FUNCTION_WHERE_TYPE | FilterExt.FUNCTION_WHERE_TYPE | true
        "OTHER"                       | "OTHER"                       | false
        null                          | null                          | false
    }

    def "test constructor with FieldDescribeExt"() {
        given: "一个IFieldDescribe对象"
        def fieldDescribe = new ObjectReferenceFieldDescribe(["api_name": "test_field", "label": "Test Field"])

        def fieldMap = ["api_name": "test_field", "label": "Test Field"]
        GroovyMock(FieldDescribeExt, global: true)
        FieldDescribeExt.of(fieldDescribe) >> Mock(FieldDescribeExt) {
            toMap() >> fieldMap
        }

        when: "创建ObjectReferenceWrapper"
        def wrapper = ObjectReferenceWrapper.of(fieldDescribe)

        then: "应该正确初始化"
        wrapper != null
        wrapper.getIFieldDescribe() == fieldDescribe
    }

    def "test edge cases with null values"() {
        given: "包含null值的ObjectReference"
        def objectReferenceField = new ObjectReferenceFieldDescribe()
        def wrapper = ObjectReferenceWrapper.of(objectReferenceField)

        when: "调用各种方法"
        def whereType = wrapper.getWhereType()
        def relatedWhereType = wrapper.getRelatedWhereType()
        def targetApiName = wrapper.getTargetApiName()
        def lookupRoles = wrapper.getLookupRoles()
        def isFunctionType = wrapper.isFunctionWhereType()

        then: "应该正确处理null值"
        whereType == null
        relatedWhereType == null
        targetApiName == null
        lookupRoles == null
        isFunctionType == false
    }
} 