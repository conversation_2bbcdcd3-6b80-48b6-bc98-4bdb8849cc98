package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSON
import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.core.exception.ValidateException
import com.facishare.paas.appframework.metadata.dto.LocationInfo
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.describe.IFieldDescribe
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.*
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.apache.commons.lang3.StringUtils
import org.powermock.reflect.Whitebox
import spock.lang.Shared
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

class ObjectDataExtTest extends Specification {
    @Shared
    String whatListObjDescribeJson1 = '''{"tenant_id":"74255","package":"CRM","is_active":true,"last_modified_time":1653879332284,"create_time":1634038081159,"description":"whatlist字段对象","last_modified_by":"1000","display_name":"zl-whatlist","created_by":"1000","version":196,"is_open_display_name":true,"index_version":200,"icon_index":16,"is_deleted":false,"api_name":"object_zl_whatlist__c","icon_path":"","is_udef":true,"define_type":"custom","short_name":"hcs","_id":"6165714174fdaf000176168e","fields":{"tenant_id":{"describe_api_name":"object_zl_whatlist__c","is_index":false,"is_active":true,"create_time":1634038081159,"pattern":"","description":"tenant_id","is_unique":false,"label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","status":"released","max_length":200},"related_api_names":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":*************,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"关联业务对象","default_value":[],"label":"关联业务对象","type":"select_many","field_num":10,"is_required":false,"api_name":"related_api_names","is_dynamic":true,"options":[{"label":"客户地址","value":"AccountAddrObj"},{"label":"客户","value":"AccountObj"},{"label":"联系人","value":"ContactObj"},{"label":"部门","value":"DepartmentObj"},{"label":"销售线索","value":"LeadsObj"},{"label":"市场活动1","value":"MarketingEventObj"},{"label":"商机2.0明细","value":"NewOpportunityLinesObj"},{"label":"商机2.0","value":"NewOpportunityObj"},{"label":"xkk-detail1","value":"object_4s712__c"},{"label":"750乡镇映射","value":"object_AP2Z9__c"},{"label":"sk2测试","value":"object_fcB1t__c"},{"label":"zz测试从","value":"object_fs58r__c"},{"label":"wj-测试主属性自定义","value":"object_Ju6l7__c"},{"label":"显示字段","value":"object_OQgGQ__c"},{"label":"fj-UI事件主","value":"object_qep6N__c"},{"label":"E对象","value":"object_UXpl0__c"},{"label":"wj-从人员部门","value":"object_ZkmdG__c"},{"label":"订单产品","value":"SalesOrderProductObj"},{"label":"规格","value":"SpecificationObj"}],"define_type":"package","_id":"616572dd983987000180e4f4","is_index_field":false,"is_single":false,"config":{},"index_name":"a_2","status":"released","help_text":""},"lock_rule":{"describe_api_name":"object_zl_whatlist__c","is_index":false,"is_active":true,"create_time":1634038081055,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定规则","default_value":"default_lock_rule","rules":[],"label":"锁定规则","type":"lock_rule","field_num":1,"is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"6165714174fdaf0001761686","is_index_field":false,"label_r":"锁定规则","is_single":false,"index_name":"s_3","status":"new","help_text":""},"data_own_organization":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":1647329914394,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"归属组织","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"6230427a04a720000162e6fd","is_index_field":false,"label_r":"归属组织","is_single":true,"index_name":"a_3","status":"released","help_text":""},"field_Tj8f2__c":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":1652084808856,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"收款状态","type":"select_one","field_num":18,"used_in":"component","is_required":false,"api_name":"field_Tj8f2__c","options":[{"label":"未收款","value":"incomplete"},{"label":"已收款","value":"complete"}],"define_type":"custom","_id":"6278d0498a43130001ddad76","is_index_field":false,"is_single":false,"config":{},"index_name":"s_11","status":"new","help_text":""},"field_xVkl9__c":{"describe_api_name":"object_zl_whatlist__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"date_time","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"l_1","is_index":true,"is_active":true,"create_time":1652084808857,"is_encrypted":false,"default_value":"","label":"收款时间","time_zone":"GMT+8","field_num":19,"api_name":"field_xVkl9__c","date_format":"yyyy-MM-dd HH:mm:ss","_id":"6278d0498a43130001ddad77","is_index_field":false,"status":"new","help_text":""},"field_6ubp8__c":{"describe_api_name":"object_zl_whatlist__c","default_is_expression":false,"auto_adapt_places":false,"remove_mask_roles":{},"is_unique":false,"description":"","type":"currency","decimal_places":2,"default_to_zero":true,"used_in":"component","is_required":false,"define_type":"custom","is_single":false,"index_name":"d_4","max_length":14,"is_index":true,"is_active":true,"create_time":1652084808854,"is_encrypted":false,"length":12,"default_value":"","label":"收款金额","currency_unit":"￥","field_num":16,"api_name":"field_6ubp8__c","_id":"6278d0498a43130001ddad74","is_index_field":false,"is_show_mask":false,"round_mode":4,"status":"new","help_text":""},"lock_user":{"describe_api_name":"object_zl_whatlist__c","is_index":false,"is_active":true,"create_time":1634038081076,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"加锁人","where_type":"field","label":"加锁人","type":"employee","field_num":4,"is_need_convert":false,"wheres":[],"is_required":false,"api_name":"lock_user","define_type":"package","_id":"6165714174fdaf0001761688","is_index_field":false,"label_r":"加锁人","is_single":true,"index_name":"a_1","status":"new","help_text":""},"mc_exchange_rate":{"describe_api_name":"object_zl_whatlist__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":6,"default_to_zero":true,"is_required":false,"define_type":"package","label_r":"汇率","is_single":false,"index_name":"d_1","max_length":16,"is_index":true,"is_active":true,"create_time":1634038081151,"is_encrypted":false,"step_value":1,"display_style":"input","length":10,"default_value":"","label":"汇率","field_num":6,"api_name":"mc_exchange_rate","_id":"6165714174fdaf000176168b","is_index_field":false,"round_mode":4,"status":"new","help_text":""},"field_i135X__c":{"describe_api_name":"object_zl_whatlist__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"used_in":"component","is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"t_4","max_length":100,"is_index":true,"is_active":true,"create_time":1652084808855,"is_encrypted":false,"default_value":"","label":"收款方式","field_num":17,"api_name":"field_i135X__c","_id":"6278d0498a43130001ddad75","is_index_field":false,"status":"new","help_text":""},"is_deleted":{"describe_api_name":"object_zl_whatlist__c","is_index":false,"create_time":1634038081159,"description":"is_deleted","is_unique":false,"default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"life_status_before_invalid":{"describe_api_name":"object_zl_whatlist__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"作废前生命状态","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"作废前生命状态","is_single":false,"index_name":"t_2","max_length":256,"is_index":false,"is_active":true,"create_time":1634038081076,"is_encrypted":false,"default_value":"","label":"作废前生命状态","field_num":7,"is_need_convert":false,"api_name":"life_status_before_invalid","_id":"6165714174fdaf0001761689","is_index_field":false,"status":"new","help_text":""},"object_describe_api_name":{"describe_api_name":"object_zl_whatlist__c","is_index":false,"is_active":true,"create_time":1634038081159,"pattern":"","description":"object_describe_api_name","is_unique":false,"label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","status":"released","max_length":200},"out_owner":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":1634038081159,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"config":{"display":1},"index_name":"o_owner","status":"released","description":""},"owner_department":{"describe_api_name":"object_zl_whatlist__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"负责人主属部门","is_single":true,"index_name":"owner_dept","max_length":100,"is_index":true,"is_active":true,"create_time":1634038081141,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"6165714174fdaf000176167d","is_index_field":false,"status":"new","help_text":""},"field_0P1HF__c":{"describe_api_name":"object_zl_whatlist__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_10","is_index":true,"is_active":true,"create_time":1648279569561,"is_encrypted":false,"target_api_name":"MarketingEventObj","label":"查找关联市场活动","target_related_list_name":"target_related_list_6yzFG__c","field_num":15,"target_related_list_label":"zl-whatlist","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_0P1HF__c","_id":"623ec01329a6b000011494cc","is_index_field":true,"status":"new","help_text":""},"mc_functional_currency":{"describe_api_name":"object_zl_whatlist__c","is_index":false,"is_active":true,"create_time":1634038081152,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"本位币","type":"select_one","field_num":9,"is_required":false,"api_name":"mc_functional_currency","options":[{"font_color":"#181c25","fe_key":"CNY_a1miwf8Ft","fe_saved":1,"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"6165714174fdaf000176168c","is_index_field":false,"label_r":"本位币","is_single":false,"config":{},"index_name":"s_7","status":"new","help_text":""},"field_W300j__c":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":1647256087078,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"单选","type":"select_one","field_num":14,"is_required":false,"api_name":"field_W300j__c","options":[{"label":"A","value":"35jdnAe2L"},{"label":"B","value":"gr9i3ySs4"},{"label":"C","value":"QikkO24xt"},{"not_usable":true,"label":"其他","value":"other"}],"define_type":"custom","_id":"622f2217bebc850001f37de8","is_index_field":false,"is_single":false,"config":{},"index_name":"s_9","status":"new","help_text":""},"owner":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":1634038081140,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"负责人","type":"employee","is_need_convert":false,"wheres":[],"is_required":true,"api_name":"owner","define_type":"package","_id":"6165714174fdaf000176167c","is_index_field":false,"label_r":"负责人","is_single":true,"index_name":"owner","status":"new","help_text":""},"last_modified_time":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"create_time":1634038081159,"description":"last_modified_time","is_unique":false,"label":"最后修改时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"package":{"describe_api_name":"object_zl_whatlist__c","is_index":false,"is_active":true,"create_time":1634038081159,"pattern":"","description":"package","is_unique":false,"label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","status":"released","max_length":200},"lock_status":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":1634038081076,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"锁定状态","default_value":"0","label":"锁定状态","type":"select_one","field_num":2,"is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","_id":"6165714174fdaf0001761687","is_index_field":false,"label_r":"锁定状态","is_single":false,"config":{},"index_name":"s_4","status":"new","help_text":""},"create_time":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"create_time":1634038081159,"description":"create_time","is_unique":false,"label":"创建时间","time_zone":"","type":"date_time","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":1634038081148,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"normal","label":"生命状态","type":"select_one","field_num":3,"is_need_convert":false,"is_required":false,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective"},{"label":"审核中","value":"under_review"},{"label":"正常","value":"normal"},{"label":"变更中","value":"in_change"},{"label":"作废","value":"invalid"}],"define_type":"package","_id":"6165714174fdaf0001761684","is_index_field":false,"label_r":"生命状态","is_single":false,"config":{},"index_name":"s_5","status":"new","help_text":""}'''
    @Shared
    String whatListObjDescribeJson2 = ''',"related_object_data":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":1627976765265,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"关联业务数据","label":"关联业务数据","type":"what_list_data","is_abstract":true,"field_num":11,"is_required":false,"api_name":"related_object_data","define_type":"package","_id":"616572dd983987000180e4f5","is_index_field":false,"is_single":false,"index_name":"s_8","status":"released","help_text":"","max_length":10000},"last_modified_by":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":1634038081159,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released","description":""},"out_tenant_id":{"describe_api_name":"object_zl_whatlist__c","is_index":false,"is_active":true,"create_time":1634038081159,"pattern":"","description":"out_tenant_id","is_unique":false,"label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","config":{"display":0},"index_name":"o_ei","status":"released","max_length":200},"display_name":{"describe_api_name":"object_zl_whatlist__c","return_type":"text","expression_type":"js","is_index":true,"expression":"$owner__r.employee_create_time$","is_active":true,"create_time":1635143219042,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"计算字段1","type":"formula","decimal_places":2,"default_to_zero":false,"is_required":false,"api_name":"display_name","define_type":"package","_id":"61764e3381e1500001d219eb","is_index_field":false,"is_single":false,"index_name":"t_1","status":"new","help_text":""},"created_by":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":1634038081159,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released","description":""},"version":{"describe_api_name":"object_zl_whatlist__c","is_index":false,"create_time":1634038081159,"length":8,"description":"version","is_unique":false,"label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"mc_currency":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":1634038081150,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","default_value":"","label":"币种","type":"select_one","field_num":5,"is_required":false,"api_name":"mc_currency","options":[{"label":"BHD - Bahraini Dinar","value":"BHD"},{"not_usable":true,"label":"BDT - 孟加拉国塔卡","value":"BDT"},{"not_usable":true,"label":"BGN - Bulgarian Lev","value":"BGN"},{"label":"AWG - 阿鲁巴岛弗罗林","value":"AWG"},{"label":"USD - U.S. Dollar","value":"USD"},{"label":"AED - UAE Dirham","value":"AED"},{"label":"ALL - 阿尔巴尼亚列克","value":"ALL"},{"label":"BAM - 自由兑换马克","value":"BAM"},{"label":"ANG - 荷属安地列斯盾","value":"ANG"},{"label":"ARS - 阿根廷比索","value":"ARS"},{"label":"AMD - 亚美尼亚打兰","value":"AMD"},{"label":"AUD - Australian Dollar","value":"AUD"},{"label":"AOA - 安哥拉宽扎","value":"AOA"},{"label":"BBD - 巴巴多斯元","value":"BBD"},{"label":"AFN - Afghanistan Afghani (New)","value":"AFN"},{"label":"AZN - 阿塞拜疆马纳特","value":"AZN"},{"label":"CNY - China Yuan","value":"CNY"}],"define_type":"package","_id":"6165714174fdaf000176168a","is_index_field":false,"label_r":"币种","is_single":false,"config":{},"index_name":"s_6","status":"new","help_text":""},"record_type":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":1634038081147,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"record_type","label":"业务类型","type":"record_type","is_need_convert":false,"is_required":false,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"预设业务类型","label":"预设业务类型"}],"define_type":"package","_id":"6165714174fdaf0001761683","is_index_field":false,"label_r":"业务类型","is_single":false,"config":{},"index_name":"r_type","status":"released","help_text":""},"relevant_team":{"describe_api_name":"object_zl_whatlist__c","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","description":"Member Employee","define_type":"package","is_unique":false,"label":"Member Employee","is_single":true,"type":"employee","help_text":"Member Employee"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"description":"Member Role","define_type":"package","is_unique":false,"label":"Member Role","type":"select_one","help_text":"Member Role"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"description":"Member Permission Type","define_type":"package","is_unique":false,"label":"Member Permission Type","type":"select_one","help_text":"Member Permission Type"}},"is_index":true,"is_active":true,"create_time":1634038081149,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"6165714174fdaf0001761685","is_index_field":false,"label_r":"相关团队","is_single":false,"index_name":"a_team","status":"new","help_text":"相关团队"},"related_object":{"describe_api_name":"object_zl_whatlist__c","is_index":false,"is_active":true,"create_time":1627976765277,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"动态关联","label":"动态关联","group_type":"what_list","type":"group","relation_table":"biz_behavior_record_relation","wheres":[{"func_api_name":"func_SU31H__c"}],"is_required":false,"api_name":"related_object","define_type":"package","_id":"616572dd983987000180e4f6","fields":{"id_field":"related_object_data","api_name_field":"related_api_names"},"is_index_field":false,"is_single":false,"index_name":"s_2","status":"released","help_text":""},"field_y01PZ__c":{"describe_api_name":"object_zl_whatlist__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"function","type":"object_reference","relation_outer_data_privilege":"not_related","related_where_type":"","wheres":[{"connector":"OR","filters":[{"value_type":9,"operator":"IN","field_values":["func_25Z6N__c"],"field_name":"id"}]}],"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"index_name":"s_1","is_index":true,"is_active":true,"create_time":1635145039939,"is_encrypted":false,"default_value":"","target_api_name":"LeadsObj","label":"查找关联ldd","target_related_list_name":"target_related_list_21h9P__c","field_num":12,"target_related_list_label":"zl-whatlist","action_on_target_delete":"set_null","related_wheres":[],"api_name":"field_y01PZ__c","_id":"6176555081e1500001d242df","is_index_field":true,"status":"new","help_text":""},"field_2zoUh__c":{"describe_api_name":"object_zl_whatlist__c","prefix":"","auto_adapt_places":false,"is_unique":true,"description":"","start_number":1,"type":"auto_number","is_required":false,"define_type":"custom","postfix":"","is_single":false,"index_name":"s_13","is_index":true,"auto_number_type":"function","is_active":true,"create_time":1653876982341,"is_encrypted":false,"default_value":"01","serial_number":2,"label":"自增编号","field_num":20,"condition":"NONE","api_name":"field_2zoUh__c","func_api_name":"func_CVecG__c","_id":"629428f60db9ed0001a69fd4","is_index_field":false,"status":"new","help_text":""},"data_own_department":{"describe_api_name":"object_zl_whatlist__c","is_index":true,"is_active":true,"create_time":1634038081159,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","where_type":"field","label":"归属部门","type":"department","is_need_convert":false,"wheres":[],"is_required":false,"api_name":"data_own_department","define_type":"package","_id":"626b5f91d669f50001b8e500","is_index_field":false,"label_r":"归属部门","is_single":true,"index_name":"data_owner_dept_id","status":"released","help_text":""},"field_9o2l5__c":{"describe_api_name":"object_zl_whatlist__c","is_index":false,"is_active":true,"create_time":1644551051430,"is_encrypted":false,"auto_adapt_places":false,"quote_field_type":"number","is_unique":false,"description":"","label":"引用字段111234","type":"quote","quote_field":"field_y01PZ__c__r.field_k4dnZ__c","is_required":false,"api_name":"field_9o2l5__c","define_type":"custom","_id":"6205db8bdac8a00001258dac","is_index_field":false,"is_single":false,"index_name":"d_2","status":"new","help_text":""},"field_Dqp83__c":{"describe_api_name":"object_zl_whatlist__c","is_index":false,"is_active":true,"create_time":1652084808858,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"description":"","label":"支付(收款)组件","group_type":"payment","type":"group","amount_is_readonly":false,"is_required":false,"api_name":"field_Dqp83__c","define_type":"custom","_id":"6278d0498a43130001ddad78","fields":{"pay_time_field":"field_xVkl9__c","pay_status_field":"field_Tj8f2__c","pay_type_field":"field_i135X__c","pay_amount_field":"field_6ubp8__c"},"is_index_field":false,"amount_input_type":"manual_input","is_single":false,"index_name":"s_12","status":"new","help_text":""},"name":{"describe_api_name":"object_zl_whatlist__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":true,"description":"name","type":"text","default_to_zero":false,"is_required":true,"define_type":"system","input_mode":"","label_r":"主属性","is_single":false,"index_name":"name","max_length":100,"is_index":true,"is_active":true,"create_time":1634038081247,"is_encrypted":false,"default_value":"","label":"主属性","api_name":"name","_id":"6165714174fdaf000176167b","is_index_field":false,"status":"new","help_text":""},"mc_exchange_rate_version":{"describe_api_name":"object_zl_whatlist__c","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"description":"","type":"text","default_to_zero":false,"is_required":false,"define_type":"package","input_mode":"","label_r":"汇率版本","is_single":false,"index_name":"t_3","max_length":256,"is_index":false,"is_active":true,"create_time":1634038081153,"is_encrypted":false,"default_value":"","label":"汇率版本","field_num":8,"api_name":"mc_exchange_rate_version","_id":"6165714174fdaf000176168d","is_index_field":false,"status":"new","help_text":""},"_id":{"describe_api_name":"object_zl_whatlist__c","is_index":false,"is_active":true,"create_time":1634038081159,"pattern":"","description":"_id","is_unique":false,"label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","status":"released","max_length":200},"field_h0rpO__c":{"describe_api_name":"object_zl_whatlist__c","default_is_expression":false,"auto_adapt_places":false,"is_unique":false,"description":"","type":"number","decimal_places":2,"default_to_zero":true,"is_required":false,"define_type":"custom","is_single":false,"index_name":"d_3","max_length":14,"is_index":true,"is_active":true,"create_time":1647256053008,"is_encrypted":false,"step_value":1,"display_style":"input","length":12,"default_value":"","label":"数字","field_num":13,"api_name":"field_h0rpO__c","_id":"622f21f5bebc850001f37b4c","is_index_field":false,"round_mode":4,"status":"new","help_text":""}},"release_version":"6.4","actions":{}}'''
    @Shared
    String whatListObjDescribeJson = whatListObjDescribeJson1 + whatListObjDescribeJson2

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def "test removePGNotSupportValue"() {
        given:
        def list = [new ObjectData(["aa": "7\u00007\u0000"])]
        def desc = new ObjectDescribe(["fields": ["aa": ["api_name": "aa", "type": "text"]]])

        when:
        ObjectDataExt.removePGNotSupportValue(list, desc)

        then:
        list.get(0).get("aa") == "77"

    }

    def "test diff"() {
        given:
        def oldData = new ObjectData()
        oldData.fromJsonString(olddataStr)
        def newData = new ObjectData()
        newData.fromJsonString(newdataStr)
        def describe = new ObjectDescribe()
        describe.fromJsonString(describeStr)

        when:
        def result = ObjectDataExt.of(oldData).diff(newData, describe)
        then:
        result.containsKey("name__lang")
        result.containsKey("owner_department")

        where:
        olddataStr << ["""{"dept_code":"yyss","dept_parent_path":"999999.1031.1037.1032","tenant_id":"74255","data_own_organization":["999999"],"extend_obj_data_id":"623d678a965ec40001bbc84e","is_deleted":false,"object_describe_api_name":"DepartmentObj","owner_department_id":"1036","owner_department":"部门多语__默认002","searchAfterId":["1665818181479","1036"],"owner":["1000"],"name__lang":{"en":"部门多语_ennnn","zh-CN":"部门多语__中文"},"lock_status":"0","package":"CRM","last_modified_time":1665818181479,"create_time":1648191370189,"life_status":"normal","last_modified_by":["-10000"],"version":"46","created_by":["1000"],"record_type":"default__c","data_own_department":["1000"],"parent_id":["1032"],"name":"部门多语__默认002","_id":"1036","dept_id":"1036","status":"0"}"""]
        newdataStr << ['''{"record_type":"default__c","object_describe_api_name":"DepartmentObj","object_describe_id":"61a2f5880fedc40001298093","name":"部门多语__默认002","name__lang":{"en":"部门多语_en","zh-CN":"部门多语__中文"},"dept_code":"yyss","manager_id":[],"assistant_id":[],"parent_id":["1032"],"field_GjI40__c":"","_id":"1036","dept_parent_path":"999999.1031.1037.1032","data_own_organization":["999999"],"owner_department":"ys","lock_status":"0","life_status":"normal","data_own_department":["1000"],"dept_id":"1036","status":"0","requestId":"da6e91b85acf4466814e055f36fd3fbe","tenant_id":"74255","extend_obj_data_id":"623d678a965ec40001bbc84e","is_deleted":false,"owner_department_id":"1036","searchAfterId":["1665818181479","1036"],"owner":["1000"],"package":"CRM","last_modified_time":1665818181479,"create_time":1648191370189,"last_modified_by":["-10000"],"created_by":["1000"]}''']
        describeStr << ["""{"tenant_id":"74255","store_table_name":"org_dept","package":"CRM","is_active":true,"last_modified_time":1665743674165,"create_time":1638069640472,"description":"描述企业的部门基本信息","last_modified_by":"1000","display_name":"部门","version":44,"index_version":200,"is_deleted":false,"api_name":"DepartmentObj","is_udef":true,"define_type":"package","display_name_r":"部门","short_name":"dpt","_id":"61a2f5880fedc40001298093","fields":{"dept_code":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1658398308026,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"部门编码","is_unique":true,"label":"部门编码","type":"text","is_required":true,"api_name":"dept_code","define_type":"package","_id":"62d926646092e469f2f9d4bf","is_single":false,"is_extend":false,"label_r":"部门编码","is_index_field":false,"index_name":"t_1","config":{"edit":0,"enable":0,"attrs":{"label":0}},"max_length":100,"help_text":"部门编码在相关导入、导出表格中会作为部门唯一识别项，请采用识别性较强的文本/字符作为部门编码，eg，北京研发分公司的产品部的部门编码：【北研-产品部】","status":"released"},"dept_parent_path":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590320,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"部门祖先路径","is_unique":false,"label":"部门祖先路径","type":"tree_path","is_need_convert":false,"is_required":false,"api_name":"dept_parent_path","define_type":"package","_id":"61a2f5880fedc40001298071","is_single":false,"label_r":"部门祖先路径","is_index_field":false,"index_name":"s_5","max_length":500,"status":"released"},"tenant_id":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1638069640472,"pattern":"","is_unique":false,"description":"tenant_id","label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","max_length":200,"status":"released"},"lock_rule":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1612322590320,"is_encrypted":false,"auto_adapt_places":false,"description":"锁定规则","is_unique":false,"rules":[],"default_value":"default_lock_rule","label":"锁定规则","type":"lock_rule","is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"61a2f5880fedc40001298074","is_single":false,"label_r":"锁定规则","is_index_field":false,"index_name":"s_1","status":"released"},"assistant_id":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1656400934719,"is_encrypted":false,"auto_adapt_places":false,"description":"助理","is_unique":false,"label":"助理","type":"employee","is_required":false,"api_name":"assistant_id","define_type":"package","_id":"62baac2657066e0001230900","is_single":false,"is_extend":false,"label_r":"助理","is_index_field":false,"index_name":"a_3","help_text":"对归属于该部门/组织的数据拥有读写权限","status":"released"},"data_own_organization":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1647329839731,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"归属组织","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"6230422f04a720000162e2db","is_single":true,"label_r":"归属组织","is_index_field":false,"index_name":"a_6","status":"released"},"lock_user":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1612322590362,"is_encrypted":false,"auto_adapt_places":false,"description":"加锁人","is_unique":false,"label":"加锁人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"61a2f5880fedc40001298076","is_single":true,"label_r":"加锁人","is_index_field":false,"index_name":"a_4","status":"released"},"dept_children":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"auto_adapt_places":false,"description":"部门子部门序列","is_unique":false,"label":"部门子部门序列","type":"department","is_required":false,"api_name":"dept_children","define_type":"package","_id":"61a2f5880fedc40001298077","is_single":false,"label_r":"部门子部门序列","is_index_field":false,"index_name":"a_1","status":"released"},"extend_obj_data_id":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"连接通表的记录ID,扩展字段用","is_unique":true,"label":"扩展字段在mt_data中的记录ID","type":"text","is_required":false,"api_name":"extend_obj_data_id","define_type":"system","_id":"61a2f5880fedc40001298078","is_single":false,"is_extend":false,"is_index_field":false,"index_name":"t_2","max_length":64,"status":"released"},"is_deleted":{"describe_api_name":"DepartmentObj","is_index":false,"create_time":1638069640472,"is_unique":false,"description":"is_deleted","default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"manager_id":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1656400934719,"is_encrypted":false,"auto_adapt_places":false,"description":"部门负责人","is_unique":false,"label":"部门负责人","type":"employee","is_required":false,"api_name":"manager_id","define_type":"package","_id":"62baac2657066e0001230924","is_single":true,"is_extend":false,"label_r":"部门负责人","is_index_field":false,"index_name":"a_5","help_text":"对归属于该部门/组织的数据拥有读写权限","status":"released"},"life_status_before_invalid":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"作废前生命状态","is_unique":false,"label":"作废前生命状态","type":"text","is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"61a2f5880fedc4000129807b","is_single":false,"label_r":"作废前生命状态","is_index_field":false,"index_name":"t_3","max_length":256,"status":"released"},"field_GjI40__c":{"describe_api_name":"DepartmentObj","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"is_extend":true,"index_name":"t_4","max_length":100,"is_index":true,"is_active":true,"create_time":1638069640333,"is_encrypted":false,"default_value":"","label":"单行文本","field_num":1,"api_name":"field_GjI40__c","_id":"61a2f5880fedc4000129807c","is_index_field":false,"help_text":"","status":"new"},"object_describe_api_name":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1638069640472,"pattern":"","is_unique":false,"description":"object_describe_api_name","label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","max_length":200,"status":"released"},"owner_department":{"describe_api_name":"DepartmentObj","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","is_single":true,"is_extend":false,"label_r":"负责人主属部门","index_name":"owner_dept","max_length":100,"is_index":false,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"61a2f5880fedc4000129807e","is_index_field":false,"help_text":"","status":"released"},"out_owner":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1638069640472,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"index_name":"o_owner","config":{"display":1},"status":"released"},"owner":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590320,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"负责人","type":"employee","is_need_convert":false,"is_required":true,"api_name":"owner","define_type":"package","_id":"61a2f5880fedc40001298080","is_single":true,"is_extend":false,"label_r":"负责人","is_index_field":false,"index_name":"owner","help_text":"","status":"released"},"lock_status":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590350,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"default_value":"0","label":"锁定状态","type":"select_one","is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","_id":"61a2f5880fedc40001298081","is_single":true,"label_r":"锁定状态","is_index_field":false,"index_name":"s_2","config":{},"status":"released"},"package":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1638069640472,"pattern":"","is_unique":false,"description":"package","label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","max_length":200,"status":"released"},"last_modified_time":{"describe_api_name":"DepartmentObj","is_index":true,"create_time":1638069640472,"is_unique":false,"description":"last_modified_time","label":"最后修改时间","type":"date_time","time_zone":"","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"create_time":{"describe_api_name":"DepartmentObj","is_index":true,"create_time":1638069640472,"is_unique":false,"description":"create_time","label":"创建时间","type":"date_time","time_zone":"","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590362,"is_encrypted":false,"auto_adapt_places":false,"description":"生命状态","is_unique":false,"default_value":"normal","label":"生命状态","type":"select_one","is_required":true,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective"},{"label":"审核中","value":"under_review"},{"label":"正常","value":"normal"},{"label":"变更中","value":"in_change"},{"label":"作废","value":"invalid"}],"define_type":"package","_id":"61a2f5880fedc40001298085","is_single":true,"label_r":"生命状态","is_index_field":false,"index_name":"s_3","config":{},"status":"released"},"last_modified_by":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1638069640472,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released"},"out_tenant_id":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1638069640472,"pattern":"","is_unique":false,"description":"out_tenant_id","label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","index_name":"o_ei","config":{"display":0},"max_length":200,"status":"released"},"version":{"describe_api_name":"DepartmentObj","is_index":false,"create_time":1638069640472,"length":8,"is_unique":false,"description":"version","label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"created_by":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1638069640472,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released"},"relevant_team":{"describe_api_name":"DepartmentObj","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","is_unique":false,"define_type":"package","description":"成员员工","label":"成员员工","type":"employee","is_single":true,"help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"is_unique":false,"define_type":"package","description":"成员角色","label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"is_unique":false,"define_type":"package","description":"成员权限类型","label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"61a2f5880fedc4000129808b","is_single":false,"is_extend":false,"label_r":"相关团队","is_index_field":false,"index_name":"a_team","help_text":"相关团队","status":"new"},"record_type":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1647329824305,"is_encrypted":false,"auto_adapt_places":false,"description":"record_type","is_unique":false,"label":"业务类型","type":"record_type","is_need_convert":false,"is_required":true,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"部门","label":"部门"},{"is_active":true,"api_name":"organization__c","description":"组织","label":"组织"}],"define_type":"package","_id":"62304220757be60001ae583a","is_single":false,"is_extend":false,"label_r":"业务类型","is_index_field":false,"index_name":"r_type","config":{},"help_text":"","status":"released"},"data_own_department":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1638069640472,"is_unique":false,"label":"归属部门","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_department","define_type":"package","is_single":true,"index_name":"data_owner_dept_id","status":"released"},"parent_id":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"auto_adapt_places":false,"description":"上级部门","is_unique":false,"label":"上级部门","type":"department","is_required":false,"api_name":"parent_id","define_type":"package","_id":"61a2f5880fedc4000129808d","is_single":true,"label_r":"上级部门","is_index_field":false,"index_name":"a_2","status":"released"},"name":{"describe_api_name":"DepartmentObj","default_is_expression":false,"auto_adapt_places":false,"pattern":"","description":"部门名称","is_unique":true,"type":"text","default_to_zero":false,"is_required":true,"define_type":"package","input_mode":"","is_single":false,"is_extend":false,"label_r":"部门名称","index_name":"name","max_length":50,"enable_multi_lang":true,"is_index":true,"is_active":true,"create_time":1658398308026,"is_encrypted":false,"default_value":"","label":"部门名称","api_name":"name","_id":"62d926646092e469f2f9d4c0","is_index_field":false,"help_text":"","status":"released"},"order_by":{"describe_api_name":"DepartmentObj","is_index":false,"create_time":1638069640472,"length":8,"is_unique":false,"description":"order_by","label":"order_by","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"order_by","define_type":"system","index_name":"l_by","round_mode":4,"status":"released"},"_id":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1638069640472,"pattern":"","is_unique":false,"description":"_id","label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","max_length":200,"status":"released"},"dept_id":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"部门ID，只在企业内唯一","is_unique":true,"label":"部门ID","type":"text","is_required":true,"api_name":"dept_id","define_type":"package","_id":"61a2f5880fedc40001298091","is_single":false,"is_extend":false,"label_r":"部门ID","is_index_field":false,"index_name":"t_5","max_length":64,"status":"released"},"status":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590383,"is_encrypted":false,"auto_adapt_places":false,"description":"表示部门状态，启用或停用","is_unique":false,"default_value":"0","label":"部门状态","type":"select_one","is_required":true,"api_name":"status","options":[{"label":"启用","value":"0"},{"label":"停用","value":"1"},{"label":"删除","value":"2"}],"define_type":"package","_id":"61a2f5880fedc40001298092","is_single":false,"is_extend":false,"label_r":"部门状态","is_index_field":false,"index_name":"s_4","config":{},"status":"released"}},"release_version":"6.4","actions":{}}"""]
    }

    def "test get Ref Object Data Ids"() {
        given:

        AppFrameworkConfig.displayNameSupportWhatListFieldBlackListObjects = ['ActiveRecordObj', 'TelesalesRecordObj', 'ServiceLogObj', 'JournalObj', 'ScheduleObj', 'BlogObj', 'AnnounceObj'] as Set
        IObjectDescribe objectDescribe = new ObjectDescribe(JSON.parseObject(whatListObjDescribeJson))
        def dataList = []
        IObjectData objectData1 = Spy(ObjectData)
        objectData1.set("field_i3yKp__c", "id")
        def map1 = [
                "ContactObj"     : [
                        "5ee34a72a3744d0001484cc9"
                ],
                "object_OQgGQ__c": [
                        "61e8c555d7a6be0001ae03de"
                ]
        ]
        objectData1.set("related_object", map1)
        IObjectData objectData2 = Spy(ObjectData)
        objectData2.set("field_i3yKp__c", "id")
        def map2 = [
                "AccountAddrObj": [
                        "61d6a889308ef7000149a532",
                        "620f7e9bedd10100011a52e3"
                ],
                "ContactObj"    : [
                        "0cac1d37b5d344c29cb4810c40f0a8be",
                        "431cf73d2ba94f38ba4b167fb427e179",
                        "7113e1374ccc414986a6acbc63439f5e",
                        "5ee34a72a3744d0001484cc9"
                ],
                "AnnounceObj"   : [
                        "63b6790cb089fa0001fa0ec7"
                ]
        ]
        objectData2.set("related_object", map2)
        dataList << objectData1 << objectData2

        when:
        Map<String, List<String>> result = ObjectDataExt.getRefObjectDataIds(objectDescribe, dataList)

        then:
        result.get("ContactObj").size() == 4
        result.keySet().size() == 3
    }

    def 'compareNumber test'() {
        when:
        def compared = ObjectDataExt.compareNumber(leftValue, rightValue)
        then:
        result == compared
        where:
        leftValue | rightValue || result
        '10.5'    | '10.50'    || 0
        '10.5'    | '10.1'     || 1
        '10.5'    | '10.6'     || -1
    }

    def 'test diffDetail'() {
        def oldData = new ObjectData()
        oldData.fromJsonString(olddataStr)
        def newData = new ObjectData()
        newData.fromJsonString(newdataStr)
        def describe = new ObjectDescribe()
        describe.fromJsonString(describeStr)

        def oldDetails = [:]
        oldDetails.put('DepartmentObj', [oldData])
        def newDetails = [:]
        newDetails.put('DepartmentObj', [newData])
        def describeMap = [:]
        describeMap.put('DepartmentObj', describe)
        when:
        def detailResult = ObjectDataExt.diffDetail(oldDetails, newDetails, describeMap, true)
        def result = ObjectDataExt.of(detailResult.get('DepartmentObj').get(0)).toMap() as Map
        then:
        println result
        result.containsKey("name__lang")
        result.containsKey("owner_department")
        where:
        olddataStr << ["""{"dept_code":"yyss","dept_parent_path":"999999.1031.1037.1032","tenant_id":"74255","data_own_organization":["999999"],"extend_obj_data_id":"623d678a965ec40001bbc84e","is_deleted":false,"object_describe_api_name":"DepartmentObj","owner_department_id":"1036","owner_department":"部门多语__默认002","searchAfterId":["1665818181479","1036"],"owner":["1000"],"name__lang":{"en":"部门多语_ennnn","zh-CN":"部门多语__中文"},"lock_status":"0","package":"CRM","last_modified_time":1665818181479,"create_time":1648191370189,"life_status":"normal","last_modified_by":["-10000"],"version":"46","created_by":["1000"],"record_type":"default__c","data_own_department":["1000"],"parent_id":["1032"],"name":"部门多语__默认002","_id":"1036","dept_id":"1036","status":"0"}"""]
        newdataStr << ['''{"record_type":"default__c","object_describe_api_name":"DepartmentObj","object_describe_id":"61a2f5880fedc40001298093","name":"部门多语__默认002","name__lang":{"en":"部门多语_en","zh-CN":"部门多语__中文"},"dept_code":"yyss","manager_id":[],"assistant_id":[],"parent_id":["1032"],"field_GjI40__c":"","_id":"1036","dept_parent_path":"999999.1031.1037.1032","data_own_organization":["999999"],"owner_department":"ys","lock_status":"0","life_status":"normal","data_own_department":["1000"],"dept_id":"1036","status":"0","requestId":"da6e91b85acf4466814e055f36fd3fbe","tenant_id":"74255","extend_obj_data_id":"623d678a965ec40001bbc84e","is_deleted":false,"owner_department_id":"1036","searchAfterId":["1665818181479","1036"],"owner":["1000"],"package":"CRM","last_modified_time":1665818181479,"create_time":1648191370189,"last_modified_by":["-10000"],"created_by":["1000"]}''']
        describeStr << ["""{"tenant_id":"74255","store_table_name":"org_dept","package":"CRM","is_active":true,"last_modified_time":1665743674165,"create_time":1638069640472,"description":"描述企业的部门基本信息","last_modified_by":"1000","display_name":"部门","version":44,"index_version":200,"is_deleted":false,"api_name":"DepartmentObj","is_udef":true,"define_type":"package","display_name_r":"部门","short_name":"dpt","_id":"61a2f5880fedc40001298093","fields":{"dept_code":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1658398308026,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"部门编码","is_unique":true,"label":"部门编码","type":"text","is_required":true,"api_name":"dept_code","define_type":"package","_id":"62d926646092e469f2f9d4bf","is_single":false,"is_extend":false,"label_r":"部门编码","is_index_field":false,"index_name":"t_1","config":{"edit":0,"enable":0,"attrs":{"label":0}},"max_length":100,"help_text":"部门编码在相关导入、导出表格中会作为部门唯一识别项，请采用识别性较强的文本/字符作为部门编码，eg，北京研发分公司的产品部的部门编码：【北研-产品部】","status":"released"},"dept_parent_path":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590320,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"部门祖先路径","is_unique":false,"label":"部门祖先路径","type":"tree_path","is_need_convert":false,"is_required":false,"api_name":"dept_parent_path","define_type":"package","_id":"61a2f5880fedc40001298071","is_single":false,"label_r":"部门祖先路径","is_index_field":false,"index_name":"s_5","max_length":500,"status":"released"},"tenant_id":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1638069640472,"pattern":"","is_unique":false,"description":"tenant_id","label":"tenant_id","type":"text","is_need_convert":false,"is_required":true,"api_name":"tenant_id","define_type":"system","index_name":"ei","max_length":200,"status":"released"},"lock_rule":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1612322590320,"is_encrypted":false,"auto_adapt_places":false,"description":"锁定规则","is_unique":false,"rules":[],"default_value":"default_lock_rule","label":"锁定规则","type":"lock_rule","is_need_convert":false,"is_required":false,"api_name":"lock_rule","define_type":"package","_id":"61a2f5880fedc40001298074","is_single":false,"label_r":"锁定规则","is_index_field":false,"index_name":"s_1","status":"released"},"assistant_id":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1656400934719,"is_encrypted":false,"auto_adapt_places":false,"description":"助理","is_unique":false,"label":"助理","type":"employee","is_required":false,"api_name":"assistant_id","define_type":"package","_id":"62baac2657066e0001230900","is_single":false,"is_extend":false,"label_r":"助理","is_index_field":false,"index_name":"a_3","help_text":"对归属于该部门/组织的数据拥有读写权限","status":"released"},"data_own_organization":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1647329839731,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"归属组织","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_organization","define_type":"package","_id":"6230422f04a720000162e2db","is_single":true,"label_r":"归属组织","is_index_field":false,"index_name":"a_6","status":"released"},"lock_user":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1612322590362,"is_encrypted":false,"auto_adapt_places":false,"description":"加锁人","is_unique":false,"label":"加锁人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"lock_user","define_type":"package","_id":"61a2f5880fedc40001298076","is_single":true,"label_r":"加锁人","is_index_field":false,"index_name":"a_4","status":"released"},"dept_children":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"auto_adapt_places":false,"description":"部门子部门序列","is_unique":false,"label":"部门子部门序列","type":"department","is_required":false,"api_name":"dept_children","define_type":"package","_id":"61a2f5880fedc40001298077","is_single":false,"label_r":"部门子部门序列","is_index_field":false,"index_name":"a_1","status":"released"},"extend_obj_data_id":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"连接通表的记录ID,扩展字段用","is_unique":true,"label":"扩展字段在mt_data中的记录ID","type":"text","is_required":false,"api_name":"extend_obj_data_id","define_type":"system","_id":"61a2f5880fedc40001298078","is_single":false,"is_extend":false,"is_index_field":false,"index_name":"t_2","max_length":64,"status":"released"},"is_deleted":{"describe_api_name":"DepartmentObj","is_index":false,"create_time":1638069640472,"is_unique":false,"description":"is_deleted","default_value":false,"label":"is_deleted","type":"true_or_false","is_need_convert":false,"is_required":false,"api_name":"is_deleted","define_type":"system","index_name":"is_del","status":"released"},"manager_id":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1656400934719,"is_encrypted":false,"auto_adapt_places":false,"description":"部门负责人","is_unique":false,"label":"部门负责人","type":"employee","is_required":false,"api_name":"manager_id","define_type":"package","_id":"62baac2657066e0001230924","is_single":true,"is_extend":false,"label_r":"部门负责人","is_index_field":false,"index_name":"a_5","help_text":"对归属于该部门/组织的数据拥有读写权限","status":"released"},"life_status_before_invalid":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"作废前生命状态","is_unique":false,"label":"作废前生命状态","type":"text","is_need_convert":false,"is_required":false,"api_name":"life_status_before_invalid","define_type":"package","_id":"61a2f5880fedc4000129807b","is_single":false,"label_r":"作废前生命状态","is_index_field":false,"index_name":"t_3","max_length":256,"status":"released"},"field_GjI40__c":{"describe_api_name":"DepartmentObj","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"custom","input_mode":"","is_single":false,"is_extend":true,"index_name":"t_4","max_length":100,"is_index":true,"is_active":true,"create_time":1638069640333,"is_encrypted":false,"default_value":"","label":"单行文本","field_num":1,"api_name":"field_GjI40__c","_id":"61a2f5880fedc4000129807c","is_index_field":false,"help_text":"","status":"new"},"object_describe_api_name":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1638069640472,"pattern":"","is_unique":false,"description":"object_describe_api_name","label":"object_describe_api_name","type":"text","is_need_convert":false,"is_required":true,"api_name":"object_describe_api_name","define_type":"system","index_name":"api_name","max_length":200,"status":"released"},"owner_department":{"describe_api_name":"DepartmentObj","default_is_expression":false,"auto_adapt_places":false,"pattern":"","is_unique":false,"type":"text","default_to_zero":false,"is_required":false,"define_type":"package","is_single":true,"is_extend":false,"label_r":"负责人主属部门","index_name":"owner_dept","max_length":100,"is_index":false,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"default_value":"","label":"负责人主属部门","is_need_convert":false,"api_name":"owner_department","_id":"61a2f5880fedc4000129807e","is_index_field":false,"help_text":"","status":"released"},"out_owner":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1638069640472,"is_unique":false,"label":"外部负责人","type":"employee","is_need_convert":false,"is_required":false,"api_name":"out_owner","define_type":"system","is_single":true,"index_name":"o_owner","config":{"display":1},"status":"released"},"owner":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590320,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"负责人","type":"employee","is_need_convert":false,"is_required":true,"api_name":"owner","define_type":"package","_id":"61a2f5880fedc40001298080","is_single":true,"is_extend":false,"label_r":"负责人","is_index_field":false,"index_name":"owner","help_text":"","status":"released"},"lock_status":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590350,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"default_value":"0","label":"锁定状态","type":"select_one","is_need_convert":false,"is_required":false,"api_name":"lock_status","options":[{"label":"未锁定","value":"0"},{"label":"锁定","value":"1"}],"define_type":"package","_id":"61a2f5880fedc40001298081","is_single":true,"label_r":"锁定状态","is_index_field":false,"index_name":"s_2","config":{},"status":"released"},"package":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1638069640472,"pattern":"","is_unique":false,"description":"package","label":"package","type":"text","is_need_convert":false,"is_required":false,"api_name":"package","define_type":"system","index_name":"pkg","max_length":200,"status":"released"},"last_modified_time":{"describe_api_name":"DepartmentObj","is_index":true,"create_time":1638069640472,"is_unique":false,"description":"last_modified_time","label":"最后修改时间","type":"date_time","time_zone":"","is_need_convert":false,"is_required":false,"api_name":"last_modified_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"md_time","status":"released"},"create_time":{"describe_api_name":"DepartmentObj","is_index":true,"create_time":1638069640472,"is_unique":false,"description":"create_time","label":"创建时间","type":"date_time","time_zone":"","is_need_convert":false,"is_required":false,"api_name":"create_time","define_type":"system","date_format":"yyyy-MM-dd HH:mm:ss","index_name":"crt_time","status":"released"},"life_status":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590362,"is_encrypted":false,"auto_adapt_places":false,"description":"生命状态","is_unique":false,"default_value":"normal","label":"生命状态","type":"select_one","is_required":true,"api_name":"life_status","options":[{"label":"未生效","value":"ineffective"},{"label":"审核中","value":"under_review"},{"label":"正常","value":"normal"},{"label":"变更中","value":"in_change"},{"label":"作废","value":"invalid"}],"define_type":"package","_id":"61a2f5880fedc40001298085","is_single":true,"label_r":"生命状态","is_index_field":false,"index_name":"s_3","config":{},"status":"released"},"last_modified_by":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1638069640472,"is_unique":false,"label":"最后修改人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"last_modified_by","define_type":"system","is_single":true,"index_name":"md_by","status":"released"},"out_tenant_id":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1638069640472,"pattern":"","is_unique":false,"description":"out_tenant_id","label":"out_tenant_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"out_tenant_id","define_type":"system","index_name":"o_ei","config":{"display":0},"max_length":200,"status":"released"},"version":{"describe_api_name":"DepartmentObj","is_index":false,"create_time":1638069640472,"length":8,"is_unique":false,"description":"version","label":"version","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"version","define_type":"system","index_name":"version","round_mode":4,"status":"released"},"created_by":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1638069640472,"is_unique":false,"label":"创建人","type":"employee","is_need_convert":true,"is_required":false,"api_name":"created_by","define_type":"system","is_single":true,"index_name":"crt_by","status":"released"},"relevant_team":{"describe_api_name":"DepartmentObj","embedded_fields":{"teamMemberEmployee":{"is_index":true,"is_need_convert":true,"is_required":false,"api_name":"teamMemberEmployee","is_unique":false,"define_type":"package","description":"成员员工","label":"成员员工","type":"employee","is_single":true,"help_text":"成员员工"},"teamMemberRole":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberRole","options":[{"label":"负责人","value":"1"},{"label":"普通成员","value":"4"}],"is_unique":false,"define_type":"package","description":"成员角色","label":"成员角色","type":"select_one","help_text":"成员角色"},"teamMemberPermissionType":{"is_index":true,"is_need_convert":false,"is_required":false,"api_name":"teamMemberPermissionType","options":[{"label":"只读","value":"1"},{"label":"读写","value":"2"}],"is_unique":false,"define_type":"package","description":"成员权限类型","label":"成员权限类型","type":"select_one","help_text":"成员权限类型"}},"is_index":true,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"auto_adapt_places":false,"is_unique":false,"label":"相关团队","type":"embedded_object_list","is_need_convert":false,"is_required":false,"api_name":"relevant_team","define_type":"package","_id":"61a2f5880fedc4000129808b","is_single":false,"is_extend":false,"label_r":"相关团队","is_index_field":false,"index_name":"a_team","help_text":"相关团队","status":"new"},"record_type":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1647329824305,"is_encrypted":false,"auto_adapt_places":false,"description":"record_type","is_unique":false,"label":"业务类型","type":"record_type","is_need_convert":false,"is_required":true,"api_name":"record_type","options":[{"is_active":true,"api_name":"default__c","description":"部门","label":"部门"},{"is_active":true,"api_name":"organization__c","description":"组织","label":"组织"}],"define_type":"package","_id":"62304220757be60001ae583a","is_single":false,"is_extend":false,"label_r":"业务类型","is_index_field":false,"index_name":"r_type","config":{},"help_text":"","status":"released"},"data_own_department":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1638069640472,"is_unique":false,"label":"归属部门","type":"department","is_need_convert":false,"is_required":false,"api_name":"data_own_department","define_type":"package","is_single":true,"index_name":"data_owner_dept_id","status":"released"},"parent_id":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"auto_adapt_places":false,"description":"上级部门","is_unique":false,"label":"上级部门","type":"department","is_required":false,"api_name":"parent_id","define_type":"package","_id":"61a2f5880fedc4000129808d","is_single":true,"label_r":"上级部门","is_index_field":false,"index_name":"a_2","status":"released"},"name":{"describe_api_name":"DepartmentObj","default_is_expression":false,"auto_adapt_places":false,"pattern":"","description":"部门名称","is_unique":true,"type":"text","default_to_zero":false,"is_required":true,"define_type":"package","input_mode":"","is_single":false,"is_extend":false,"label_r":"部门名称","index_name":"name","max_length":50,"enable_multi_lang":true,"is_index":true,"is_active":true,"create_time":1658398308026,"is_encrypted":false,"default_value":"","label":"部门名称","api_name":"name","_id":"62d926646092e469f2f9d4c0","is_index_field":false,"help_text":"","status":"released"},"order_by":{"describe_api_name":"DepartmentObj","is_index":false,"create_time":1638069640472,"length":8,"is_unique":false,"description":"order_by","label":"order_by","type":"number","decimal_places":0,"is_need_convert":false,"is_required":false,"api_name":"order_by","define_type":"system","index_name":"l_by","round_mode":4,"status":"released"},"_id":{"describe_api_name":"DepartmentObj","is_index":false,"is_active":true,"create_time":1638069640472,"pattern":"","is_unique":false,"description":"_id","label":"_id","type":"text","is_need_convert":false,"is_required":false,"api_name":"_id","define_type":"system","index_name":"_id","max_length":200,"status":"released"},"dept_id":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590373,"is_encrypted":false,"auto_adapt_places":false,"pattern":"","description":"部门ID，只在企业内唯一","is_unique":true,"label":"部门ID","type":"text","is_required":true,"api_name":"dept_id","define_type":"package","_id":"61a2f5880fedc40001298091","is_single":false,"is_extend":false,"label_r":"部门ID","is_index_field":false,"index_name":"t_5","max_length":64,"status":"released"},"status":{"describe_api_name":"DepartmentObj","is_index":true,"is_active":true,"create_time":1612322590383,"is_encrypted":false,"auto_adapt_places":false,"description":"表示部门状态，启用或停用","is_unique":false,"default_value":"0","label":"部门状态","type":"select_one","is_required":true,"api_name":"status","options":[{"label":"启用","value":"0"},{"label":"停用","value":"1"},{"label":"删除","value":"2"}],"define_type":"package","_id":"61a2f5880fedc40001298092","is_single":false,"is_extend":false,"label_r":"部门状态","is_index_field":false,"index_name":"s_4","config":{},"status":"released"}},"release_version":"6.4","actions":{}}"""]
    }

    def "test validateFileFieldDataType throw exception"() {
        given:
        IFieldDescribe fieldDescribe = new FileAttachmentFieldDescribe()
        fieldDescribe.setApiName("field_file__c")
        def value = [[["ext": "jpg", "path": "N_202305_31_35da364a35b2455684284fbaf8ac32ae.jpg", "filename": "营业执照（陆航建德分公司）.jpg", "create_time": 1685525050327, "size": 39306]],
                     ["ext": "pdf", "path": "N_202406_28_4ed3a2b4e83f45b384518b5437e1474a.pdf", "filename": "2024.6.12总办会会议纪要.pdf", "create_time": 1719556001238, "size": 3604403],
                     ["ext": "pdf", "path": "N_202406_28_585eb63ce3584361a84d5882734d82cc.pdf", "filename": "2024年宕渣界定报告.pdf", "create_time": 1719556001238, "size": 455719]] as List
        when:
        ObjectDataExt.of(["field_file__c": value]).validateFileFieldDataType(fieldDescribe)
        then:
        def e = thrown(ValidateException)
        e.message == "paas.udobj.data_fieldpaas.metadata.pattern_illegal"
    }

    def "test validateFileFieldDataType"() {
        given:
        IFieldDescribe fieldDescribe = new FileAttachmentFieldDescribe()
        fieldDescribe.setApiName("field_file__c")
        def value = [["ext": "jpg", "path": "N_202305_31_35da364a35b2455684284fbaf8ac32ae.jpg", "filename": "营业执照（陆航建德分公司）.jpg", "create_time": 1685525050327, "size": 39306],
                     ["ext": "pdf", "path": "N_202406_28_4ed3a2b4e83f45b384518b5437e1474a.pdf", "filename": "2024.6.12总办会会议纪要.pdf", "create_time": 1719556001238, "size": 3604403],
                     ["ext": "pdf", "path": "N_202406_28_585eb63ce3584361a84d5882734d82cc.pdf", "filename": "2024年宕渣界定报告.pdf", "create_time": 1719556001238, "size": 455719]] as List
        when:
        ObjectDataExt.of(["field_file__c": value]).validateFileFieldDataType(fieldDescribe)
        then:
        noExceptionThrown()
    }

    def "test validateFileFieldDataType thrown"() {
        given:
        IFieldDescribe fieldDescribe = new FileAttachmentFieldDescribe()
        fieldDescribe.setApiName("field_file__c")
        def value = [["ext": "jpg", "filename": "营业执照（陆航建德分公司）.jpg", "create_time": 1685525050327, "size": 39306],
                     ["ext": "pdf", "path": "N_202406_28_4ed3a2b4e83f45b384518b5437e1474a.pdf", "filename": "2024.6.12总办会会议纪要.pdf", "create_time": 1719556001238, "size": 3604403],
                     ["ext": "pdf", "path": "N_202406_28_585eb63ce3584361a84d5882734d82cc.pdf", "filename": "2024年宕渣界定报告.pdf", "create_time": 1719556001238, "size": 455719]] as List
        when:
        ObjectDataExt.of(["field_file__c": value]).validateFileFieldDataType(fieldDescribe)
        then:
        def e = thrown(ValidateException)
        e.message == "paas.udobj.data_fieldpaas.metadata.data.file_is_required_error"
    }


    def "test FileEqualHelper"() {
        given:
        def fileHelper1 = ObjectDataExt.FileEqualHelper.of(fileMap1, needFileName)
        def fileHelper2 = ObjectDataExt.FileEqualHelper.of(fileMap2, needFileName)
        when:
        def ret = fileHelper1 == fileHelper2
        then:
        expect == ret
        where:
        needFileName | fileMap1                                                                                                                                                                | fileMap2                                                                                                                                                             | expect
        true         | ["ext": "pdf", "path": "N_202406_28_4ed3a2b4e83f45b384518b5437e1474a.pdf", "filename": "2024.6.12总办会会议纪要.pdf", "create_time": 1719556001238, "size": 3604403]    | ["ext": "pdf", "path": "N_202406_28_4ed3a2b4e83f45b384518b5437e1474a.pdf", "filename": "2024.6.12总办会会议纪要.pdf", "create_time": 1719556001238, "size": 3604403] | true
        true         | ["ext": "pdf", "path": "N_202406_28_4ed3a2b4e83f45b384518b5437e1474a.pdf", "filename": "2024.6.12总办会会议纪要(1).pdf", "create_time": 1719556001238, "size": 3604403] | ["ext": "pdf", "path": "N_202406_28_4ed3a2b4e83f45b384518b5437e1474a.pdf", "filename": "2024.6.12总办会会议纪要.pdf", "create_time": 1719556001238, "size": 3604403] | false
        false        | ["ext": "pdf", "path": "N_202406_28_4ed3a2b4e83f45b384518b5437e1474a.pdf", "filename": "2024.6.12总办会会议纪要(1).pdf", "create_time": 1719556001238, "size": 3604403] | ["ext": "pdf", "path": "N_202406_28_4ed3a2b4e83f45b384518b5437e1474a.pdf", "filename": "2024.6.12总办会会议纪要.pdf", "create_time": 1719556001238, "size": 3604403] | true
    }

    def "test removeFieldForLog"() {
        given:

        IObjectDescribe describe = new ObjectDescribe()
        List<IFieldDescribe> fieldDescribeList = []
        IFieldDescribe owner = new EmployeeFieldDescribe()
        owner.setApiName("owner")
        fieldDescribeList.add(owner)
        IFieldDescribe selectOneFieldDescribe = new SelectOneFieldDescribe()
        selectOneFieldDescribe.setApiName("name")
        fieldDescribeList.add(selectOneFieldDescribe)
        IFieldDescribe textFieldDescribe = new TextFieldDescribe()
        textFieldDescribe.setApiName("text__c")
        fieldDescribeList.add(textFieldDescribe)
        describe.setFieldDescribes(fieldDescribeList)
        describe.setApiName("AccountObj")

        IObjectData objectData = new ObjectData()
        objectData.setName("ceshi")
        objectData.setOwner(Lists.newArrayList("1000"))
        objectData.set(FieldDescribeExt.getLookupNameByFieldName(owner.getApiName()), Lists.newArrayList("1000"))
        objectData.set(FieldDescribeExt.getSelectOther(selectOneFieldDescribe.getApiName()), "单选其他")
        objectData.set(textFieldDescribe.getApiName(), "text_value")

        when:
        Map<String, Object> map = Maps.newHashMap()
        map.put("udobj", "owner")
        map.put("AccountObj", "name")
        Whitebox.setInternalState(AppFrameworkConfig, "unSupportFieldForModifyLog", map)
        Whitebox.setInternalState(AppFrameworkConfig, "ignoreFieldTypesForDiff", Sets.newHashSet(IFieldType.TEXT))
        ObjectDataExt.of(objectData).removeFieldForLog(describe)
        then:
        noExceptionThrown()
    }

    def "test convertRichTextToMetadataWithApiName"() {
        given:
        def objectData = new ObjectData(bigTextMap)
        def objectDataExt = ObjectDataExt.of(objectData)
        when:
        Whitebox.setInternalState(objectDataExt, "MAX_SIZE", 5)
        objectDataExt.convertRichTextToMetadataWithApiName(['html_rich_text__c'] as Set)
        then:
        noExceptionThrown()
        objectData.get('html_rich_text__c') == value__c
        objectData.get('html_rich_text__c__e') == value__e
        objectData.containsField('html_rich_text__c__o') == contains__o
        objectData.containsField('html_rich_text__c') == contains__c
        objectData.containsField('html_rich_text__c__e') == contains__e
        where:
        bigTextMap                                                                                                                                         | value__c      | value__e                                                                                         | contains__o | contains__c | contains__e
        ["html_rich_text__c": '''<p style="margin: 0px;"><strong>测试一下</strong></p><p style="margin: 0px;"><br></p>''']                                 | '测试一下'    | '''<p style="margin: 0px;"><strong>测试一下</strong></p><p style="margin: 0px;"><br></p>'''      | false       | true        | true
        ["html_rich_text__c": '''<p style="margin: 0px;"><strong>测试一下超过5</strong></p><p style="margin: 0px;"><br></p>''']                            | '测试一下超5' | '''<p style="margin: 0px;"><strong>测试一下超过5</strong></p><p style="margin: 0px;"><br></p>''' | false       | true        | true
        ["html_rich_text__c": '''<p style="margin: 0px;"><strong>测试一下</strong></p><p style="margin: 0px;"><br></p>''', "html_rich_text__c__o": "测试"] | '测试一下'    | '''<p style="margin: 0px;"><strong>测试一下</strong></p><p style="margin: 0px;"><br></p>'''      | false       | true        | true
        ["html_rich_text__c": null]                                                                                                                        | null          | null                                                                                             | false       | true        | true
    }


    def "test convertBigTextToMetadataWithField"() {
        given:
        def bigTextField = new BigTextFieldDescribe()
        bigTextField.setApiName('big_text__c')
        when:
        Whitebox.setInternalState(ObjectDataExt, "MAX_SIZE", 5)
        def objectData = new ObjectData(bigTextMap)
        ObjectDataExt.of(objectData).convertBigTextToMetadataWithField([bigTextField] as Set)
        then:
        noExceptionThrown()
        objectData.get('big_text__c') == value__c
        objectData.get('big_text__c__e') == value__e
        objectData.containsField('big_text__c__o') == contains__o
        objectData.containsField('big_text__c') == contains__c
        objectData.containsField('big_text__c__e') == contains__e
        where:
        bigTextMap                                              | value__c     | value__e        | contains__o | contains__c | contains__e
        ["big_text__c": "测试一下"]                             | '测试一下'   | '测试一下'      | false       | true        | true
        ["big_text__c": "测试一下", "big_text__c__o": "测试一"] | '测试一'     | '测试一下'      | false       | true        | true
        ["big_text__c": "测试一下超过5"]                        | '测试一下超' | '测试一下超过5' | false       | true        | true
        ["big_text__c": null]                                   | null         | null            | false       | true        | true
    }

    @Unroll
    def "test synchronize with #scenario"() {
        given: "准备测试数据"

        when: "调用synchronize方法"
        def result = ObjectDataExt.synchronize(new ObjectData(input))
        def dataMap = (result as ObjectData).getContainerDocument()

        then: "验证结果"
        result != null
        dataMap != null
        dataMap.getClass().getName().contains("SynchronizedMap")
        // 如果输入已经是SynchronizedMap，应该返回同一个实例
        if (input.getClass().getName().contains("SynchronizedMap")) {
            dataMap.is(input)
        }

        where: "测试数据"
        scenario                   | input
        "normal map"               | [key: "value"]
        "empty map"                | new HashMap()
        "already synchronized map" | Collections.synchronizedMap(new HashMap())
    }

    def "test synchronize with concurrent operations"() {
        given: "准备并发测试数据"
        def objectData = new ObjectData(new HashMap())
        objectData.set("key", "value")
        def syncObjectData = ObjectDataExt.synchronize(objectData)

        when: "并发操作同步后的map"
        def threads = (1..10).collect { threadNum ->
            Thread.start {
                syncObjectData.set("key${threadNum}", "value${threadNum}")
                syncObjectData.get("key${threadNum}")
            }
        }
        threads*.join()

        then: "验证并发操作结果"
        (syncObjectData as ObjectData).getContainerDocument().size() == 11  // 原始的1个 + 新增的10个
        (1..10).each { num ->
            assert syncObjectData.get("key${num}") == "value${num}"
        }
    }

    def "test getLocalValue"() {
        given:
        def objectData = new ObjectData()
        objectData.set("locationField", locationValue)

        when:
        def result = ObjectDataExt.of(objectData).getLocalValue("locationField")

        then:
        result != null
        if (StringUtils.isNotBlank(locationValue)) {
            assert result.getLongitude() == BigDecimal.valueOf(Double.parseDouble(expectedLongitude))
            assert result.getLatitude() == BigDecimal.valueOf(Double.parseDouble(expectedLatitude))
        } else {
            assert result.getLongitude() == null
            assert result.getLatitude() == null
        }

        where:
        locationValue         | expectedLongitude | expectedLatitude
        "120.5#%\$30.5"       | "120.5"           | "30.5"
        "114.05#%\$22.55"     | "114.05"          | "22.55"
        "-74.006#%\$40.7128"  | "-74.006"         | "40.7128"
        "0.0#%\$0.0"          | "0.0"             | "0.0"
        ""                    | ""                | ""
        null                  | ""                | ""
    }

    def "test LocationInfo_of method"() {
        when:
        def result = LocationInfo.of(value, dataId)
        
        then:
        if (result == null) {
            assert StringUtils.isEmpty(value) || StringUtils.isEmpty(dataId)
        } else {
            assert result.getLongitude() == BigDecimal.valueOf(Double.parseDouble(expectedValue.split(":")[0]))
            assert result.getLatitude() == BigDecimal.valueOf(Double.parseDouble(expectedValue.split(":")[1]))
            assert result.getDataId() == dataId
        }
        
        where:
        value           | dataId     | expectedValue
        "120.5:30.5"    | "data001"  | "120.5:30.5"
        "114.05:22.55"  | "data002"  | "114.05:22.55"
        ""              | "data003"  | null
        "120.5:30.5"    | ""         | null
        null            | "data004"  | null
    }

    def "test LocationInfo_toString method"() {
        given:
        def locationInfo = new LocationInfo(
                longitude: longitude ? BigDecimal.valueOf(Double.parseDouble(longitude)) : null,
                latitude: latitude ? BigDecimal.valueOf(Double.parseDouble(latitude)) : null,
                dataId: dataId
        )
        
        when:
        def result = locationInfo.toString()
        
        then:
        result == expected
        
        where:
        longitude   | latitude    | dataId     | expected
        "120.5"     | "30.5"      | "data001"  | "120.5:30.5"  // 修复后的实现，现在正确使用longitude和latitude
        "114.05"    | "22.55"     | "data002"  | "114.05:22.55"
        null        | "30.5"      | "data003"  | ":30.5"
        "120.5"     | null        | "data004"  | "120.5:"
        null        | null        | "data005"  | ":"
    }
}
