package com.facishare.paas.appframework.metadata

import com.alibaba.fastjson.JSONArray
import com.facishare.paas.appframework.common.util.JacksonUtils
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.data.IDuplicatedSearch
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.fxiaoke.common.PasswordUtil
import spock.lang.Specification
import spock.lang.Unroll

/**
 * create by z<PERSON><PERSON> on 2020/10/16
 */
@Unroll
class DuplicatedSearchExtTest extends Specification {


    def "test_generate_duplicate_search_data"() {
        given: "准备数据"
        and: "mock 描述"
        IObjectDescribe describe = Mock(IObjectDescribe)
        and: "准备 duplicatedSearch"
        IDuplicatedSearch duplicatedSearch = JacksonUtils.fromJson(duplicatedSearchJson, IDuplicatedSearch)
        and: "准备 objectData"

        List<IObjectData> objectDataList = JSONArray.parseArray(objectDataJson, Map).collect { ObjectDataExt.of(it) }
        when:
        describe.getTenantId() >> "78057"
        describe.getApiName() >> "object_t1XtF__c"
        def result = DuplicatedSearchExt.of(duplicatedSearch).generateDuplicateSearchData(objectDataList, describe)
        then:
        println result
        JacksonUtils.readTree(JacksonUtils.toJson(expect)) == JacksonUtils.readTree(JacksonUtils.toJson(result))
        where:
        duplicatedSearchJson                                                                                                    | objectDataJson                                                                                                                                              || expect
        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"field_98WZ4__c","field_value":"PRECISE","mapping_field":"kw0"},{"connector":"AND","field_name":"name",
"field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,"support_import":true}"""                              | """[{"_id":"1","name":"主属性",
"field_98WZ4__c":"自定义字段"}]"""                                                                                                                       || ["DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_1_name_主属性"              : ["1"],
                                                                                                                                                             "DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_1_field_98WZ4__c_自定义字段": ["1"]]

        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"field_98WZ4__c","field_value":"PRECISE","mapping_field":"kw0"},{"connector":"AND","field_name":"name",
"field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,"support_import":true}"""                              | """[{"_id":"2","name":"主属性",
"field_98WZ4__c":null}]"""                                                                                                                               || ["DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_1_name_主属性": ["2"]]

        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"field_98WZ4__c","field_value":"PRECISE","mapping_field":"kw0"},{"connector":"AND","field_name":"name",
"field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,"support_import":true}"""                              | """[{"_id":"1","name":"主属性",
"field_98WZ4__c":null},{"_id":"2","name":"主属性",
"field_98WZ4__c":"自定义字段"}]"""                                                                                                                       || ["DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_1_name_主属性"              : ["1", "2"],
                                                                                                                                                             "DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_1_field_98WZ4__c_自定义字段": ["2"]]

        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"field_98WZ4__c","field_value":"PRECISE","mapping_field":"kw0"}]},{"connector":"OR",
"conditions":[{"connector":"AND","field_name":"name","field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,
"support_import":true}"""                                                                                            | """[{"_id":"1","name":"主属性",
"field_98WZ4__c":null},{"_id":"2","name":"主属性",
"field_98WZ4__c":"自定义字段"}]"""                                                                                                                       || ["DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_1_name_主属性"              : ["1", "2"],
                                                                                                                                                             "DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_1_field_98WZ4__c_自定义字段": ["2"]]

    }

    def "test_generate_duplicate_search_data_FUZZY_duplicatedSearch"() {
        given: "准备数据"
        and: "mock 描述"
        IObjectDescribe describe = Mock(IObjectDescribe)
        and: "准备 duplicatedSearch"
        IDuplicatedSearch duplicatedSearch = JacksonUtils.fromJson(duplicatedSearchJson, IDuplicatedSearch)
        and: "准备 objectData"

        List<IObjectData> objectDataList = JSONArray.parseArray(objectDataJson, Map).collect { ObjectDataExt.of(it) }
        when:
        describe.getTenantId() >> "78057"
        describe.getApiName() >> "object_t1XtF__c"
        def result = DuplicatedSearchExt.of(duplicatedSearch).generateDuplicateSearchData(objectDataList, describe)
        then:
        println result
        result.isEmpty()
        where:
        duplicatedSearchJson                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | objectDataJson || _
        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW","enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND","field_name":"field_98WZ4__c","field_value":"FUZZY","mapping_field":"kw0"},{"connector":"AND","field_name":"name","field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,"support_import":true}""" | """[{"_id":"1","name":"主属性",
"field_98WZ4__c":"自定义字段"}]"""                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           || _
    }


    def "test_generate_duplicate_search_data_FUZZY_and PRECISE"() {
        given: "准备数据"
        and: "mock 描述"
        IObjectDescribe describe = Mock(IObjectDescribe)
        and: "准备 duplicatedSearch"
        IDuplicatedSearch duplicatedSearch = JacksonUtils.fromJson(duplicatedSearchJson, IDuplicatedSearch)
        and: "准备 objectData"

        List<IObjectData> objectDataList = JSONArray.parseArray(objectDataJson, Map).collect { ObjectDataExt.of(it) }
        when:
        describe.getTenantId() >> "78057"
        describe.getApiName() >> "object_t1XtF__c"
        def result = DuplicatedSearchExt.of(duplicatedSearch).generateDuplicateSearchData(objectDataList, describe)
        then:
        println result
        JacksonUtils.readTree(JacksonUtils.toJson(expect)) == JacksonUtils.readTree(JacksonUtils.toJson(result))
        where:
        duplicatedSearchJson                                                                                                   | objectDataJson                                                                                                                                             || expect
        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"field_98WZ4__c","field_value":"PRECISE","mapping_field":"kw0"}]},{"connector":"OR",
"conditions":[{"connector":"AND","field_name":"name","field_value":"FUZZY"}]}]},"operator_id":"1000","version":1,
"support_import":true}"""                                                                                           | """[{"_id":"1","name":"主属性",
"field_98WZ4__c":null},{"_id":"2","name":"主属性",
"field_98WZ4__c":"自定义字段"}]"""                                                                                                                      || ["DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_1_field_98WZ4__c_自定义字段": ["2"]]

        """{"id":"5f5f6cf972f16e00019564c5","tenant_id":"78057","describe_api_name":"object_t1XtF__c","type":"NEW",
"enable":true,"effective":true,"useable_rules":{"show_num":5,"show_fields":[],"related_describes":[],
"special_config":{"transforms":[]},"rules":[{"connector":"OR","conditions":[{"connector":"AND",
"field_name":"field_98WZ4__c","field_value":"FUZZY","mapping_field":"kw0"}]},{"connector":"OR",
"conditions":[{"connector":"AND","field_name":"name","field_value":"PRECISE"}]}]},"operator_id":"1000","version":1,
"support_import":true}"""                                                                                           | """[{"_id":"1","name":"主属性",
"field_98WZ4__c":null},{"_id":"2","name":"主属性",
"field_98WZ4__c":"自定义字段"}]"""                                                                                                                      || ["DUPLICATED_SEARCH_DATA_78057_object_t1XtF__c_1_name_主属性": ["1", "2"]]
    }

    def "Password decode"() {
        expect:
        def str = PasswordUtil.decode("8DA9D5B1E41238DC8BC1ED3502290B5C60E49EC07367ECBACDFD96E99012E87C")
        println str
    }

    def "test RuleCondition"() {
        given:
        def condition = new DuplicatedSearchExt.RuleCondition(fieldName, emptyPolicy, policy, distance)

        expect:
        condition.getFieldName() == fieldName
        condition.getEmptyPolicy() == emptyPolicy.toString()
        condition.getPolicy().equals(policy?.name() ?: "")
        condition.getDistance() == expectedDistance
        condition.ignoreEmpty() == (emptyPolicy == IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY)

        where:
        fieldName | emptyPolicy                                | policy                            | distance | expectedDistance
        "field1"  | IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY | IDuplicatedSearch.Policy.PRECISE  | "300"    | "300"
        "field2"  | IDuplicatedSearch.EmptyPolicy.MATCH_EMPTY  | IDuplicatedSearch.Policy.DISTANCE | "500"    | "500"
        "field3"  | IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY | null                              | "0"      | "0"
        "field4"  | IDuplicatedSearch.EmptyPolicy.MATCH_EMPTY  | IDuplicatedSearch.Policy.FUZZY    | ""       | "0"
        "field5"  | IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY | IDuplicatedSearch.Policy.PRECISE  | null     | "0"
    }

    def "test RuleCondition from"() {
        given:
        def condition = Mock(IDuplicatedSearch.Condition)
        condition.getFieldName() >> fieldName
        condition.getEmptyPolicy() >> emptyPolicy
        condition.getFieldValue() >> policy
        condition.getRange() >> distance

        when:
        def ruleCondition = DuplicatedSearchExt.RuleCondition.from(condition)

        then:
        ruleCondition.getFieldName() == fieldName
        ruleCondition.getEmptyPolicy() == emptyPolicy.toString()
        ruleCondition.getPolicy().equals(policy?.name() ?: "")
        ruleCondition.getDistance() == expectedDistance

        where:
        fieldName | emptyPolicy                                | policy                            | distance | expectedDistance
        "field1"  | IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY | IDuplicatedSearch.Policy.PRECISE  | "300"    | "300"
        "field2"  | IDuplicatedSearch.EmptyPolicy.MATCH_EMPTY  | IDuplicatedSearch.Policy.DISTANCE | "500"    | "500"
        "field3"  | IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY | null                              | "0"      | "0"
        "field4"  | IDuplicatedSearch.EmptyPolicy.MATCH_EMPTY  | IDuplicatedSearch.Policy.FUZZY    | ""       | "0"
        "field5"  | IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY | IDuplicatedSearch.Policy.PRECISE  | null     | "0"
    }

    def "test isNeedDuplicatedV2"() {
        given:
        UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_SUPPORT_DISTANCE, tenantId) >> isGrayDuplicated
        UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_NOT_USE_REDIS, tenantId) >> isGrayDuplicated

        def duplicatedSearch = new IDuplicatedSearch()
        duplicatedSearch.setTenantId(tenantId)
        def useableRules = new IDuplicatedSearch.RulesDef()
        duplicatedSearch.setUseableRules(useableRules)

        def rules = []
        if (hasDistancePolicy) {
            def rule = new IDuplicatedSearch.Rule()
            def conditions = []
            def condition = new IDuplicatedSearch.Condition()
            condition.setFieldValue(IDuplicatedSearch.Policy.DISTANCE)
            conditions.add(condition)
            rule.setConditions(conditions)
            rules.add(rule)
        } else {
            def rule = new IDuplicatedSearch.Rule()
            def conditions = []
            def condition = new IDuplicatedSearch.Condition()
            condition.setFieldValue(IDuplicatedSearch.Policy.PRECISE)
            conditions.add(condition)
            rule.setConditions(conditions)
            rules.add(rule)
        }
        useableRules.setRules(rules)

        def duplicatedSearchExt = DuplicatedSearchExt.of(duplicatedSearch)

        when:
        def result = duplicatedSearchExt.isNeedDuplicatedV2()

        then:
        result == expected

        where:
        tenantId | isGrayDuplicated | hasDistancePolicy | expected
        "74255"  | true             | true              | false
        "74255"  | false            | true              | true
        "74255"  | true             | false             | false
        "74255"  | false            | false             | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试DuplicatedSearchExt的of静态工厂方法
     */
    def "ofTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.getRuleApiName() >> "test_duplicate_search"
        duplicatedSearch.isEnable() >> true

        when:
        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        then:
        ext != null
        ext.getDuplicatedSearch() == duplicatedSearch
        ext.getRuleApiName() == "test_duplicate_search"
        ext.isEnable() == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isSupportImport静态方法
     */
    @Unroll
    def "isSupportImportTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = mockDuplicatedSearch
        if (duplicatedSearch) {
            duplicatedSearch.setEnable(isEnable)
            duplicatedSearch.setSupportImport(supportImport)
            if (isEnable && supportImport) {
                duplicatedSearch.setUseableRules(hasRules ? Mock(IDuplicatedSearch.RulesDef) : null)
            }
        }

        when:
        boolean result = DuplicatedSearchExt.isSupportImport(duplicatedSearch)

        then:
        result == expectedResult

        where:
        mockDuplicatedSearch    | isEnable | supportImport | hasRules | expectedResult
        new IDuplicatedSearch() | true     | true          | true     | true
        new IDuplicatedSearch() | false    | true          | true     | false
        new IDuplicatedSearch() | true     | false         | true     | false
        new IDuplicatedSearch() | true     | true          | false    | false
        null                    | false    | false         | false    | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isEffective静态方法
     */
    @Unroll
    def "isEffectiveTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = mockDuplicatedSearch
        if (duplicatedSearch) {
            duplicatedSearch.setEffective(isEffective)
        }

        when:
        boolean result = DuplicatedSearchExt.isEffective(duplicatedSearch)

        then:
        result == expectedResult

        where:
        mockDuplicatedSearch    | isEffective | expectedResult
        new IDuplicatedSearch() | true        | true
        new IDuplicatedSearch() | false       | false
        null                    | false       | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isEnableDuplicate静态方法
     */
    @Unroll
    def "isEnableDuplicateStaticTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = mockDuplicatedSearch
        if (duplicatedSearch) {
            duplicatedSearch.setEnable(isEnable)
        }

        when:
        boolean result = DuplicatedSearchExt.isEnableDuplicate(duplicatedSearch)

        then:
        result == expectedResult

        where:
        mockDuplicatedSearch    | isEnable | expectedResult
        new IDuplicatedSearch() | true     | true
        new IDuplicatedSearch() | false    | false
        null                    | false    | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试generateDuplicateSearchData方法
     */
    def "generateDuplicateSearchDataTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.isEnable() >> true

        IDuplicatedSearch.RulesDef rules = Mock(IDuplicatedSearch.RulesDef)
        IDuplicatedSearch.Rule rule = Mock(IDuplicatedSearch.Rule)
        IDuplicatedSearch.Condition condition = Mock(IDuplicatedSearch.Condition)

        condition.getFieldName() >> "name"
        condition.getFieldValue() >> IDuplicatedSearch.Policy.PRECISE
        rule.getConditions() >> [condition]
        rules.getRules() >> [rule]
        duplicatedSearch.getUseableRules() >> rules

        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        List<IObjectData> dataList = [
                new ObjectData([_id: "1", name: "张三"]),
                new ObjectData([_id: "2", name: "李四"]),
                new ObjectData([_id: "3", name: "张三"])
        ]

        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("TestObj")
        describe.setTenantId("test_tenant")

        when:
        Map<String, Set<String>> result = ext.generateDuplicateSearchData(dataList, describe)

        then:
        result != null
        result.size() >= 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试generateRedissonDuplicateSearchKey方法
     */
    def "generateRedissonDuplicateSearchKeyTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.isEnable() >> true
        duplicatedSearch.getVersion() >> 1

        IDuplicatedSearch.RulesDef rules = Mock(IDuplicatedSearch.RulesDef)
        IDuplicatedSearch.Rule rule = Mock(IDuplicatedSearch.Rule)
        IDuplicatedSearch.Condition condition = Mock(IDuplicatedSearch.Condition)

        condition.getFieldName() >> "name"
        rule.getConditions() >> [condition]
        rules.getRules() >> [rule]
        duplicatedSearch.getUseableRules() >> rules

        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        IObjectData objectData = new ObjectData([_id: "1", name: "张三"])
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("TestObj")
        describe.setTenantId("test_tenant")

        when:
        Set<String> result = ext.generateRedissonDuplicateSearchKey(objectData, describe)

        then:
        result != null
        result.size() >= 0
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSearchKey静态方法
     */
    def "getSearchKeyStaticTestValid"() {
        given:
        IObjectData objectData = new ObjectData([name: "张三", email: "<EMAIL>"])
        String tenantId = "test_tenant"
        String describeApiName = "TestObj"
        String fieldName = "name"
        int version = 1

        when:
        String result = DuplicatedSearchExt.getSearchKey(objectData, tenantId, describeApiName, fieldName, version)

        then:
        result != null
        result.contains(DuplicatedSearchExt.DUPLICATED_SEARCH_DATA)
        result.contains(tenantId)
        result.contains(describeApiName)
        result.contains(fieldName)
        result.contains("张三")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getSearchKey静态方法处理空值
     */
    def "getSearchKeyStaticTestEmpty"() {
        given:
        IObjectData objectData = new ObjectData([name: null])
        String tenantId = "test_tenant"
        String describeApiName = "TestObj"
        String fieldName = "name"
        int version = 1

        when:
        String result = DuplicatedSearchExt.getSearchKey(objectData, tenantId, describeApiName, fieldName, version)

        then:
        result == ""
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRedisKey方法
     */
    def "getRedisKeyTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.isEnable() >> true

        IDuplicatedSearch.RulesDef rules = Mock(IDuplicatedSearch.RulesDef)
        IDuplicatedSearch.Rule rule = Mock(IDuplicatedSearch.Rule)
        IDuplicatedSearch.Condition condition = Mock(IDuplicatedSearch.Condition)

        condition.getFieldName() >> "name"
        condition.getFieldValue() >> IDuplicatedSearch.Policy.PRECISE
        rule.getConditions() >> [condition]
        rules.getRules() >> [rule]
        duplicatedSearch.getUseableRules() >> rules

        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        IObjectData objectData = new ObjectData([name: "张三"])
        IObjectDescribe describe = new ObjectDescribe()
        describe.setApiName("TestObj")
        String tenantId = "test_tenant"

        when:
        Set<String> result = ext.getRedisKey(describe, objectData, tenantId)

        then:
        result != null
        result.size() >= 0
        result.every { it.startsWith(DuplicatedSearchExt.DUPLICATED_SEARCH_LOCK) }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRuleFieldNameByPrecise方法
     */
    def "getRuleFieldNameByPreciseTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.isEnable() >> true

        IDuplicatedSearch.RulesDef rules = Mock(IDuplicatedSearch.RulesDef)
        IDuplicatedSearch.Rule rule1 = Mock(IDuplicatedSearch.Rule)
        IDuplicatedSearch.Rule rule2 = Mock(IDuplicatedSearch.Rule)

        IDuplicatedSearch.Condition condition1 = Mock(IDuplicatedSearch.Condition)
        IDuplicatedSearch.Condition condition2 = Mock(IDuplicatedSearch.Condition)
        IDuplicatedSearch.Condition condition3 = Mock(IDuplicatedSearch.Condition)

        condition1.getFieldName() >> "name"
        condition1.getFieldValue() >> IDuplicatedSearch.Policy.PRECISE
        condition2.getFieldName() >> "email"
        condition2.getFieldValue() >> IDuplicatedSearch.Policy.PRECISE
        condition3.getFieldName() >> "phone"
        condition3.getFieldValue() >> IDuplicatedSearch.Policy.FUZZY // 非精确匹配

        rule1.getConditions() >> [condition1, condition2]
        rule2.getConditions() >> [condition3] // 包含模糊匹配，应被过滤
        rules.getRules() >> [rule1, rule2]
        duplicatedSearch.getUseableRules() >> rules

        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        when:
        Set<List<String>> result = ext.getRuleFieldNameByPrecise()

        then:
        result != null
        result.size() == 1 // 只有rule1符合精确匹配条件
        result.first().containsAll(["name", "email"])
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRuleFieldNames方法
     */
    def "getRuleFieldNamesTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.isEnable() >> true

        IDuplicatedSearch.RulesDef rules = Mock(IDuplicatedSearch.RulesDef)
        IDuplicatedSearch.Rule rule = Mock(IDuplicatedSearch.Rule)
        IDuplicatedSearch.Condition condition1 = Mock(IDuplicatedSearch.Condition)
        IDuplicatedSearch.Condition condition2 = Mock(IDuplicatedSearch.Condition)

        condition1.getFieldName() >> "name"
        condition2.getFieldName() >> "email"
        rule.getConditions() >> [condition1, condition2]
        rules.getRules() >> [rule]
        duplicatedSearch.getUseableRules() >> rules

        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        when:
        Set<List<String>> result = ext.getRuleFieldNames()

        then:
        result != null
        result.size() == 1
        result.first().containsAll(["name", "email"])
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试machFuzzyRule方法
     */
    @Unroll
    def "machFuzzyRuleTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.isEnable() >> isEnable

        if (isEnable) {
            IDuplicatedSearch.RulesDef rules = Mock(IDuplicatedSearch.RulesDef)
            IDuplicatedSearch.Rule rule = Mock(IDuplicatedSearch.Rule)
            IDuplicatedSearch.Condition condition = Mock(IDuplicatedSearch.Condition)

            condition.getFieldValue() >> policy
            rule.getConditions() >> [condition]
            rules.getRules() >> [rule]
            duplicatedSearch.getUseableRules() >> rules
        }

        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        when:
        boolean result = ext.machFuzzyRule()

        then:
        result == expectedResult

        where:
        isEnable | policy                           | expectedResult
        true     | IDuplicatedSearch.Policy.FUZZY   | true
        true     | IDuplicatedSearch.Policy.PRECISE | false
        false    | IDuplicatedSearch.Policy.FUZZY   | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isEnableDuplicate实例方法
     */
    def "isEnableDuplicateInstanceTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.isEnable() >> true

        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        when:
        boolean result = ext.isEnableDuplicate()

        then:
        result == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isEnableRelatedDuplicate方法
     */
    def "isEnableRelatedDuplicateTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.isEnable() >> false

        IDuplicatedSearch.RulesDef rules = Mock(IDuplicatedSearch.RulesDef)
        IDuplicatedSearch.RelatedDescribe relatedDescribe = Mock(IDuplicatedSearch.RelatedDescribe)
        relatedDescribe.isEnable() >> true

        rules.getRelatedDescribes() >> [relatedDescribe]
        duplicatedSearch.getUseableRules() >> rules

        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        when:
        boolean result = ext.isEnableRelatedDuplicate()

        then:
        result == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试dataContainsDuplicatedField方法
     */
    def "dataContainsDuplicatedFieldTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.isEnable() >> true

        IDuplicatedSearch.RulesDef rules = Mock(IDuplicatedSearch.RulesDef)
        IDuplicatedSearch.Rule rule = Mock(IDuplicatedSearch.Rule)
        IDuplicatedSearch.Condition condition = Mock(IDuplicatedSearch.Condition)

        condition.getFieldName() >> "name"
        condition.getFieldValue() >> IDuplicatedSearch.Policy.PRECISE
        rule.getConditions() >> [condition]
        rules.getRules() >> [rule]
        duplicatedSearch.getUseableRules() >> rules

        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        IObjectData objectData = new ObjectData([name: "张三", email: "<EMAIL>"])

        when:
        boolean result = ext.dataContainsDuplicatedField(objectData)

        then:
        result == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试dataContainsDuplicatedField方法不包含字段
     */
    def "dataContainsDuplicatedFieldTestNotContains"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.isEnable() >> true

        IDuplicatedSearch.RulesDef rules = Mock(IDuplicatedSearch.RulesDef)
        IDuplicatedSearch.Rule rule = Mock(IDuplicatedSearch.Rule)
        IDuplicatedSearch.Condition condition = Mock(IDuplicatedSearch.Condition)

        condition.getFieldName() >> "phone"
        condition.getFieldValue() >> IDuplicatedSearch.Policy.PRECISE
        rule.getConditions() >> [condition]
        rules.getRules() >> [rule]
        duplicatedSearch.getUseableRules() >> rules

        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        IObjectData objectData = new ObjectData([name: "张三", email: "<EMAIL>"])

        when:
        boolean result = ext.dataContainsDuplicatedField(objectData)

        then:
        result == false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试subListDuplicateRuleInfoByRuleApiName静态方法
     */
    def "subListDuplicateRuleInfoByRuleApiNameTestValid"() {
        given:
        List<IDuplicatedSearch> duplicatedSearchList = [
                createMockDuplicatedSearch("rule1"),
                createMockDuplicatedSearch("rule2"),
                createMockDuplicatedSearch("rule3")
        ]
        String duplicateRuleApiName = "rule2"

        when:
        List<IDuplicatedSearch> result = DuplicatedSearchExt.subListDuplicateRuleInfoByRuleApiName(duplicateRuleApiName, duplicatedSearchList)

        then:
        result != null
        result.size() == 1
        result.get(0).getRuleApiName() == "rule3"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试subListDuplicateRuleInfoByRuleApiName找不到规则
     */
    def "subListDuplicateRuleInfoByRuleApiNameTestNotFound"() {
        given:
        List<IDuplicatedSearch> duplicatedSearchList = [
                createMockDuplicatedSearch("rule1"),
                createMockDuplicatedSearch("rule2")
        ]
        String duplicateRuleApiName = "rule_not_exist"

        when:
        List<IDuplicatedSearch> result = DuplicatedSearchExt.subListDuplicateRuleInfoByRuleApiName(duplicateRuleApiName, duplicatedSearchList)

        then:
        result == duplicatedSearchList
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getRealRuleApiName方法
     */
    @Unroll
    def "getRealRuleApiNameTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.getRuleApiName() >> ruleApiName

        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        when:
        String result = ext.getRealRuleApiName()

        then:
        result == expectedResult

        where:
        ruleApiName | expectedResult
        "test_rule" | "test_rule"
        null        | ""
        ""          | ""
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试isTool方法
     */
    @Unroll
    def "isToolTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.getType() >> type

        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        when:
        boolean result = ext.isTool()

        then:
        result == expectedResult

        where:
        type                        | expectedResult
        IDuplicatedSearch.Type.TOOL | true
        IDuplicatedSearch.Type.NEW  | false
        null                        | false
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试RuleCondition内部类
     */
    def "ruleConditionTestValid"() {
        given:
        String fieldName = "name"
        IDuplicatedSearch.EmptyPolicy emptyPolicy = IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY
        IDuplicatedSearch.Policy policy = IDuplicatedSearch.Policy.PRECISE
        String distance = "100"

        when:
        DuplicatedSearchExt.RuleCondition ruleCondition = new DuplicatedSearchExt.RuleCondition(fieldName, emptyPolicy, policy, distance)

        then:
        ruleCondition.getFieldName() == fieldName
        ruleCondition.getEmptyPolicy() == emptyPolicy.toString()
        ruleCondition.getPolicy() == policy.name()
        ruleCondition.getDistance() == distance
        ruleCondition.ignoreEmpty() == true
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试RuleCondition的from静态方法
     */
    def "ruleConditionFromTestValid"() {
        given:
        IDuplicatedSearch.Condition condition = Mock(IDuplicatedSearch.Condition)
        condition.getFieldName() >> "name"
        condition.getEmptyPolicy() >> IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY
        condition.getFieldValue() >> IDuplicatedSearch.Policy.PRECISE
        condition.getRange() >> "100"

        when:
        DuplicatedSearchExt.RuleCondition ruleCondition = DuplicatedSearchExt.RuleCondition.from(condition)

        then:
        ruleCondition.getFieldName() == "name"
        ruleCondition.getEmptyPolicy() == IDuplicatedSearch.EmptyPolicy.IGNORE_EMPTY.toString()
        ruleCondition.getPolicy() == IDuplicatedSearch.Policy.PRECISE.name()
        ruleCondition.getDistance() == "100"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试RuleCondition的compareTo方法
     */
    def "ruleConditionCompareToTestValid"() {
        given:
        DuplicatedSearchExt.RuleCondition condition1 = new DuplicatedSearchExt.RuleCondition("name", null, null, null)
        DuplicatedSearchExt.RuleCondition condition2 = new DuplicatedSearchExt.RuleCondition("email", null, null, null)
        DuplicatedSearchExt.RuleCondition condition3 = new DuplicatedSearchExt.RuleCondition("name", null, null, null)

        when:
        int result1 = condition1.compareTo(condition2)
        int result2 = condition2.compareTo(condition1)
        int result3 = condition1.compareTo(condition3)

        then:
        result1 > 0  // "name" > "email"
        result2 < 0  // "email" < "name"
        result3 == 0 // "name" == "name"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试wheresExt和dataScopeExt的getter和setter
     */
    def "wheresExtAndDataScopeExtTestValid"() {
        given:
        IDuplicatedSearch duplicatedSearch = Mock(IDuplicatedSearch)
        duplicatedSearch.getWheres() >> "original_wheres"
        duplicatedSearch.getDataScope() >> "original_data_scope"

        DuplicatedSearchExt ext = DuplicatedSearchExt.of(duplicatedSearch)

        when:
        ext.setWheresExt("custom_wheres")
        ext.setDataScopeExt("custom_data_scope")

        then:
        ext.getWheresExt() == "custom_wheres"
        ext.getDataScopeExt() == "custom_data_scope"

        when:
        ext.setWheresExt(null)
        ext.setDataScopeExt(null)

        then:
        ext.getWheresExt() == "original_wheres"
        ext.getDataScopeExt() == "original_data_scope"
    }



    private IDuplicatedSearch createMockDuplicatedSearch(String ruleApiName) {
        IDuplicatedSearch mock = Mock(IDuplicatedSearch)
        mock.getRuleApiName() >> ruleApiName
        return mock
    }
}
