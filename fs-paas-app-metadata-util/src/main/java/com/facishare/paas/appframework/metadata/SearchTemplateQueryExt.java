package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.appframework.metadata.search.ast.ExprToFilterGroupConverterService;
import com.facishare.paas.appframework.metadata.search.ast.Lexer;
import com.facishare.paas.appframework.metadata.search.ast.Parser;
import com.facishare.paas.appframework.metadata.search.ast.Token;
import com.facishare.paas.metadata.api.*;
import com.facishare.paas.metadata.api.condition.IConditions;
import com.facishare.paas.metadata.api.condition.TermConditions;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.search.*;
import com.facishare.paas.metadata.impl.search.*;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.trace.TraceContext;
import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import joptsimple.internal.Strings;
import lombok.Getter;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.bson.Document;

import java.util.*;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2017/11/10
 */
@Slf4j
public class SearchTemplateQueryExt {

    public final static int FUNCTION_RESULT_MAX_SIZE = 500;
    public final static int FUNCTION_RESULT_MAX_GRAY_SIZE = 800;
    public static final String SEARCH_SOURCE_DB = "db";
    public static final Set<String> AGG_FUNCTIONS = ImmutableSet.of(Count.TYPE_COUNT, Count.TYPE_SUM, Count.TYPE_MAX,
            Count.TYPE_MIN, "avg");


    public enum SearchSource {
        ES("es"), DB("db");

        private final String key;

        SearchSource(String key) {
            this.key = key;
        }

        public String getKey() {
            return key;
        }
    }

    @Delegate
    @Getter
    private ISearchTemplateQuery query;

    private SearchTemplateQueryExt(ISearchTemplateQuery query) {
        if (query instanceof SearchTemplateQueryExt) {
            this.query = ((SearchTemplateQueryExt) query).getQuery();
        } else {
            this.query = query;
        }
    }

    private void trimFieldValue(List<IFilter> list) {
        if (ObjectUtils.isNotEmpty(list)) {
            list.forEach(filter -> {
                if (filter != null && ObjectUtils.isNotEmpty(filter.getFieldValues())) {
                    // with any leading and character with a code greater than '\u0020' in the string removed
                    List<String> values = Lists.newArrayList();
                    for (Object value : filter.getFieldValues()) {
                        if (Objects.isNull(value)) {
                            continue;
                        }
                        values.add(String.valueOf(value).trim());
                    }
                    filter.setFieldValues(values);
                }
            });
        }
    }

    public void trimFieldValue() {
        final Optional<List<Wheres>> wheresOpt = Optional.ofNullable(query.getWheres());
        wheresOpt.ifPresent(wheres -> wheres.forEach(where -> trimFieldValue(where.getFilters())));
        final Optional<List<IFilter>> filtersOpt = Optional.ofNullable(query.getFilters());
        filtersOpt.ifPresent(this::trimFieldValue);


    }

    public static SearchTemplateQueryExt of(ISearchTemplateQuery query) {
        if (query == null) {
            return null;
        }
        return new SearchTemplateQueryExt(query);
    }

    public static <T extends Map> SearchTemplateQueryExt of(List<T> wheres) {
        Document document = new Document();
        document.put("wheres", wheres);
        return of(SearchTemplateQuery.fromJsonString(JSON.toJSONString(document)));
    }

    public static SearchTemplateQueryExt ofFilters(List<IFilter> filters) {
        Document document = new Document();
        document.put("filters", filters);
        return of(SearchTemplateQuery.fromJsonString(JSON.toJSONString(document)));
    }

    public static SearchTemplateQueryExt ofWheres(List<Wheres> wheres) {
        return ofWheresAndFilters(wheres, Lists.newArrayList());
    }

    public static SearchTemplateQueryExt ofWheresAndFilter(List<Wheres> wheres, IFilter filter) {
        return ofWheresAndFilters(wheres, Lists.newArrayList(filter));
    }

    public static SearchTemplateQueryExt ofWheresAndFilters(List<Wheres> wheres, List<IFilter> filters) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setWheres(wheres);
        SearchTemplateQueryExt searchTemplateQueryExt = SearchTemplateQueryExt.of(searchTemplateQuery);
        searchTemplateQueryExt.addFilters(filters);
        return searchTemplateQueryExt;
    }

    public static boolean validateQueryPattern(ISearchTemplateQuery query) {
        String pattern = query.getPattern();
        if (Strings.isNullOrEmpty(pattern)) {
            return true;
        }
        int num = pattern.toLowerCase().replace("or", "and").split("and").length;
        return num == query.getFilters().size();
    }

    public ISearchTemplateQuery copy() {
        return SearchTemplateQuery.fromJsonString(toJsonString());
    }

    public SearchTemplateQueryExt limit(int limit) {
        setLimit(limit);
        return this;
    }

    /**
     * 转成SFA专用的searchQuery，只支持精确查询和排序
     *
     * @return
     */
    public ISearchQuery toSearchQuery(String objectApiName) {
        ISearchQuery searchQuery = new com.facishare.paas.metadata.impl.search.SearchQuery();
        IConditions condition = new TermConditions();
        searchQuery.addCondition(condition);

        getFilters().stream()
                .filter(x -> x.getOperator().equals(Operator.EQ) || x.getOperator().equals(Operator.IS))
                .forEach(x -> convertFilter(condition, x, objectApiName));

        List<Order> orders = getOrders().stream()
                .map(x -> x.isAsc() ? Order.asc(x.getFieldName()) : Order.desc(x.getFieldName()))
                .collect(Collectors.toList());

        searchQuery.setOrders(orders);
        searchQuery.setOffset(getOffset());
        searchQuery.setLimit(getLimit());

        return searchQuery;
    }

    /**
     * 将Filter(元数据使用)转成Condition(SFA使用)，状态码99表示作废，0-98表示正常
     *
     * @param condition
     * @param filter
     * @param objectApiName
     */
    private void convertFilter(IConditions condition, IFilter filter, String objectApiName) {
        if (IObjectData.IS_DELETED.equals(filter.getFieldName())) {
            String statusFieldName = ObjectAPINameMapping.getStatusFieldAPIName(objectApiName);
            if ("1".equals(filter.getFieldValues().get(0))) {
                condition.addCondition(statusFieldName, "99");
            }
        } else {
            condition.addCondition(filter.getFieldName(), filter.getFieldValues().get(0));
        }

    }

    public List<Order> getConvertOrders() {
        if (CollectionUtils.empty(getOrders())) {
            return Lists.newArrayList();
        }
        return getOrders().stream().map(x -> new Order(x.getFieldName(), x.isAsc())).collect(Collectors.toList());
    }

    public void mergeWithSearchTemplate(ISearchTemplate searchTemplate) {
        if (searchTemplate == null || CollectionUtils.empty(searchTemplate.getFilters())) {
            return;
        }
        addFilters(searchTemplate.getFilters());
    }

    public void setDefaultOrderBy() {
        setOrders(Lists.newArrayList(new OrderBy(IObjectData.CREATE_TIME, true), new OrderBy(IObjectData.ID, true)));
    }

    public void setDefaultOrderBy(ObjectDescribeExt objectDescribe) {
        setDefaultOrderBy(objectDescribe, false);
    }

    public void setDefaultOrderBy(ObjectDescribeExt objectDescribe, boolean isRelatedPage) {
        //如果没有设置排序参数,则默认按照最新修改时间倒序。
        if (CollectionUtils.empty(getOrders())) {
            OrderBy orderBy = OrderByExt.defaultOrderBy(objectDescribe, isRelatedPage);

            resetOrderBy(Lists.newArrayList(orderBy));
        }

    }

    public void resetOrderBy(List<OrderBy> orders) {
        if (getOrders() == null) {
            setOrders(Lists.newArrayList());
        }
        getOrders().clear();
        if (CollectionUtils.notEmpty(orders)) {
            getOrders().addAll(orders);
        }
    }

    public void resetSearchTemplateQuery(String idOffset) {
        if (idOffset == null) {
            return;
        }
        Optional<IFilter> filter = getFilters().stream()
                .filter(x -> IObjectData.ID.equals(x.getFieldName())
                        && Operator.LT.equals(x.getOperator())
                        && IFieldDescribe.DEFINE_TYPE_SYSTEM.equals(x.getFieldDefineType()))
                .findFirst();
        if (filter.isPresent()) {
            filter.get().setFieldValues(Lists.newArrayList(idOffset));
        } else {
            addFilter(Operator.LT, IObjectData.ID, idOffset, IFieldDescribe.DEFINE_TYPE_SYSTEM);
        }
    }

    public boolean addDeletedFilterIfNoDeletedFilter() {
        List<IFilter> filters = query.getFilters();
        boolean result = false;
        if (CollectionUtils.notEmpty(filters)) {
            result = filters.stream().anyMatch(f -> IObjectData.IS_DELETED.equals(f.getFieldName()));
        }
        if (!result) {
            addIsDeletedFalseFilter();
        }
        return result;
    }

    public boolean addDeletedFilterIfNoDeletedFilter(boolean includeInvalid) {
        List<IFilter> filters = query.getFilters();
        boolean result = false;
        if (CollectionUtils.notEmpty(filters)) {
            result = filters.stream().anyMatch(f -> IObjectData.IS_DELETED.equals(f.getFieldName()));
        }
        if (!result) {
            if (includeInvalid) {
                addIsDeletedFilterIncludeInvalid();
            } else {
                addIsDeletedFalseFilter();
            }
        }
        return result;
    }

    public SearchTemplateQueryExt addIsDeletedFalseFilter() {
        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.IS_DELETED);
        filter.setOperator(Operator.EQ);
        filter.setFieldValues(Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue())));
        addFilters(Lists.newArrayList(filter));
        return this;
    }

    public SearchTemplateQueryExt addIsDeletedFilterIncludeInvalid() {
        IFilter filter = new Filter();
        filter.setFieldName(IObjectData.IS_DELETED);
        filter.setOperator(Operator.IN);
        filter.setFieldValues(Lists.newArrayList(String.valueOf(DELETE_STATUS.NORMAL.getValue()), String.valueOf(DELETE_STATUS.INVALID.getValue())));
        addFilters(Lists.newArrayList(filter));
        return this;
    }


    public void handleOutUserDataPrivilege(User user, OutDataPrivilege outDataPrivilege, ObjectDescribeExt objectDescribeExt,
                                           ISearchTemplate searchTemplateFromDB, boolean isSubCascadeConfig, boolean isIgnoreSceneFilter) {
        //找到场景筛选
        List<IFilter> fieldFilters = Lists.newArrayList(getFilters());
        Optional<OutUserDefaultSceneFilter> defaultSceneFilter = fieldFilters.stream()
                .map(OutUserDefaultSceneFilter::getDefaultSceneFilter)
                .filter(x -> x.isPresent()).findAny().orElse(Optional.empty());
        //过滤掉场景Filter
        if (defaultSceneFilter.isPresent()) {
            fieldFilters =
                    getFilters().stream().filter(x -> !OutUserDefaultSceneFilter.isDefaultSceneFilter(x)).collect(Collectors.toList());
        }
        this.resetFilters(fieldFilters);
        // 替换 currentUser
        replaceCurrentUser(user);

        SearchTemplateExt searchTemplateExt = SearchTemplateExt.of(searchTemplateFromDB);
        DataRightsParameter dataRightsParameter = searchTemplateExt.getDataRightsParameter(isSubCascadeConfig);
        dataRightsParameter.setLinkAppDataAuthRange(outDataPrivilege.getValue());
        setPermissionType(2);
        setDataRightsParameter(dataRightsParameter);
        objectDescribeExt.getMasterDetailFieldDescribe().ifPresent(it -> {
            dataRightsParameter.setIsDetailObject(true);
            dataRightsParameter.setMasterIdFieldApiName(it.getApiName());
            dataRightsParameter.setMasterObjectApiName(it.getTargetApiName());
        });

        if (!isIgnoreSceneFilter) {
            addFilters(searchTemplateExt.getFiltersWithoutDefaultScenePlaceholderFilter(user.getOutUserId()));
        }
    }

    private void handelSlaveFilterInOutDataPrivilege(ObjectDescribeExt objectDescribeExt, IFilter filter) {
        if (objectDescribeExt.isSlaveObject()) {
            filter.setValueType(7);
        }
    }

    public void handelRecordType(ISearchTemplate searchTemplate, ObjectDescribeExt describeExt, boolean isIgnoreSceneRecordType) {
        if (isIgnoreSceneRecordType) {
            return;
        }
        describeExt.getRecordTypeOption(searchTemplate.getRecordType())
                .filter(x -> filterNotHasRecordType(describeExt.getTenantId()))
                .ifPresent(x -> addFilter(Operator.EQ, ISearchTemplate.RECORD_TYPE, x.getApiName()));
        List<IFilter> filterList = getFilters().stream()
                .filter(filter -> ISearchTemplate.RECORD_TYPE.equals(filter.getFieldName()))
                .filter(filter -> CollectionUtils.notEmpty(filter.getFieldValues()))
                .collect(Collectors.toList());
        if (CollectionUtils.empty(filterList)) {
            return;
        }

        filterList.stream()
                .filter(filter -> filter.getFieldValues().stream()
                        .anyMatch(x -> !describeExt.getRecordTypeOption(x).isPresent()))
                .forEach(filter -> getFilters().removeIf(a -> FilterExt.equals(a, filter)));
    }

    private boolean filterNotHasRecordType(String tenantId) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.SEARCH_TEMPLATE_QUERY_RECORD_TYPE_GRAY, tenantId)) {
            return true;
        }
        return getFilters().stream().noneMatch(x -> ISearchTemplate.RECORD_TYPE.equals(x.getFieldName()));
    }

    public void handelOrders(ISearchTemplate searchTemplate, ObjectDescribeExt objectDescribe, boolean isRelatedPage) {
        if (CollectionUtils.empty(getOrders())) {
            if (SearchTemplateExt.DefaultScene.RECENT_VISIT.getApiName().equals(searchTemplate.getApiName())) {
                OrderBy orderBy = OrderByExt.orderByField(IObjectData.ID, false);
                resetOrderBy(Lists.newArrayList(orderBy));
            } else {
                List<OrderBy> orders = OrderByExt.getDefaultOrderByFromSearchTemplate(searchTemplate, objectDescribe, isRelatedPage);
                resetOrderBy(orders);
            }
        }
        // 过滤禁用、删除的字段
        List<OrderBy> orders = OrderByExt.filterField(getOrders(), objectDescribe);
        resetOrderBy(orders);
        // 设置默认排序
        setDefaultOrderBy(objectDescribe, isRelatedPage);
    }

    public void handelFilter(IObjectDescribe describe) {
        if (CollectionUtils.empty(getFilters()) && CollectionUtils.empty(getWheres())) {
            return;
        }
        List<IFilter> filters = FilterExt.handleFilter(getFilters(), describe);
        resetFilters(filters);
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.REMOVE_WHERES_UN_ACTIVE_FILTER_FIELD, describe.getTenantId())
                || UdobjGrayConfig.isAllow(UdobjGrayConfigKey.UN_REMOVE_WHERES_UN_ACTIVE_FILTER_FIELD_OBJECT, describe.getApiName())) {
            return;
        }
        List<Wheres> wheres = FilterExt.handleWheres(getWheres(), describe);
        setWheres(wheres);
    }

    public void searchInDB() {
        setSearchSource(SearchSource.DB.getKey());
    }

    public void searchInES() {
        setSearchSource(SearchSource.ES.getKey());
    }

    /**
     * 根据 dataRightsParameter 来判断当前是否为全部场景
     *
     * @return 是否为全部场景
     */
    public boolean isAllScene() {
        IDataRightsParameter dataRightsParameter = getDataRightsParameter();
        if (Objects.isNull(dataRightsParameter)) {
            return false;
        }
        return DefObjConstants.DATA_PRIVILEGE_ROLE_TYPE.OWNER.getValue().equals(dataRightsParameter.getRoleType())
                && "all".equals(dataRightsParameter.getSceneType());
    }

    enum OutUserDefaultSceneFilter {
        ALL(Operator.IN, "${current_user}||${current_user_subordinates}||${current_user_dept_users}||$" +
                "{current_user_shared_users}"),
        OWN_BY_ME(Operator.EQ, "${current_user}");

        OutUserDefaultSceneFilter(Operator operator, String value) {
            this.operator = operator;
            this.values = Lists.newArrayList(value);
        }

        private List<String> values;
        private Operator operator;

        public static Optional<OutUserDefaultSceneFilter> getDefaultSceneFilter(IFilter filter) {
            return Arrays.stream(OutUserDefaultSceneFilter.values()).filter(x -> filter.getOperator() == x.operator
                    && x.values.equals(filter.getFieldValues())).findAny();
        }

        public static boolean isDefaultSceneFilter(IFilter filter) {
            return getDefaultSceneFilter(filter).isPresent();
        }
    }

    public void handleDataPrivilege(User user, ISearchTemplate searchTemplate, ObjectDescribeExt objectDescribe,
                                    boolean isAdmin, boolean isSubCascadeConfig, boolean isIgnoreSceneFilter, boolean isViewAllFunPrivilege) {
        // 替换 筛选条件中的 currentUser
        replaceCurrentUser(user);
        SearchTemplateExt searchTemplateExt = SearchTemplateExt.of(searchTemplate);

        //筛选场景为全部时管理员或对象配置了「系统级查看全部」或者「对象级查看全部」的功能权限不过数据权限
        if (notNeedDataPrivilege(user, objectDescribe, searchTemplateExt, isAdmin, isViewAllFunPrivilege)) {
            if (!isIgnoreSceneFilter) {
                addFilters(searchTemplateExt.getFiltersWithoutDefaultScenePlaceholderFilter(user.getUserId()));
            }
            return;
        }
        DataRightsParameter dataRightsParameter = searchTemplateExt.getDataRightsParameter(isSubCascadeConfig);
        objectDescribe.getMasterDetailFieldDescribe().ifPresent(it -> {
            dataRightsParameter.setIsDetailObject(true);
            dataRightsParameter.setMasterIdFieldApiName(it.getApiName());
            dataRightsParameter.setMasterObjectApiName(it.getTargetApiName());
        });
        setPermissionType(1);
        setDataRightsParameter(dataRightsParameter);
        if (!isIgnoreSceneFilter) {
            addFilters(searchTemplateExt.getFiltersWithoutDefaultScenePlaceholderFilter(user.getUserId()));
        }
    }

    private boolean notNeedDataPrivilege(User user, ObjectDescribeExt objectDescribe, SearchTemplateExt searchTemplateExt, boolean isAdmin, boolean isViewAllFunPrivilege) {
        // 查询人员对象，CRM 管理员也需要走数据权限
        if (ObjectDescribeExt.PERSONNEL_OBJ_API_NAME.equals(objectDescribe.getApiName())) {
            return false;
        }
        // 灰度企业的流程待办场景不需要走数据权限
        if (AppFrameworkConfig.isGrayFlowTaskPassTemplateDataRightTenantId(user.getTenantId())
                && Utils.FLOW_TASK_OBJ_API_NAME.contains(objectDescribe.getApiName())
                && SearchTemplateExt.DefaultScene.PASS.equals(searchTemplateExt.getApiName())) {
            return true;
        }
        return searchTemplateExt.isAll() && (isAdmin || isViewAllFunPrivilege);
    }

    private void replaceCurrentUser(User user) {
        List<IFilter> filters = getFilters().stream()
                .peek(it -> FilterExt.of(it).replaceCurrentUser(user.getUserIdOrOutUserIdIfOutUser()))
                .collect(Collectors.toList());
        resetFilters(filters);
    }

    public Optional<IFilter> getChainCycleFilter() {
        List<Wheres> wheres = query.getWheres();
        if (CollectionUtils.empty(wheres)) {
            return Optional.empty();
        }
        return wheres.stream()
                .flatMap(where -> where.getFilters().stream()
                        .filter(filter -> FilterExt.of(filter).hasRelatedChainObjectVariableValueType()))
                .findFirst();
    }

    /**
     * 处理四角关系的lookup过滤条件
     *
     * @param objectData
     * @param masterData
     */
    public void handleWheresFilter(IObjectData objectData, IObjectData masterData, IObjectDescribe masterDescribe) {
        List<Wheres> wheres = query.getWheres();
        if (CollectionUtils.empty(wheres)) {
            return;
        }

        if (Objects.isNull(objectData)) {
            //移除所有多角关系过滤条件
            removeAllRelated(wheres);
            return;
        }

        wheres.forEach(where -> where.getFilters().stream()
                .filter(filter -> (FilterExt.of(filter).hasRefObjectVariableValueType()
                        || FilterExt.of(filter).hasLookupI18NVariableValueType()
                        || FilterExt.of(filter).hasSpecialRefObjectVariable())
                        && CollectionUtils.notEmpty(filter.getFieldValues()))
                .forEach(filter -> {
                    Object o = getFieldValue(objectData, masterData, filter, masterDescribe);
                    FilterExt filterExt = FilterExt.of(filter);
                    filterExt.setMasterFieldWhenHasSpecialRefObjectVariable();
                    filterExt.handleFilterValueReturnSkipData(o);
                }));
    }

    /**
     * 处理主对象和本对象筛选条件，替换其变量
     * <p>
     * 如果变量所对应的数据为空，则记录在errorMsgList，
     * 且关系中有一个匹配不到变量，则移除该wheres，
     * 或关系中有一个匹配不到该筛选项，则移除该where条件，执行或条件中的其他where条件
     *
     * @param objectData
     * @param masterData
     * @param masterDescribe
     * @param targetApiName
     */
    public boolean handleWheresFilterByObjVariable(ObjectDataExt objectData, IObjectData masterData, IObjectDescribe masterDescribe, String targetApiName) {
        List<Wheres> wheres = query.getWheres();
        if (CollectionUtils.empty(wheres)) {
            return false;
        }
        if (Objects.isNull(masterDescribe)) {
            List<IFilter> masterFilter = Lists.newArrayList();
            for (Wheres where : wheres) {
                masterFilter.addAll(where.getFilters().stream().filter(filter -> FilterExt.of(filter).hasMasterFieldVariableValueType()).collect(Collectors.toList()));
            }
            if (CollectionUtils.notEmpty(masterFilter) && Objects.nonNull(objectData)) {
                log.error("Filters that have master objects that are not detail objects ,tenantId:{}, describeApiName:{},targetApiName:{}", objectData.getTenantId(), objectData.getDescribeApiName(), targetApiName);
            }
            removeMasterObjVariableFilter(wheres);
        }
        for (Wheres where : wheres) {
            for (IFilter filter : where.getFilters()) {
                if (CollectionUtils.empty(filter.getFieldValues())
                        || !(FilterExt.of(filter).hasMasterFieldVariableValueType()
                        || FilterExt.of(filter).hasNativeObjectVariable())) {
                    continue;
                }
                Object value = getFieldValue(objectData, masterData, filter, masterDescribe);
                if (FilterExt.of(filter).handleFilterValueReturnSkipData(value) && (wheres.size() == 1)) {
                    return true;
                }
            }
        }
        return false;
    }


    /**
     * @param objectData
     * @param masterData
     * @param filter
     * @param masterDescribe
     * @return
     */
    private Object getFieldValue(IObjectData objectData, IObjectData masterData, IFilter filter,
                                 IObjectDescribe masterDescribe) {
        Tuple<Boolean, String> tuple = FilterExt.of(filter).getVariableNameInValuesAndIsInMaster();
        if (Boolean.FALSE.equals(tuple.getKey())) {
            return Objects.isNull(objectData) ? null : objectData.get(tuple.getValue());
        }
        if (Objects.nonNull(masterData)) {
            if (Objects.nonNull(masterData.get(tuple.getValue())) || FilterExt.of(filter).hasMasterFieldVariableValueType()) {
                return masterData.get(tuple.getValue());
            }
        }
        if (FilterExt.of(filter).hasMasterFieldVariableValueType()) {
            return null;
        }
        log.warn("field value is empty, masterApiName=>{}, fieldApiName=>{}", masterDescribe.getApiName(), tuple.getValue());
        throw new ValidateException(I18N.text(I18NKey.SELECT_FIELD_FIRST, masterDescribe.getDisplayName(),
                ObjectDescribeExt.of(masterDescribe).getFieldLabelByName(tuple.getValue())));
    }

    /**
     * 校验lookup数据时，处理 lookup 过滤条件
     * <p>
     * 1.忽略四角、五角关系
     * 2.三角关系和四角、五角关系为OR关系时，需要忽略三角关系
     * 3.处理本对象变量作为筛选条件
     *
     * @param objectData
     */
    public void handleWheresFilterWithLookupRelation(IObjectData objectData) {
        List<Wheres> wheres = query.getWheres();
        if (CollectionUtils.empty(wheres)) {
            return;
        }
        if (Objects.isNull(objectData)) {
            // 删掉所有lookup多角关系（三角、四角、五角）
            removeAllRelated(wheres);
            removeSpecialRefObjectVariableFilter(wheres);
            return;
        }
        // 三角和四角、五角之间是或的关系时,删掉所有lookup多角关系（三角、四角、五角）
        if (filterRelationIsOr(wheres)) {
            log.debug("filter relation is or, apiName=>{}, wheres=>{}", objectData.getDescribeApiName(),
                    JSON.toJSONString(wheres));
            removeRelatedObjectFilter(wheres);
            return;
        }
        // 只删掉四角和五角关系
        removePolygonRelatedObjectFilter(wheres);
        removeSpecialRefObjectVariableFilter(wheres);
        // 处理三角关系
        wheres.forEach(where -> where.getFilters().stream()
                .filter(filter -> (FilterExt.of(filter).hasRefObjectVariableValueType() || FilterExt.of(filter).hasLookupI18NVariableValueType())
                        && CollectionUtils.notEmpty(filter.getFieldValues()))
                .forEach(filter -> {
                    // 替换表达式的值
                    Object o = objectData.get(FilterExt.of(filter).getVariableNameInValues());
                    FilterExt.of(filter).handleFilterValueReturnSkipData(o);
                }));
    }

    /**
     * 三角和四角、五角之间是或的关系
     *
     * @param wheres
     * @return
     */
    private boolean filterRelationIsOr(List<Wheres> wheres) {
        Deque<Wheres> whereDeque = Lists.newLinkedList(wheres);
        return wheres.stream().anyMatch(where -> {
            whereDeque.poll();
            return (hasTriangleRelation(where) && whereDeque.stream().anyMatch(x -> hasRectangleRelation(x) || hasPentagonRelation(x))) ||
                    ((hasRectangleRelation(where) || hasPentagonRelation(where)) && whereDeque.stream().anyMatch(this::hasTriangleRelation));
        });
    }

    private void handleMasterAndNativeObjWheresIsOr(List<Wheres> wheres) {
        Deque<Wheres> whereDeque = Lists.newLinkedList(wheres);
        wheres.forEach(where -> {
            whereDeque.poll();
            if (hasMasterAndNativeVariable(where)) {
                List<IFilter> filters = where.getFilters();
                if (CollectionUtils.empty(filters)) {
                    return;
                }
                List<IFilter> masterAndNativeFieldFilters = filters.stream()
                        .filter(filter -> FilterExt.of(filter).hasMasterFieldVariableValueType() || FilterExt.of(filter).hasNativeObjectVariable())
                        .collect(Collectors.toList());
                if (masterAndNativeFieldFilters.size() == filters.size()) {

                }
                filters.removeIf(filter -> FilterExt.of(filter).hasMasterFieldVariableValueType() || FilterExt.of(filter).hasNativeObjectVariable());
            }
        });
    }

    private static boolean hasMasterAndNativeVariable(Wheres where) {
        return where.getFilters().stream().anyMatch(filter -> FilterExt.of(filter).hasNativeObjectVariable()
                || FilterExt.of(filter).hasMasterFieldVariableValueType());
    }

    /**
     * 支持lookup过滤，替换过滤条件中的一些表达式为固定值
     *
     * @param objectData
     */
    public void handleWheresFilter(IObjectData objectData) {
        List<Wheres> wheres = query.getWheres();
        if (CollectionUtils.empty(wheres)) {
            return;
        }

        if (Objects.isNull(objectData)) {
            //移除所有多角关系过滤条件
            removeAllRelated(wheres);
            return;
        }

        for (Wheres where : wheres) {
            List<IFilter> filters = where.getFilters();
            if (CollectionUtils.empty(filters)) {
                continue;
            }

            for (IFilter filter : filters) {
                FilterExt filterExt = FilterExt.of(filter);
                if ((!filterExt.hasRefObjectVariableValueType()
                        && !filterExt.hasLookupI18NVariableValueType()
                        && !filterExt.hasSpecialRefObjectVariable())
                        || CollectionUtils.empty(filter.getFieldValues())) {
                    continue;
                }

                //替换表达式的值
                String expression = filterExt.getVariableNameInValues();
                Object value = objectData.get(expression);
                filterExt.setMasterFieldWhenHasSpecialRefObjectVariable();
                filterExt.handleFilterValueReturnSkipData(value);
            }
        }

    }

    public void handleCountFieldFilter(IObjectData masterData, IObjectDescribe detailDescribe, String refFieldName) {
        if (Strings.isNullOrEmpty(refFieldName)) {
            refFieldName = ObjectDescribeExt.of(detailDescribe).getMasterDetailField().map(x -> x.getApiName()).orElse(null);
        }
        IFieldDescribe refField = detailDescribe.getFieldDescribe(refFieldName);
        if (Objects.isNull(refField)) {
            addFilter(Operator.EQ, refFieldName, masterData.getId());
            return;
        }
        FieldDescribeExt fieldExt = FieldDescribeExt.of(refField);
        if (fieldExt.isWhatField()) {
            What what = (What) refField;
            addFilter(Operator.EQ, what.getIdFieldApiName(), masterData.getId());
            addFilter(Operator.EQ, what.getApiNameFieldApiName(), masterData.getDescribeApiName());
        } else if (fieldExt.isWhatListField()) {
            WhatList whatList = (WhatList) refField;
            addFilter(Operator.EQ, whatList.getIdFieldApiName(), Lists.newArrayList(IObjectData.ID, masterData.getId()));
            addFilter(Operator.IN, whatList.getApiNameFieldApiName(), masterData.getDescribeApiName());
        } else {
            addFilter(Operator.EQ, refFieldName, masterData.getId());
        }
    }

    /**
     * 三角关系过滤条件
     * 1. valueType=2
     * 2. fieldValue不包含'.'
     *
     * @param where
     * @return
     */
    private boolean hasTriangleRelation(Wheres where) {
        return where.getFilters().stream().anyMatch(x -> FilterExt.of(x).isTriangleRelation());
    }

    /**
     * 四角关系
     *
     * @param where
     * @return
     */
    private boolean hasRectangleRelation(Wheres where) {
        return where.getFilters().stream().anyMatch(x -> FilterExt.of(x).isRectangleRelation());
    }

    /**
     * 五角关系
     *
     * @param where
     * @return
     */
    private boolean hasPentagonRelation(Wheres where) {
        return where.getFilters().stream().anyMatch(x -> FilterExt.of(x).hasRelatedChainObjectVariableValueType());
    }

    public boolean isRectangleOrPentagonRelation() {
        return getWheres().stream()
                .anyMatch(where -> where.getFilters().stream()
                        .anyMatch(x -> FilterExt.of(x).hasRelatedChainObjectVariableValueType()
                                || FilterExt.of(x).isRectangleRelation()));
    }

    /**
     * 删除所有多角关系的lookup过滤条件
     *
     * @param wheres
     */
    public void removeRelatedObjectFilter(List<Wheres> wheres) {
        wheres.removeIf(where -> {
            List<IFilter> filters = where.getFilters();
            if (CollectionUtils.empty(filters)) {
                return false;
            }
            filters.removeIf(filter -> FilterExt.of(filter).hasRelatedChainObjectVariableValueType() ||
                    FilterExt.of(filter).isTriangleRelation() || FilterExt.of(filter).isRectangleRelation());
            return CollectionUtils.empty(filters);
        });
    }

    public void removeAllRelated(List<Wheres> wheres) {
        wheres.removeIf(where -> {
            List<IFilter> filters = where.getFilters();
            if (CollectionUtils.empty(filters)) {
                return true;
            }
            filters.removeIf(filter -> FilterExt.of(filter).hasRelatedChainObjectVariableValueType() ||
                    FilterExt.of(filter).hasRefObjectVariableValueType() ||
                    FilterExt.of(filter).hasLookupI18NVariableValueType() ||
                    FilterExt.of(filter).hasSpecialRefObjectVariable());
            return CollectionUtils.empty(filters);
        });
    }

    /**
     * 移除本对象和主对象过滤条件
     *
     * @param wheres
     */
    public static void removeMasterAndNativeObjVariableFilter(List<Wheres> wheres) {
        if (needClearWheres(wheres, filter -> FilterExt.of(filter).hasMasterFieldVariableValueType() || FilterExt.of(filter).hasNativeObjectVariable())) {
            wheres.clear();
            return;
        }
        wheres.removeIf(where -> {
            List<IFilter> filters = where.getFilters();
            if (CollectionUtils.empty(filters)) {
                return true;
            }
            filters.removeIf(filter -> FilterExt.of(filter).hasMasterFieldVariableValueType()
                    || FilterExt.of(filter).hasNativeObjectVariable());
            return CollectionUtils.empty(filters);
        });
    }

    private static boolean needClearWheres(List<Wheres> wheres, Predicate<IFilter> filterPredicate) {
        if (!WheresExt.multiWheres(wheres)) {
            return false;
        }
        return wheres.stream().anyMatch(where -> {
            if (hasMasterAndNativeVariable(where)) {
                List<IFilter> filters = where.getFilters();
                if (CollectionUtils.empty(filters)) {
                    return false;
                }
                List<IFilter> masterAndNativeFieldFilters = filters.stream()
                        .filter(filterPredicate)
                        .collect(Collectors.toList());
                return masterAndNativeFieldFilters.size() == filters.size();
            }
            return false;
        });
    }


    public void removeMasterObjVariableFilter(List<Wheres> wheres) {
        wheres.removeIf(where -> {
            List<IFilter> filters = where.getFilters();
            if (CollectionUtils.empty(filters)) {
                return true;
            }
            filters.removeIf(filter -> FilterExt.of(filter).hasMasterFieldVariableValueType());
            return CollectionUtils.empty(filters);
        });
    }

    public void removeNativeObjVariableFilter(List<Wheres> wheres) {
        wheres.removeIf(where -> {
            List<IFilter> filters = where.getFilters();
            if (CollectionUtils.empty(filters)) {
                return true;
            }
            filters.removeIf(filter -> FilterExt.of(filter).hasNativeObjectVariable());
            return CollectionUtils.empty(filters);
        });
    }

    /**
     * 删除所有五角和四角的filter
     *
     * @param wheres
     * @return
     */
    private void removePolygonRelatedObjectFilter(List<Wheres> wheres) {
        wheres.removeIf(where -> {
            List<IFilter> filters = where.getFilters();
            if (CollectionUtils.empty(filters)) {
                return false;
            }
            filters.removeIf(filter -> FilterExt.of(filter).hasRelatedChainObjectVariableValueType() ||
                    FilterExt.of(filter).isRectangleRelation());
            return CollectionUtils.empty(filters);
        });
    }

    /**
     * 删除 valueType 为 {@link FilterExt.FilterValueTypes.SPECIAL_REF_OBJECT_VARIABLE} 特殊四角关系
     *
     * @param wheres
     */
    private void removeSpecialRefObjectVariableFilter(List<Wheres> wheres) {
        wheres.removeIf(where -> {
            List<IFilter> filters = where.getFilters();
            if (CollectionUtils.empty(filters)) {
                return false;
            }
            filters.removeIf(filter -> FilterExt.of(filter).hasSpecialRefObjectVariable());
            return CollectionUtils.empty(filters);
        });
    }

    /**
     * 删除所有五角的filter
     */
    public void removePentagonRelationObjectFilter() {
        List<Wheres> wheres = getWheres();
        wheres.removeIf(where -> {
            List<IFilter> filters = where.getFilters();
            if (CollectionUtils.empty(filters)) {
                return false;
            }
            filters.removeIf(filter -> FilterExt.of(filter).hasRelatedChainObjectVariableValueType());
            return CollectionUtils.empty(filters);
        });
    }

    public void addFilter(Operator operator, String fieldName, String fieldValue, String fieldDefineType) {
        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setFieldValues(Lists.newArrayList(fieldValue));
        filter.setOperator(operator);
        filter.setFieldDefineType(fieldDefineType);
        query.getFilters().add(filter);
    }

    public SearchTemplateQueryExt addFilter(Operator operator, String fieldName, String fieldValue) {
        return addFilter(operator, fieldName, Lists.newArrayList(fieldValue));
    }

    public SearchTemplateQueryExt addFilter(Operator operator, String fieldName, List<String> fieldValues) {
        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setFieldValues(fieldValues);
        filter.setOperator(operator);
        //主对象或lookup对象的字段
        if (StringUtils.contains(fieldName, ".")) {
            filter.setIsMasterField(true);
        }
        query.getFilters().add(filter);
        return this;
    }

    public void addFilter(Operator operator, String fieldName, List<String> fieldValues, Integer valueType) {
        IFilter filter = new Filter();
        filter.setFieldName(fieldName);
        filter.setFieldValues(fieldValues);
        filter.setOperator(operator);
        filter.setValueType(valueType);
        query.getFilters().add(filter);
    }

    public SearchTemplateQueryExt addWheres(Wheres wheres) {
        getWheres().add(wheres);
        return this;
    }

    public static void patch(ISearchTemplateQuery query) {
        if (Objects.isNull(query)) {
            query = new SearchTemplateQuery();
        }
        if (Objects.isNull(query.getFilters())) {
            query.setFilters(Lists.newArrayList());
        }
    }

    public IRelatedListQuery buildRelatedListQuery(String tenantId, IObjectDescribe masterDescribe,
                                                   List<IObjectDescribe> relatedDescribes, String masterDataId) {
        IRelatedListQuery searchQuery = new RelatedListQuery();
        searchQuery.setTenantId(tenantId);
        searchQuery.setObjectDescribeAPIName(masterDescribe.getApiName());
        searchQuery.setDataId(masterDataId);

        List<RelatedObjectDescribeStructure> relatedObjectDescribeStructureList = ObjectDescribeExt.of(masterDescribe)
                .getDetailObjectDescribeStructures(relatedDescribes);
        List<IRelatedListQuery.RelatedObjectQuery> list = Lists.newArrayList();
        //遍历相关表
        for (RelatedObjectDescribeStructure associatedDescribe : relatedObjectDescribeStructureList) {
            IRelatedListQuery.RelatedObjectQuery objectQuery = buildRelatedObjectQuery(associatedDescribe);
            list.add(objectQuery);
        }

        searchQuery.setRelatedObjectQueryList(list);
        return searchQuery;
    }

    public IRelatedListQuery.RelatedObjectQuery buildRelatedObjectQuery(RelatedObjectDescribeStructure relatedDescribeStructure) {
        IRelatedListQuery.RelatedObjectQuery objectQuery = new IRelatedListQuery.RelatedObjectQuery();
        objectQuery.setLimit(getLimit());
        objectQuery.setOffset(getOffset());
        objectQuery.setRelatedListName(relatedDescribeStructure.getRelatedListName());
        objectQuery.setReferenceApiName(relatedDescribeStructure.getRelatedObjectDescribe().getApiName());
        objectQuery.setFilters(getFilters());

        return objectQuery;
    }

    public static int calculateTotalPage(int totalNum, int pageSize) {
        int totalPage = totalNum / pageSize;
        if (totalNum % pageSize != 0) {
            totalPage++;
        }
        return totalPage;
    }

    public static int calculateOffset(int pageNum, int pageSize) {
        return (pageNum - 1) * pageSize;
    }

    public static int calculatePageNum(int offset, int pageSize) {
        return offset / pageSize;
    }

    public SearchTemplateQuery toSearchTemplateQuery() {
        return (SearchTemplateQuery) query;
    }

    public void clearDataPermissionConfig() {
        query.setDataRightsParameter(null);
        query.setPermissionType(0);
    }

    public static QueryResult<IObjectData> buildEmptyResult() {
        QueryResult<IObjectData> queryResult = new QueryResult<>();
        queryResult.setData(Lists.newArrayList());
        queryResult.setTotalNumber(0);
        return queryResult;
    }

    public void handleWheresFilterWithLookupFunction() {
        List<Wheres> wheres = query.getWheres();
        if (CollectionUtils.empty(wheres)) {
            return;
        }
        wheres.removeIf(where -> {
            List<IFilter> filters = where.getFilters();
            if (CollectionUtils.empty(filters)) {
                return true;
            }
            filters.removeIf(filter -> Objects.equals(filter.getValueType(), FilterExt.FilterValueTypes.FUNCTION_VARIABLE));
            return CollectionUtils.empty(filters);
        });
    }

    /**
     * 优先以order_by字段排序
     */
    public void orderByFirst() {
        // 从对象明细按照"order_by"字段升序排序
        List<OrderBy> newOrderBys = Lists.newArrayList(OrderByExt.relatedDetailOrderBy());
        Optional.ofNullable(getOrders()).ifPresent(newOrderBys::addAll);
        resetOrderBy(newOrderBys);
    }

    public void convert2SystemZone(Supplier<IObjectDescribe> supplier) {
        Objects.requireNonNull(supplier);
        List<IFilter> filters = getFilters();
        List<Wheres> wheres = getWheres();
        if (CollectionUtils.empty(filters) && CollectionUtils.empty(wheres)) {
            return;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(supplier.get());
        CollectionUtils.nullToEmpty(filters).forEach(filter -> FilterExt.convert2SystemZone(describeExt, filter));
        CollectionUtils.nullToEmpty(wheres).stream()
                .map(Wheres::getFilters)
                .filter(CollectionUtils::notEmpty)
                .flatMap(Collection::stream)
                .forEach(filter -> FilterExt.convert2SystemZone(describeExt, filter));
    }

    public void validateWheresAndFilters(User user, IObjectDescribe describe) {
//        if(!AppFrameworkConfig.isGrayDataTypeValidate(user.getTenantId())) {
//            return;
//        }
        if (Objects.isNull(describe)) {
            return;
        }
        validateWheresAndFilters(user, describe, TenantUtil.isFilterValidate(user.getTenantId()));
    }

    public void validateWheresAndFilters(User user, IObjectDescribe describe, boolean validateFilterIsIndexField) {
        validateFilterNumAndFieldValueNum(user.getTenantId());
//        if(!AppFrameworkConfig.isGrayDataTypeValidate(user.getTenantId())) {
//            return;
//        }
        if (Objects.isNull(describe)) {
            return;
        }
        CollectionUtils.nullToEmpty(getWheres()).forEach(where -> checkFilter(user, describe, where.getFilters(), validateFilterIsIndexField));
        checkFilter(user, describe, getFilters(), validateFilterIsIndexField);
    }

    public void validateFilterNumAndFieldValueNum(String tenantId) {
        validateFilterNum(tenantId);
        validateFieldValueNum(tenantId);
    }

    private void validateFilterNum(String tenantId) {
        int maxFilterNum = AppFrameworkConfig.getMaxFilterNumWhenSearchData();
        if (maxFilterNum <= 0) {
            return;
        }
        List<IFilter> filterList = getFilters();
        List<Wheres> wheresList = getWheres();
        long filterNum = CollectionUtils.nullToEmpty(wheresList).stream()
                .mapToLong(wheres -> CollectionUtils.size(wheres.getFilters()))
                .sum();
        filterNum += CollectionUtils.size(filterList);
        if (filterNum > maxFilterNum) {
            log.warn("filter num:{} over max value:{},tenantId:{}", filterNum, maxFilterNum, tenantId);
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.VALIDATE_FILTER_NUM_AND_FIELD_VALUE_NUM_GRAY_EI, tenantId)) {
                throw new ValidateException(String.format("filter num:%s over max value:%s", filterNum, maxFilterNum));
            }
        }
    }

    private void validateFieldValueNum(String tenantId) {
        int maxFieldValueNum = AppFrameworkConfig.getMaxFieldValueNumWhenSearchData();
        if (maxFieldValueNum <= 0) {
            return;
        }
        validateFieldValueNum(tenantId, getFilters(), maxFieldValueNum);
        CollectionUtils.nullToEmpty(getWheres()).forEach(wheres -> validateFieldValueNum(tenantId, wheres.getFilters(), maxFieldValueNum));
    }

    private static void validateFieldValueNum(String tenantId, List<IFilter> filterList, int maxFieldValueNum) {
        if (CollectionUtils.empty(filterList)) {
            return;
        }
        filterList.forEach(filter -> {
            int filterValueNum = CollectionUtils.size(filter.getFieldValues());
            if (filterValueNum > maxFieldValueNum) {
                log.warn("field:{} value num:{} over max value:{},tenantId:{}", filter.getFieldName(), filterValueNum,
                        maxFieldValueNum, tenantId);
                if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.VALIDATE_FILTER_NUM_AND_FIELD_VALUE_NUM_GRAY_EI, tenantId)) {
                    throw new ValidateException(String.format("field:%s value num:%s over max value:%s", filter.getFieldName(),
                            filterValueNum, maxFieldValueNum));
                }
            }
        });
    }

    private void checkFilter(User user, IObjectDescribe describe, List<IFilter> filters, boolean filterValidate) {
        CollectionUtils.nullToEmpty(filters).forEach(filter -> {
            try {
                FilterExt.of(filter).validateFilter(describe, filterValidate);
            } catch (ValidateException e) {
                boolean grayDataTypeValidate = AppFrameworkConfig.isGrayDataTypeValidate(user.getTenantId());
                boolean ignoreDataTypeValidate = AppFrameworkConfig.isIgnoreDataTypeValidate(describe.getApiName(), filter.getFieldName());
                if (filterValidate && !ignoreDataTypeValidate) {
                    throw e;
                }
                if (!grayDataTypeValidate || ignoreDataTypeValidate) {
                    AuditLogDTO dto = AuditLogDTO.builder()
                            .appName(RuntimeUtils.getAppName())
                            .serverIp(RuntimeUtils.getServerIp())
                            .profile(RuntimeUtils.getProfile())
                            .action("dataValidate")
                            .tenantId(user.getTenantId())
                            .userId(user.getUserId())
                            .objectApiNames(describe.getApiName())
                            .num(0)
                            .message(e.getMessage())
                            .traceId(TraceContext.get().getTraceId())
                            .extra("FilterValidator")
                            .build();
                    BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
                } else {
                    throw e;
                }
            }
        });
    }

    public void onlyQueryTotalNumIgnorePermission() {
        setOffset(0);
        setLimit(1);
        setNeedReturnCountNum(true);
        setFindExplicitTotalNum(true);
        clearDataPermissionConfig();
    }

    public static SearchTemplateQuery mergeByAPLPlugin(String searchQueryInfo, String newSearchQueryInfo) {
        SearchTemplateQuery newQuery = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(newSearchQueryInfo);
        SearchTemplateQuery query = (SearchTemplateQuery) SearchTemplateQuery.fromJsonString(searchQueryInfo);
        SearchTemplateQueryExt queryExt = of(query);
        if (Objects.nonNull(queryExt)) {
            queryExt.mergeByAPLPlugin(newQuery);
        }
        return query;
    }

    public void mergeByAPLPlugin(SearchTemplateQuery newQuery) {
        query.resetFilters(newQuery.getFilters());
        if (CollectionUtils.notEmpty(newQuery.getOrders())) {
            OrderBy orderBy = newQuery.getOrders().get(0);
            SearchTemplateQueryExt.of(query).resetOrderBy(Lists.newArrayList(orderBy));
        }
    }

    public boolean onlyIdFilter() {
        return query.getFilters().stream()
                .allMatch(filter -> IObjectData.ID.equals(filter.getFieldName()));
    }

    public boolean onlyIdOrder() {
        return query.getOrders().stream()
                .allMatch(filter -> IObjectData.ID.equals(filter.getFieldName()));
    }

    public ISearchTemplateQuery processFilterGroupByPatternExpression() {
        List<IFilter> filters = query.getFilters();
        String pattern = StringUtils.trimToEmpty(query.getPattern()).toUpperCase();

        // 早期返回条件检查
        if (CollectionUtils.empty(filters) || StringUtils.isBlank(pattern)) {
            return query;
        }

        try {
            // 清空历史的 filterGroup 设置
            resetFilterGroups(filters);

            // 转换表达式为filterGroup结构
            ExprToFilterGroupConverterService.ConversionResponse response = convertPatternToFilterGroup(pattern);
            if (!response.isSuccess()) {
                log.warn("Convert pattern to filterGroup failed: pattern={}, error={}", pattern, response.getErrorMessage());
                return query;
            }

            // 应用filterGroup设置
            applyFilterGroupSettings(filters, response);

            log.debug("Successfully processed filterGroup pattern: pattern={}, filterCount={}, groupCount={}",
                    pattern, filters.size(), response.getFilterGroupMap().size());

        } catch (Exception e) {
            log.error("Error processing filterGroup pattern: pattern={}, error={}", pattern, e.getMessage(), e);
            // 发生异常时确保filterGroup被清空，避免不一致状态
            resetFilterGroups(filters);
        }

        return query;
    }

    /**
     * 重置所有filter的filterGroup为空
     */
    private void resetFilterGroups(List<IFilter> filters) {
        filters.forEach(filter -> filter.setFilterGroup(""));
    }

    /**
     * 使用服务转换pattern为filterGroup结构
     */
    private ExprToFilterGroupConverterService.ConversionResponse convertPatternToFilterGroup(String pattern) {
        // 可以考虑将service作为成员变量注入，避免每次创建
        ExprToFilterGroupConverterService converterService = new ExprToFilterGroupConverterService();
        return converterService.convertFromString(pattern);
    }

    /**
     * 应用filterGroup设置到filters
     * 优化：使用索引映射，避免O(n²)复杂度
     */
    private void applyFilterGroupSettings(List<IFilter> filters,
                                          ExprToFilterGroupConverterService.ConversionResponse response) {
        Map<String, List<String>> filterGroupMap = response.getFilterGroupMap();
        List<String> ungroupedFilters = response.getUngroupedFilters();

        // 构建 filterId -> filterGroup 的映射，避免嵌套循环
        Map<String, String> filterIdToGroupMap = buildFilterIdToGroupMap(filterGroupMap);

        // O(n) 复杂度设置filterGroup
        for (int i = 0; i < filters.size(); i++) {
            String filterId = String.valueOf(i + 1);
            IFilter filter = filters.get(i);

            // 检查是否在某个filterGroup中
            String group = filterIdToGroupMap.get(filterId);
            if (group != null) {
                filter.setFilterGroup(group);
            } else if (ungroupedFilters != null && ungroupedFilters.contains(filterId)) {
                // 显式处理ungroupedFilters，保持filterGroup为空
                filter.setFilterGroup("");
            } else {
                // 如果filterId既不在filterGroup中也不在ungrouped中，记录警告
                log.warn("Filter with id {} is not found in conversion result, pattern might be invalid", filterId);
                filter.setFilterGroup("");
            }
        }

        // 验证设置结果
        validateFilterGroupSettings(filters, filterGroupMap, ungroupedFilters);
    }

    /**
     * 构建 filterId 到 filterGroup 的映射
     * 将 O(n*m*k) 复杂度优化为 O(n+m*k)
     */
    private Map<String, String> buildFilterIdToGroupMap(Map<String, List<String>> filterGroupMap) {
        Map<String, String> filterIdToGroupMap = Maps.newHashMap();

        filterGroupMap.forEach((group, filterIds) -> {
            if (CollectionUtils.notEmpty(filterIds)) {
                filterIds.forEach(filterId -> {
                    if (filterIdToGroupMap.containsKey(filterId)) {
                        log.warn("FilterId {} appears in multiple groups, this might indicate an invalid pattern", filterId);
                    }
                    filterIdToGroupMap.put(filterId, group);
                });
            }
        });

        return filterIdToGroupMap;
    }

    /**
     * 验证filterGroup设置的正确性
     */
    private void validateFilterGroupSettings(List<IFilter> filters,
                                             Map<String, List<String>> filterGroupMap,
                                             List<String> ungroupedFilters) {
        if (!log.isDebugEnabled()) {
            return;
        }

        // 统计各个filterGroup的filter数量
        Map<String, Long> groupCounts = filters.stream()
                .filter(filter -> StringUtils.isNotBlank(filter.getFilterGroup()))
                .collect(Collectors.groupingBy(IFilter::getFilterGroup, Collectors.counting()));

        long ungroupedCount = filters.stream()
                .filter(filter -> StringUtils.isBlank(filter.getFilterGroup()))
                .count();

        log.debug("FilterGroup assignment result: grouped={}, ungrouped={}, total={}",
                groupCounts, ungroupedCount, filters.size());

        // 验证group数量是否与预期一致
        filterGroupMap.forEach((group, expectedFilterIds) -> {
            Long actualCount = groupCounts.get(group);
            if (actualCount == null || !actualCount.equals((long) expectedFilterIds.size())) {
                log.warn("FilterGroup {} expected {} filters but got {}",
                        group, expectedFilterIds.size(), actualCount);
            }
        });
    }

    /**
     * 解析 pattern 表达式并返回 SearchQuery AST
     * <p>
     * 与 processFilterGroupByPatternExpression 不同的是：
     * 1. 不处理 filterGroup 设置
     * 2. 直接返回解析后的 SearchQuery 语法树
     * 3. 可用于表达式验证、分析等场景
     *
     * @return 解析后的 SearchQuery，如果解析失败则返回 null
     */
    public SearchQuery parsePatternToSearchQuery() {
        String pattern = StringUtils.trimToEmpty(query.getPattern()).toUpperCase();

        // 早期返回条件检查
        if (StringUtils.isBlank(pattern)) {
            log.debug("Pattern is blank, returning null SearchQuery");
            return null;
        }

        List<IFilter> filters = query.getFilters();
        if (CollectionUtils.empty(filters)) {
            log.debug("No filters available for pattern resolution");
            return null;
        }

        try {
            // 词法分析 - 将表达式字符串转换为 token 列表
            List<Token> tokens = tokenizePattern(pattern);
            if (CollectionUtils.empty(tokens)) {
                log.warn("Failed to tokenize pattern: {}", pattern);
                return null;
            }

            // 语法分析 - 将 token 列表解析为 SearchQuery AST
            SearchQuery searchQuery = parseTokensToSearchQuery(tokens);

            // 替换占位符索引为实际的 filter
            SearchQuery resolvedSearchQuery = replaceFilterPlaceholders(searchQuery, filters);

            log.debug("Successfully parsed pattern to SearchQuery: pattern={}, tokenCount={}, filterCount={}",
                    pattern, tokens.size(), filters.size());

            return resolvedSearchQuery;

        } catch (Exception e) {
            log.error("Error parsing pattern to SearchQuery: pattern={}, error={}", pattern, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 词法分析：将 pattern 字符串转换为 token 列表
     */
    private List<Token> tokenizePattern(String pattern) {
        try {
            return Lexer.tokenize(pattern);
        } catch (Exception e) {
            log.error("Error during tokenization: pattern={}, error={}", pattern, e.getMessage(), e);
            throw new RuntimeException("Failed to tokenize pattern: " + pattern, e);
        }
    }

    /**
     * 语法分析：将 token 列表解析为 SearchQuery AST
     */
    private SearchQuery parseTokensToSearchQuery(List<Token> tokens) {
        try {
            Parser parser = new Parser(tokens);
            return parser.parseExpression();
        } catch (Exception e) {
            log.error("Error during parsing: tokenCount={}, error={}", tokens.size(), e.getMessage(), e);
            throw new RuntimeException("Failed to parse tokens to SearchQuery", e);
        }
    }

    /**
     * 递归替换 SearchQuery AST 中的占位符索引为实际的 filter
     *
     * @param searchQuery 包含占位符的 SearchQuery AST
     * @param filters     实际的 filter 列表
     * @return 替换后的 SearchQuery AST
     */
    private SearchQuery replaceFilterPlaceholders(SearchQuery searchQuery, List<IFilter> filters) {
        if (searchQuery == null || searchQuery.isEmpty()) {
            return searchQuery;
        }

        if (searchQuery.isFilterNode()) {
            // 如果是过滤器节点，检查是否需要替换占位符
            return replaceFilterPlaceholder(searchQuery, filters);
        }

        // 如果是操作符节点，递归处理子节点
        List<SearchQuery> children = searchQuery.getSearchQueryContainer();
        if (CollectionUtils.notEmpty(children)) {
            List<SearchQuery> resolvedChildren = children.stream()
                    .map(child -> replaceFilterPlaceholders(child, filters))
                    .filter(Objects::nonNull) // 过滤掉null值（索引越界的情况）
                    .collect(Collectors.toList());

            // 如果所有子节点都被过滤掉了，返回null
            if (CollectionUtils.empty(resolvedChildren)) {
                return null;
            }

            // 创建新的 SearchQuery 对象，保持原有的连接符
            return createSearchQueryWithChildren(searchQuery, resolvedChildren);
        }

        return searchQuery;
    }

    /**
     * 替换单个 filter 节点中的占位符
     */
    private SearchQuery replaceFilterPlaceholder(SearchQuery filterNode, List<IFilter> filters) {
        IFilter filter = filterNode.getFilter();
        if (filter != null && Parser.FILTER_INDEX_FIELD_NAME.equals(filter.getFieldName())) {
            // 这是一个占位符，需要替换为实际的 filter
            List<String> fieldValues = filter.getFieldValues();
            if (CollectionUtils.notEmpty(fieldValues)) {
                try {
                    int filterIndex = Integer.parseInt(fieldValues.get(0)) - 1; // 索引从1开始，数组从0开始
                    if (filterIndex >= 0 && filterIndex < filters.size()) {
                        IFilter actualFilter = filters.get(filterIndex);
                        // 使用静态工厂方法创建新的 SearchQuery 对象，包含实际的 filter
                        return SearchQueryImpl.filter(actualFilter);
                    } else {
                        log.warn("Filter index {} is out of bounds, available filters: {}",
                                filterIndex + 1, filters.size());
                        // 索引越界时返回null，让上层过滤掉这个节点
                        return null;
                    }
                } catch (NumberFormatException e) {
                    log.warn("Invalid filter index format: {}", fieldValues.get(0));
                    return null;
                }
            }
        }

        // 如果不是占位符，返回原节点
        return filterNode;
    }

    /**
     * 创建包含指定子节点的 SearchQuery 对象
     */
    private SearchQuery createSearchQueryWithChildren(SearchQuery originalQuery, List<SearchQuery> children) {
        if (CollectionUtils.empty(children)) {
            // 如果没有子节点，返回null
            return null;
        }

        if (children.size() == 1) {
            // 如果只有一个子节点，直接返回该子节点
            return children.get(0);
        }

        // 根据原查询的连接符类型创建新的查询
        SearchQuery.Connector connector = originalQuery.getConnector();

        if (SearchQuery.Connector.AND.equals(connector)) {
            // 使用 and 连接
            return SearchQueryImpl.andSearchQuery(children);
        } else if (SearchQuery.Connector.OR.equals(connector)) {
            // 使用 or 连接
            return SearchQueryImpl.orSearchQuery(children);
        } else {
            // 默认使用 and 连接
            log.debug("Unknown connector type: {}, using AND as default", connector);
            return SearchQueryImpl.andSearchQuery(children);
        }
    }
}
