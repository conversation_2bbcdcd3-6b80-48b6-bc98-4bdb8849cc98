package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.ui.layout.IMultiTableComponent;
import com.facishare.paas.metadata.ui.layout.ITableComponent;
import lombok.Getter;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;

import java.util.List;

/**
 * Created by zhouwr on 2018/3/1
 */
@Slf4j
public class MultiTableComponentExt {

    @Getter
    @Delegate
    private IMultiTableComponent multiTableComponent;

    private MultiTableComponentExt(IMultiTableComponent multiTableComponent) {
        this.multiTableComponent = multiTableComponent;
    }

    public static MultiTableComponentExt of(IMultiTableComponent multiTableComponent) {
        return new MultiTableComponentExt(multiTableComponent);
    }

    public List<ITableComponent> getChildComponentsSilently() {
        try {
            return CollectionUtils.nullToEmpty(getChildComponents());
        } catch (MetadataServiceException e) {
            log.error("getChildComponents error,multiTableComponent:{}", multiTableComponent, e);
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR);
        }
    }

    public void removeButtonByActionsDeeply(List<String> actions) {
        List<ITableComponent> childComponents = getChildComponentsSilently();
        if (CollectionUtils.notEmpty(childComponents)) {
            childComponents.forEach(x -> ComponentExt.of(x).removeButtonByActionsDeeply(actions));
        }

        ComponentExt.of(multiTableComponent).removeButtonByActions(actions);
    }
}
