package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.impl.ui.layout.TableColumn;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.google.common.collect.Lists;
import org.bson.Document;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2024/3/11
 */
public class FlowTaskListMobileComponentExt extends BaseFlowTaskListComponentExt {

    public static final String MOBILE_FIELD_TYPE = "mobile_field_type";
    /**
     * 根据业务类型使用待办摘要布局中配置的字段
     */
    public static final String MOBILE_FIELD_TYPE_USER_LAYOUT = "0";
    /**
     * 使用当前布局配置的固定字段
     */
    public static final String MOBILE_FIELD_TYPE_USER_DEFINE = "1";

    public static final String INCLUDE_FIELDS = "include_fields";

    private FlowTaskListMobileComponentExt(IComponent component) {
        super(component);
    }

    public static FlowTaskListMobileComponentExt of(IComponent component) {
        return new FlowTaskListMobileComponentExt(component);
    }

    public String getMobileFieldType() {
        return get(MOBILE_FIELD_TYPE, String.class);
    }

    public void setMobileFieldType(String mobileFieldType) {
        set(MOBILE_FIELD_TYPE, mobileFieldType);
    }

    public boolean mobileFieldTypeIsUserDefine() {
        return MOBILE_FIELD_TYPE_USER_DEFINE.equals(getMobileFieldType());
    }

    public List<ITableColumn> getIncludeFields() {
        List<Map> collection = get(INCLUDE_FIELDS, List.class);
        return CollectionUtils.nullToEmpty(collection)
                .stream()
                .map(TableColumn::new)
                .collect(Collectors.toList());
    }

    public void resetIncludeFields(List<ITableColumn> includeFields) {
        set(INCLUDE_FIELDS, Lists.newArrayList());
        CollectionUtils.nullToEmpty(includeFields).forEach(this::addIncludeField);
    }

    private void addIncludeField(ITableColumn column) {
        Object list = get(INCLUDE_FIELDS);
        if (list instanceof List) {
            ((List) list).add(Document.parse(column.toJsonString()));
        }
    }
}
