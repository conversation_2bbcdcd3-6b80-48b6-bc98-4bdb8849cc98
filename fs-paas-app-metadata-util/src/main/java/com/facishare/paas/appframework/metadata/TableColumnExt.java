package com.facishare.paas.appframework.metadata;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.ui.layout.TableColumn;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.experimental.Delegate;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.facishare.crm.openapi.Utils.APPROVAL_TASK_API_NAME;
import static com.facishare.crm.userdefobj.DefObjConstants.FLOW_LIST_LAYOUT_FIELD;

/**
 * Created by zhouwr on 2018/2/27
 */
public class TableColumnExt {

    private static final String API_NAME = "api_name";
    public static final String IS_SHOW_LABEL = "is_show_label";

    @Getter
    @Delegate
    private ITableColumn tableColumn;

    private TableColumnExt(ITableColumn tableColumn) {
        this.tableColumn = tableColumn;
    }

    public static TableColumnExt of(ITableColumn tableColumn) {
        return new TableColumnExt(tableColumn);
    }

    public static TableColumnExt of(Map map) {
        return new TableColumnExt(new TableColumn(map));
    }

    public static TableColumnExt of(String apiName, String label, String type) {
        TableColumn tableColumn = new TableColumn();
        tableColumn.setName(apiName);
        tableColumn.set(API_NAME, apiName);
        tableColumn.setRenderType(type);
        tableColumn.setLabelName(label);
        return of(tableColumn);
    }

    public static TableColumnExt of(IFieldDescribe field) {
        TableColumn tableColumn = new TableColumn();
        tableColumn.setName(field.getApiName());
        tableColumn.set(API_NAME, field.getApiName());
        tableColumn.setRenderType(field.getType());
        tableColumn.setLabelName(field.getLabel());
        return of(tableColumn);
    }

    public static ITableColumn buildNameColumn() {
        TableColumn tableColumn = new TableColumn();
        tableColumn.setName(IObjectData.NAME);
        tableColumn.set(API_NAME, IObjectData.NAME);
        tableColumn.setRenderType(IFieldType.TEXT);

        return tableColumn;
    }

    public static List<ITableColumn> buildWhatListDefaultColumns(ObjectDescribeExt taskDescribe,
                                                                 ObjectDescribeExt whatDescribe) {
        if (APPROVAL_TASK_API_NAME.equals(taskDescribe.getApiName())) {
            List<String> fieldNameList = DefObjConstants.getApprovalTaskFlowLayoutField(whatDescribe.getApiName());
            if (CollectionUtils.notEmpty(fieldNameList)) {
                List<ITableColumn> tableColumns = fieldNameList.stream()
                        .map(whatDescribe::getFieldDescribeSilently)
                        .filter(Optional::isPresent)
                        .map(Optional::get)
                        .map(fieldDescribe -> TableColumnExt.of(WhatComponentExt.getWhatFieldName(whatDescribe.getApiName(), fieldDescribe.getApiName()),
                                fieldDescribe.getLabel(), fieldDescribe.getType()).getTableColumn())
                        .collect(Collectors.toList());
                tableColumns.add(0, TableColumnExt.of(taskDescribe.getFieldDescribeSilently(ObjectDescribeExt.WORKFLOW_INSTANCE_ID).orElseGet(() -> taskDescribe.getNameField())).getTableColumn());
                return tableColumns;
            }
        }
        return buildTableColumns(taskDescribe, whatDescribe.getNameField());
    }

    private static List<ITableColumn> buildTableColumns(ObjectDescribeExt describeExt, IFieldDescribe nameField) {

        if (!FLOW_LIST_LAYOUT_FIELD.containsKey(describeExt.getApiName())) {
            return Lists.newArrayList();
        }
        List<String> fieldNameList = FLOW_LIST_LAYOUT_FIELD.get(describeExt.getApiName());
        List<ITableColumn> result = fieldNameList.stream()
                .map(describeExt::getFieldDescribeSilently)
                .filter(Optional::isPresent)
                .map(fieldDescribe -> TableColumnExt.of(fieldDescribe.get()).getTableColumn())
                .collect(Collectors.toList());

        result.add(TableColumnExt.of(WhatComponentExt.getWhatFieldName(nameField.getDescribeApiName(), nameField.getApiName()),
                nameField.getLabel(),
                nameField.getType()).getTableColumn());
        return result;
    }

    public String getApiName() {
        String fieldName = getName();
        if (fieldName != null) {
            return fieldName;
        }
        return get(API_NAME, String.class);
    }

    public Boolean getIsShowLabel() {
        return get(IS_SHOW_LABEL, Boolean.class);
    }

    public void setIsShowLabel(Boolean isShowLabel) {
        set(IS_SHOW_LABEL, isShowLabel);
    }

    public Map<String, Object> toMap() {
        return ((DocumentBasedBean) tableColumn).getContainerDocument();
    }

    public void remove(String key) {
        ((DocumentBasedBean) tableColumn).remove(key);
    }
}
