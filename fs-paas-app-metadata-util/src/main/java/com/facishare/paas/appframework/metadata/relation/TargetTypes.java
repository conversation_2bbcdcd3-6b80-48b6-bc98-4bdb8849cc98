package com.facishare.paas.appframework.metadata.relation;

public interface TargetTypes {
    String GLOBAL_VARIABLE = "GlobalVariable";
    String RELATED_DESCRIBE_FIELD = "Describe.RelatedDescribe.Field";
    String FUNCTION = "Function";
    String DESCRIBE_LAYOUT = "Describe.Layout";
    String DESCRIBE_FIELD = "Describe.Field"; // 字段

    String USEDOBJ_TAGGROUP_TAGID = "UsedObj.TagGroup.TagId";//打分器绑定对象和标签分组
}
