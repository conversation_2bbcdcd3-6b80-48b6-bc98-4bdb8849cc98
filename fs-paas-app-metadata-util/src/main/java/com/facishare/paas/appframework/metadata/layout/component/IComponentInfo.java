package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.metadata.api.JsonCompatible;

import java.util.Map;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/02/02
 */
public interface IComponentInfo extends JsonCompatible {
    String RENDER_TYPE = "render_type";
    String PAGE_TYPE = "page_type";

    String PAGE_TYPE_LIST = "list";
    String PAGE_TYPE_SELECTED = "selected";
    String PAGE_TYPE_RELATED = "related";

    /**
     * @return 渲染类型
     */
    String getRenderType();

    /**
     * @return 所属页面
     */
    String getPageType();

    default void setPageType(String pageType) {
        set(PAGE_TYPE, pageType);
    }

    default void setRenderType(String renderType) {
        set(RENDER_TYPE, renderType);
    }

    IComponentInfo copy();
}
