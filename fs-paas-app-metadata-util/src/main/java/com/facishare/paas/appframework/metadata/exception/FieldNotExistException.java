package com.facishare.paas.appframework.metadata.exception;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.i18n.I18NKey;

/**
 * Created by zhouwr on 2018/7/13
 */
public class FieldNotExistException extends MetaDataBusinessException {

    public FieldNotExistException(String fieldLabel) {
        super(I18N.text(I18NKey.FIELD_DISABLED, fieldLabel), AppFrameworkErrorCode.FIELD_NOT_EXIST_ERROR);
    }

    public FieldNotExistException(String expressionLabel, String fieldLabel) {
        super(I18N.text(I18NKey.RELATED_FIELD_DISABLED, expressionLabel, fieldLabel), AppFrameworkErrorCode.FIELD_NOT_EXIST_ERROR);
    }
}
