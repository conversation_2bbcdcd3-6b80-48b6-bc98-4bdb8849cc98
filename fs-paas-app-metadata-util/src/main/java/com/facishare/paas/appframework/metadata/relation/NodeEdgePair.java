package com.facishare.paas.appframework.metadata.relation;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;
import lombok.experimental.Delegate;

import java.util.Deque;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * create by z<PERSON><PERSON> on 2020/08/17
 */
@Getter
@ToString
@EqualsAndHashCode
public class NodeEdgePair {
    @Delegate(excludes = {Comparable.class})
    private final FieldNode fieldNode;
    @Delegate
    private final RelateEdge relateEdge;

    private NodeEdgePair(FieldNode fieldNode, RelateEdge relateEdge) {
        this.fieldNode = fieldNode;
        this.relateEdge = relateEdge;
    }

    public static NodeEdgePair of(FieldNode fieldNode, RelateEdge relateEdge) {
        return new NodeEdgePair(fieldNode, relateEdge);
    }

    /**
     * 找到当本对象字段变更时，通过关联（主）对象的统计字段，影响到当前对象的计算字段（默认值）
     *
     * @param nodeEdgePairs
     * @param expectedObjectApiNames
     * @return
     */
    public static List<NodeEdgePair> filterNodeEdgePair(Deque<NodeEdgePair> nodeEdgePairs,
                                                        Set<String> expectedObjectApiNames) {
        List<NodeEdgePair> result = Lists.newArrayList();
        List<NodeEdgePair> L2RNodeEdgePairs = Lists.newArrayList();
        boolean isL2R = false;
        for (NodeEdgePair nodeEdgePair : nodeEdgePairs) {
            //本对象的计算字段
            if (!isL2R && (isRelateType(nodeEdgePair, RelateType.S2S)
                    // 主从对象，并且在（新建、编辑）对象范围内
                    || (expectedObjectApiNames.contains(nodeEdgePair.getObjectApiName()) && isRelateType(nodeEdgePair, RelateType.M2D, RelateType.D2M)))) {
                result.add(nodeEdgePair);
                continue;
            }
            // 关联对象的统计字段（只支持一级）
            if (isRelateType(nodeEdgePair, RelateType.L2R, RelateType.M2D)) {
                if (!isL2R) {
                    isL2R = true;
                    L2RNodeEdgePairs.add(nodeEdgePair);
                    continue;
                } else {
                    break;
                }
            }
            // 页面对象的计算字段直接、间接引用了关联对象（主对象）的统计字段
            if (isRelateType(nodeEdgePair, RelateType.R2L, RelateType.D2M)) {
                if (isL2R && expectedObjectApiNames.contains(nodeEdgePair.getObjectApiName())) {
                    isL2R = false;
                    L2RNodeEdgePairs.add(nodeEdgePair);
                    result.addAll(L2RNodeEdgePairs);
                    L2RNodeEdgePairs.clear();
                    continue;
                } else {
                    break;
                }
            }
            //关联对象的计算字段
            if (isL2R) {
                L2RNodeEdgePairs.add(nodeEdgePair);
            }
        }
        return result;
    }

    private static boolean isRelateType(NodeEdgePair nodeEdgePair, RelateType... relateTypes) {
        HashSet<RelateType> relateTypeSet = Sets.newHashSet(relateTypes);
        return nodeEdgePair.getRelateEdgeNodes().stream().anyMatch(it -> relateTypeSet.contains(it.getRelateType()));
    }
}
