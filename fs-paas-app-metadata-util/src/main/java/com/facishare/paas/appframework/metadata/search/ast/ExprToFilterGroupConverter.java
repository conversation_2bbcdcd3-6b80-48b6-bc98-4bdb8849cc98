package com.facishare.paas.appframework.metadata.search.ast;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.search.SearchQuery;
import com.facishare.paas.metadata.api.search.IFilter;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * 将SearchQuery转换为filterGroup结构
 * <p>
 * filterGroup支持的表达式结构为：
 * 1. 同一个filterGroup内的条件之间是AND关系
 * 2. 不同filterGroup之间是OR关系
 * 3. 没有filterGroup的条件与所有条件都是AND关系
 * <p>
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/5/19
 */
@Data
public class ExprToFilterGroupConverter {

    private final FilterGroupValidator validator = new FilterGroupValidator();

    /**
     * 将SearchQuery转换为filterGroup结构
     *
     * @param searchQuery AST语法树节点
     * @return 转换结果
     * @throws IllegalArgumentException 如果节点不符合filterGroup支持的结构
     */
    public ConversionResult convert(SearchQuery searchQuery) {
        // 先验证结构是否可转换
        FilterGroupValidator.ValidationResult validationResult = validator.validate(searchQuery);
        if (!validationResult.isValid()) {
            throw new ValidateException(I18NExt.getOrDefault(I18NKey.EXPRESSION_STRUCTURE_NOT_SUPPORT_CONVERT_TO_FILTER_GROUP, "表达式结构不支持转换为filterGroup: {0}", validationResult.getReason())); // ignoreI18n
        }

        // 计算需要的filterGroup数量
        int groupCount = validator.estimateFilterGroupCount(searchQuery);

        // 创建转换结果
        ConversionResult result = new ConversionResult();

        // 如果是顶层AND节点，且包含OR子节点，需要特殊处理
        if (!searchQuery.isFilterNode() && searchQuery.getConnector() == SearchQuery.Connector.AND) {
            // 检查是否包含OR子节点
            boolean hasOrChild = false;
            for (SearchQuery child : searchQuery.getSearchQueryContainer()) {
                if (!child.isFilterNode() && child.getConnector() == SearchQuery.Connector.OR) {
                    hasOrChild = true;
                    break;
                }
            }

            if (hasOrChild) {
                // 处理AND节点的子节点
                for (SearchQuery child : searchQuery.getSearchQueryContainer()) {
                    if (child.isFilterNode()) {
                        // 过滤器节点直接加入ungrouped
                        // 需要通过某种方式获取过滤器的索引，这里先用占位符
                        result.addUngrouped(getFilterIndex(child));
                    } else {
                        // 操作符节点需要分配filterGroup
                        convertNode(child, result, "1", 1, groupCount);
                    }
                }
                return result;
            }
        }

        // 其他情况，正常处理
        convertNode(searchQuery, result, "1", 1, groupCount);
        return result;
    }

    /**
     * 递归转换节点
     *
     * @param searchQuery  当前节点
     * @param result       转换结果
     * @param currentGroup 当前处理的filterGroup值
     * @param groupCounter 当前filterGroup计数
     * @param totalGroups  总filterGroup数量
     * @return 下一个可用的filterGroup计数
     */
    private int convertNode(SearchQuery searchQuery, ConversionResult result, String currentGroup, int groupCounter, int totalGroups) {
        // 如果是过滤器节点，直接添加到当前filterGroup
        if (searchQuery.isFilterNode()) {
            String filterIndex = getFilterIndex(searchQuery);
            result.addToGroup(currentGroup, filterIndex);
            return groupCounter;
        }

        // 如果是操作符节点
        if (!searchQuery.isEmpty()) {
            // 根据操作符类型处理
            if (searchQuery.getConnector() == SearchQuery.Connector.AND) {
                // AND操作符：所有子节点都使用相同的filterGroup
                for (SearchQuery child : searchQuery.getSearchQueryContainer()) {
                    groupCounter = convertNode(child, result, currentGroup, groupCounter, totalGroups);
                }
            } else if (searchQuery.getConnector() == SearchQuery.Connector.OR) {
                // OR操作符：每个子节点使用不同的filterGroup
                for (SearchQuery child : searchQuery.getSearchQueryContainer()) {
                    if (child.isFilterNode() ||
                            (!child.isFilterNode() && child.getConnector() == SearchQuery.Connector.AND)) {
                        // 为每个子节点分配一个新的filterGroup
                        String nextGroup = String.valueOf(groupCounter);
                        groupCounter = convertNode(child, result, nextGroup, groupCounter + 1, totalGroups);
                    } else {
                        // 递归处理复杂的OR节点
                        groupCounter = convertNode(child, result, null, groupCounter, totalGroups);
                    }
                }
            }

            return groupCounter;
        }

        return groupCounter;
    }

    /**
     * 从SearchQuery中获取过滤器索引
     *
     * @param searchQuery 过滤器SearchQuery
     * @return 过滤器索引字符串
     */
    private String getFilterIndex(SearchQuery searchQuery) {
        if (searchQuery.isFilterNode()) {
            IFilter filter = searchQuery.getFilter();
            if (filter != null && Parser.FILTER_INDEX_FIELD_NAME.equals(filter.getFieldName())) {
                // 这是我们在Parser中创建的临时Filter，从中获取索引
                return filter.getFieldValues().get(0);
            }
        }
        // 如果无法获取索引，返回默认值
        return "1";
    }

    /**
     * 转换结果
     */
    @Data
    public static class ConversionResult {
        // key是filterGroup的值，value是该filterGroup下的条件ID列表
        private Map<String, List<String>> filterGroupMap = Maps.newHashMap();
        // 没有分配filterGroup的条件ID列表
        private List<String> ungroupedFilters = Lists.newArrayList();

        /**
         * 添加一个条件到指定的filterGroup
         *
         * @param filterGroup filterGroup值
         * @param filterId    条件ID
         */
        public void addToGroup(String filterGroup, String filterId) {
            List<String> filterIds = filterGroupMap.computeIfAbsent(filterGroup, k -> Lists.newArrayList());
            filterIds.add(filterId);
        }

        /**
         * 添加一个未分组的条件
         *
         * @param filterId 条件ID
         */
        public void addUngrouped(String filterId) {
            ungroupedFilters.add(filterId);
        }
    }
} 