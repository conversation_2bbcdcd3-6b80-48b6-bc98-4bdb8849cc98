package com.facishare.paas.appframework.metadata.layout.rule;

import com.facishare.paas.appframework.metadata.ObjectDescribeExt;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2021/07/19
 */
public interface ILayoutRuleFilter {
    String FIELD_NAME = "field_name";
    String OPERATOR = "operator";
    String FIELD_VALUES = "field_values";
    String VALUE_TYPE = "value_type";

    String getFieldName();

    String getOperator();

    List<?> getFieldValues();

    Integer getValueType();

    void convertDateFieldFilter(ObjectDescribeExt describeExt, boolean toSystemZone);
}
