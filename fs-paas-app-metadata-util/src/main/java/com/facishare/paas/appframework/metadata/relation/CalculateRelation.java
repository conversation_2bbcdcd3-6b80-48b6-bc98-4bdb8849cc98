package com.facishare.paas.appframework.metadata.relation;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.google.gson.annotations.Expose;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by zhouwr on 2018/4/27
 */
public class CalculateRelation {

    public static final String CALCULATE_RELATION = "calculate_relation";

    //字段变更需要计算的字段(包括统计字段、计算字段和默认值是计算公式的字段)
    //Map的key是对象的apiName，value是需要计算的字段的apiName集合
    public static final String CALCULATE_FIELDS = "calculate_fields";

    public static final String RELATED_FIELDS = "relate_fields";

    private Map<String, Object> data;

    public CalculateRelation() {
        data = Maps.newHashMap();
        data.put(CALCULATE_FIELDS, Maps.newHashMap());
        data.put(RELATED_FIELDS, Maps.newHashMap());
    }

    public CalculateRelation(Map<String, Object> data) {
        this.data = data;
        if (data.get(CALCULATE_FIELDS) == null) {
            data.put(CALCULATE_FIELDS, Maps.newHashMap());
        }
        if (data.get(RELATED_FIELDS) == null) {
            data.put(RELATED_FIELDS, Maps.newHashMap());
        }
    }

    public Map<String, Object> toMap() {
        return data;
    }

    public boolean hasCalculateFields() {
        return CollectionUtils.notEmpty(getCalculateFields());
    }

    public void addCalculateFields(String objectApiName, Collection<String> fieldApiNames) {
        if (CollectionUtils.empty(fieldApiNames)) {
            return;
        }
        getCalculateFields().putIfAbsent(objectApiName, Sets.newHashSet());
        getCalculateFields().get(objectApiName).addAll(fieldApiNames);
    }

    public void addCalculateField(String objectApiName, String fieldApiName) {
        addCalculateFields(objectApiName, Lists.newArrayList(fieldApiName));
    }

    public Map<String, Set<String>> getCalculateFields() {
        return (Map<String, Set<String>>) data.get(CALCULATE_FIELDS);
    }

    public List<String> getCalculateFields(String objectApiName) {
        Set<String> calculateFields = getCalculateFields().get(objectApiName);
        if (CollectionUtils.empty(calculateFields)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(calculateFields);
    }

    public Map<String, Set<RelateField>> getRelateFields() {
        return (Map<String, Set<RelateField>>) data.get(RELATED_FIELDS);
    }

    public void addRelateFields(String objectApiName, Collection<RelateField> relateFields) {
        if (CollectionUtils.empty(relateFields)) {
            return;
        }
        getRelateFields().putIfAbsent(objectApiName, Sets.newHashSet());
        getRelateFields().get(objectApiName).addAll(relateFields);
    }

    public void addRelateField(String objectApiName, RelateField relateField) {
        addRelateFields(objectApiName, Lists.newArrayList(relateField));
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @EqualsAndHashCode(of = {"fieldName"})
    public static class RelateField {
        private String fieldName;
        private int order;
        private String type;

        @JsonIgnore
        @JSONField(serialize = false, deserialize = false)
        @Expose(serialize = false, deserialize = false)
        private transient boolean calculateAllData;

        public static RelateField of(String fieldName, int order, String type) {
            return of(fieldName, order, type, false);
        }

        public static RelateField of(String fieldName, int order, String type, boolean calculateAllData) {
            return new RelateField(fieldName, order, type, calculateAllData);
        }

        public boolean typeIsDefaultValue() {
            return FieldNode.NodeType.DEFAULT_VALUE.getCode().equals(type);
        }
    }

}
