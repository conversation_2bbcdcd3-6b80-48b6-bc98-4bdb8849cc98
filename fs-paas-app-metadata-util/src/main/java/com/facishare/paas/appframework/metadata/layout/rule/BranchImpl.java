package com.facishare.paas.appframework.metadata.layout.rule;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.impl.AbstractDocumentBasedDBRecord;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/07/19
 */
public class BranchImpl extends AbstractDocumentBasedDBRecord implements IBranch {
    private static final long serialVersionUID = -730188418988666674L;

    public BranchImpl() {
    }

    public BranchImpl(Map map) {
        super(map);
    }

    @Override
    public List<ILayoutRuleFilter> getConditions() {
        List<Map> conditions = (List<Map>) get(CONDITIONS);
        if (CollectionUtils.empty(conditions)) {
            return Collections.emptyList();
        }
        return conditions.stream()
                .map(LayoutRuleFilter::new)
                .collect(Collectors.toList());
    }
}
