package com.facishare.paas.appframework.metadata.exception;

import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;

/**
 * <AUTHOR>
 * @date 2019-06-27 10:50
 */
public class ExportException extends AppBusinessException {

    public ExportException(String message) {
        super(message, AppFrameworkErrorCode.EXPORT_ERROR);
    }

    public ExportException(String message, Throwable cause) {
        super(message, cause, AppFrameworkErrorCode.EXPORT_ERROR);
    }
}
