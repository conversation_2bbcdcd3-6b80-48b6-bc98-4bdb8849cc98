package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.ui.layout.TableColumn;
import com.facishare.paas.metadata.impl.ui.layout.component.WhatComponent;
import com.facishare.paas.metadata.ui.layout.ITableColumn;
import com.facishare.paas.metadata.ui.layout.IWhatComponent;
import com.facishare.paas.metadata.util.CopyOnWriteMap;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Getter;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

public class WhatComponentExt {
    @Getter
    @Delegate
    private IWhatComponent whatComponent;

    private WhatComponentExt(IWhatComponent tableComponent) {
        this.whatComponent = tableComponent;
    }

    public static WhatComponentExt of(IWhatComponent component) {
        return new WhatComponentExt(component);
    }

    public void removeFields(Set<String> toRemove) {
        List<Map> fields = (List<Map>) convert().get(IWhatComponent.INCLUDE_FIELDS);
        if (CollectionUtils.empty(fields)) {
            return;
        }
        fields.removeIf(x -> toRemove.contains(TableColumnExt.of(x).getApiName()));
    }

    public void removeFieldByTypes(ObjectDescribeExt describe, String... fieldTypes) {
        Set<String> types = Sets.newHashSet(fieldTypes);
        List<Map> fields = (List<Map>) convert().get(IWhatComponent.INCLUDE_FIELDS);
        if (CollectionUtils.empty(fields)) {
            return;
        }
        fields.removeIf(x -> isInTypes(describe, TableColumnExt.of(x).getTableColumn(), types));
    }

    public void adjustFieldRenderType(String objectApiName) {
        getFieldList().forEach(x -> x.setRenderType(LayoutExt.getRenderType(objectApiName,
                getActualFieldNameOfWhatField(TableColumnExt.of(x).getApiName()), x.getRenderType())));
    }

    private boolean isInTypes(ObjectDescribeExt describe, ITableColumn formField, Set<String> fieldTypeSet) {
        return describe.getFieldDescribeSilently(getActualFieldNameOfWhatField(TableColumnExt.of(formField).getApiName()))
                .map(o -> fieldTypeSet.contains(o.getType())).orElse(false);
    }

    public List<ITableColumn> getFieldList() {
        List<Map> fields = (List<Map>) convert().get(IWhatComponent.INCLUDE_FIELDS);
        return null != fields ? fields.stream().map(TableColumn::new).collect(Collectors.toList()) : Lists.newArrayList();
    }

    private Map convert() {
        return ((WhatComponent) whatComponent).getContainerDocument();
    }

    public void correctLabel(ObjectDescribeExt describeExt) {
        getFieldList().stream()
                .filter(a -> !Strings.isNullOrEmpty(a.getName()))
                .forEach(x -> describeExt.getFieldDescribeSilently(getActualFieldNameOfWhatField(TableColumnExt.of(x).getApiName()))
                        .ifPresent(f -> x.setLabelName(f.getLabel())));
    }

    public void correctLabel(ObjectDescribeExt describe, ObjectDescribeExt whatDescribe) {
        getFieldList().stream()
                .filter(it -> !Strings.isNullOrEmpty(it.getName()))
                .forEach(it -> getActualField(it, describe, whatDescribe).ifPresent(x -> it.setLabelName(x.getLabel())));
    }

    private Optional<IFieldDescribe> getActualField(ITableColumn column, ObjectDescribeExt describe, ObjectDescribeExt whatDescribe) {
        String fieldName = TableColumnExt.of(column).getApiName();
        if (!isWhatField(fieldName)) {
            return describe.getFieldDescribeSilently(fieldName);
        }
        Tuple<String, String> tuple = getWhatObjectApiNameAndActualFieldName(column);
        return Optional.ofNullable(tuple)
                .filter(it -> Objects.equals(it.getKey(), whatDescribe.getApiName()))
                .map(it -> whatDescribe.getFieldDescribe(it.getValue()));
    }

    public void setDefaultFieldListIfEmpty() {
        if (CollectionUtils.notEmpty(getFieldList())) {
            return;
        }
        setIncludeFields(Lists.newArrayList(TableColumnExt.buildNameColumn()));
    }

    public static String getWhatFieldName(String whatObjectApiName, String fieldApiName) {
        return String.format("%s.%s", whatObjectApiName, fieldApiName);
    }

    public static boolean isWhatField(String fieldName) {
        return !Strings.isNullOrEmpty(fieldName) && fieldName.contains(".");
    }

    public static String getActualFieldNameOfWhatField(String fieldName) {
        return isWhatField(fieldName) ? fieldName.substring(fieldName.lastIndexOf(".") + 1) : fieldName;
    }

    private static Tuple<String, String> getWhatObjectApiNameAndActualFieldName(ITableColumn column) {
        String fieldName = TableColumnExt.of(column).getApiName();
        if (isWhatField(fieldName)) {
            return Tuple.of(StringUtils.substringBefore(fieldName, "."), StringUtils.substringAfter(fieldName, "."));
        }
        return null;
    }
}
