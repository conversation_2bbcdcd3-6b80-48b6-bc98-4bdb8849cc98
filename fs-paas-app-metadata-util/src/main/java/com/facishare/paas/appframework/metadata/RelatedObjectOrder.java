package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.impl.ui.layout.component.RelatedObjectList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.jetbrains.annotations.NotNull;

import java.io.InputStream;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by liyiguang on 2017/11/14.
 */
public abstract class RelatedObjectOrder {

    private static Map<String, List<String>> map;

    static {
        init();
    }

    private static void init() {
        try {
            InputStream inputStream = ObjectRelateMapping.class.getResourceAsStream("/object_related_order.json");
            map = JSON.parseObject(inputStream, Map.class);
        } catch (Exception e) {
            throw new RuntimeException("ObjectRelateMapping init failed", e);
        }
    }

    public static List<RelatedObjectList> order(String relateAPIName, List<RelatedObjectList> components) {
        List<String> orderList = map.get(relateAPIName);
        if (CollectionUtils.empty(orderList) || CollectionUtils.empty(components)) {
            return components;
        }
        Map<String, Integer> orderMap = Maps.newHashMap();
        for (int i = 0; i < orderList.size(); i++) {
            orderMap.put(orderList.get(i), i);
        }

        List<RelatedObjectList> ret = components.stream().map(x -> {
            Integer order = orderMap.get(x.getRefObjectApiName()) != null ? orderMap.get(x.getRefObjectApiName()) : Integer.MAX_VALUE;
            return new OrderedComponent(x, order);
        }).sorted().map(x -> x.getComponent()).collect(Collectors.toList());

        return ret;
    }

    @Data
    @AllArgsConstructor
    private static class OrderedComponent implements Comparable<OrderedComponent> {
        private RelatedObjectList component;
        private Integer order;

        @Override
        public int compareTo(@NotNull OrderedComponent o) {
            return this.order.compareTo(o.order);
        }
    }

    public static void main(String[] args) {
        List<String> data = Lists.newArrayList(
                "CRMEmail", "AccountFinInfoObj", "ContactObj", "AccountAddrObj", "abc");

        List<RelatedObjectList> components = data.stream().map(x-> {
            RelatedObjectList component = new RelatedObjectList();
            component.setRefObjectApiName(x);
            return component;
        }).collect(Collectors.toList());

        System.out.println(order("LeadsObj",components));

    }


}
