package com.facishare.paas.appframework.metadata.layout.strcuture;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.GridComponentExt;
import com.facishare.paas.appframework.metadata.ListLayoutExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.appframework.metadata.layout.component.FiltersComponentExt;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.ui.layout.TabSection;
import com.facishare.paas.metadata.impl.ui.layout.component.ComponentFactory;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IGridComponent;
import com.facishare.paas.metadata.ui.layout.INavigationComponent;
import com.facishare.paas.metadata.ui.layout.ITabsComponent;
import com.google.common.collect.Lists;
import org.bson.Document;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2022/11/14
 */
public class StructureLayout extends DocumentBasedBean {
    public static final String COMPONENTS = "components";
    public static final String LABEL_PAGE_NAME = "labelPageName";
    public static final String CAN_DELETE = "canDelete";
    public static final String CUSTOMER_PAGE_LAYOUT_TYPE = "customerPageLayoutType";
    public static final String LAYOUT = "layout";

    public static final String LABEL_INDEX = "labelIndex";

    public StructureLayout() {
    }

    public StructureLayout(Map map) {
        super(map);
    }

    public List<IComponent> getComponents() {
        List<Map> list = (List) get(COMPONENTS);
        if (CollectionUtils.empty(list)) {
            return Lists.newArrayList();
        }
        List<IComponent> result = Lists.newArrayList();
        try {
            for (Map map : list) {
                IComponent component = ComponentFactory.newInstance(map);
                result.add(component);
            }
        } catch (MetadataServiceException e) {
            throw new MetaDataException(SystemErrorCode.METADATA_COMPONENTS_ERROR, e);
        }
        return result;
    }

    public void setComponents(List<IComponent> components) {
        if (components.isEmpty()) {
            return;
        }
        set(COMPONENTS, new ArrayList());
        components.forEach(this::addComponent);
    }

    public void addComponent(IComponent component) {
        Object list = map.get(COMPONENTS);
        if (list instanceof List) {
            ((List) list).add(Document.parse(component.toJsonString()));
        }
    }

    public Optional<IComponent> getComponentByType(String type) {
        return getComponents().stream()
                .filter(component -> Objects.equals(type, component.getType()))
                .findFirst();
    }

    public Optional<ListComponentExt> getListLayoutComponent() {
        return getComponentByType(ListComponentExt.COMPONENT_TYPE_LIST).map(ListComponentExt::of);
    }

    public String getLabelPageName() {
        return get(LABEL_PAGE_NAME, String.class);
    }

    public void setLabelPageName(String label) {
        set(LABEL_PAGE_NAME, label);
    }

    public String getLayoutIndex() {
        return get(LABEL_INDEX, String.class);
    }

    public void setLayoutIndex(String index) {
        set(LABEL_INDEX, index);
    }

    public Boolean getCanDelete() {
        return get(CAN_DELETE, Boolean.class);
    }

    public void setCanDelete(Boolean canDelete) {
        set(CAN_DELETE, canDelete);
    }

    public Integer getCustomerPageLayoutType() {
        return get(CUSTOMER_PAGE_LAYOUT_TYPE, Integer.class);
    }

    public void setCustomerPageLayoutType(Integer customerPageLayoutType) {
        set(CUSTOMER_PAGE_LAYOUT_TYPE, customerPageLayoutType);
    }

    public List<LayoutRow> getLayoutRow() {
        List<Map> list = (List<Map>) get(LAYOUT);
        if (CollectionUtils.empty(list)) {
            return Lists.newArrayList();
        }
        return list.stream()
                .map(LayoutRow::new)
                .collect(Collectors.toList());
    }

    public void resetLayoutRow(List<LayoutRow> layoutRows) {
        if (CollectionUtils.empty(layoutRows)) {
            return;
        }
        set(LAYOUT, Lists.newArrayList());
        layoutRows.forEach(this::addLayoutRow);
    }

    protected void addLayoutRow(LayoutRow layoutRow) {
        Object list = map.get(LAYOUT);
        if (list instanceof List) {
            ((List) list).add(Document.parse(layoutRow.toJsonString()));
        }
    }

    public boolean needHomePageFilters() {
        return getComponents().stream()
                .anyMatch(component -> ListLayoutExt.HOME_PAGE_FILTERS_COMPONENT_TYPE.contains(component.getType()));
    }

    public void setHomePageFilters(User user, Object customFilter, boolean defaultFiltersFlag) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.BI_FILTER_COMPONENT_GRAY, user.getTenantId())) {
            set(FiltersComponentExt.COMPONENT_TYPE_FILTERS, customFilter);
            return;
        }
        List<FiltersComponentExt.BiFilter> biFiltersInLayout = getBiFilters();
        if (CollectionUtils.empty(biFiltersInLayout)) {
            set(FiltersComponentExt.COMPONENT_TYPE_FILTERS, customFilter);
            return;
        }
        // 用户没有个人配置，这里查到的是业务补充的筛选器，此时继续用布局中的配置
        if (defaultFiltersFlag) {
            return;
        }
        Map<String, FiltersComponentExt.BiFilter> customBiFilterMap = buildBiFilters(customFilter).stream()
                .collect(Collectors.toMap(FiltersComponentExt.BiFilter::getFilterType, Function.identity()));
        List<FiltersComponentExt.BiFilter> resultBiFilters = Lists.newArrayList();
        biFiltersInLayout.forEach(layoutBiFilter -> {
            String filterType = layoutBiFilter.getFilterType();
            if (FiltersComponentExt.BiFilter.FILTER_TYPE_DATE.equals(filterType)) {
                resultBiFilters.add(covertDateFilter(layoutBiFilter, customBiFilterMap.get(FiltersComponentExt.BiFilter.FILTER_TYPE_DATE)));
                return;
            }
            if (FiltersComponentExt.BiFilter.FILTER_TYPE_SELECTOR.equals(filterType)) {
                resultBiFilters.add(covertSelectFilter(user, layoutBiFilter, customBiFilterMap.get(FiltersComponentExt.BiFilter.FILTER_TYPE_SELECTOR)));
                return;
            }
            resultBiFilters.add(layoutBiFilter);
        });
        resetBiFilters(resultBiFilters);
    }

    private void resetBiFilters(List<FiltersComponentExt.BiFilter> biFilters) {
        if (CollectionUtils.empty(biFilters)) {
            set(FiltersComponentExt.COMPONENT_TYPE_FILTERS, Lists.newArrayList());
            return;
        }
        List<Map> newBiFLiters = biFilters.stream()
                .map(DocumentBasedBean::getContainerDocument)
                .collect(Collectors.toList());
        set(FiltersComponentExt.COMPONENT_TYPE_FILTERS, newBiFLiters);
    }

    private FiltersComponentExt.BiFilter covertSelectFilter(User user, FiltersComponentExt.BiFilter layoutBiFilter, FiltersComponentExt.BiFilter customBiFilter) {
        FiltersComponentExt.BiFilter result = new FiltersComponentExt.BiFilter();
        result.setFilterType(FiltersComponentExt.BiFilter.FILTER_TYPE_SELECTOR);
        if (Objects.nonNull(customBiFilter) && layoutBiFilter.canEdit()) {
            result.setFilterData(customBiFilter.getFilterData());
            return result;
        }
        result.setFilterData(layoutBiFilter.getFilterData());
        return result;
    }

    private FiltersComponentExt.BiFilter covertDateFilter(FiltersComponentExt.BiFilter layoutBiFilter, FiltersComponentExt.BiFilter customBiFilter) {
        FiltersComponentExt.BiFilter result = new FiltersComponentExt.BiFilter();
        result.setFilterType(FiltersComponentExt.BiFilter.FILTER_TYPE_DATE);
        if (Objects.nonNull(customBiFilter) && layoutBiFilter.canEdit()) {
            result.setFilterData(customBiFilter.getFilterData());
            return result;
        }
        result.setFilterData(layoutBiFilter.getFilterData());
        return result;
    }

    private List<FiltersComponentExt.BiFilter> getBiFilters() {
        Object filter = get(FiltersComponentExt.COMPONENT_TYPE_FILTERS);
        return buildBiFilters(filter);
    }

    private List<FiltersComponentExt.BiFilter> buildBiFilters(Object filter) {
        if (filter instanceof List) {
            List<Map> filters = (List<Map>) filter;
            return FiltersComponentExt.buildFilters(filters);
        }
        return Lists.newArrayList();
    }

    public void removeComponentsAndClearLayoutStructure(List<String> components) {
        if (CollectionUtils.empty(components)) {
            return;
        }
        removeComponents(components);

        getLayoutRow().forEach(row -> {
            List<List<String>> columns = row.getComponents();
            columns.forEach(column -> column.removeIf(components::contains));
        });
    }

    public void removeComponents(List<String> toRemoveComponentNames) {
        if (CollectionUtils.empty(toRemoveComponentNames)) {
            return;
        }
        List<IComponent> components = getComponents();
        components.removeIf(x -> toRemoveComponentNames.contains(x.getName()));

        //清理页签里的组件
        components.stream().filter(x -> ComponentExt.of(x).isTabs()).map(x -> (ITabsComponent) x).forEach(x -> {
            List<TabSection> tabSections = x.getTabs();
            Iterator<TabSection> tabSectionIterator = tabSections.iterator();
            Iterator<List<String>> childComponents = CollectionUtils.nullToEmpty(x.getComponents()).iterator();
            while (childComponents.hasNext()) {
                tabSectionIterator.next();
                List<String> tabChild = CollectionUtils.nullToEmpty(childComponents.next());
                tabChild.removeIf(toRemoveComponentNames::contains);
                if (CollectionUtils.empty(tabChild)) {
                    tabSectionIterator.remove();
                    childComponents.remove();
                }
            }
            x.setTabs(tabSections);
        });

        //清理导航里的组件
        components.stream().filter(x -> ComponentExt.of(x).isNavigation()).map(x -> (INavigationComponent) x).forEach(x -> {
            List<String> children = x.getComponents();
            children.removeIf(toRemoveComponentNames::contains);
        });

        //清理栅格容器里的组件
        components.stream().filter(x -> ComponentExt.of(x).isGrid()).map(x -> (IGridComponent) x).forEach(x -> {
            List<List<String>> childrenList = GridComponentExt.of(x).getComponents();
            childrenList.forEach(children -> children.removeIf(toRemoveComponentNames::contains));
        });

        setComponents(components);
    }

    public Optional<FiltersComponentExt> getBiFilterComponent() {
        return getComponentByType(FiltersComponentExt.COMPONENT_TYPE_FILTERS)
                .map(FiltersComponentExt::of);
    }
}
