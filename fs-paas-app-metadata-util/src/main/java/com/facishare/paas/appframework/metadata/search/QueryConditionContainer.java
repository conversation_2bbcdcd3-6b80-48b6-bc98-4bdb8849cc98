package com.facishare.paas.appframework.metadata.search;

import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.google.common.collect.Lists;
import lombok.Data;

import java.util.List;

@Data
public class QueryConditionContainer {

    private List<IFilter> filters = Lists.newArrayList();

    private List<Wheres> wheres = Lists.newArrayList();

    public void addFilter(IFilter filter) {
        if (filter == null) {
            filters = Lists.newArrayList();
        }
        filters.add(filter);
    }

    public void addWheres(List<Wheres> wheres) {
        if (this.wheres == null) {
            this.wheres = Lists.newArrayList();
        }
        this.wheres.addAll(wheres);
    }

}
