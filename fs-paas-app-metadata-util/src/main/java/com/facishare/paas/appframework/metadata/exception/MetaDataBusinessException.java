package com.facishare.paas.appframework.metadata.exception;

import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.ErrorCode;
import com.facishare.paas.auth.common.exception.AuthException;
import com.facishare.paas.metadata.exception.MetadataServiceException;


/**
 * 元数据业务异常
 * <p>
 * Created by l<PERSON><PERSON><PERSON><PERSON> on 2017/6/27.
 */
public class MetaDataBusinessException extends AppBusinessException {

    public MetaDataBusinessException(String message) {
        super(message, AppFrameworkErrorCode.META_DATA_ERROR);
    }

    public MetaDataBusinessException(String message, int errorCode) {
        super(message, errorCode);
    }

    public MetaDataBusinessException(String message, ErrorCode errorCode) {
        super(message, errorCode);
    }

    public MetaDataBusinessException(String message, Throwable cause) {
        super(message, cause, AppFrameworkErrorCode.META_DATA_ERROR);
    }

    public MetaDataBusinessException(MetadataServiceException cause) {
        super(cause.getMessage(), () -> cause.getErrorCode().getCode());
    }

    public MetaDataBusinessException(AuthException cause) {
        this(cause.getMessage(), cause.getCode());
    }
}
