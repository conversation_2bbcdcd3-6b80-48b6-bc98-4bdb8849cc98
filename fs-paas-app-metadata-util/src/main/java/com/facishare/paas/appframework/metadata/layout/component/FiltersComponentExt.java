package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ui.layout.component.AbstractComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * BI图标的筛选器组件
 * create by zhaoju on 2020/12/02
 */
public class FiltersComponentExt {
    public static final String COMPONENT_TYPE_FILTERS = "filters";

    @Getter
    @Delegate
    private IComponent component;

    private FiltersComponentExt(IComponent component) {
        if (!COMPONENT_TYPE_FILTERS.equals(component.getType())) {
            throw new IllegalArgumentException("type error!");
        }
        this.component = component;
    }

    public static FiltersComponentExt of(IComponent component) {
        return new FiltersComponentExt(component);
    }

    public List<BiFilter> getFilters() {
        List<Map> filters = (List<Map>) component.get("filters");
        return buildFilters(filters);
    }

    public static List<BiFilter> buildFilters(List<Map> filters) {
        if (CollectionUtils.empty(filters)) {
            return Lists.newArrayList();
        }
        List<BiFilter> result = Lists.newArrayList();
        filters.forEach(filter -> result.add(new BiFilter(filter)));
        return result;
    }

    public Optional<BiFilter> getFilter(String filterType) {
        return getFilters().stream()
                .filter(filter -> Objects.equals(filterType, (filter.getFilterType())))
                .findFirst();
    }

    public void updateSelectorFilter(Object filter) {
        if (!(filter instanceof List)) {
            return;
        }
        BiFilter selectorBiFilter = buildFilters((List<Map>) filter).stream()
                .filter(biFilter -> Objects.equals(biFilter.getFilterType(), BiFilter.FILTER_TYPE_SELECTOR))
                .findFirst()
                .orElse(null);
        if (Objects.isNull(selectorBiFilter)) {
            return;
        }
        getFilter(BiFilter.FILTER_TYPE_SELECTOR).ifPresent(selectorFilter -> {
            selectorFilter.setFilterData(selectorBiFilter.getFilterData());
        });
    }

    public static class BiFilter extends AbstractComponent {
        private static final long serialVersionUID = 3540828418983666784L;

        public static final String FILTER_TYPE = "filterType";
        public static final String FILTER_DATA = "filterData";

        public static final String FILTER_TYPE_SELECTOR = "selector";
        public static final String FILTER_TYPE_DATE = "date";
        public static final String FILTER_TYPE_PAGED_EFAULT = "pageDefault";

        public boolean needMergeFilterType() {
            String filterType = getFilterType();
            return FILTER_TYPE_SELECTOR.equals(filterType) || FILTER_TYPE_DATE.equals(filterType);
        }


        public BiFilter() {
            super();
        }

        public BiFilter(Map map) {
            super(map);
        }

        public String getFilterType() {
            return get(FILTER_TYPE, String.class);
        }

        public void setFilterType(String filterType) {
            set(FILTER_TYPE, filterType);
        }

        public String getFilterData() {
            return get(FILTER_DATA, String.class);
        }

        public void setFilterData(String filterData) {
            set(FILTER_DATA, filterData);
        }

        public void mergeFrom(BiFilter that) {
            if (Objects.isNull(that)) {
                return;
            }
            setFilterData(that.getFilterData());
        }

        public boolean canEdit() {
            BiFilterData biFilterData = BiFilterData.from(getFilterData());
            return BooleanUtils.isNotFalse(biFilterData.canEdit());
        }

        @Override
        public void onFieldDelete(IFieldDescribe deletedField) throws MetadataServiceException {

        }

        @Override
        public void onFieldUpdate(String fieldName, Map field) throws MetadataServiceException {

        }
    }

    private static class BiFilterData extends AbstractComponent {
        private static final long serialVersionUID = -2695691650361115057L;

        public static final String BI_FILTER_DATA_TYPE = "type";
        public static final String BI_FILTER_DATA_CAN_EDIT = "canEdit";
        public static final String BI_FILTER_DATA_EMPS_AND_DEPS = "empsAndDeps";
        public static final String BI_FILTER_DATA_IS_ALL = "isAll";

        private static BiFilterData from(String jsonStr) {
            BiFilterData biFilterData = new BiFilterData();
            biFilterData.fromJsonString(jsonStr);
            return biFilterData;
        }

        public Boolean canEdit() {
            return get(BI_FILTER_DATA_CAN_EDIT, Boolean.class);
        }

        @Override
        public void onFieldDelete(IFieldDescribe iFieldDescribe) throws MetadataServiceException {

        }

        @Override
        public void onFieldUpdate(String s, Map map) throws MetadataServiceException {

        }
    }
}
