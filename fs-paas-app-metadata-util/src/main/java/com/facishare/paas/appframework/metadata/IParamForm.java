package com.facishare.paas.appframework.metadata;

import java.util.Map;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/02/11
 */
public interface IParamForm {
    String IS_REQUIRED = "is_required";
    String OBJECT_API_NAME = "object_api_name";
    String API_NAME = "api_name";
    String LABEL = "label";
    String TYPE = "type";
    String DEFINE_TYPE = "define_type";
    String USED_IN = "used_in";

    String DEFINE_TYPE_SYSTEM = "system";
    String DEFINE_TYPE_CUSTOM = "custom";

    boolean getIsRequired();

    void setIsRequired(boolean isRequired);

    String getObjectApiName();

    void setObjectApiName(String objectApiName);

    String getApiName();

    void setApiName(String apiName);

    String getLabel();

    void setLabel(String label);

    String getType();

    void setType(String type);

    String getUsedIn();

    void setUsedIn(String usedIn);

    void mergeFromFieldDescribe(FieldDescribeExt fieldDescribeExt);

    String convertToFieldApiName();

    /**
     * 因为历史数据原因，系统预置的参数一定是 system， 自定义参数可能是 custom， 也可能是 null， 判断的时候需要注意一下
     * @return 参数类型
     */
    String getDefineType();

    Map<String, Object> toMap();
}
