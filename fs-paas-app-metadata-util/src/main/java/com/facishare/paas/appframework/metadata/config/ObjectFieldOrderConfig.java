package com.facishare.paas.appframework.metadata.config;

import com.github.autoconf.ConfigFactory;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.Maps;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/08/21
 */
public enum ObjectFieldOrderConfig {
    DEFAULT_ORDER(),
    ;

    ObjectFieldOrderConfig() {
        init();
    }

    public List<String> getFieldOrder(String objectApiName) {
        return objectApiName2FiledOrderInfo.getOrDefault(objectApiName, Collections.emptyList());
    }

    private void init() {
        objectApiName2FiledOrderInfo = Maps.newHashMap();
        ConfigFactory.getConfig("field_privielge_order", config -> {
            List<String> lines = config.getLines();
            for (String line : lines) {
                String[] split = line.split("=");

                String[] fields = split[1].split(",");
                List<String> fieldOrderInfo = ImmutableList.copyOf(fields);
                objectApiName2FiledOrderInfo.put(split[0], fieldOrderInfo);
            }
        });
    }

    public Map<String, List<String>> objectApiName2FiledOrderInfo;
}
