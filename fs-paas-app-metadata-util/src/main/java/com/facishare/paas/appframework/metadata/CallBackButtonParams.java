package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.ObjectAction;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/09/24
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CallBackButtonParams {
    private String buttonApiName;
    private String actionName;
    private List<String> ids;
    private List<String> buttonParams;

    public static CallBackButtonParams of(List<String> ids, ObjectAction objectAction, List<String> params) {
        return builder()
                .buttonApiName(objectAction.getButtonApiName())
                .actionName(objectAction.getActionCode())
                .buttonParams(params)
                .ids(ids)
                .build();
    }

    public String toJson() {
        return JSON.toJSONString(this);
    }
}
