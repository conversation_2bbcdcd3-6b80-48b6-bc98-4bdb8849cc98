package com.facishare.paas.appframework.metadata.layout.component;

import org.apache.commons.lang3.BooleanUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/11/16
 */
public class ViewComponentInfo extends AbstractComponentInfoDocument implements IViewComponentInfo {
    private static final long serialVersionUID = 1640838484577233334L;

    public ViewComponentInfo() {
    }

    public ViewComponentInfo(Map map) {
        super(map);
    }
    public static IViewComponentInfo treeView() {
        IViewComponentInfo viewInfo = new ViewComponentInfo();
        viewInfo.setName(IViewComponentInfo.TREE_VIEW);
        viewInfo.setIsDefault(false);
        viewInfo.setIsShow(true);
        return viewInfo;
    }
    @Override
    public String getName() {
        return get(NAME, String.class);
    }

    @Override
    public void setName(String name) {
        set(NAME, name);
    }

    @Override
    public boolean isShow() {
        return BooleanUtils.isNotFalse(get(IS_SHOW, Boolean.class));
    }

    @Override
    public void setIsShow(boolean isShow) {
        set(IS_SHOW, isShow);
    }

    @Override
    public boolean isDefault() {
        return BooleanUtils.isTrue(get(IS_DEFAULT, Boolean.class));
    }

    @Override
    public void setIsDefault(boolean isDefault) {
        set(IS_DEFAULT, isDefault);
    }

    @Override
    public List<String> getTimeDimension() {
        return getList(TIME_DIMENSION);
    }

    @Override
    public void setTimeDimension(List<String> timeDimension) {
        set(TIME_DIMENSION, timeDimension);
    }

    @Override
    public String getDisplayType() {
        return get(DISPLAY_TYPE, String.class);
    }

    @Override
    public void setDisplayType(String displayType) {
        set(DISPLAY_TYPE, displayType);
    }

    @Override
    public List<String> getFields() {
        return getList(FIELDS);
    }

    @Override
    public void setFields(List<String> fields) {
        set(FIELDS, fields);
    }

    @Override
    public String getLocationField() {
        return get(LOCATION_FIELD, String.class);
    }

    @Override
    public void setLocationField(String locationField) {
        set(LOCATION_FIELD, locationField);
    }

    @Override
    public IMapViewBubbleInfo getBubbleInfo() {
        Map map = get(BUBBLE_INFO, Map.class);
        if (Objects.isNull(map)) {
            return null;
        }
        return new MapViewBubbleInfo(map);
    }

    @Override
    public void setBubbleInfo(IMapViewBubbleInfo bubbleInfo) {
        if (Objects.isNull(bubbleInfo)) {
            return;
        }
        set(BUBBLE_INFO, ((MapViewBubbleInfo) bubbleInfo).getContainerDocument());
    }
}
