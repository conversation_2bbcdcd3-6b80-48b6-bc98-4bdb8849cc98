package com.facishare.paas.appframework.metadata.restdriver;

import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.metadata.ResourceLoader;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 注册管理各个SFA老对象的数据转换器
 * <p>
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2017/9/20.
 */
@Service
public class ObjectDataConverterManager {

    private Map<String, ObjectDataConverter> converterMap = Maps.newHashMap();

    public ObjectDataConverter getObjectDataConverter(String apiName) {
        return converterMap.computeIfAbsent(apiName, key -> {
            Set<Tuple<String, String>> mappings = ResourceLoader.loadMapping(getResourcePackageName(), key);
            if (Objects.isNull(mappings)) {
                return null;
            }
            return new BaseObjectDataConverter(mappings);
        });
    }

    protected String getResourcePackageName() {
        return "object_fields_mapping";
    }

}
