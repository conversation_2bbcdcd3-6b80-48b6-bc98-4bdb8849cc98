package com.facishare.paas.appframework.metadata.filter;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.LocalDateUtils;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.describe.DateFieldDescribe;
import com.facishare.paas.metadata.impl.describe.SelectOneFieldDescribe;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.timezone.DateTimeFormat;
import com.facishare.paas.timezone.DateUtils;
import com.facishare.paas.timezone.TimeZoneContext;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Builder;

import java.time.LocalDate;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.LocalDateUtils.*;

/**
 * Created by zhouwr on 2018/4/19
 */
@Builder
public class ObjectDataFilter {

    private static final Set<String> manyFields = Sets.newHashSet(IFieldType.EMPLOYEE, IFieldType.SELECT_MANY, IFieldType.EMPLOYEE_MANY, IFieldType.DEPARTMENT_MANY);

    private ObjectDescribeExt describeExt;
    private SearchTemplateQueryExt queryExt;
    private String filterLabel;

    public List<IObjectData> doFilter(List<IObjectData> dataList) {
        return doFilter(dataList, false);
    }

    public List<IObjectData> doFilter(List<IObjectData> dataList, boolean skipFieldJudgment) {
        if (CollectionUtils.empty(dataList)) {
            return dataList;
        }
        List<IObjectData> dataListToFillId = ObjectDataExt.fillDataId(dataList);
        dataList = filterWithFilters(dataList, queryExt.getFilters(), skipFieldJudgment);
        dataList = filterWithWheres(dataList, queryExt.getWheres(), skipFieldJudgment);
        ObjectDataExt.removeDataId(dataListToFillId);

        return dataList;
    }

    private List<IObjectData> filterWithFilters(List<IObjectData> resultList, List<IFilter> filterList, boolean skipFieldJudgment) {
        if (CollectionUtils.empty(filterList) || CollectionUtils.empty(resultList)) {
            return resultList;
        }
        for (IFilter filter : filterList) {
            resultList = filterWithFilter(resultList, filter, skipFieldJudgment);
        }

        return resultList;
    }

    private List<IObjectData> filterWithWheres(List<IObjectData> resultList, List<Wheres> wheresList, boolean skipFieldJudgment) {
        if (CollectionUtils.empty(wheresList) || CollectionUtils.empty(resultList)) {
            return resultList;
        }

        List<IObjectData> validDataList = Lists.newArrayList();
        for (Wheres wheres : wheresList) {
            List<IObjectData> orResultList = filterWithFilters(resultList, wheres.getFilters(), skipFieldJudgment);
            validDataList.addAll(orResultList);
        }
        return validDataList.stream().distinct().collect(Collectors.toList());
    }

    private List<IObjectData> filterWithFilter(List<IObjectData> resultList, IFilter filter, boolean skipFieldJudgment) {
        if (CollectionUtils.empty(resultList)) {
            return resultList;
        }
        handleFilterByOperator(filter);
        String fieldName = filter.getFieldName();
        String fieldType = skipFieldJudgment ? IFieldType.TEXT : getFieldType(filter);
        List<String> filterValues = filter.getFieldValues();
        Comparator comparator = getComparator(filter, skipFieldJudgment);

        return resultList.stream()
                .filter(x -> {
                    Object leftValue = convertFieldValue(x.get(fieldName), filter, skipFieldJudgment);
                    Object rightValue = comparator.needFilterValues() ? filterValues : getFilterValue(filter, x, fieldType, skipFieldJudgment);
                    return comparator.compare(leftValue, rightValue, fieldType);
                })
                .collect(Collectors.toList());
    }

    private Object convertFieldValue(Object value, IFilter filter, boolean skipFieldJudgment) {
        if (Objects.isNull(value)) {
            return value;
        }
        if (skipFieldJudgment) {
            return value instanceof Collection ? value : String.valueOf(value);
        }
        String fieldName = filter.getFieldName();
        //将部门字段补的特殊字符去掉
        fieldName = getDepartOrginalFieldApiName(fieldName);

        IFieldDescribe fieldDescribe = describeExt.getActiveFieldDescribe(fieldName, filterLabel);
        if (IFieldType.SELECT_ONE.equals(fieldDescribe.getType())) {
            List<ISelectOption> selectOptions = ((SelectOneFieldDescribe) fieldDescribe).getSelectOptions();
            if (selectOptions.stream().map(x -> x.getValue()).collect(Collectors.toList()).contains(String.valueOf(value))) {
                return String.valueOf(value);
            } else {
                return null;
            }
        }
        if (Operator.EQL.equals(filter.getOperator())) {
            value = convertDateFieldValue(value, filter.getFieldName());
        }
        return value instanceof Collection ? value : String.valueOf(value);
    }

    private Object convertDateFieldValue(Object value, String filterValueFiledApiName) {
        if (Objects.isNull(value)) {
            return value;
        }
        IFieldDescribe fieldDescribe = describeExt.getActiveFieldDescribe(filterValueFiledApiName, filterLabel);
        if (IFieldType.DATE.equals(fieldDescribe.getType())) {
            DateFieldDescribe dateFieldDescribe = (DateFieldDescribe) fieldDescribe;
            return LocalDateUtils.transDateByFormat(dateFieldDescribe.getDateFormat(), (Long) value);
        }
        return value instanceof Collection ? value : String.valueOf(value);
    }

    private String getFieldType(IFilter filter) {
        String fieldName = filter.getFieldName();
        //将部门字段补的特殊字符去掉
        fieldName = getDepartOrginalFieldApiName(fieldName);

        IFieldDescribe fieldDescribe = describeExt.getActiveFieldDescribe(fieldName, filterLabel);
        if (IFieldType.COUNT.equals(fieldDescribe.getType())) {
            return ((Count) fieldDescribe).getReturnType();
        }
        if (IFieldType.FORMULA.equals(fieldDescribe.getType())) {
            return ((Formula) fieldDescribe).getReturnType();
        }
        return fieldDescribe.getType();
    }

    private Comparator getComparator(IFilter filter, boolean skipFieldJudgment) {
        Operator operator = filter.getOperator();
        if (skipFieldJudgment) {
            return Comparator.of(operator);
        }
        String fieldName = filter.getFieldName();
        //将部门字段补的特殊字符去掉
        fieldName = getDepartOrginalFieldApiName(fieldName);

        IFieldDescribe fieldDescribe = describeExt.getActiveFieldDescribe(fieldName, filterLabel);

        if (operator == Operator.IN) {
            if (IFieldType.SELECT_MANY.equals(fieldDescribe.getType())
                    || IFieldType.DEPARTMENT_MANY.equals(fieldDescribe.getType())
                    || IFieldType.EMPLOYEE_MANY.equals(fieldDescribe.getType())
            ) {
                return Comparator.CONTAINS;
            }
            if (IFieldType.EMPLOYEE.equals(fieldDescribe.getType())) {
                if (Boolean.FALSE.equals(((Employee) fieldDescribe).isSingle())) {
                    return Comparator.CONTAINS;
                }
            }
            if (IFieldType.DEPARTMENT.equals(fieldDescribe.getType())) {
                if (Boolean.FALSE.equals(((Department) fieldDescribe).isSingle())) {
                    return Comparator.CONTAINS;
                }
            }
        } else if (operator == Operator.NIN) {
            if (IFieldType.SELECT_MANY.equals(fieldDescribe.getType())
                    || IFieldType.DEPARTMENT_MANY.equals(fieldDescribe.getType())
                    || IFieldType.EMPLOYEE_MANY.equals(fieldDescribe.getType())
            ) {
                return Comparator.NCONTAINS;
            }
            if (IFieldType.EMPLOYEE.equals(fieldDescribe.getType())) {
                if (Boolean.FALSE.equals(((Employee) fieldDescribe).isSingle())) {
                    return Comparator.NCONTAINS;
                }
            }
            if (IFieldType.DEPARTMENT.equals(fieldDescribe.getType())) {
                if (Boolean.FALSE.equals(((Department) fieldDescribe).isSingle())) {
                    return Comparator.NCONTAINS;
                }
            }
        }

        return Comparator.of(operator);
    }

    private String getDepartOrginalFieldApiName(String fieldName) {
        if (ObjectDataExt.isParentDeptSymbolField(fieldName)) {
            fieldName = fieldName.substring("$parentDept".length(), fieldName.length() - 1);
        }
        return fieldName;
    }

    private Object getFilterValue(IFilter filter, IObjectData data, String fieldType, boolean skipFieldJudgment) {
        List<String> filterValues = filter.getFieldValues();
        Object value;
        if (manyFields.contains(fieldType)) {
            return filterValues;
        }
        if (FilterExt.of(filter).hasObjectVariableValueType()) {
            String variableName = FilterExt.of(filter).getVariableNameInValues();
            if (Operator.EQL.equals(filter.getOperator())) {
                value = convertDateFieldValue(data.get(variableName), variableName);
            } else {
                value = convertFieldValue(data.get(variableName), filter, skipFieldJudgment);
            }
        } else {
            value = CollectionUtils.empty(filterValues) ? null : filterValues.get(0);
        }
        return value;
    }

    /**
     * 使用 EQL 筛选日期,需要转换为 BETWEEN startTime and endTime
     *
     * @param filter
     */
    private void handleFilterByOperator(IFilter filter) {
        Operator operator = filter.getOperator();
        if (operator != Operator.EQL) {
            return;
        }
        IFieldDescribe fieldDescribe = describeExt.getActiveFieldDescribe(filter.getFieldName(), filterLabel);
        // 暂时只处理真实类型是日期的字段,不处理计算,引用字段
        if (!IFieldType.DATE.equals(fieldDescribe.getType())) {
            return;
        }
        List<String> filterValues = filter.getFieldValues();
        Date dateField = (Date) fieldDescribe;
        String dateFormat = dateField.getDateFormat();
        if (FilterExt.of(filter).hasConstantValueType()) {
            Tuple<Long, Long> tuple = getStartTimeAndEndTime(dateFormat, Long.parseLong(filterValues.get(0)));
            List<String> values = Lists.newArrayList(String.valueOf(tuple.getKey()), String.valueOf(tuple.getValue()));
            filter.setFieldValues(values);
            filter.setOperator(Operator.BETWEEN);
        }
    }

    private Tuple<Long, Long> getStartTimeAndEndTime(String dateFormat, long timestamp) {
        LocalDate localDate = DateTimeFormat.DATE.convertToLocalDateTime(timestamp, TimeZoneContext.DEFAULT_TIME_ZONE).toLocalDate();
        if (YEAR_DATE.equals(dateFormat)) {
            long startTime = DateUtils.getDayStartTime(DateUtils.getYearStartDay(localDate), TimeZoneContext.DEFAULT_TIME_ZONE);
            long endTime = DateUtils.getDayEndTime(DateUtils.getYearEndDay(localDate), TimeZoneContext.DEFAULT_TIME_ZONE);
            return Tuple.of(startTime, endTime);
        }
        if (QUOTE_DATE.equals(dateFormat)) {
            long startTime = DateUtils.getDayStartTime(DateUtils.getQuarterStartDay(localDate), TimeZoneContext.DEFAULT_TIME_ZONE);
            long endTime = DateUtils.getDayEndTime(DateUtils.getQuarterEndDay(localDate), TimeZoneContext.DEFAULT_TIME_ZONE);
            return Tuple.of(startTime, endTime);
        }
        if (MONTH_DATE.equals(dateFormat)) {
            long startTime = DateUtils.getDayStartTime(DateUtils.getMonthStartDay(localDate), TimeZoneContext.DEFAULT_TIME_ZONE);
            long endTime = DateUtils.getDayEndTime(DateUtils.getMonthEndDay(localDate), TimeZoneContext.DEFAULT_TIME_ZONE);
            return Tuple.of(startTime, endTime);
        }
        long startTime = DateUtils.getDayStartTime(localDate, TimeZoneContext.DEFAULT_TIME_ZONE);
        long endTime = DateUtils.getDayEndTime(localDate, TimeZoneContext.DEFAULT_TIME_ZONE);
        return Tuple.of(startTime, endTime);
    }

}
