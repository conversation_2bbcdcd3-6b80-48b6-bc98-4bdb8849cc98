package com.facishare.paas.appframework.metadata.layout.component;

import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.StringUtils;

import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class SuspendedComponentInfoExt extends SuspendedComponentInfo {

    @Getter
    @Delegate
    protected final IComponent component;

    public SuspendedComponentInfoExt(IComponent component) {
        this.component = component;
    }

    public List<Integer> removeActions(Map<String, List<IRecordTypeOption>> validRecordTypeListMap) {
        List<SuspendedActionInfo> actions = getActions();
        List<Integer> actionIndexList = Lists.newArrayList();
        Iterator<SuspendedActionInfo> iterator = actions.iterator();
        while (iterator.hasNext()) {
            SuspendedActionInfo action = iterator.next();
            if (StringUtils.equals(SuspendedActionInfo.NEW_OBJECT, action.getType())) {
                if (validRecordTypeListMap.containsKey(action.getRelatedObjectApiName())) {
                    List<String> recordApiNames = validRecordTypeListMap.get(action.getRelatedObjectApiName()).stream()
                            .map(IRecordTypeOption::getApiName).collect(Collectors.toList());
                    if (StringUtils.isNotEmpty(action.getRelatedObjectRecordType()) // 为空 "" 表示全部业务类型
                            && !recordApiNames.contains(action.getRelatedObjectRecordType())) {
                        actionIndexList.add(actions.indexOf(action));
                        iterator.remove();
                    }
                } else {
                    actionIndexList.add(actions.indexOf(action));
                    iterator.remove();
                }
            }
        }
        setActions(actions);
        return actionIndexList;
    }

    public void removeI18nPropsActions(List<Integer> actionIndexList) {
        Iterator<Map> iterator = getActionsInI18nProps().iterator();
        int currentIndex = 0;
        while (iterator.hasNext()) {
            iterator.next();
            if (actionIndexList.contains(currentIndex)) {
                iterator.remove();
            }
            currentIndex++;
        }
    }

    public static SuspendedComponentInfoExt of(IComponent component) {
        return new SuspendedComponentInfoExt(component);
    }

    public List<Map> getActionsInI18nProps () {
        Object actions = getI18NProps().get(ACTIONS);
        if (actions instanceof List) {
            return (List<Map>) actions;
        }
        return Lists.newArrayList();
    }
}
