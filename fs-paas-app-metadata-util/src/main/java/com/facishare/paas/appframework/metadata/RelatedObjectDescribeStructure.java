package com.facishare.paas.appframework.metadata;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

/**
 * Created by zhouwr on 2017/11/1
 */
@Data
@Builder
public class RelatedObjectDescribeStructure {
    @JsonProperty("RelatedObjectDescribe")
    @SerializedName("RelatedObjectDescribe")
    private IObjectDescribe relatedObjectDescribe;
    private String fieldApiName;
    private String fieldLabel;
    private String relatedListName;
    private String relatedListLabel;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        RelatedObjectDescribeStructure that = (RelatedObjectDescribeStructure) o;
        return Objects.equals(relatedObjectDescribe.getApiName(), that.relatedObjectDescribe.getApiName()) &&
                Objects.equals(relatedListName, that.relatedListName);
    }

    @Override
    public int hashCode() {
        return Objects.hash(relatedObjectDescribe.getApiName(), relatedListName);
    }
}
