package com.facishare.paas.appframework.metadata.state;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/9/5
 */
public enum MergeState {
    ADD {
        @Override
        protected MergeState add() {
            return ADD;
        }

        @Override
        protected MergeState edit() {
            return ADD;
        }

        @Override
        protected MergeState delete() {
            return UNKNOWN;
        }
    },
    EDIT {
        @Override
        protected MergeState add() {
            return EDIT;
        }

        @Override
        protected MergeState edit() {
            return EDIT;
        }

        @Override
        protected MergeState delete() {
            return DELETE;
        }
    },
    DELETE {
        @Override
        protected MergeState add() {
            return EDIT;
        }

        @Override
        protected MergeState edit() {
            return EDIT;
        }

        @Override
        protected MergeState delete() {
            return DELETE;
        }
    },
    UNKNOWN {
        @Override
        protected MergeState add() {
            return ADD;
        }

        @Override
        protected MergeState edit() {
            return UNKNOWN;
        }

        @Override
        protected MergeState delete() {
            return UNKNOWN;
        }
    },
    ;

    public MergeState change(MergeState mergeState) {
        switch (mergeState) {
            case ADD:
                return add();
            case EDIT:
                return edit();
            case DELETE:
                return delete();
            default:
                return UNKNOWN;
        }
    }

    protected abstract MergeState add();

    protected abstract MergeState edit();

    protected abstract MergeState delete();

}
