package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.SelectMany;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.experimental.Delegate;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2018/6/25
 */
public class SelectManyExt {

    @Getter
    @Delegate
    private SelectMany selectMany;

    private SelectManyExt(SelectMany selectMany) {
        this.selectMany = selectMany;
    }

    public static SelectManyExt of(SelectMany selectMany) {
        return new SelectManyExt(selectMany);
    }

    public List<String> getLabelsByValues(List<String> values) {

        if (CollectionUtils.empty(values)) {
            return Lists.newArrayList();
        }
        List<ISelectOption> selectOptions = selectMany.getSelectOptions();
        return selectOptions.stream()
                .filter(x -> values.contains(x.getValue()))
                .map(ISelectOption::getLabel)
                .collect(Collectors.toList());
    }

    public String getOptionType() {
        return get(SelectMany.OPTION_TYPE, String.class);
    }

    public String getOptionApiName() {
        return get(SelectMany.OPTION_API_NAME, String.class);
    }

    public void setOptionApiName(String optionApiName) {
        set(SelectMany.OPTION_API_NAME, optionApiName);
        set(SelectMany.OPTION_TYPE, SelectMany.GENERAL_OPTION_TYPE);
    }

}
