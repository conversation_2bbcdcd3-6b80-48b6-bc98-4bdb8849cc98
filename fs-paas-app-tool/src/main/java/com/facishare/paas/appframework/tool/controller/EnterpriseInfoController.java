package com.facishare.paas.appframework.tool.controller;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.tool.dto.EnterpriseInfo;
import com.facishare.paas.appframework.tool.dto.EnterpriseInfoDto;
import com.facishare.paas.appframework.tool.service.EnterpriseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Path("/v1/inner/rest/enterpriseInfo")
@Controller
@RestAPI
public class EnterpriseInfoController {
    @Autowired
    private EnterpriseInfoService enterprisesInfoService;

    @POST
    @Path("/batchFindEnterpriseInfo")
    public EnterpriseInfoDto.Result getTeamMember(EnterpriseInfoDto.Arg arg) {
        if (Objects.isNull(arg)) {
            throw new ValidateException(I18N.text(I18NKey.PARAM_EMPTY));
        }
        if (CollectionUtils.empty(arg.getTenantIdList())){
            return EnterpriseInfoDto.Result.builder().build();
        }
        Map<String, EnterpriseInfo> enterpriseInfoByTenantId =
                enterprisesInfoService.findEnterpriseInfoByTenantId(arg.getTenantIdList());
        return EnterpriseInfoDto.Result.builder().result(enterpriseInfoByTenantId).build();
    }

}
