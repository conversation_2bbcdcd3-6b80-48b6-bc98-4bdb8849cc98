<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:p="http://www.springframework.org/schema/p"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="restServiceProxyFactory" class="com.facishare.rest.core.RestServiceProxyFactory"
          p:configName="fs-paas-appframework-rest" init-method="init"/>

    <bean id="newPaasMetadataResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.rest.api.resource.NewPaasMetadataResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>

    <bean id="paasOrgResource" class="com.facishare.rest.core.RestServiceProxyFactoryBean"
          p:type="com.facishare.rest.api.resource.PaasOrgResource">
        <property name="factory" ref="restServiceProxyFactory"/>
    </bean>
</beans>