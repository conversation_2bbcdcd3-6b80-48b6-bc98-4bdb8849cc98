package com.facishare.paas.appframework.button.action

import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.GlobalVarService
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverterManager
import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.api.describe.IGlobalVariableDescribe
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.GlobalVariableDescribe
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

/**
 * ParseVarRender单元测试
 * 用于测试ParseVarRender类的所有功能
 */
@Unroll
class ParseVarRenderTest extends Specification {

    GlobalVarService globalVarService = Mock(GlobalVarService)
    FieldDataConverterManager fieldDataConverterManager = Mock(FieldDataConverterManager)
    OrgService orgService = Mock(OrgService)
    
    def user = User.systemUser('74255')
    def variables = []

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试Builder模式创建ParseVarRender对象
     */
    def "builderTest"() {
        given:
        def testVariables = createTestVariables()
        
        when:
        def parseVarRender = ParseVarRender.builder()
            .globalVarService(globalVarService)
            .fieldDataConverterManager(fieldDataConverterManager)
            .orgService(orgService)
            .variables(testVariables)
            .user(user)
            .build()
        
        then:
        parseVarRender.globalVarService == globalVarService
        parseVarRender.fieldDataConverterManager == fieldDataConverterManager
        parseVarRender.orgService == orgService
        parseVarRender.variables == testVariables
        parseVarRender.user == user
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试render方法的正常执行流程，包含全局变量处理
     */
    def "renderTestNormalFlow"() {
        given:
        def globalVariable = createGlobalVariable()
        def testVariables = [globalVariable]
        
        def parseVarRender = ParseVarRender.builder()
            .globalVarService(globalVarService)
            .fieldDataConverterManager(fieldDataConverterManager)
            .orgService(orgService)
            .variables(testVariables)
            .user(user)
            .build()

        def mockGlobalVarDescribe = Mock(IGlobalVariableDescribe)
        mockGlobalVarDescribe.getApiName() >> "testVar__g"
        mockGlobalVarDescribe.getType() >> "text"

        when:
        parseVarRender.render()

        then:
        1 * globalVarService.findGlobalVariables(user.getTenantId(), ["testVar__g"]) >> ["testVar__g": mockGlobalVarDescribe]
        1 * globalVarService.parseValue(mockGlobalVarDescribe) >> "parsed_value"
        
        // 验证内部状态
        parseVarRender.globalVariables != null
        parseVarRender.globalVarParseValueMap != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试render方法处理空变量列表的场景
     */
    def "renderTestEmptyVariables"() {
        given:
        def parseVarRender = ParseVarRender.builder()
            .globalVarService(globalVarService)
            .fieldDataConverterManager(fieldDataConverterManager)
            .orgService(orgService)
            .variables([])
            .user(user)
            .build()

        when:
        parseVarRender.render()

        then:
        0 * globalVarService.findGlobalVariables(_, _)
        0 * globalVarService.parseValue(_)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试render方法处理多种类型变量的场景
     */
    def "renderTestMultipleVariableTypes"() {
        given:
        def globalVariable = createGlobalVariable()
        def objectFieldVariable = createObjectFieldVariable()
        def buttonVariable = createButtonVariable()
        def currentObjectVariable = createCurrentObjectVariable()
        
        def testVariables = [globalVariable, objectFieldVariable, buttonVariable, currentObjectVariable]
        
        def parseVarRender = ParseVarRender.builder()
            .globalVarService(globalVarService)
            .fieldDataConverterManager(fieldDataConverterManager)
            .orgService(orgService)
            .variables(testVariables)
            .user(user)
            .build()

        def mockGlobalVarDescribe = Mock(IGlobalVariableDescribe)
        mockGlobalVarDescribe.getApiName() >> "testVar__g"

        when:
        parseVarRender.render()

        then:
        1 * globalVarService.findGlobalVariables(user.getTenantId(), ["testVar__g"]) >> ["testVar__g": mockGlobalVarDescribe]
        1 * globalVarService.parseValue(mockGlobalVarDescribe) >> "parsed_value"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试pretreatmentGlobalVar方法处理globalVarService为null的场景
     */
    def "pretreatmentGlobalVarTestNullService"() {
        given:
        def testVariables = [createGlobalVariable()]
        
        def parseVarRender = ParseVarRender.builder()
            .globalVarService(null)
            .fieldDataConverterManager(fieldDataConverterManager)
            .orgService(orgService)
            .variables(testVariables)
            .user(user)
            .build()

        when:
        parseVarRender.render()

        then:
        // 不应该有任何全局变量服务调用
        noExceptionThrown()
        parseVarRender.globalVariables == null
        parseVarRender.globalVarParseValueMap == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试pretreatmentGlobalVar方法处理没有全局变量的场景
     */
    def "pretreatmentGlobalVarTestNoGlobalVariables"() {
        given:
        def testVariables = [createObjectFieldVariable()]
        
        def parseVarRender = ParseVarRender.builder()
            .globalVarService(globalVarService)
            .fieldDataConverterManager(fieldDataConverterManager)
            .orgService(orgService)
            .variables(testVariables)
            .user(user)
            .build()

        when:
        parseVarRender.render()

        then:
        0 * globalVarService.findGlobalVariables(_, _)
        0 * globalVarService.parseValue(_)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试batchParseValue方法的正常执行
     */
    def "batchParseValueTest"() {
        given:
        def testVariables = [createGlobalVariable()]
        
        def parseVarRender = ParseVarRender.builder()
            .globalVarService(globalVarService)
            .fieldDataConverterManager(fieldDataConverterManager)
            .orgService(orgService)
            .variables(testVariables)
            .user(user)
            .build()

        def globalVarDescribe1 = Mock(IGlobalVariableDescribe)
        globalVarDescribe1.getApiName() >> "var1__g"
        def globalVarDescribe2 = Mock(IGlobalVariableDescribe)
        globalVarDescribe2.getApiName() >> "var2__g"

        when:
        parseVarRender.render()

        then:
        1 * globalVarService.findGlobalVariables(user.getTenantId(), ["testVar__g"]) >> [
            "var1__g": globalVarDescribe1,
            "var2__g": globalVarDescribe2
        ]
        1 * globalVarService.parseValue(globalVarDescribe1) >> "value1"
        1 * globalVarService.parseValue(globalVarDescribe2) >> "value2"
        
        parseVarRender.globalVarParseValueMap["var1__g"] == "value1"
        parseVarRender.globalVarParseValueMap["var2__g"] == "value2"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试render方法在变量分组后的处理逻辑
     */
    def "renderTestVariableGrouping"() {
        given:
        def globalVar1 = createGlobalVariable("var1__g")
        def globalVar2 = createGlobalVariable("var2__g")
        def objectField = createObjectFieldVariable()
        
        def testVariables = [globalVar1, globalVar2, objectField]
        
        def parseVarRender = ParseVarRender.builder()
            .globalVarService(globalVarService)
            .fieldDataConverterManager(fieldDataConverterManager)
            .orgService(orgService)
            .variables(testVariables)
            .user(user)
            .build()

        def mockGlobalVarDescribe1 = Mock(IGlobalVariableDescribe)
        mockGlobalVarDescribe1.getApiName() >> "var1__g"
        def mockGlobalVarDescribe2 = Mock(IGlobalVariableDescribe)
        mockGlobalVarDescribe2.getApiName() >> "var2__g"

        when:
        parseVarRender.render()

        then:
        1 * globalVarService.findGlobalVariables(user.getTenantId(), ["var1__g", "var2__g"]) >> [
            "var1__g": mockGlobalVarDescribe1,
            "var2__g": mockGlobalVarDescribe2
        ]
        1 * globalVarService.parseValue(mockGlobalVarDescribe1) >> "value1"
        1 * globalVarService.parseValue(mockGlobalVarDescribe2) >> "value2"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试render方法处理Variable.Type.handelVariable调用
     */
    def "renderTestHandelVariableCall"() {
        given:
        def testVariables = [createObjectFieldVariable()]
        
        def parseVarRender = ParseVarRender.builder()
            .globalVarService(globalVarService)
            .fieldDataConverterManager(fieldDataConverterManager)
            .orgService(orgService)
            .variables(testVariables)
            .user(user)
            .build()

        when:
        parseVarRender.render()

        then:
        // 验证不会抛出异常，说明handelVariable方法被正确调用
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试pretreatmentButtonVariables方法（当前为空实现）
     */
    def "pretreatmentButtonVariablesTest"() {
        given:
        def testVariables = [createButtonVariable()]
        
        def parseVarRender = ParseVarRender.builder()
            .globalVarService(globalVarService)
            .fieldDataConverterManager(fieldDataConverterManager)
            .orgService(orgService)
            .variables(testVariables)
            .user(user)
            .build()

        when:
        parseVarRender.render()

        then:
        // 当前pretreatmentButtonVariables是空实现，不应该抛出异常
        noExceptionThrown()
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试transient字段的初始化状态
     */
    def "transientFieldsTest"() {
        given:
        def testVariables = []
        
        def parseVarRender = ParseVarRender.builder()
            .globalVarService(globalVarService)
            .fieldDataConverterManager(fieldDataConverterManager)
            .orgService(orgService)
            .variables(testVariables)
            .user(user)
            .build()

        expect:
        parseVarRender.globalVariables == null
        parseVarRender.globalVarParseValueMap == null
    }

    // 辅助方法
    private Variable createGlobalVariable(String name = "testVar__g") {
        def variable = new Variable(name, Mock(IObjectDescribe), new ObjectData(), Mock(IUdefButton), [:])
        variable.setType(Variable.Type.GLOBAL_CONSTANT)
        variable.setVariableName(name)
        return variable
    }

    private Variable createObjectFieldVariable() {
        def variable = new Variable("testField", Mock(IObjectDescribe), new ObjectData(), Mock(IUdefButton), [:])
        variable.setType(Variable.Type.OBJECT_FIELD)
        variable.setVariableName("testField")
        return variable
    }

    private Variable createButtonVariable() {
        def variable = new Variable("var_test", Mock(IObjectDescribe), new ObjectData(), Mock(IUdefButton), [:])
        variable.setType(Variable.Type.BUTTON_VARIABLES)
        variable.setVariableName("var_test")
        return variable
    }

    private Variable createCurrentObjectVariable() {
        def variable = new Variable("__current_object__", Mock(IObjectDescribe), new ObjectData(), Mock(IUdefButton), [:])
        variable.setType(Variable.Type.CURRENT_OBJECT)
        variable.setVariableName("__current_object__")
        return variable
    }

    private List<Variable> createTestVariables() {
        return [
            createGlobalVariable(),
            createObjectFieldVariable()
        ]
    }
} 