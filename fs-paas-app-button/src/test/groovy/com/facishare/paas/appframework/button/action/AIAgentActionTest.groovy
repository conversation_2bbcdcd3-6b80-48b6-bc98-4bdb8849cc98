package com.facishare.paas.appframework.button.action

import com.facishare.paas.appframework.button.dto.ButtonExecutor
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IUdefAction
import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.impl.ObjectData
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll

/**
 * AIAgentAction单元测试
 * 用于测试AIAgentAction类的所有功能
 */
@Unroll
class AIAgentActionTest extends Specification {

    AIAgentAction aiAgentAction

    def setup() {
        aiAgentAction = new AIAgentAction()
    }

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getType方法返回正确的ActionExecutorType
     */
    def "getTypeTest"() {
        when:
        def result = aiAgentAction.getType()

        then:
        result == ActionExecutorType.AI_AGENT
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法的正常执行流程
     */
    def "invokeTestNormalFlow"() {
        given:
        def user = User.systemUser('74255')
        def describe = Mock(IObjectDescribe)
        def button = Mock(IUdefButton)
        def action = Mock(IUdefAction)

        def agentApiName = "test_agent_api"
        def actionParameter = """{"agent_api_name": "${agentApiName}"}"""
        action.getActionParamter() >> actionParameter

        def context = ActionExecutorContext.builder()
                .user(user)
                .describe(describe)
                .button(button)
                .action(action)
                .build()

        def objectData = new ObjectData(['_id': '123', 'name': 'test'])
        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
                .describeApiName("test_api")
                .objectDataId("123")
                .objectData(objectData)
                .buttonApiName("test_button")
                .build()

        when:
        def result = aiAgentAction.invoke(arg, context)

        then:
        result != null
        result.hasReturnValue == true
        result.returnType == "AIAgent"
        result.returnValue instanceof AIAgentAction.AIAgentActionResult
        ((AIAgentAction.AIAgentActionResult) result.returnValue).agentApiName == agentApiName
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理不同agentApiName的场景
     */
    def "invokeTestDifferentAgentApiNames"() {
        given:
        def user = User.systemUser('74255')
        def describe = Mock(IObjectDescribe)
        def button = Mock(IUdefButton)
        def action = Mock(IUdefAction)

        def actionParameter = """{"agent_api_name": "${agentApiName}"}"""
        action.getActionParamter() >> actionParameter

        def context = ActionExecutorContext.builder()
                .user(user)
                .describe(describe)
                .button(button)
                .action(action)
                .build()

        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
                .describeApiName("test_api")
                .objectData(new ObjectData())
                .build()

        when:
        def result = aiAgentAction.invoke(arg, context)

        then:
        result.hasReturnValue == true
        result.returnType == "AIAgent"
        ((AIAgentAction.AIAgentActionResult) result.returnValue).agentApiName == agentApiName

        where:
        agentApiName << ["simple_agent", "complex_agent_name", "agent_with_123", "测试Agent"]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试AIAgentActionParameter.fromJsonString方法的正常解析
     */
    def "aiAgentActionParameterFromJsonStringTest"() {
        given:
        def agentApiName = "test_agent_api"
        def jsonString = """{"agent_api_name": "${agentApiName}"}"""

        when:
        def parameter = AIAgentAction.AIAgentActionParameter.fromJsonString(jsonString)

        then:
        parameter != null
        parameter.agentApiName == agentApiName
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试AIAgentActionParameter.fromJsonString方法处理复杂JSON的场景
     */
    def "aiAgentActionParameterFromJsonStringTestComplex"() {
        given:
        def jsonString = '''
        {
            "agent_api_name": "complex_agent",
            "extra_field": "should_be_ignored",
            "nested_object": {
                "key": "value"
            }
        }
        '''

        when:
        def parameter = AIAgentAction.AIAgentActionParameter.fromJsonString(jsonString)

        then:
        parameter != null
        parameter.agentApiName == "complex_agent"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试AIAgentActionParameter.fromJsonString方法处理null的场景
     */
    def "aiAgentActionParameterFromJsonStringErrorNull"() {
        when:
        def result = AIAgentAction.AIAgentActionParameter.fromJsonString(null)

        then:
        result == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试AIAgentActionParameter.fromJsonString方法处理空字符串的场景
     */
    def "aiAgentActionParameterFromJsonStringErrorEmpty"() {
        when:
        def result = AIAgentAction.AIAgentActionParameter.fromJsonString("")

        then:
        result == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试AIAgentActionParameter.fromJsonString方法处理非法JSON的场景
     */
    def "aiAgentActionParameterFromJsonStringErrorInvalidJson"() {
        given:
        def invalidJson = "invalid json string"

        when:
        AIAgentAction.AIAgentActionParameter.fromJsonString(invalidJson)

        then:
        thrown(Exception)
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试AIAgentActionResult.of方法创建结果对象
     */
    def "aiAgentActionResultOfTest"() {
        given:
        def agentApiName = "test_agent_result"

        when:
        def result = AIAgentAction.AIAgentActionResult.of(agentApiName)

        then:
        result != null
        result.agentApiName == agentApiName
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试AIAgentActionResult.of方法处理不同参数类型
     */
    def "aiAgentActionResultOfTestDifferentTypes"() {
        when:
        def result = AIAgentAction.AIAgentActionResult.of(agentApiName)

        then:
        result.agentApiName == agentApiName

        where:
        agentApiName << ["simple", "complex_name", null, ""]
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法构建完整的ButtonExecutor.Result
     */
    def "invokeTestResultConstruction"() {
        given:
        def user = User.systemUser('74255')
        def describe = Mock(IObjectDescribe)
        def button = Mock(IUdefButton)
        def action = Mock(IUdefAction)

        def agentApiName = "result_test_agent"
        def actionParameter = """{"agent_api_name": "${agentApiName}"}"""
        action.getActionParamter() >> actionParameter

        def context = ActionExecutorContext.builder()
                .user(user)
                .describe(describe)
                .button(button)
                .action(action)
                .build()

        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
                .describeApiName("test_api")
                .objectData(new ObjectData())
                .build()

        when:
        def result = aiAgentAction.invoke(arg, context)

        then:
        // 验证结果的所有关键属性
        result.hasReturnValue == true
        result.returnType == "AIAgent"
        result.returnValue != null
        result.returnValue instanceof AIAgentAction.AIAgentActionResult

        // 验证返回值的内容
        def aiAgentResult = (AIAgentAction.AIAgentActionResult) result.returnValue
        aiAgentResult.agentApiName == agentApiName
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理空的agentApiName
     */
    def "invokeTestEmptyAgentApiName"() {
        given:
        def user = User.systemUser('74255')
        def describe = Mock(IObjectDescribe)
        def button = Mock(IUdefButton)
        def action = Mock(IUdefAction)

        def actionParameter = '{"agent_api_name": ""}'
        action.getActionParamter() >> actionParameter

        def context = ActionExecutorContext.builder()
                .user(user)
                .describe(describe)
                .button(button)
                .action(action)
                .build()

        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
                .describeApiName("test_api")
                .objectData(new ObjectData())
                .build()

        when:
        def result = aiAgentAction.invoke(arg, context)

        then:
        result.hasReturnValue == true
        result.returnType == "AIAgent"
        ((AIAgentAction.AIAgentActionResult) result.returnValue).agentApiName == ""
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试invoke方法处理缺少agent_api_name字段的JSON
     */
    def "invokeTestMissingAgentApiNameField"() {
        given:
        def user = User.systemUser('74255')
        def describe = Mock(IObjectDescribe)
        def button = Mock(IUdefButton)
        def action = Mock(IUdefAction)

        def actionParameter = '{"other_field": "value"}'
        action.getActionParamter() >> actionParameter

        def context = ActionExecutorContext.builder()
                .user(user)
                .describe(describe)
                .button(button)
                .action(action)
                .build()

        ButtonExecutor.Arg arg = ButtonExecutor.Arg.builder()
                .describeApiName("test_api")
                .objectData(new ObjectData())
                .build()

        when:
        def result = aiAgentAction.invoke(arg, context)

        then:
        result.hasReturnValue == true
        result.returnType == "AIAgent"
        ((AIAgentAction.AIAgentActionResult) result.returnValue).agentApiName == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试AIAgentActionParameter数据类的getter和setter方法
     */
    def "aiAgentActionParameterDataClassTest"() {
        given:
        def parameter = new AIAgentAction.AIAgentActionParameter()
        def testApiName = "test_data_class_agent"

        when:
        parameter.setAgentApiName(testApiName)

        then:
        parameter.getAgentApiName() == testApiName
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试AIAgentActionResult数据类的getter方法
     */
    def "aiAgentActionResultDataClassTest"() {
        given:
        def testApiName = "test_result_agent"

        when:
        def result = AIAgentAction.AIAgentActionResult.of(testApiName)

        then:
        result.getAgentApiName() == testApiName
    }
} 