package com.facishare.paas.appframework.button.action

import com.facishare.paas.appframework.button.dto.UpdatesPojo
import com.facishare.paas.appframework.common.service.OrgService
import com.facishare.paas.appframework.common.service.dto.QueryDeptInfoByDeptIds
import com.facishare.paas.appframework.common.service.dto.UserInfo
import com.facishare.paas.appframework.common.util.DateTimeUtils
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.GlobalVarService
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.appframework.metadata.dataconvert.DataConvertContext
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverter
import com.facishare.paas.appframework.metadata.dataconvert.FieldDataConverterManager
import com.facishare.paas.appframework.metadata.expression.Expression
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.IUdefButton
import com.facishare.paas.metadata.api.describe.IFieldType
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import com.facishare.paas.metadata.api.describe.SelectOne
import com.facishare.paas.metadata.impl.ObjectData
import com.facishare.paas.metadata.impl.describe.*
import com.facishare.paas.timezone.DateTimeFormatUtils
import com.facishare.paas.timezone.TimeZoneContextHolder
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

/**
 * GenerateByAI
 * ParseVarService单元测试类
 *
 * 测试覆盖范围：
 * 1. parseVar方法 - 解析各种类型的变量（全局常量、对象字段、表单字段、按钮变量、当前对象）
 * 2. parseVarForDisplay方法 - 为显示目的解析变量，包括特殊格式化处理
 * 3. 各种辅助方法 - getData、getDisplayData、getVar、getDataList、getVarList等
 *
 * 修复的问题：
 * 1. 依赖注入：使用Whitebox正确设置@Autowired字段
 * 2. 常量使用：使用字符串而不是接口常量来避免编译问题
 * 3. 字段描述创建：使用FieldDescribeFactory.newInstance()正确创建字段描述对象
 * 4. 静态方法模拟：使用Groovy元编程模拟静态方法调用
 * 5. 业务逻辑适配：根据实际代码逻辑调整测试期望
 *
 * 支持的变量类型：
 * - GLOBAL_CONSTANT: 全局常量变量
 * - OBJECT_FIELD: 对象字段变量  
 * - VAR_FIELD: 表单字段变量（支持部门、员工、单选、多选等类型）
 * - BUTTON_VARIABLES: 按钮变量（var_executor、var_execution_time）
 * - CURRENT_OBJECT: 当前对象变量
 */
@Unroll
class ParseVarServiceTest extends Specification {

    ParseVarService parseVarService
    GlobalVarService globalVarService = Mock(GlobalVarService)
    FieldDataConverterManager fieldDataConverterManager = Mock(FieldDataConverterManager)
    OrgService orgService = Mock(OrgService)

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        parseVarService = new ParseVarService()
        // 使用Whitebox正确设置@Autowired字段
        Whitebox.setInternalState(parseVarService, "globalVarService", globalVarService)
        Whitebox.setInternalState(parseVarService, "fieldDataConverterManager", fieldDataConverterManager)
        Whitebox.setInternalState(parseVarService, "orgService", orgService)

        // 验证注入是否成功
        assert Whitebox.getInternalState(parseVarService, "globalVarService") != null
        assert Whitebox.getInternalState(parseVarService, "fieldDataConverterManager") != null
        assert Whitebox.getInternalState(parseVarService, "orgService") != null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析全局常量变量的正常场景
     */
    def "parseVarTestGlobalConstant"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def globalVar = new GlobalVariableDescribe()
        globalVar.setApiName("testVar__g")
        globalVar.setType(IFieldType.TEXT)
        globalVar.setValue("test value")

        def variable = new Variable(getVariableName("\$testVar__g\$"), createMockObjectDescribe(), createMockObjectData(), createMockButton(), [:])
        variable.setType(Variable.Type.GLOBAL_CONSTANT)

        def variables = [variable]

        when:
        globalVarService.findGlobalVariables(tenantId, ["testVar__g"]) >> ["testVar__g": globalVar]
        globalVarService.parseValue(globalVar, false) >> "parsed value"
        def result = parseVarService.parseVar(variables, user)

        then:
        result.size() == 1
        result[0].getValue() == "parsed value"
        result[0].getFieldType() == IFieldType.TEXT
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析对象字段变量的正常场景
     */
    def "parseVarTestObjectField"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def objectData = createMockObjectData()
        objectData.set("name", "test name")

        def describe = createMockObjectDescribe()
        def fieldDescribe = FieldDescribeFactory.newInstance([
                "api_name": "name",
                "type"    : IFieldType.TEXT
        ])
        describe.setFieldDescribes([fieldDescribe])

        def variable = new Variable(getVariableName("\$name\$"), describe, objectData, createMockButton(), [:])
        variable.setType(Variable.Type.OBJECT_FIELD)

        def variables = [variable]

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.parseVar(variables, user)

        then:
        result.size() == 1
        result[0].getValue() == "test name"
        result[0].getFieldType() == IFieldType.TEXT
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析表单字段变量的正常场景
     */
    def "parseVarTestVarField"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def args = ["form_test": "form value"]
        def button = Mock(IUdefButton)
        def paramForm = [["api_name": "form_test", "type": "text"]]
        button.getParamForm() >> paramForm

        def variable = new Variable(getVariableName("\$form_test\$"), createMockObjectDescribe(), createMockObjectData(), button, args)
        variable.setType(Variable.Type.VAR_FIELD)

        def variables = [variable]

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.parseVar(variables, user)

        then:
        result.size() == 1
        result[0].getValue() == "form value"
        result[0].getFieldType() == "text"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析按钮变量var_executor的正常场景
     */
    def "parseVarTestButtonVariablesExecutor"() {
        given:
        def tenantId = '74255'
        def userId = '1000'
        def user = new User(tenantId, userId)

        def variable = new Variable(getVariableName("\$var_executor\$"), createMockObjectDescribe(), createMockObjectData(), createMockButton(), [:])
        variable.setType(Variable.Type.BUTTON_VARIABLES)

        def variables = [variable]

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.parseVar(variables, user)

        then:
        result.size() == 1
        result[0].getValue() == [userId]
        result[0].getFieldType() == IFieldType.EMPLOYEE
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析按钮变量var_execution_time在parseVar方法中不被处理（返回null值）
     */
    def "parseVarTestButtonVariablesExecutionTimeNotSupported"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)

        def variable = new Variable(getVariableName("\$var_execution_time\$"), createMockObjectDescribe(), createMockObjectData(), createMockButton(), [:])
        variable.setType(Variable.Type.BUTTON_VARIABLES)

        def variables = [variable]

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.parseVar(variables, user)

        then:
        result.size() == 1
        // parseVar方法不处理var_execution_time，所以值应该为null
        result[0].getValue() == null
        result[0].getFieldType() == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析当前对象变量的正常场景
     */
    def "parseVarTestCurrentObject"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def objectData = createMockObjectData()
        objectData.set("id", "123")
        objectData.set("name", "test")

        def variable = new Variable(getVariableName("\$__current_object__\$"), createMockObjectDescribe(), objectData, createMockButton(), [:])
        variable.setType(Variable.Type.CURRENT_OBJECT)

        def variables = [variable]

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.parseVar(variables, user)

        then:
        result.size() == 1
        result[0].getValue() instanceof Map
        ((Map) result[0].getValue()).containsKey("id")
        ((Map) result[0].getValue()).containsKey("name")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理全局常量的正常场景
     */
    def "parseVarForDisplayTestGlobalConstant"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def globalVar = new GlobalVariableDescribe()
        globalVar.setApiName("testVar__g")
        globalVar.setType(IFieldType.TEXT)
        globalVar.setValue("test value")

        def variable = new Variable(getVariableName("\$testVar__g\$"), createMockObjectDescribe(), createMockObjectData(), createMockButton(), [:])
        variable.setType(Variable.Type.GLOBAL_CONSTANT)

        def variables = [variable]

        when:
        globalVarService.findGlobalVariables(tenantId, ["testVar__g"]) >> ["testVar__g": globalVar]
        globalVarService.parseValue(globalVar, false) >> "parsed value"
        def result = parseVarService.parseVarForDisplay(variables, user)

        then:
        result.size() == 1
        result[0].getValue() != null
        result[0].getFieldType() == IFieldType.TEXT
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理对象字段的正常场景
     */
    def "parseVarForDisplayTestObjectField"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def objectData = createMockObjectData()
        objectData.set("name", "test name")

        def describe = createMockObjectDescribe()
        def fieldDescribe = FieldDescribeFactory.newInstance([
                "api_name": "name",
                "type"    : IFieldType.TEXT
        ])
        describe.setFieldDescribes([fieldDescribe])

        def variable = new Variable(getVariableName("\$name\$"), describe, objectData, createMockButton(), [:])
        variable.setType(Variable.Type.OBJECT_FIELD)

        def variables = [variable]
        def fieldDataConverter = Mock(FieldDataConverter)

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        fieldDataConverterManager.getFieldDataConverter(IFieldType.TEXT) >> fieldDataConverter
        fieldDataConverter.convertFieldData(objectData, fieldDescribe, _ as DataConvertContext) >> "converted value"
        def result = parseVarService.parseVarForDisplay(variables, user)

        then:
        result.size() == 1
        result[0].getValue() == "converted value"
        result[0].getFieldType() == IFieldType.TEXT
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理表单字段-部门类型的正常场景
     */
    def "parseVarForDisplayTestVarFieldDepartment"() {
        given:
        def tenantId = '74255'
        def userId = '1000'
        def user = new User(tenantId, userId)
        def args = ["form_dept": ["dept1", "dept2"]]
        def button = Mock(IUdefButton)
        def paramForm = [["api_name": "form_dept", "type": "department"]]
        button.getParamForm() >> paramForm

        def variable = new Variable(getVariableName("\$form_dept\$"), createMockObjectDescribe(), createMockObjectData(), button, args)
        variable.setType(Variable.Type.VAR_FIELD)

        def variables = [variable]
        def deptInfo = new QueryDeptInfoByDeptIds.DeptInfo()
        deptInfo.setDeptName("部门名称")

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        orgService.getDeptInfoNameByIds(tenantId, userId, ["dept1", "dept2"]) >> [deptInfo]
        def result = parseVarService.parseVarForDisplay(variables, user)

        then:
        result.size() == 1
        result[0].getValue() == "部门名称"
        result[0].getFieldType() == "department"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理表单字段-员工类型的正常场景
     */
    def "parseVarForDisplayTestVarFieldEmployee"() {
        given:
        def tenantId = '74255'
        def userId = '1000'
        def user = new User(tenantId, userId)
        def args = ["form_emp": ["emp1", "emp2"]]
        def button = Mock(IUdefButton)
        def paramForm = [["api_name": "form_emp", "type": "employee"]]
        button.getParamForm() >> paramForm

        def variable = new Variable(getVariableName("\$form_emp\$"), createMockObjectDescribe(), createMockObjectData(), button, args)
        variable.setType(Variable.Type.VAR_FIELD)

        def variables = [variable]
        def userInfo = new UserInfo()
        userInfo.setName("用户名称")

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        orgService.getUserNameByIds(tenantId, userId, ["emp1", "emp2"]) >> [userInfo]
        def result = parseVarService.parseVarForDisplay(variables, user)

        then:
        result.size() == 1
        result[0].getValue() == "用户名称"
        result[0].getFieldType() == "employee"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理表单字段-单选类型的正常场景
     */
    def "parseVarForDisplayTestVarFieldSelectOne"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def args = ["form_select": "option1"]
        def button = Mock(IUdefButton)
        def paramForm = [["api_name": "form_select", "type": "select_one"]]
        button.getParamForm() >> paramForm

        def describe = createMockObjectDescribe()

        def selectOneField = new SelectOneFieldDescribe([
                "api_name": "select"
        ])
        def selectOption = new SelectOption(["value": "option1", "label": "选项1"])
        selectOneField.setSelectOptions([selectOption])
        describe.setFieldDescribes([selectOneField])

        def variable = new Variable(getVariableName("\$form_select\$"), describe, createMockObjectData(), button, args)
        variable.setType(Variable.Type.VAR_FIELD)

        def variables = [variable]

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.parseVarForDisplay(variables, user)

        then:
        result.size() == 1
        result[0].getValue() == "选项1"
        result[0].getFieldType() == "select_one"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理表单字段-单选其他选项的正常场景
     */
    def "parseVarForDisplayTestVarFieldSelectOneOther"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def args = [
                "form_select"   : SelectOne.OPTION_OTHER_VALUE,
                "form_select__o": "其他值"
        ]
        def button = Mock(IUdefButton)
        def paramForm = [["api_name": "form_select", "type": "select_one"]]
        button.getParamForm() >> paramForm

        def describe = createMockObjectDescribe()
        def selectOneField = new SelectOneFieldDescribe([
                "api_name": "select"
        ])
        def selectOption = new SelectOption(["value": "other", "label": "其他"])
        selectOneField.setSelectOptions([selectOption])
        describe.setFieldDescribes([selectOneField])

        def variable = new Variable(getVariableName("\$form_select\$"), describe, createMockObjectData(), button, args)
        variable.setType(Variable.Type.VAR_FIELD)

        def variables = [variable]

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.parseVarForDisplay(variables, user)

        then:
        result.size() == 1
        result[0].getValue() == "其他：其他值"
        result[0].getFieldType() == "select_one"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理表单字段-多选类型的正常场景
     */
    def "parseVarForDisplayTestVarFieldSelectMany"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def args = ["form_multi": ["option1", "option2"]]
        def button = Mock(IUdefButton)
        def paramForm = [["api_name": "form_multi", "type": "select_many"]]
        button.getParamForm() >> paramForm

        def describe = createMockObjectDescribe()
        def selectManyField = new SelectManyFieldDescribe([
                "api_name": "multi"
        ])
        def selectOption1 = new SelectOption(["value": "option1", "label": "选项1"])
        def selectOption2 = new SelectOption(["value": "option2", "label": "选项2"])
        selectManyField.setSelectOptions([selectOption1, selectOption2])
        describe.setFieldDescribes([selectManyField])

        def variable = new Variable(getVariableName("\$form_multi\$"), describe, createMockObjectData(), button, args)
        variable.setType(Variable.Type.VAR_FIELD)

        def variables = [variable]

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.parseVarForDisplay(variables, user)

        then:
        result.size() == 1
        result[0].getValue() == "选项1，选项2"
        result[0].getFieldType() == "select_many"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理按钮变量var_executor的正常场景
     */
    def "parseVarForDisplayTestButtonVariablesExecutor"() {
        given:
        def tenantId = '74255'
        def userId = '1000'
        def user = new User(tenantId, userId)

        def variable = new Variable("\$var_executor\$", createMockObjectDescribe(), createMockObjectData(), createMockButton(), [:])
        variable.setType(Variable.Type.BUTTON_VARIABLES)

        def variables = [variable]
        def userInfo = new UserInfo()
        userInfo.setName("执行者名称")

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        orgService.getUserNameByIds(tenantId, userId, [userId]) >> [userInfo]
        def result = parseVarService.parseVarForDisplay(variables, user)

        then:
        result.size() == 1
        result[0].getValue() == "执行者名称"
        result[0].getFieldType() == IFieldType.EMPLOYEE
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseVarForDisplay方法处理按钮变量var_execution_time的正常场景
     */
    def "parseVarForDisplayTestButtonVariablesExecutionTime"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)

        def variable = new Variable("\$var_execution_time\$", createMockObjectDescribe(), createMockObjectData(), createMockButton(), [:])
        variable.setType(Variable.Type.BUTTON_VARIABLES)

        def variables = [variable]

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.parseVarForDisplay(variables, user)

        then:
        result.size() == 1
        result[0].getValue() != null
        result[0].getFieldType() == IFieldType.DATE_TIME
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getData方法的正常场景
     */
    def "getDataTest"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def apiName = "\$name\$"
        def args = [:]
        def objectData = createMockObjectData()
        objectData.set("name", "test name")
        def describe = createMockObjectDescribe()
        def fieldDescribe = FieldDescribeFactory.newInstance([
                "api_name": "name",
                "type"    : IFieldType.TEXT
        ])
        describe.setFieldDescribes([fieldDescribe])
        def button = createMockButton()

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.getData(apiName, args, objectData, user, describe, button)

        then:
        result == "test name"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDisplayData方法的正常场景
     */
    def "getDisplayDataTest"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def apiName = "\$name\$"
        def args = [:]
        def objectData = createMockObjectData()
        objectData.set("name", "test name")
        def describe = createMockObjectDescribe()
        def fieldDescribe = FieldDescribeFactory.newInstance([
                "api_name": "name",
                "type"    : IFieldType.TEXT
        ])
        describe.setFieldDescribes([fieldDescribe])
        def button = createMockButton()
        def fieldDataConverter = Mock(FieldDataConverter)

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        fieldDataConverterManager.getFieldDataConverter(IFieldType.TEXT) >> fieldDataConverter
        fieldDataConverter.convertFieldData(objectData, fieldDescribe, _ as DataConvertContext) >> "display value"
        def result = parseVarService.getDisplayData(apiName, args, objectData, user, describe, button)

        then:
        result == "display value"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getVar方法的正常场景
     */
    def "getVarTest"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def apiName = "\$name\$"
        def args = [:]
        def objectData = createMockObjectData()
        objectData.set("name", "test name")
        def describe = createMockObjectDescribe()
        def fieldDescribe = FieldDescribeFactory.newInstance([
                "api_name": "name",
                "type"    : IFieldType.TEXT
        ])
        describe.setFieldDescribes([fieldDescribe])
        def button = createMockButton()

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.getVar(apiName, args, objectData, user, describe, button)

        then:
        result instanceof Variable
        result.getValue() == "test name"
        result.getFieldType() == IFieldType.TEXT
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getDataList方法的正常场景
     */
    def "getDataListTest"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def apiNames = ["\$name\$", "\$status\$"]
        def args = [:]
        def objectData = createMockObjectData()
        objectData.set("name", "test name")
        objectData.set("status", "active")
        def describe = createMockObjectDescribe()
        def nameField = FieldDescribeFactory.newInstance([
                "api_name": "name",
                "type"    : IFieldType.TEXT
        ])
        def statusField = FieldDescribeFactory.newInstance([
                "api_name": "status",
                "type"    : IFieldType.TEXT
        ])
        describe.setFieldDescribes([nameField, statusField])
        def button = createMockButton()

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.getDataList(apiNames, args, objectData, user, describe, button)

        then:
        result.size() == 2
        result[0] == "test name"
        result[1] == "active"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getVarList方法的正常场景
     */
    def "getVarListTest"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def apiNames = ["\$name\$", "\$status\$"]
        def args = [:]
        def objectData = createMockObjectData()
        objectData.set("name", "test name")
        objectData.set("status", "active")
        def describe = createMockObjectDescribe()
        def nameField = FieldDescribeFactory.newInstance([
                "api_name": "name",
                "type"    : IFieldType.TEXT
        ])
        def statusField = FieldDescribeFactory.newInstance([
                "api_name": "status",
                "type"    : IFieldType.TEXT
        ])
        describe.setFieldDescribes([nameField, statusField])
        def button = createMockButton()

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.getVarList(apiNames, args, objectData, user, describe, button)

        then:
        result.size() == 2
        result[0] instanceof Variable
        result[1] instanceof Variable
        result[0].getValue() == "test name"
        result[1].getValue() == "active"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getVarListByField方法的正常场景
     */
    def "getVarListByFieldTest"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def fieldList = [
                UpdatesPojo.Field.builder()
                        .field("name")
                        .value("\$name\$")
                        .build()
        ]
        def args = [:]
        def objectData = createMockObjectData()
        objectData.set("name", "test name")
        def describe = createMockObjectDescribe()
        def nameField = FieldDescribeFactory.newInstance([
                "api_name": "name",
                "type"    : IFieldType.TEXT
        ])
        describe.setFieldDescribes([nameField])
        def button = createMockButton()

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.getVarListByField(fieldList, args, objectData, user, describe, button)

        then:
        result.size() == 1
        result[0] instanceof Variable
        result[0].getValue() == "test name"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析变量时全局变量服务返回空的异常场景
     */
    def "parseVarErrorEmptyGlobalVars"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def variable = new Variable(getVariableName("\$testVar__g\$"), createMockObjectDescribe(), createMockObjectData(), createMockButton(), [:])
        variable.setType(Variable.Type.GLOBAL_CONSTANT)
        def variables = [variable]

        when:
        globalVarService.findGlobalVariables(tenantId, ["testVar__g"]) >> [:]
        def result = parseVarService.parseVar(variables, user)

        then:
        result.size() == 1
        result[0].getValue() == null
        result[0].getFieldType() == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试解析变量时对象字段不存在的异常场景
     */
    def "parseVarErrorFieldNotFound"() {
        given:
        def tenantId = '74255'
        def user = User.systemUser(tenantId)
        def objectData = createMockObjectData()
        def describe = createMockObjectDescribe()
        describe.setFieldDescribes([])

        def variable = new Variable(getVariableName("\$nonexistent\$"), describe, objectData, createMockButton(), [:])
        variable.setType(Variable.Type.OBJECT_FIELD)
        def variables = [variable]

        when:
        globalVarService.findGlobalVariables(tenantId, []) >> [:]
        def result = parseVarService.parseVar(variables, user)

        then:
        result.size() == 1
        result[0].getValue() == null
        result[0].getFieldType() == null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseValue方法在非时区灰度场景下的正常处理
     */
    def "parseValueTestTimeZoneGray"() {
        given:
        def value = 1672531200000L // 2023-01-01 00:00:00 的时间戳
        def type = IFieldType.DATE

        when:
        // 使用元编程模拟静态方法，确保走正常分支
        DateTimeUtils.metaClass.static.isGrayTimeZone = { -> false }
        ObjectDataExt.metaClass.static.parseVale = { val, tp -> "2023-01-01" }

        // 使用Whitebox调用私有方法
        def result = Whitebox.invokeMethod(parseVarService, "parseValue", value, type)

        then:
        result == "2023-01-01"

        cleanup:
        // 清理元编程修改
        DateTimeUtils.metaClass = null
        ObjectDataExt.metaClass = null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseValue方法处理时区灰度异常的场景
     */
    def "parseValueErrorTimeZoneGrayException"() {
        given:
        def value = "invalid date"
        def type = IFieldType.DATE

        when:
        // 使用元编程模拟静态方法
        DateTimeUtils.metaClass.static.isGrayTimeZone = { -> true }
        TimeZoneContextHolder.metaClass.static.getTenantTimeZone = { ->
            TimeZone.getTimeZone("Asia/Shanghai").toZoneId()
        }
        DateTimeFormatUtils.metaClass.static.formatWithTimezoneInfo = { val, zone, tp ->
            throw new RuntimeException("Invalid date")
        }

        // 使用Whitebox调用私有方法
        def result = Whitebox.invokeMethod(parseVarService, "parseValue", value, type)

        then:
        result == value

        cleanup:
        // 清理元编程修改
        DateTimeUtils.metaClass = null
        TimeZoneContextHolder.metaClass = null
        DateTimeFormatUtils.metaClass = null
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试parseValue方法在非时区灰度场景下的正常处理
     */
    def "parseValueTestNormalCase"() {
        given:
        def value = "test value"
        def type = IFieldType.TEXT

        when:
        // 使用元编程模拟静态方法
        DateTimeUtils.metaClass.static.isGrayTimeZone = { -> false }
        ObjectDataExt.metaClass.static.parseVale = { val, tp -> "parsed test value" }

        // 使用Whitebox调用私有方法
        def result = Whitebox.invokeMethod(parseVarService, "parseValue", value, type)

        then:
        result == "parsed test value"

        cleanup:
        // 清理元编程修改
        DateTimeUtils.metaClass = null
        ObjectDataExt.metaClass = null
    }

    private IObjectDescribe createMockObjectDescribe() {
        def describe = new ObjectDescribe()
        describe.setApiName("TestObj")
        describe.setFieldDescribes([])
        return describe
    }

    private IObjectData createMockObjectData() {
        return new ObjectData(['object_describe_api_name': 'TestObj'])
    }

    private IUdefButton createMockButton() {
        def button = Mock(IUdefButton)
        button.getApiName() >> "testButton"
        button.getParamForm() >> []
        return button
    }

    private String getVariableName(String expression) {
        Expression.of(expression).parseVariableNames().get(0)
    }
} 