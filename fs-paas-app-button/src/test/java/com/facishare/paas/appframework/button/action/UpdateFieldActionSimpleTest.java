package com.facishare.paas.appframework.button.action;

import org.junit.Assert;
import org.junit.Test;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * UpdateFieldAction简化测试 专门测试多语言数据处理变更的核心逻辑 GenerateByAI
 */
public class UpdateFieldActionSimpleTest {

    /**
     * GenerateByAI 测试内容描述：测试getFormFieldApiName方法 - 核心变更逻辑
     */
    @Test
    public void testGetFormFieldApiName_CoreLogic() {
        // Given
        UpdateFieldAction updateFieldAction = new UpdateFieldAction();

        // When & Then - 测试核心的form_前缀添加逻辑
        String result1 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", "name_ml");
        Assert.assertEquals("多语言字段应正确添加form_前缀", "form_name_ml", result1);

        String result2 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", "description_ml");
        Assert.assertEquals("描述多语言字段应正确添加form_前缀", "form_description_ml", result2);

        String result3 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", "title");
        Assert.assertEquals("普通字段应正确添加form_前缀", "form_title", result3);
    }

    /**
     * GenerateByAI 测试内容描述：测试getMultiLangDataMap方法的参数验证逻辑
     */
    @Test
    public void testGetMultiLangDataMap_ParameterValidation() {
        // Given
        UpdateFieldAction updateFieldAction = new UpdateFieldAction();

        // When & Then - 测试null参数处理
        @SuppressWarnings("unchecked")
        Map<String, Object> result1 = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                updateFieldAction, "getMultiLangDataMap", null, null, null);
        Assert.assertNotNull("null参数应返回非null结果", result1);
        Assert.assertTrue("null参数应返回空Map", result1.isEmpty());

        // 测试空Map参数
        Map<String, Object> emptyMap = new HashMap<>();
        @SuppressWarnings("unchecked")
        Map<String, Object> result2 = (Map<String, Object>) ReflectionTestUtils.invokeMethod(
                updateFieldAction, "getMultiLangDataMap", emptyMap, null, null);
        Assert.assertNotNull("空Map参数应返回非null结果", result2);
        Assert.assertTrue("空Map参数应返回空Map", result2.isEmpty());
    }

    /**
     * GenerateByAI 测试内容描述：验证变更的核心影响 - form_前缀在多语言数据获取中的作用
     */
    @Test
    public void testFormPrefixLogic_ChangeImpact() {
        // Given
        UpdateFieldAction updateFieldAction = new UpdateFieldAction();

        // 模拟变更前后的差异
        // 变更前：直接使用 multiLangApiName
        // 变更后：使用 getFormFieldApiName(multiLangApiName)
        String multiLangApiName = "field1_ml";

        // When - 应用变更后的逻辑
        String formFieldName = (String) ReflectionTestUtils.invokeMethod(
                updateFieldAction, "getFormFieldApiName", multiLangApiName);

        // Then - 验证变更效果
        Assert.assertEquals("变更后应添加form_前缀", "form_field1_ml", formFieldName);
        Assert.assertTrue("变更后的字段名应以form_开头", formFieldName.startsWith("form_"));
        Assert.assertTrue("变更后的字段名应包含原始字段名", formFieldName.contains(multiLangApiName));
    }

    /**
     * GenerateByAI 测试内容描述：测试字段名转换的边界情况和特殊字符处理
     */
    @Test
    public void testGetFormFieldApiName_EdgeCasesAndSpecialCharacters() {
        // Given
        UpdateFieldAction updateFieldAction = new UpdateFieldAction();

        // When & Then - 测试各种边界情况
        // 空字符串
        String result1 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", "");
        Assert.assertEquals("空字符串应正确处理", "form_", result1);

        // 包含下划线的字段名
        String result2 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", "user_name_ml");
        Assert.assertEquals("下划线字段名应正确处理", "form_user_name_ml", result2);

        // 包含数字的字段名
        String result3 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", "field123_ml");
        Assert.assertEquals("数字字段名应正确处理", "form_field123_ml", result3);

        // 长字段名
        String longFieldName = "very_long_field_name_with_multiple_underscores_ml";
        String result4 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", longFieldName);
        Assert.assertEquals("长字段名应正确处理", "form_" + longFieldName, result4);
    }

    /**
     * GenerateByAI 测试内容描述：验证方法的幂等性和性能特征
     */
    @Test
    public void testGetFormFieldApiName_IdempotencyAndPerformance() {
        // Given
        UpdateFieldAction updateFieldAction = new UpdateFieldAction();
        String testField = "performance_test_field_ml";

        // When - 多次调用测试幂等性
        String result1 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", testField);
        String result2 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", testField);
        String result3 = (String) ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", testField);

        // Then - 验证幂等性
        Assert.assertEquals("多次调用应返回相同结果", result1, result2);
        Assert.assertEquals("多次调用应返回相同结果", result2, result3);

        // 验证性能 - 简单的性能测试
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 1000; i++) {
            ReflectionTestUtils.invokeMethod(updateFieldAction, "getFormFieldApiName", testField);
        }
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 1000次调用应该在合理时间内完成（比如100ms）
        Assert.assertTrue("方法执行应该足够快", duration < 100);
    }
}
