package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataServiceImpl;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.TeamMember;
import com.facishare.paas.appframework.privilege.UserRoleInfoServiceImpl;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.*;

/**
 * Created by linqy on 18/01/26.
 * 员工转换管理器
 */
@Slf4j
@Service
public class GetEmployeeManager {
    @Autowired
    private OrgService orgService;
    @Autowired
    private UserRoleInfoServiceImpl userRoleInfoService;
    @Autowired
    private MetaDataServiceImpl metadataService;

    @PostConstruct
    public void init() {
        ExtProcessType.metadataService = metadataService;
        ExtProcessType.orgService = orgService;
        AssigneeHandlerType.orgService = orgService;
        AssigneeHandlerType.userRoleInfoService = userRoleInfoService;
    }

    public Set<String> getReceives(User user, String describeApiName, String dataId, Map<String, List<String>> recieves) {
        Set<String> userIds = Sets.newHashSet();

        recieves.keySet().forEach((key) -> {
            try {
                if (Objects.equals(key, ExtProcessType.KEY)) {
                    List<String> values = recieves.get(key);
                    values.forEach(part -> userIds.addAll(ExtProcessType.valueOf(part).execute(user, describeApiName, dataId)));
                } else {
                    userIds.addAll(AssigneeHandlerType.valueOf(key).execute(user, describeApiName, dataId, recieves));
                }
            } catch (IllegalArgumentException e) {
                log.info("GetEmployeeManager.getReceives not support :{},{},{}", key, recieves, e);
            }
        });

        return userIds;
    }

    private enum ExtProcessType implements ExtProcessTypeHandler {
        data_group("团队成员") {// ignoreI18n
            @Override
            public List<String> execute(User user, String describeApiName, String dataId) {
                return getDataGroup(user, describeApiName, dataId);
            }
        },
        data_owner_leader("数据负责人上级") {// ignoreI18n
            @Override
            public List<String> execute(User user, String describeApiName, String dataId) {
                return getDataOwnerLeader(user, getOwner(getData(user, describeApiName, dataId).getOwnerId().orElse(null)));
            }
        },
        data_group_leader("数据团队成员上级") {// ignoreI18n
            @Override
            public List<String> execute(User user, String describeApiName, String dataId) {
                return getDataGroupLeader(user, describeApiName, dataId);
            }
        },
        data_owner_main_dept_leader("数据负责人所在主部门负责人") {// ignoreI18n
            @Override
            public List<String> execute(User user, String describeApiName, String dataId) {
                return getDataOwnerMainDeptLeader(user, getOwner(getData(user, describeApiName, dataId).getOwnerId().orElse(null)));
            }
        },
        data_group_main_dept_leader("数据相关团队成员所在主部门负责人") {// ignoreI18n
            @Override
            public List<String> execute(User user, String describeApiName, String dataId) {
                return getDataGroupMainDeptLeader(user, describeApiName, dataId);
            }
        },
        owner("数据负责人") {// ignoreI18n
            @Override
            public List<String> execute(User user, String describeApiName, String dataId) {
                return getOwner(getData(user, describeApiName, dataId).getOwnerId().orElse(null));
            }
        };


        private static Map<String, ObjectDataExt> dataCache = Maps.newHashMap();

        /**
         * @param user
         * @param owner
         * @return 获取数据负责人上级
         */
        private static List<String> getDataOwnerLeader(User user, List<String> owner) {
            return getLeaders(user, owner);
        }


        /**
         * @param user
         * @param owner
         * @return 数据负责人所在主部门负责人
         */
        private static List<String> getDataOwnerMainDeptLeader(User user, List<String> owner) {
            return getDeptLeader(user, owner);
        }


        /**
         * @param user
         * @return 数据详情
         */
        private static ObjectDataExt getData(User user, String describeApiName, String dataId) {
            return dataCache.computeIfAbsent(dataId, (key) -> ObjectDataExt.of(metadataService.findObjectData(user, key, describeApiName)));
        }

        /**
         * @param user
         * @param userIds
         * @return 获取直属上级
         */
        private static List<String> getLeaders(User user, List<String> userIds) {
            return orgService.getReportingObjectsByUserIds(user, userIds);
        }

        /**
         * @param user
         * @param userIds
         * @return 获取部门leader
         */
        private static List<String> getDeptLeader(User user, List<String> userIds) {
            return orgService.getDeptLeadersByUserIds(user, userIds);
        }

        /**
         * @param user
         * @param entityId
         * @param objectId
         * @return 获取团队成员
         */
        private static List<String> getDataGroup(User user, String entityId, String objectId) {
            List<String> ret = Lists.newArrayList();
            ObjectDataExt data = getData(user, entityId, objectId);
            if (CollectionUtils.empty(data.getRelevantTeamFromObjectData())) {
                return ret;
            }
            data.getRelevantTeamFromObjectData()
                    .stream().filter(x -> TeamMember.MemberType.EMPLOYEE.getValue().equals(TeamMember.MemberType.of(x.getTeamMemberType()).getValue()))
                    .forEach(x -> ret.addAll(x.getTeamMemberEmployee()));
            return ret;
        }


        /**
         * @param user
         * @param entityId
         * @param objectId
         * @return 数据相关团队成员所在主部门负责人
         */
        private static List<String> getDataGroupMainDeptLeader(User user, String entityId, String objectId) {
            return getDeptMainLeader(user, getDataGroup(user, entityId, objectId));
        }

        /**
         * @param user
         * @param users
         * @return 获取祝部门负责人
         */
        private static List<String> getDeptMainLeader(User user, List<String> users) {
            return orgService.getDeptLeadersByUserIds(user, users);
        }

        /**
         * @param user
         * @param entityId
         * @param objectId
         * @return 数据团队成员上级
         */
        private static List<String> getDataGroupLeader(User user, String entityId, String objectId) {
            return getLeaders(user, getDataGroup(user, entityId, objectId));
        }

        /**
         * 获取数据负责人
         *
         * @param ownerId
         * @return
         */
        private static List<String> getOwner(Object ownerId) {
            List<String> result = Lists.newArrayList();
            if (ownerId instanceof List) {
                result.addAll((Collection<? extends String>) ownerId);
            } else if (!Strings.isNullOrEmpty(ownerId + "")) {
                result.add(Double.valueOf(ownerId + "").intValue() + "");
            }
            return result;
        }

        ExtProcessType(String desc) {
        }

        public static void destroy() {
            dataCache.clear();
        }

        public static String KEY = "ext_process";
        public static MetaDataServiceImpl metadataService;
        private static OrgService orgService;

    }

    private enum AssigneeHandlerType implements AssigneesHandler {
        dataVars("扩展字段") {// ignoreI18n
            @Override
            public List<String> execute(User user, String describeApiName, String dataId, Map<String, List<String>> recieves) {
                List<String> keys = recieves.get(this.name());
                List<String> rst = Lists.newArrayList();
                for (Object key : keys) {
                    rst.addAll(ExtProcessType.valueOf(key + "").execute(user, describeApiName, dataId));
                }
                ExtProcessType.destroy();
                return rst;
            }
        },
        dept("部门") {// ignoreI18n
            @Override
            public List<String> execute(User user, String describeApiName, String dataId, Map<String, List<String>> recieves) {
                List<String> deptIds = recieves.get(this.name());
                return orgService.getMembersByDeptIds(user, deptIds);
            }
        },
        group("用户组") {// ignoreI18n
            @Override
            public List<String> execute(User user, String describeApiName, String dataId, Map<String, List<String>> recieves) {
                List<String> group = recieves.get(this.name());
                return orgService.getMembersByGroupIds(user, group);
            }
        },
        person("用户") {// ignoreI18n
            @Override
            public List<String> execute(User user, String describeApiName, String dataId, Map<String, List<String>> recieves) {
                return recieves.get(this.name());
            }
        },
        dept_leader("部门负责人") {// ignoreI18n
            @Override
            public List<String> execute(User user, String describeApiName, String dataId, Map<String, List<String>> recieves) {
                List<String> deptLeader = recieves.get(this.name());
                return orgService.getDeptLeaders(user, deptLeader);
            }
        },
        role("角色") {// ignoreI18n
            @Override
            public List<String> execute(User user, String describeApiName, String dataId, Map<String, List<String>> recieves) {
                List<String> roles = recieves.get(this.name());
                return userRoleInfoService.queryRoleUsersByRoles(user, roles);
            }
        };

        AssigneeHandlerType(String desc) {
        }

        private static OrgService orgService;
        private static UserRoleInfoServiceImpl userRoleInfoService;

    }


    public interface AssigneesHandler {
        List<String> execute(User user, String describeApiName, String dataId, Map<String, List<String>> recieves);
    }

    public interface ExtProcessTypeHandler {
        List<String> execute(User user, String describeApiName, String dataId);
    }
}
