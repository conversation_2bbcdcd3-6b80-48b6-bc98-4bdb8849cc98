package com.facishare.paas.appframework.button.action;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

import java.util.Collection;
import java.util.Map;

/**
 * create by z<PERSON><PERSON> on 2020/06/20
 */
@Data
@Builder
public class ButtonExecutorContext {
    @NonNull
    private User user;
    private String stage;
    private String actionSource;
    @NonNull
    private IUdefButton button;
    @NonNull
    private IObjectDescribe describe;

    private Collection<String> ignoreFields;

    private Map<String, Object> searchQuery;
}
