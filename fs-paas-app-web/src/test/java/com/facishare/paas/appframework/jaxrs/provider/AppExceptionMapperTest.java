package com.facishare.paas.appframework.jaxrs.provider;

import com.facishare.paas.appframework.core.exception.*;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.jaxrs.model.InnerAPIResult;
import com.facishare.paas.appframework.jaxrs.model.RestAPIResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.lenient;

/**
 * AppExceptionMapper单元测试
 * GenerateByAI
 */
@ExtendWith(MockitoExtension.class)
class AppExceptionMapperTest {

    @InjectMocks
    private AppExceptionMapper appExceptionMapper;

    @Mock
    private RequestContext mockRequestContext;

    @Mock
    private APPException mockAppException;

    @Mock
    private SystemErrorCode mockErrorCode;

    private static final String TEST_ERROR_MESSAGE = "测试错误信息";
    private static final String TEST_ERROR_CODE = "TEST_ERROR_001";
    private static final String TEST_ERROR_DESCRIPTION = "测试错误描述";

    @BeforeEach
    void setUp() {
        // 配置Mock行为 - 使用lenient以避免UnnecessaryStubbingException
        lenient().when(mockErrorCode.getCode()).thenReturn(TEST_ERROR_CODE);
        lenient().when(mockErrorCode.getDescription()).thenReturn(TEST_ERROR_DESCRIPTION);
        lenient().when(mockAppException.getErrorCode()).thenReturn(mockErrorCode);
        lenient().when(mockAppException.getErrorMessage()).thenReturn(TEST_ERROR_MESSAGE);
        lenient().when(mockAppException.getSupplements()).thenReturn(new String[]{"param1", "param2"});
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试logError方法对于特定异常类型使用warn级别记录日志
     */
    @ParameterizedTest
    @MethodSource("provideSpecialExceptionTypes")
    @DisplayName("正常场景 - logError方法对特定异常使用warn级别")
    void testLogError_SpecialExceptionsUseWarnLevel(APPException exception) {
        // 执行被测试方法
        assertDoesNotThrow(() -> {
            appExceptionMapper.logError(exception, mockRequestContext);
        }, "logError方法不应抛出异常");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试logError方法对于普通异常使用error级别记录日志
     */
    @Test
    @DisplayName("正常场景 - logError方法对普通异常使用error级别")
    void testLogError_NormalExceptionsUseErrorLevel() {
        // 准备测试数据 - 普通APPException
        APPException normalException = new ActionClassLoadException(TEST_ERROR_MESSAGE);

        // 执行被测试方法
        assertDoesNotThrow(() -> {
            appExceptionMapper.logError(normalException, mockRequestContext);
        }, "logError方法不应抛出异常");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toCepResponse方法返回正确的CEP响应格式
     */
    @Test
    @DisplayName("正常场景 - toCepResponse返回正确格式")
    void testToCepResponse_ReturnsCorrectFormat() {
        // 执行被测试方法
        Response response = appExceptionMapper.toCepResponse(mockAppException, mockRequestContext);

        // 验证结果
        assertNotNull(response, "CEP响应不应为null");
        assertEquals(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), response.getStatus(), "状态码应为500");
        assertEquals("AppException", response.getEntity(), "响应体应为'AppException'");
        assertEquals(MediaType.TEXT_PLAIN, response.getMediaType().toString(), "媒体类型应为text/plain");

        // 验证响应头
        assertEquals(TEST_ERROR_CODE, response.getHeaderString("X-fs-Error-Code"), "错误码头应正确设置");
        assertEquals(TEST_ERROR_DESCRIPTION, response.getHeaderString("X-fs-Fail-Message"), "错误信息头应正确设置");
        assertNotNull(response.getHeaderString("X-fs-Error-Params"), "错误参数头应存在");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toInnerResponse方法返回正确的内部API响应格式
     */
    @Test
    @DisplayName("正常场景 - toInnerResponse返回正确格式")
    void testToInnerResponse_ReturnsCorrectFormat() {
        // 执行被测试方法
        Response response = appExceptionMapper.toInnerResponse(mockAppException, mockRequestContext);

        // 验证结果
        assertNotNull(response, "内部API响应不应为null");
        assertEquals(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), response.getStatus(), "状态码应为500");
        assertTrue(response.getEntity() instanceof InnerAPIResult, "响应体应为InnerAPIResult类型");

        InnerAPIResult result = (InnerAPIResult) response.getEntity();
        assertEquals(500, result.getErrCode(), "错误码应为500");
        assertEquals(TEST_ERROR_MESSAGE, result.getErrMessage(), "错误信息应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试toRestResponse方法返回正确的REST API响应格式
     */
    @Test
    @DisplayName("正常场景 - toRestResponse返回正确格式")
    void testToRestResponse_ReturnsCorrectFormat() {
        // 执行被测试方法
        Response response = appExceptionMapper.toRestResponse(mockAppException, mockRequestContext);

        // 验证结果
        assertNotNull(response, "REST API响应不应为null");
        assertEquals(Response.Status.INTERNAL_SERVER_ERROR.getStatusCode(), response.getStatus(), "状态码应为500");
        assertTrue(response.getEntity() instanceof RestAPIResult, "响应体应为RestAPIResult类型");

        RestAPIResult result = (RestAPIResult) response.getEntity();
        assertEquals(500, result.getCode(), "错误码应为500");
        assertEquals(TEST_ERROR_MESSAGE, result.getMessage(), "错误信息应正确");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getErrorMessage方法处理null异常的情况
     */
    @Test
    @DisplayName("异常场景 - getErrorMessage处理null异常")
    void testGetErrorMessage_HandlesNullException() throws Exception {
        // 使用反射调用私有方法
        java.lang.reflect.Method method = AppExceptionMapper.class.getDeclaredMethod("getErrorMessage", APPException.class);
        method.setAccessible(true);

        // 执行被测试方法
        String result = (String) method.invoke(appExceptionMapper, (APPException) null);

        // 验证结果
        assertEquals("AppException", result, "null异常应返回默认错误信息");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getErrorMessage方法返回正确的错误信息
     */
    @Test
    @DisplayName("正常场景 - getErrorMessage返回正确错误信息")
    void testGetErrorMessage_ReturnsCorrectMessage() throws Exception {
        // 使用反射调用私有方法
        java.lang.reflect.Method method = AppExceptionMapper.class.getDeclaredMethod("getErrorMessage", APPException.class);
        method.setAccessible(true);

        // 执行被测试方法
        String result = (String) method.invoke(appExceptionMapper, mockAppException);

        // 验证结果
        assertEquals(TEST_ERROR_MESSAGE, result, "应返回异常的错误信息");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试类的基本属性和继承关系
     */
    @Test
    @DisplayName("正常场景 - 验证类的基本属性")
    void testClassBasicProperties() {
        // 验证类实例化
        assertNotNull(appExceptionMapper, "AppExceptionMapper实例不应为null");

        // 验证继承关系
        assertTrue(appExceptionMapper instanceof AbstractExceptionMapper,
                "AppExceptionMapper应该继承自AbstractExceptionMapper");
    }

    /**
     * 提供特殊异常类型的测试数据
     */
    private static Stream<Arguments> provideSpecialExceptionTypes() {
        return Stream.of(
                Arguments.of(new ServiceNotFoundException("Service not found")),
                Arguments.of(new ActionDefNotFoundError("Action not found")),
                Arguments.of(new ControllerDefNotFoundError("Controller not found")),
                Arguments.of(new JsonSerializeError(new RuntimeException("Serialize error")))
        );
    }
} 