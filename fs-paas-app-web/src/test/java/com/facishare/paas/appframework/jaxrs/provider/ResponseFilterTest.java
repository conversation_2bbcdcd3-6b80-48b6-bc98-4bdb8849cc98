package com.facishare.paas.appframework.jaxrs.provider;

import com.facishare.idempotent.IdempotentRequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import javax.ws.rs.container.ContainerRequestContext;
import javax.ws.rs.container.ContainerResponseContext;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;

/**
 * ResponseFilter单元测试
 * GenerateByAI
 */
@ExtendWith(MockitoExtension.class)
class ResponseFilterTest {

    @InjectMocks
    private ResponseFilter responseFilter;

    @Mock
    private ContainerRequestContext mockRequestContext;

    @Mock
    private ContainerResponseContext mockResponseContext;

    @BeforeEach
    void setUp() {
        // 初始化测试环境
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filter方法正确调用清理上下文的静态方法
     */
    @Test
    @DisplayName("正常场景 - filter方法执行上下文清理")
    void testFilter_ExecutesContextCleanup() {
        try (MockedStatic<RequestContextManager> mockedContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<IdempotentRequestContext> mockedIdempotentContext = mockStatic(IdempotentRequestContext.class)) {

            // 执行被测试方法
            assertDoesNotThrow(() -> {
                responseFilter.filter(mockRequestContext, mockResponseContext);
            }, "filter方法不应抛出异常");

            // 验证静态方法调用
            mockedContextManager.verify(RequestContextManager::removeContext, times(1));
            mockedIdempotentContext.verify(IdempotentRequestContext::cleanRequestId, times(1));
            mockedIdempotentContext.verify(IdempotentRequestContext::cleanIdempotentKey, times(1));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filter方法处理null请求上下文
     */
    @Test
    @DisplayName("边界场景 - filter方法处理null请求上下文")
    void testFilter_HandleNullRequestContext() {
        try (MockedStatic<RequestContextManager> mockedContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<IdempotentRequestContext> mockedIdempotentContext = mockStatic(IdempotentRequestContext.class)) {

            // 执行被测试方法
            assertDoesNotThrow(() -> {
                responseFilter.filter(null, mockResponseContext);
            }, "filter方法处理null请求上下文不应抛出异常");

            // 验证静态方法依然被调用
            mockedContextManager.verify(RequestContextManager::removeContext, times(1));
            mockedIdempotentContext.verify(IdempotentRequestContext::cleanRequestId, times(1));
            mockedIdempotentContext.verify(IdempotentRequestContext::cleanIdempotentKey, times(1));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filter方法处理null响应上下文
     */
    @Test
    @DisplayName("边界场景 - filter方法处理null响应上下文")
    void testFilter_HandleNullResponseContext() {
        try (MockedStatic<RequestContextManager> mockedContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<IdempotentRequestContext> mockedIdempotentContext = mockStatic(IdempotentRequestContext.class)) {

            // 执行被测试方法
            assertDoesNotThrow(() -> {
                responseFilter.filter(mockRequestContext, null);
            }, "filter方法处理null响应上下文不应抛出异常");

            // 验证静态方法依然被调用
            mockedContextManager.verify(RequestContextManager::removeContext, times(1));
            mockedIdempotentContext.verify(IdempotentRequestContext::cleanRequestId, times(1));
            mockedIdempotentContext.verify(IdempotentRequestContext::cleanIdempotentKey, times(1));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试filter方法处理两个上下文都为null的情况
     */
    @Test
    @DisplayName("边界场景 - filter方法处理两个上下文都为null")
    void testFilter_HandleBothContextsNull() {
        try (MockedStatic<RequestContextManager> mockedContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<IdempotentRequestContext> mockedIdempotentContext = mockStatic(IdempotentRequestContext.class)) {

            // 执行被测试方法
            assertDoesNotThrow(() -> {
                responseFilter.filter(null, null);
            }, "filter方法处理两个null上下文不应抛出异常");

            // 验证静态方法依然被调用
            mockedContextManager.verify(RequestContextManager::removeContext, times(1));
            mockedIdempotentContext.verify(IdempotentRequestContext::cleanRequestId, times(1));
            mockedIdempotentContext.verify(IdempotentRequestContext::cleanIdempotentKey, times(1));
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当RequestContextManager.removeContext抛出异常时的处理
     */
    @Test
    @DisplayName("异常场景 - RequestContextManager抛出异常时的处理")
    void testFilter_RequestContextManagerException() {
        try (MockedStatic<RequestContextManager> mockedContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<IdempotentRequestContext> mockedIdempotentContext = mockStatic(IdempotentRequestContext.class)) {

            // 配置Mock抛出异常
            mockedContextManager.when(RequestContextManager::removeContext).thenThrow(new RuntimeException("Context cleanup error"));

            // 执行被测试方法并验证异常传播
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                responseFilter.filter(mockRequestContext, mockResponseContext);
            });

            assertEquals("Context cleanup error", exception.getMessage(), "应传播异常");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试当IdempotentRequestContext方法抛出异常时的处理
     */
    @Test
    @DisplayName("异常场景 - IdempotentRequestContext抛出异常时的处理")
    void testFilter_IdempotentRequestContextException() {
        try (MockedStatic<RequestContextManager> mockedContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<IdempotentRequestContext> mockedIdempotentContext = mockStatic(IdempotentRequestContext.class)) {

            // 配置Mock抛出异常
            mockedIdempotentContext.when(IdempotentRequestContext::cleanRequestId)
                    .thenThrow(new RuntimeException("Idempotent cleanup error"));

            // 执行被测试方法并验证异常传播
            RuntimeException exception = assertThrows(RuntimeException.class, () -> {
                responseFilter.filter(mockRequestContext, mockResponseContext);
            });

            assertEquals("Idempotent cleanup error", exception.getMessage(), "应传播异常");
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试类的基本属性
     */
    @Test
    @DisplayName("正常场景 - 验证类的基本属性")
    void testClassBasicProperties() {
        // 验证类实例化
        assertNotNull(responseFilter, "ResponseFilter实例不应为null");

        // 验证实现的接口
        assertTrue(responseFilter instanceof javax.ws.rs.container.ContainerResponseFilter,
                "ResponseFilter应该实现ContainerResponseFilter接口");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试多次调用filter方法确保清理操作的幂等性
     */
    @Test
    @DisplayName("正常场景 - 多次调用filter方法验证幂等性")
    void testFilter_MultipleCallsIdempotency() {
        try (MockedStatic<RequestContextManager> mockedContextManager = mockStatic(RequestContextManager.class);
             MockedStatic<IdempotentRequestContext> mockedIdempotentContext = mockStatic(IdempotentRequestContext.class)) {

            // 多次执行被测试方法
            for (int i = 0; i < 3; i++) {
                assertDoesNotThrow(() -> {
                    responseFilter.filter(mockRequestContext, mockResponseContext);
                }, "第" + (i + 1) + "次调用filter方法不应抛出异常");
            }

            // 验证静态方法被调用了3次
            mockedContextManager.verify(RequestContextManager::removeContext, times(3));
            mockedIdempotentContext.verify(IdempotentRequestContext::cleanRequestId, times(3));
            mockedIdempotentContext.verify(IdempotentRequestContext::cleanIdempotentKey, times(3));
        }
    }
} 