package com.facishare.paas.appframework.rest.service

import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.ServiceFacade
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.impl.ObjectData
import com.google.common.collect.Sets
import spock.lang.Specification
import spock.lang.Unroll

/**
 * ObjectDataRestService 单元测试
 */
class ObjectDataRestServiceTest extends Specification {

    ObjectDataRestService objectDataRestService
    ServiceFacade serviceFacade

    def setup() {
        serviceFacade = Mock(ServiceFacade)
        objectDataRestService = new ObjectDataRestService(
                serviceFacade: serviceFacade
        )

        // 设置请求上下文
        def user = User.builder().tenantId("12345").userId("test_user").build()
        def requestContext = RequestContext.builder().user(user).tenantId("12345").build()
        RequestContextManager.setContext(requestContext)
    }

    /**
     * 测试 getTriggerWorkFlowDataList 方法
     * 验证当有审批流ID列表时，能否正确过滤掉这些ID对应的数据
     */
    @Unroll
    def "getTriggerWorkFlowDataList方法测试 - #testCase"() {
        given: "准备测试数据"
        def dataList = createObjectDataList(dataIds)
        def triggerApprovalDataIds = triggerIds ? Sets.newHashSet(triggerIds) : Sets.newHashSet()

        when: "调用获取触发工作流数据列表方法"
        def result = objectDataRestService.getTriggerWorkFlowDataList(dataList, triggerApprovalDataIds)

        then: "验证结果"
        result.size() == expectedSize
        if (expectedSize > 0) {
            result.each { data ->
                assert !triggerApprovalDataIds.contains(data.getId())
            }
        }

        where:
        testCase                    | dataIds                    | triggerIds                | expectedSize
        "空审批流ID列表"            | ["1", "2", "3"]            | []                        | 3
        "部分数据触发审批流"        | ["1", "2", "3", "4"]        | ["1", "3"]                | 2
        "所有数据都触发审批流"      | ["1", "2", "3"]            | ["1", "2", "3"]           | 0
        "审批流ID列表包含不存在的ID" | ["1", "2", "3"]            | ["1", "4", "5"]           | 2
        "数据列表为空"             | []                         | ["1", "2", "3"]           | 0
        "审批流ID列表为null"       | ["1", "2", "3"]            | null                      | 3
    }

    /**
     * 创建测试用的数据列表
     */
    private List<IObjectData> createObjectDataList(List<String> ids) {
        return ids.collect { id ->
            def data = new ObjectData()
            data.setId(id)
            data.setTenantId("12345")
            data.setDescribeApiName("TestObject")
            return data
        }
    }
} 