package com.facishare.paas.appframework.jaxrs.provider;

import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.rest.CEPXHeader;
import com.facishare.paas.appframework.jaxrs.model.InnerAPIResult;
import com.facishare.paas.appframework.jaxrs.model.RestAPIResult;
import org.springframework.stereotype.Component;

import javax.ws.rs.core.MediaType;
import javax.ws.rs.core.Response;
import javax.ws.rs.ext.Provider;

/**
 * APP business exception mapper
 * <p>
 * Created by liyiguang on 2017/6/27.
 */
@Provider
@Component
public class AppBusinessExceptionMapper extends AbstractExceptionMapper<AppBusinessException> {

    @Override
    protected void logError(AppBusinessException exception, RequestContext context) {
        log.warn("request failed:{}-{},context:{}", exception.getErrorCode(), exception.getMessage(), context, exception);
    }

    @Override
    protected Response toCepResponse(AppBusinessException exception, RequestContext context) {
        if (exception.isSupportI18nCode()) {
            Response.ResponseBuilder builder = Response.status(exception.getStatus())
                    .header(CEPXHeader.ERROR_CODE.key(), exception.getErrorCode())
                    .header(CEPXHeader.FAILURE_MESSAGE.key(), exception.getMessage())
                    .type(MediaType.TEXT_PLAIN);
            return builder.build();
        } else {
            Response.ResponseBuilder builder = Response.status(exception.getStatus())
                    .header(CEPXHeader.FAILURE_CODE.key(), exception.getErrorCode())
                    .header(CEPXHeader.FAILURE_MESSAGE.key(), encode(exception.getMessage()))
                    .type(MediaType.TEXT_PLAIN);
            return builder.build();
        }
    }

    @Override
    protected Response toInnerResponse(AppBusinessException exception, RequestContext context) {
        return Response.status(exception.getStatus())
                .entity(InnerAPIResult.fail(exception.getErrorCode(), exception.getMessage()))
                .build();
    }

    @Override
    protected Response toRestResponse(AppBusinessException exception, RequestContext context) {
        return Response.status(exception.getStatus())
                .entity(RestAPIResult.fail(exception.getErrorCode(), exception.getMessage()))
                .build();
    }
}
