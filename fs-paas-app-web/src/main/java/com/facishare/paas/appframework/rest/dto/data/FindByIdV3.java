package com.facishare.paas.appframework.rest.dto.data;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import lombok.*;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Map;

/**
 * Created by zhouwr on 2022/4/29.
 */
public interface FindByIdV3 {

    @Data
    @EqualsAndHashCode(callSuper = true)
    class Arg extends FindDataV3.Arg {
        private String dataId;
        private Boolean includeDescribe;
        private Boolean includeStatistics;
        private Boolean fillMaskField;
        private Boolean useSnapshotForApproval;
        private Boolean formatData;

        public boolean includeDescribe() {
            return BooleanUtils.isTrue(includeDescribe);
        }

        public boolean includeStatistics() {
            return BooleanUtils.isTrue(includeStatistics);
        }

        public boolean fillMaskField() {
            return BooleanUtils.isTrue(fillMaskField);
        }

        public boolean useSnapshotForApproval() {
            return BooleanUtils.isTrue(useSnapshotForApproval);
        }

        public boolean formatData() {
            return BooleanUtils.isTrue(formatData);
        }

        @Override
        public MetaDataFindService.QueryContext buildQueryContext() {
            MetaDataFindService.QueryContext queryContext = super.buildQueryContext();
            queryContext.setSearchRichTextExtra(true);
            queryContext.setNotFillMask(!fillMaskField());
            return queryContext;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private ObjectDataDocument objectData;
        private ObjectDescribeDocument objectDescribe;
        private Map<String, Object> snapshot;
        private ObjectDescribeDocument objectDescribeExt;
    }

}
