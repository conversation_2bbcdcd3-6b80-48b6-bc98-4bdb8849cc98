package com.facishare.paas.appframework.rest.dto.data;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.metadata.MetaDataFindService;
import lombok.Data;

import java.util.List;

/**
 * Created by zhouwr on 2022/5/6.
 */
public interface FindDataV3 {
    @Data
    class Arg {
        //对象apiName
        private String describeApiName;
        //需要返回的字段
        private List<String> selectFields;
        //是否返回已作废数据
        private Boolean includeInvalid;
        //是否返回相关团队
        private Boolean includeRelevantTeam;
        //是否实时计算落地的计算字段
        private Boolean calculateFormula;
        //是否实时计算引用字段
        private Boolean calculateQuote;
        //是否实时计算统计字段
        private Boolean calculateCount;
        //是否补充各种字段的__r
        private Boolean fillExtendInfo;
        //是否将引用字段转换成页面展示的格式
        private Boolean convertQuoteForView;
        //是否补充单选多选字段的__r
        private Boolean needOptionLabel;
        //统计时结果为空期望的返回值 "null"、 "zero"、 "default"，默认为"default"，即 db 返回 null， es 返回 0
        private String statOnEmptyResult;

        public MetaDataFindService.QueryContext buildQueryContext() {
            RequestContext requestContext = RequestContextManager.getContext();
            return MetaDataFindService.QueryContext.builder()
                    .user(requestContext.getUser())
                    .projectionFields(selectFields)
                    .includeInvalid(Boolean.TRUE.equals(includeInvalid))
                    .skipRelevantTeam(!Boolean.TRUE.equals(includeRelevantTeam))
                    .calculateFormula(Boolean.TRUE.equals(calculateFormula))
                    .calculateQuote(Boolean.TRUE.equals(calculateQuote))
                    .calculateCount(Boolean.TRUE.equals(calculateCount))
                    .fillExtendInfo(Boolean.TRUE.equals(fillExtendInfo))
                    .convertQuoteForView(Boolean.TRUE.equals(convertQuoteForView))
                    .needOptionLabel(Boolean.TRUE.equals(needOptionLabel))
                    .notFillQuote(true)
                    .notFillMask(true)
                    .statOnEmptyResult(statOnEmptyResult)
                    .build();
        }
    }
}
