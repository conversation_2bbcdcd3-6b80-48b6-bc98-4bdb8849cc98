package com.facishare.paas.appframework.rest.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.rest.dto.data.IntelliFormData;
import com.facishare.paas.appframework.rest.service.IntelliFormDataService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;

/**
 * Created by Yuanxl on 2018/5/10
 */
@Controller
@Path("/v1/inner/rest/intelliForm/object_data")
@Slf4j
@RestAPI
public class IntelliFormDataController {

    @Autowired
    IntelliFormDataService intelliFormDataService;

    /**
     * 更换负责人
     * @param json 对象信息
     * @return 结果
     */
    @POST
    @Path("/change_owner")
    public Object changeOwner(String json){
        IntelliFormData.ChangeOwnerArg arg = JSON.parseObject(json, IntelliFormData.ChangeOwnerArg.class);

        return intelliFormDataService.changeOwner(arg);
    }

    /**
     * 创建数据
     * @param json 包含基本信息的对象数据
     * @return 创建后的数据
     */
    @POST
    @Path("/create")
    public IntelliFormData.CreateDataResult createObjectData(String json){
        IntelliFormData.CreateDataArg arg = JSON.parseObject(json, IntelliFormData.CreateDataArg.class);

        return intelliFormDataService.createObjectData(arg);
    }

    /**
     * 根据数据Id查询数据
     * @param json 相关参数
     * @return 数据
     */
    @POST
    @Path("/find_by_id")
    public IntelliFormData.FindByIdResult findObjectDataById(String json) {
        IntelliFormData.FindByIdArg arg = JSON.parseObject(json, IntelliFormData.FindByIdArg.class);

        return intelliFormDataService.findObjectDataById(arg);
    }

    /**
     * 查询对象数据列表
     * @param json 分页信息
     * @return 数据列表
     */
    @POST
    @Path("/find_list")
    public IntelliFormData.FindDataListResult findDataList(String json){
        RequestContext context = RequestContextManager.getContext();
        IntelliFormData.FindDataListArg arg = JSON.parseObject(json, IntelliFormData.FindDataListArg.class);

        return intelliFormDataService.findDataList(arg, context);
    }

}
