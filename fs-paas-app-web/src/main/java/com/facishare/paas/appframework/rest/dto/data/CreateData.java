package com.facishare.paas.appframework.rest.dto.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created By Yuanxl on 2018/5/2
 */
public interface CreateData {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String json;
        String descAPIName;
        Boolean isTool;
        Boolean isSpecifyTime;
        Boolean triggerWorkFlow;
        Boolean calculateDefaultValue;
    }

    class Result extends CommonResult.ObjectDataResult {}
}
