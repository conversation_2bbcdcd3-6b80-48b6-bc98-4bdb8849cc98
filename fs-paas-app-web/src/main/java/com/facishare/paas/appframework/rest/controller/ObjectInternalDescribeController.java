package com.facishare.paas.appframework.rest.controller;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.rest.dto.data.CheckCycle;
import com.facishare.paas.appframework.rest.dto.data.FindDescribeByTenantAndApiName;
import com.facishare.paas.appframework.rest.service.ObjectInternalDescribeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.*;
import java.util.Optional;

/**
 * Created by Yuanxl on 2018/5/8
 */
@RestAPI
@Controller
@Path("/v1/inner/rest/internal/object_describe")
@Slf4j
public class ObjectInternalDescribeController {

    @Autowired
    ObjectInternalDescribeService objectInternalDescribeService;

    /**
     * 根据企业ID和对象的api name 查询object describe对象
     * @param apiName 对象的apiName
     * @param includeRefDescribe 是否包含它关联的对象的describe信息
     * @return bject describe对象
     */
    @GET
    @Path("/{apiName}")
    public FindDescribeByTenantAndApiName.Result findByTenantIdAndDescribeAPIName(@PathParam("apiName") String apiName,
                                                                                  @QueryParam("include_ref_describe")
                                                                                          String includeRefDescribe){
        RequestContext context = RequestContextManager.getContext();
        FindDescribeByTenantAndApiName.Arg arg = FindDescribeByTenantAndApiName.Arg.builder()
                .apiName(apiName)
                .inCludeRefDescribe(Optional.ofNullable(includeRefDescribe).orElse(Boolean.FALSE.toString()))
                .build();

        return objectInternalDescribeService.findByTenantIdAndDescribeAPIName(arg, context);
    }

    /**
     * 检查关联是否成环
     * @param body 源apiName和目标apiName的json格式
     * @return 返回实体封装布尔值
     */
    @POST
    @Path("/check_cycle")
    public CheckCycle.Result checkCycle(String body){
        CheckCycle.Arg arg = JSON.parseObject(body, CheckCycle.Arg.class);
        RequestContext context = RequestContextManager.getContext();
        return objectInternalDescribeService.checkCycle(arg, context);
    }

}
