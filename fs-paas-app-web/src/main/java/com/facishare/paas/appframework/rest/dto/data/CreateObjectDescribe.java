package com.facishare.paas.appframework.rest.dto.data;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface CreateObjectDescribe {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String describeJson;
        boolean isActive;
    }

    class Result extends CommonResult.DescribeResult {}
}

