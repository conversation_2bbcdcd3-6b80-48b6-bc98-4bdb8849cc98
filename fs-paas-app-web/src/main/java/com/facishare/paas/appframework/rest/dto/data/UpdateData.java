package com.facishare.paas.appframework.rest.dto.data;

import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.facishare.paas.appframework.core.predef.action.BaseObjectSaveAction;
import com.facishare.paas.appframework.metadata.dto.DataConflictsResult;
import lombok.*;

public interface UpdateData {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String dataJson;
        String dataId;
        String describeAPIName;
        boolean triggerFlow;
        boolean isTool;
        boolean isSpecifyTime;
        boolean incrementalUpdate;
        boolean applyValidationRule;
        boolean applyDataPrivilegeCheck;
        boolean includeDescribe;
        boolean notValidate;
        boolean useSnapshotForApproval;
        Boolean skipImmutableFieldValidate;
        private boolean processDataConflicts;
        boolean skipModifyLog;
    }

    @Data
    @ToString(callSuper = true)
    @EqualsAndHashCode(callSuper = true)
    class Result extends CommonResult.ObjectDataResult {
        //是否被版本号校验拦截
        private Boolean versionCheckBlocked;
        // 冲突字段
        private BaseObjectSaveAction.DataConflicts dataConflicts;

        public void setDataConflictsResult(DataConflictsResult dataConflictsResult) {
            dataConflicts = BaseObjectSaveAction.DataConflicts.builder()
                    .fields(dataConflictsResult.getFields())
                    .lastData(ObjectDataDocument.of(dataConflictsResult.getLastData()))
                    .currentData(ObjectDataDocument.of(dataConflictsResult.getCurrentData()))
                    .build();
        }
    }
}
