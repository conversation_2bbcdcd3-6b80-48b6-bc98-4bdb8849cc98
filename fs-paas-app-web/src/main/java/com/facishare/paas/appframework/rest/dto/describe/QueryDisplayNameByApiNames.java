package com.facishare.paas.appframework.rest.dto.describe;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2022/4/28.
 */
public interface QueryDisplayNameByApiNames {

    @Data
    class Arg {
        private List<String> apiNames;
        private Boolean includeInvalid;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        private Map<String, String> displayNames;
    }

}
