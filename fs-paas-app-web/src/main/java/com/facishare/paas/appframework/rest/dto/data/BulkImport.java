package com.facishare.paas.appframework.rest.dto.data;

import com.facishare.paas.appframework.core.predef.service.dto.objectImport.FindImportInfosByNames;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 批量导入数据
 * Created By Yuanxl on 2018/4/27
 */
public interface BulkImport {

    /**
     * 导入一行的信息
     */
    @Data
    class ImportRowInfo {
        private int rowNo;
        private List<FindImportInfosByNames.ImportCellInfo> cellInfoList;
    }

    /**
     * 主属性字段信息
     */
    @Data
    class PrimaryFieldInfo {
        private String describeApiName;
        private String describeDisplayName;
        private String primaryFieldName = "name";

        public PrimaryFieldInfo(IObjectDescribe objectDescribe){
            describeApiName = objectDescribe.getApiName();
            describeDisplayName = objectDescribe.getDisplayName();
        }
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        List<FindImportInfosByNames.CellStruct> cellStructList;
    }

}
