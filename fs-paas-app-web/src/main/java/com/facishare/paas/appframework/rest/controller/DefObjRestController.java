package com.facishare.paas.appframework.rest.controller;

import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.rest.dto.data.FindTeamMember;
import com.facishare.paas.appframework.rest.service.DefObjectRestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.POST;
import javax.ws.rs.Path;
import javax.ws.rs.PathParam;
import java.util.Optional;

/**
 * Created by Yuanxl on 2018/5/8
 */
@Controller
@Slf4j
@Path("/v1/inner/rest/object_data")
@RestAPI
public class DefObjRestController {

    @Autowired
    DefObjectRestService defObjectRestService;

    @POST
    @Path("/{ObjectDescribeApiName}/privilege/getTeamMember")
    public FindTeamMember.Result getTeamMember(String dataID, @PathParam("ObjectDescribeApiName") String objectDescribeApiName) {
        RequestContext context = RequestContextManager.getContext();
        FindTeamMember.Arg arg = FindTeamMember.Arg.builder()
                .dataId(Optional.ofNullable(dataID).orElse(""))
                .describeAPIName(Optional.ofNullable(objectDescribeApiName).orElse(""))
                .build();
        return defObjectRestService.getTeamMember(arg, context);

    }
}
