package com.facishare.paas.appframework.rest.controller;

import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.jaxrs.annotation.RestAPI;
import com.facishare.paas.appframework.privilege.model.role.Role;
import com.facishare.paas.appframework.rest.dto.data.CreatePredefinedRole;
import com.facishare.paas.appframework.rest.dto.data.DeleteRoleObjectFuncAccessByApiNames;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import javax.ws.rs.DELETE;
import javax.ws.rs.GET;
import javax.ws.rs.POST;
import javax.ws.rs.Path;
import java.util.Map;

import static com.facishare.crm.common.exception.CRMErrorCode.ROLE_CODE_INVALID;
import static com.facishare.crm.common.exception.CRMErrorCode.SUCCESS;

/**
 * Created by luxin on 2018/8/27.
 */
@Controller
@Slf4j
@Path("/v1/inner/rest")
@RestAPI
public class PrivilegeRoleController {
    @Autowired
    private com.facishare.paas.appframework.privilege.RoleService roleService;


    @Path("/crm_roles")
    @GET
    public Object getAllCrmRoleInfo() {
        RequestContext context = RequestContextManager.getContext();
        return roleService.getCrmRoleInfoList(context.getTenantId());
    }


    @Path("/role_objects_func_access")
    @DELETE
    public Object deleteRoleObjectFuncAccessByApiNames(DeleteRoleObjectFuncAccessByApiNames.Arg arg) {
        Map<String, Object> result = Maps.newHashMap();
        result.put("msg", "success");
        result.put("result", true);
        return result;
    }

    @Path("/add_predefined_role")
    @POST
    public Object createPredefinedRole(CreatePredefinedRole.Arg arg) {
        RequestContext context = RequestContextManager.getContext();
        Role role = Role.getRoleByRoleCode(arg.getRoleCode());
        Map<String, Object> result = Maps.newHashMap();
        result.put("msg", SUCCESS.getCode());
        result.put("code", SUCCESS.getMessage());

        if (role == null) {
            result.put("code", ROLE_CODE_INVALID.getCode());
            result.put("msg", ROLE_CODE_INVALID.getMessage());
            return result;
        }
        try {
            roleService.addPredefinedRole(context.getTenantId(), role);
        } catch (PermissionError e) {
            result.put("code", e.getErrorCode());
            result.put("msg", e.getMessage());
        }
        return result;
    }

}
