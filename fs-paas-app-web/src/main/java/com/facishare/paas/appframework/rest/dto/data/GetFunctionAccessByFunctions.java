package com.facishare.paas.appframework.rest.dto.data;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2018/7/12.
 */
public interface GetFunctionAccessByFunctions {

    @Data
    class Arg {
        List<String> funcCodes;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        Map<String, Boolean> funcCodeStatusMapping;

    }
}
