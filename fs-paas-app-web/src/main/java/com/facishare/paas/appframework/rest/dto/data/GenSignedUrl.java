package com.facishare.paas.appframework.rest.dto.data;

import com.fxiaoke.stone.commons.domain.constant.AuthModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * 根据 signature 生成 signedUrl
 */
public interface GenSignedUrl {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        private Map<String, Object> data;
        private Boolean nested;
        private Options options;
    }
    
    @Data
    class Options {
        private AuthModel authModel;
        private Integer expireTime;
    }
    
    @Builder
    @Data
    class Result {
        private Map<String, Object> data;
    }
}
