package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDescribeDocument;
import com.facishare.paas.appframework.metadata.dto.RefObjectDescribeListResult;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by Yuanxl on 2018/5/8
 */
public interface FindDescribeByTenantAndApiName {

    @Builder
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String apiName;
        String inCludeRefDescribe;
    }

    @Data
    class Result {
        @JSONField(name = "describe")
        @JsonProperty(value = "describe")
        @SerializedName("describe")
        ObjectDescribeDocument objectDescribeDocument;

        @JSONField(name = "refObjectDescribeList")
        @JsonProperty(value = "refObjectDescribeList")
        @SerializedName("refObjectDescribeList")
        List<RefObjectDescribeListResult> refObjectDescribeList;

    }
}
