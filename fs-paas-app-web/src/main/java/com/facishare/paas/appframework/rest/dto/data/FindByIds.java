package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.core.model.ObjectDataDocument;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * Created by Yuanxl on 2018/4/25
 */
public interface FindByIds {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        List<String> idList;
        String descAPIName;
        boolean includeDescribe;
        boolean includeLookup;
        boolean includeQuoteValue;
        boolean useDbCalculateValue;
    }

    @Data
    class Result {

        @JSONField(name = "object_data")
        @JsonProperty(value = "object_data")
        @SerializedName("object_data")
        List<ObjectDataDocument> objectData;
    }
}
