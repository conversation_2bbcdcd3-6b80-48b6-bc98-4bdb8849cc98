package com.facishare.paas.appframework.rest.dto.data;


import com.facishare.paas.appframework.privilege.model.ObjectPrivilegeInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2018/7/12.
 */
public interface BatchGetObjectFunctionInfo {

    @Data
    class Arg {
        List<String> apiNames;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        List<ObjectPrivilegeInfo> objectFunctionInfoList;
    }

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    class FunctionInfo {
        private String displayName;
        private String functionNumber;
        private Boolean isEditable;
    }
}
