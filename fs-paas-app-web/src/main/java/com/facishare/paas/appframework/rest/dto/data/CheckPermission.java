package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

public interface CheckPermission {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        List<CheckPermissionData> permissionDataList;
    }

    @Data
    class CheckPermissionData {
        private String dataId;
        private String dataType;
        private String tenantId;
        private String userId;
    }

    @Data
    class Result {
        @J<PERSON>NField(name = "permission_result")
        @JsonProperty(value = "permission_result")
        @SerializedName("permission_result")
        Integer permissionResult;
    }

}
