package com.facishare.paas.appframework.rest.dto.data;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface FindNameByIds {

    @Data
    class Result {

//        @JSONField(name = "queryResult")
//        @JsonProperty(value = "queryResult")
//        @SerializedName("queryResult")
//        QueryResult<INameCache> queryResult;

        @JSONField(name = "queryResult")
        @JsonProperty(value = "queryResult")
        @SerializedName("queryResult")
        ResultData queryResult;
    }

    @Data
    class ResultData {
        List<Map<String, Object>> data;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        List<String> idList;
        String describeAPIName;
        /**
         * 命名规范
         * ui_optimized: 只下发name，按前端的显示规则处理。
         * 对象开启了displayName，开启了数据多语，使用displayName字段的languageName,languageName为空时或没有开启数据多语，使用displayName字段的值。
         * 对象没有开启displayName，开启了数据多语，使用name字段的languageName,languageName为空时或没有开启数据多语，使用name字段的值。
         * <p>
         * 默认情况下，name、displayName和languageName都下发，不用做任何处理。
         */
        private String namingConvention;

        public static final String UI_OPTIMIZED = "ui_optimized";
    }

}
