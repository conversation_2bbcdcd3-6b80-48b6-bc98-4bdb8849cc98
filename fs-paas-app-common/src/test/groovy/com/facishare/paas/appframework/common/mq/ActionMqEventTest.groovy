package com.facishare.paas.appframework.common.mq

import com.alibaba.fastjson.JSON
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import java.lang.reflect.Field

/**
 * GenerateByAI
 * 测试内容描述：ActionMqEvent类的单元测试
 */
class ActionMqEventTest extends Specification {

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ActionMqEvent的基本属性设置和获取
     */
    def "测试ActionMqEvent的基本属性设置和获取"() {
        given: "创建一个ActionMqEvent对象并设置其属性"
        def event = new ActionMqEvent()
        event.tenantId = 1001
        event.tenantAccount = "test_account"
        event.appId = "test_app_id"
        event.packageName = "test_package"
        event.apiName = "test_api_name"
        event.objectIdsStr = "1,2,3"
        event.actionCode = ActionMqEvent.ADD_ACTION
        event.actionContent = [key: "value"]
        event.operatorId = 12345
        event.actionTime = *************L
        event.source = "test_source"

        expect: "验证所有属性都被正确设置"
        event.tenantId == 1001
        event.tenantAccount == "test_account"
        event.appId == "test_app_id"
        event.packageName == "test_package"
        event.apiName == "test_api_name"
        event.objectIdsStr == "1,2,3"
        event.actionCode == "Add"
        event.actionContent instanceof Map
        event.actionContent.key == "value"
        event.operatorId == 12345
        event.actionTime == *************L
        event.source == "test_source"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ActionMqEvent的常量值
     */
    def "测试ActionMqEvent的常量值"() {
        expect: "验证常量值是否正确"
        ActionMqEvent.ADD_ACTION == "Add"
        ActionMqEvent.ABOLISH_ACTION == "Abolish"
        ActionMqEvent.DELETE_ACTION == "Delete"
        ActionMqEvent.RECOVER_ACTION == "Recover"
        ActionMqEvent.SUPER_ADMIN_USER_ID == "-10000"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ActionMqEvent的toString方法
     */
    def "测试ActionMqEvent的toString方法"() {
        given: "创建一个完整的ActionMqEvent对象"
        def event = new ActionMqEvent()
        event.tenantId = 1001
        event.tenantAccount = "test_account"
        event.appId = "test_app_id"
        event.packageName = "test_package"
        event.apiName = "test_api_name"
        event.objectIdsStr = "1,2,3"
        event.actionCode = ActionMqEvent.ADD_ACTION
        event.actionContent = "test_content"
        event.operatorId = 12345
        event.actionTime = *************L
        event.source = "test_source"

        when: "调用toString方法"
        def result = event.toString()

        then: "返回的字符串应包含所有属性"
        result.contains("tenantId=1001")
        result.contains("tenantAccount='test_account'")
        result.contains("appId='test_app_id'")
        result.contains("packageName='test_package'")
        result.contains("objectIdsStr='1,2,3'")
        result.contains("actionCode='Add'")
        result.contains("actionContent=test_content")
        result.contains("operatorId=12345")
        result.contains("actionTime=*************")
        result.contains("source='test_source'")
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ActionMqEvent的JSON序列化
     */
    def "测试ActionMqEvent的JSON序列化"() {
        given: "创建一个完整的ActionMqEvent对象"
        def event = new ActionMqEvent()
        event.tenantId = 1001
        event.tenantAccount = "test_account"
        event.appId = "test_app_id"
        event.packageName = "test_package"
        event.apiName = "test_api_name"
        event.objectIdsStr = "1,2,3"
        event.actionCode = ActionMqEvent.ADD_ACTION
        event.actionContent = "test_content"
        event.operatorId = 12345
        event.actionTime = *************L
        event.source = "test_source"

        when: "将对象序列化为JSON"
        def jsonStr = JSON.toJSONString(event)

        then: "JSON字符串应包含正确的字段名和值"
        jsonStr.contains('"TenantID":1001')
        jsonStr.contains('"TenantAccount":"test_account"')
        jsonStr.contains('"AppID":"test_app_id"')
        jsonStr.contains('"Package":"test_package"')
        jsonStr.contains('"ObjectApiName":"test_api_name"')
        jsonStr.contains('"ObjectID":"1,2,3"')
        jsonStr.contains('"ActionCode":"Add"')
        jsonStr.contains('"ActionContent":"test_content"')
        jsonStr.contains('"OperatorID":12345')
        jsonStr.contains('"ActionTime":*************')
        jsonStr.contains('"Source":"test_source"')
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ActionMqEvent的JSON反序列化
     */
    def "测试ActionMqEvent的JSON反序列化"() {
        given: "准备一个JSON字符串"
        def jsonStr = '''{
            "TenantID": 1001,
            "TenantAccount": "test_account",
            "AppID": "test_app_id",
            "Package": "test_package",
            "ObjectApiName": "test_api_name",
            "ObjectID": "1,2,3",
            "ActionCode": "Add",
            "ActionContent": "test_content",
            "OperatorID": 12345,
            "ActionTime": *************,
            "Source": "test_source"
        }'''

        when: "将JSON字符串反序列化为对象"
        def event = JSON.parseObject(jsonStr, ActionMqEvent.class)

        then: "对象的属性应被正确设置"
        event.tenantId == 1001
        event.tenantAccount == "test_account"
        event.appId == "test_app_id"
        event.packageName == "test_package"
        event.apiName == "test_api_name"
        event.objectIdsStr == "1,2,3"
        event.actionCode == "Add"
        event.actionContent == "test_content"
        event.operatorId == 12345
        event.actionTime == *************L
        event.source == "test_source"
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试ActionMqEvent的Lombok生成的equals和hashCode方法
     */
    def "测试ActionMqEvent的equals和hashCode方法"() {
        given: "创建两个相同属性的ActionMqEvent对象"
        def event1 = new ActionMqEvent()
        event1.tenantId = 1001
        event1.tenantAccount = "test_account"
        event1.appId = "test_app_id"
        
        def event2 = new ActionMqEvent()
        event2.tenantId = 1001
        event2.tenantAccount = "test_account"
        event2.appId = "test_app_id"
        
        and: "创建一个属性不同的ActionMqEvent对象"
        def event3 = new ActionMqEvent()
        event3.tenantId = 1002
        event3.tenantAccount = "different_account"
        event3.appId = "test_app_id"

        expect: "相同属性的对象应该equals返回true，hashCode相同"
        event1 == event2
        event1.hashCode() == event2.hashCode()
        
        and: "不同属性的对象应该equals返回false，hashCode可能不同"
        event1 != event3
    }
} 