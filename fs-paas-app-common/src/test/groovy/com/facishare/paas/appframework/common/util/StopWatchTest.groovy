package com.facishare.paas.appframework.common.util

import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：StopWatch工具类的单元测试
 */
class StopWatchTest extends Specification {

    /**
     * GenerateByAI
     * 测试内容描述：测试创建StopWatch实例
     */
    def "create test"() {
        when:
        def stopWatch = StopWatch.create("testTag")
        
        then:
        stopWatch != null
        stopWatch instanceof StopWatch
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试lap方法记录步骤耗时
     */
    def "lap test"() {
        given:
        def stopWatch = StopWatch.create("testLap")
        
        when:
        // 执行一些操作
        Thread.sleep(10)
        stopWatch.lap("step1")
        
        // 再执行其他操作
        Thread.sleep(20)
        stopWatch.lap("step2")
        
        then:
        // lap方法不会抛出异常
        noExceptionThrown()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试log方法输出耗时日志
     */
    def "log test"() {
        given:
        def stopWatch = StopWatch.create("testLog")
        
        when:
        // 执行一些操作
        Thread.sleep(10)
        stopWatch.lap("step1")
        
        // 记录日志
        stopWatch.log()
        
        then:
        // log方法不会抛出异常
        noExceptionThrown()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试logSlow方法在超过阈值时输出日志
     */
    def "logSlow test"() {
        given:
        def stopWatch = StopWatch.create("testLogSlow")
        
        when:
        // 执行一些操作
        Thread.sleep(10)
        stopWatch.lap("step1")
        
        // 设置阈值为5毫秒(已超过)，应该会输出日志
        stopWatch.logSlow(5)
        
        then:
        // logSlow方法不会抛出异常
        noExceptionThrown()
        
        when:
        // 设置阈值为1000毫秒(未超过)，不应该输出日志
        stopWatch.logSlow(1000)
        
        then:
        // logSlow方法不会抛出异常
        noExceptionThrown()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试连续调用多个步骤
     */
    def "multiple steps test"() {
        given:
        def stopWatch = StopWatch.create("multiSteps")
        
        when:
        // 执行多个步骤
        for (int i = 0; i < 5; i++) {
            Thread.sleep(5) // 模拟操作耗时
            stopWatch.lap("step" + i)
        }
        
        // 记录日志
        stopWatch.log()
        
        then:
        // 所有方法调用不会抛出异常
        noExceptionThrown()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试实际场景中的使用模式
     */
    def "real scenario usage test"() {
        given:
        def stopWatch = StopWatch.create("realScenario")
        
        when:
        // 模拟实际业务场景中的使用模式
        stopWatch.lap("start")
        
        // 第一阶段处理
        processPhase1()
        stopWatch.lap("phase1")
        
        // 第二阶段处理
        processPhase2()
        stopWatch.lap("phase2")
        
        // 记录日志
        stopWatch.log()
        
        then:
        // 所有方法调用不会抛出异常
        noExceptionThrown()
    }
    
    /**
     * 模拟第一阶段处理
     */
    private void processPhase1() {
        // 模拟耗时操作
        Thread.sleep(10)
    }
    
    /**
     * 模拟第二阶段处理
     */
    private void processPhase2() {
        // 模拟耗时操作
        Thread.sleep(20)
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试边界情况
     */
    def "boundary cases test"() {
        when:
        // 使用空字符串作为标签名
        def stopWatch1 = StopWatch.create("")
        stopWatch1.lap("step")
        stopWatch1.log()
        
        then:
        // 不会抛出异常
        noExceptionThrown()
        
        when:
        // 使用null作为lap名称（可能会抛出异常，取决于底层实现）
        def stopWatch2 = StopWatch.create("nullTest")
        
        then:
        try {
            stopWatch2.lap(null)
            // 如果没有异常，则通过测试
            true
        } catch (Exception e) {
            // 如果抛出异常，捕获并继续测试
            true
        }
        
        when:
        // 连续调用log不会有问题
        def stopWatch3 = StopWatch.create("multiLog")
        stopWatch3.lap("step")
        stopWatch3.log()
        stopWatch3.log()
        
        then:
        // 不会抛出异常
        noExceptionThrown()
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试性能
     */
    def "performance test"() {
        given:
        def iterations = 1000
        def startTime = System.nanoTime()
        
        when:
        // 创建大量StopWatch实例并调用方法
        for (int i = 0; i < iterations; i++) {
            def stopWatch = StopWatch.create("perf" + i)
            stopWatch.lap("step")
            // 不调用log方法避免大量日志输出
        }
        
        def endTime = System.nanoTime()
        def duration = (endTime - startTime) / 1_000_000 // 转换为毫秒
        
        then:
        // 创建1000个实例应该很快（小于1000毫秒）
        duration < 1000
        println "Created and used $iterations StopWatch instances in $duration ms"
    }
} 