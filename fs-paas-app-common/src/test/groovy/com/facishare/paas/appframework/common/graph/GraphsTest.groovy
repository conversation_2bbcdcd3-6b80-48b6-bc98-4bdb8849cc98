package com.facishare.paas.appframework.common.graph

import spock.lang.Specification

/**
 * GenerateByAI
 * 测试内容描述：Graphs工具类的单元测试
 */
class GraphsTest extends Specification {

    def "test hasCycle with acyclic directed graph"() {
        given: "创建无环有向图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.putEdge("A", "B")
        graph.putEdge("B", "C")

        when: "检查是否有环"
        def result = Graphs.hasCycle(graph)

        then: "应该返回false"
        !result
    }

    def "test hasCycle with cyclic directed graph"() {
        given: "创建有环有向图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.putEdge("A", "B")
        graph.putEdge("B", "C")
        graph.putEdge("C", "A") // 形成环

        when: "检查是否有环"
        def result = Graphs.hasCycle(graph)

        then: "应该返回true"
        result
    }

    def "test hasCycle with self loop"() {
        given: "创建有自环的图"
        def graph = GraphBuilder.directed().allowsSelfLoops(true).build()
        graph.addNode("A")
        graph.putEdge("A", "A") // 自环

        when: "检查是否有环"
        def result = Graphs.hasCycle(graph)

        then: "应该返回true"
        result
    }

    def "test hasCycle with empty graph"() {
        given: "创建空图"
        def graph = GraphBuilder.directed().build()

        when: "检查是否有环"
        def result = Graphs.hasCycle(graph)

        then: "应该返回false"
        !result
    }

    def "test hasCycle with undirected graph"() {
        given: "创建无向图"
        def graph = GraphBuilder.undirected().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.putEdge("A", "B")
        graph.putEdge("B", "C")
        graph.putEdge("A", "C") // 在无向图中形成环

        when: "检查是否有环"
        def result = Graphs.hasCycle(graph)

        then: "应该返回true"
        result
    }

    def "test reachableNodes from single node"() {
        given: "创建有向图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.addNode("D")
        graph.putEdge("A", "B")
        graph.putEdge("B", "C")
        graph.putEdge("A", "D")

        when: "获取从A可达的节点"
        def reachable = Graphs.reachableNodes(graph, "A")

        then: "应该包含A、B、C、D"
        reachable.size() == 4
        reachable.contains("A")
        reachable.contains("B")
        reachable.contains("C")
        reachable.contains("D")
    }

    def "test reachableNodes with disconnected components"() {
        given: "创建有断开组件的图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.addNode("D")
        graph.putEdge("A", "B")
        graph.putEdge("C", "D") // 断开的组件

        when: "获取从A可达的节点"
        def reachable = Graphs.reachableNodes(graph, "A")

        then: "应该只包含A、B"
        reachable.size() == 2
        reachable.contains("A")
        reachable.contains("B")
        !reachable.contains("C")
        !reachable.contains("D")
    }

    def "test reachableNodes with non-existent node throws exception"() {
        given: "创建图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")

        when: "获取不存在节点的可达节点"
        Graphs.reachableNodes(graph, "NonExistent")

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test transitiveClosure for directed graph"() {
        given: "创建有向图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.putEdge("A", "B")
        graph.putEdge("B", "C")

        when: "计算传递闭包"
        def closure = Graphs.transitiveClosure(graph)

        then: "传递闭包应该包含所有传递边"
        closure.hasEdgeConnecting("A", "A") // 自环
        closure.hasEdgeConnecting("A", "B") // 原始边
        closure.hasEdgeConnecting("A", "C") // 传递边
        closure.hasEdgeConnecting("B", "B") // 自环
        closure.hasEdgeConnecting("B", "C") // 原始边
        closure.hasEdgeConnecting("C", "C") // 自环
    }

    def "test transitiveClosure for undirected graph"() {
        given: "创建无向图"
        def graph = GraphBuilder.undirected().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.putEdge("A", "B")
        graph.putEdge("B", "C")

        when: "计算传递闭包"
        def closure = Graphs.transitiveClosure(graph)

        then: "传递闭包应该是完全连通的"
        closure.hasEdgeConnecting("A", "A")
        closure.hasEdgeConnecting("A", "B")
        closure.hasEdgeConnecting("A", "C")
        closure.hasEdgeConnecting("B", "A")
        closure.hasEdgeConnecting("B", "B")
        closure.hasEdgeConnecting("B", "C")
        closure.hasEdgeConnecting("C", "A")
        closure.hasEdgeConnecting("C", "B")
        closure.hasEdgeConnecting("C", "C")
    }

    def "test transpose directed graph"() {
        given: "创建有向图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.putEdge("A", "B")
        graph.putEdge("B", "C")

        when: "转置图"
        def transposed = Graphs.transpose(graph)

        then: "边的方向应该反转"
        transposed.hasEdgeConnecting("B", "A")
        transposed.hasEdgeConnecting("C", "B")
        !transposed.hasEdgeConnecting("A", "B")
        !transposed.hasEdgeConnecting("B", "C")
    }

    def "test transpose undirected graph returns same graph"() {
        given: "创建无向图"
        def graph = GraphBuilder.undirected().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.putEdge("A", "B")

        when: "转置图"
        def transposed = Graphs.transpose(graph)

        then: "应该返回相同的图"
        transposed.is(graph)
    }

    def "test double transpose returns original graph"() {
        given: "创建有向图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.putEdge("A", "B")

        when: "双重转置"
        def doubleTransposed = Graphs.transpose(Graphs.transpose(graph))

        then: "应该返回原始图"
        doubleTransposed.is(graph)
    }

    def "test inducedSubgraph"() {
        given: "创建图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")
        graph.addNode("B")
        graph.addNode("C")
        graph.addNode("D")
        graph.putEdge("A", "B")
        graph.putEdge("B", "C")
        graph.putEdge("C", "D")
        graph.putEdge("A", "D")

        when: "创建诱导子图"
        def subgraph = Graphs.inducedSubgraph(graph, ["A", "B", "C"])

        then: "子图应该只包含指定节点和它们之间的边"
        subgraph.nodes().size() == 3
        subgraph.nodes().containsAll(["A", "B", "C"])
        subgraph.hasEdgeConnecting("A", "B")
        subgraph.hasEdgeConnecting("B", "C")
        !subgraph.hasEdgeConnecting("A", "D") // D不在子图中
        !subgraph.hasEdgeConnecting("C", "D") // D不在子图中
    }

    def "test inducedSubgraph with non-existent node throws exception"() {
        given: "创建图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")

        when: "使用不存在的节点创建诱导子图"
        Graphs.inducedSubgraph(graph, ["A", "NonExistent"])

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test copyOf graph"() {
        given: "创建原始图"
        def original = GraphBuilder.directed().allowsSelfLoops(true).build()
        original.addNode("A")
        original.addNode("B")
        original.putEdge("A", "B")
        original.putEdge("A", "A")

        when: "复制图"
        def copy = Graphs.copyOf(original)

        then: "复制的图应该有相同的结构但是不同的实例"
        !copy.is(original)
        copy.nodes() == original.nodes()
        copy.edges() == original.edges()
        copy.isDirected() == original.isDirected()
        copy.allowsSelfLoops() == original.allowsSelfLoops()
    }

    def "test checkNonNegative with valid values"() {
        expect: "非负值应该通过检查"
        Graphs.checkNonNegative(0) == 0
        Graphs.checkNonNegative(1) == 1
        Graphs.checkNonNegative(100) == 100
        Graphs.checkNonNegative(0L) == 0L
        Graphs.checkNonNegative(1L) == 1L
    }

    def "test checkNonNegative with negative values throws exception"() {
        when: "使用负整数"
        Graphs.checkNonNegative(-1)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)

        when: "使用负长整数"
        Graphs.checkNonNegative(-1L)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test checkPositive with valid values"() {
        expect: "正值应该通过检查"
        Graphs.checkPositive(1) == 1
        Graphs.checkPositive(100) == 100
        Graphs.checkPositive(1L) == 1L
        Graphs.checkPositive(100L) == 100L
    }

    def "test checkPositive with non-positive values throws exception"() {
        when: "使用零"
        Graphs.checkPositive(0)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)

        when: "使用负数"
        Graphs.checkPositive(-1)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)

        when: "使用零长整数"
        Graphs.checkPositive(0L)

        then: "应该抛出IllegalArgumentException"
        thrown(IllegalArgumentException)
    }

    def "test complex graph operations"() {
        given: "创建复杂图"
        def graph = GraphBuilder.directed().build()
        ["A", "B", "C", "D", "E"].each { graph.addNode(it) }
        graph.putEdge("A", "B")
        graph.putEdge("B", "C")
        graph.putEdge("C", "D")
        graph.putEdge("D", "E")
        graph.putEdge("A", "E") // 跨越边

        when: "执行各种操作"
        def hasCycle = Graphs.hasCycle(graph)
        def reachableFromA = Graphs.reachableNodes(graph, "A")
        def transposed = Graphs.transpose(graph)
        def subgraph = Graphs.inducedSubgraph(graph, ["A", "B", "C"])

        then: "所有操作应该正确执行"
        !hasCycle
        reachableFromA.size() == 5
        transposed.hasEdgeConnecting("B", "A")
        transposed.hasEdgeConnecting("E", "A")
        subgraph.nodes().size() == 3
        subgraph.hasEdgeConnecting("A", "B")
        subgraph.hasEdgeConnecting("B", "C")
    }

    def "test edge cases with single node"() {
        given: "创建单节点图"
        def graph = GraphBuilder.directed().build()
        graph.addNode("A")

        expect: "各种操作应该正确处理单节点情况"
        !Graphs.hasCycle(graph)
        Graphs.reachableNodes(graph, "A") == ["A"] as Set
        Graphs.transpose(graph).nodes() == ["A"] as Set
        Graphs.inducedSubgraph(graph, ["A"]).nodes() == ["A"] as Set
    }
}
