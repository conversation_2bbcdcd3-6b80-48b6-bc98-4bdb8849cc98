package com.facishare.paas.appframework.common.util

import com.alibaba.fastjson.JSONArray
import com.fxiaoke.release.FsGrayReleaseBiz
import com.fxiaoke.release.GrayRule
import com.google.common.collect.Lists
import com.google.common.collect.Maps
import com.google.common.collect.Sets
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import com.facishare.paas.appframework.core.util.UdobjGrayConfig
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey
import java.lang.reflect.Field

class AppFrameworkConfigTest extends Specification {

    def "listSingleButtonGray test"() {

        given:
        Map<String, List<AppFrameworkConfig.GrayRuleHelper<Set<String>>>> map = Maps.newHashMap()
        List<Map<String, String>> parse = (List<Map<String, String>>) JSONArray.parse("[{\"ei\":\"white:74255|78057|78437|83050|81028|78436|81146|78436\",\"describeApiName\":\"SalesOrderObj\",\"buttonApiName\":\"PayInstantly_button_default,ConfirmReceipt2_button_default,BuyAgain_button_default,BalanceReduce_button_default\"},{\"ei\":\"allow\",\"describeApiName\":\"DeliveryNoteObj\",\"buttonApiName\":\"ConfirmReceipt_button_default,ViewLogistics_button_default\"},{\"ei\":\"allow\",\"describeApiName\":\"CasesObj\",\"buttonApiName\":\"CasesCreateServiceReport_button_default,button_91Rk3__c,CasesCallOut_button_default\"},{\"ei\":\"allow\",\"describeApiName\":\"SalesOrderObj\",\"buttonApiName\":\"OneMoreOrder_button_default\"},{\"ei\":\"allow\",\"describeApiName\":\"BiddingSubscriptionRulesObj\",\"buttonApiName\":\"Clone_button_default,ChangeStatus_button_default,Delete_button_default,Edit_button_default\"},{\"ei\":\"allow\",\"describeApiName\":\"HistoricalBiddingImportObj\",\"buttonApiName\":\"Clone_button_default,ChangeStatus_button_default,Delete_button_default,Edit_button_default\"},{\"ei\":\"allow\",\"describeApiName\":\"SalesStatementsObj\",\"buttonApiName\":\"ConformanceStatements_button_default,PrintReceipt_button_default\"},{\"ei\":\"allow\",\"describeApiName\":\"RequisitionNoteObj\",\"buttonApiName\":\"print_ticket_button__c\"},{\"ei\":\"allow\",\"describeApiName\":\"ALL\",\"buttonApiName\":\"Transform_button_default\"}]")
        for (Map<String, String> objectMap : parse) {
            String describeApiName = objectMap.get("describeApiName")
            Set<String> buttons = Sets.newHashSet(AppFrameworkConfig.CONFIG_SPLITTER.split(objectMap.get("buttonApiName")))
            AppFrameworkConfig.GrayRuleHelper<Set<String>> grayRuleHelper = AppFrameworkConfig.GrayRuleHelper.of(objectMap.get("ei"), buttons)
            map.computeIfAbsent(describeApiName, { it -> Lists.newArrayList() }).add(grayRuleHelper)
        }
        AppFrameworkConfig.listSingleButtonGray = map
        when:
//        Whitebox.invokeMethod(AppFrameworkConfig.class, "initButtonGray", config, "list_single_button_gray")
        def buttonApiNames = AppFrameworkConfig.listSingleButtonGray('79337', 'RFMRuleObj')
        then:
        println(buttonApiNames)
        buttonApiNames.contains("Transform_button_default")
    }

    @Unroll
    def "transMultiLanguageFilterComponentV2 test"() {


        when:
        AppFrameworkConfig.transMultiLanguageFilterComponent = ["form", "tabs", "top_info", "simple", "navigation", "grid", "suspendedComponent", "text", "form_table"]

        AppFrameworkConfig.transMultiLanguageFilterComponentV2 = ["text": new GrayRule("white:74255|78057"),
                                                                  "form_table": new GrayRule("white:74255|78057")]

        def res = AppFrameworkConfig.isFilterByTransMultiLanguageComponent(componentType, tenantId)
        then:
        res == result
        where:
        componentType | tenantId | result
        "form_table"  | "74255"  | false //在白名单中
        "text"        | "78057"  | false //在白名单中
        "text"        | "77996"  | true  //在白名单中但是企业不在
        "chart"       | ""       | false //黑白名单都不在，默认放开
        "chart"       | "77996"  | false //黑白名单都不在，默认放开
        "form"        | "77996"  | true //在白名单中，但是企业不在,判断是否是黑名单中
        "form"        | ""       | true //在白名单中，但是企业为空,判断是否是黑名单中

    }
//    def "multi_lang_support test"() {
//        given:
//        String jsonConfig = "{\"DepartmentObj\":{\"name\":[\"zh-CN\",\"en\"]},\"udobj\":{\"name\":[\"zh-CN\",\"en\"]},\"ALL\":{\"*\":[\"zh-CN\",\"zh-TW\"]}}";
//        Map<String, Map<String, List<String>>> config = JSON.parse(jsonConfig, Map.class)
//        AppFrameworkConfig.multiLangSupport = config
//        when:
//        def res = AppFrameworkConfig.getMultiLangSupport([Lang.zh_CN, Lang.en, Lang.zh_TW], describeApiName, fieldApiName)
//        then:
//        res == result
//        where:
//        describeApiName    | fieldApiName || result
//        "DepartmentObj"    | "name"       || [Lang.zh_CN, Lang.en]
//        "DepartmentObj"    | "field"      || [Lang.zh_CN, Lang.en, Lang.zh_TW]
//        "DepartmentObj__c" | "field"      || [Lang.zh_CN, Lang.en, Lang.zh_TW]
//        "DepartmentObj__c" | "name"       || [Lang.zh_CN, Lang.en]
//        "Obj"              | "name"       || [Lang.zh_CN, Lang.zh_TW]
//        "Obj"              | "field"      || [Lang.zh_CN, Lang.zh_TW]
//
//    }

    def "test isListLayoutSupportTransComponent"() {
        given:
        // 设置测试数据
        def grayRule = Mock(GrayRule)
        AppFrameworkConfig.listLayoutSupportTransComponentGray = [
                "type1": grayRule,
                "type2": null
        ]

        AppFrameworkConfig.listLayoutSupportTransComponent = ["type1"]

        when: "测试正常情况"
        grayRule.isAllow("tenant1") >> true
        def result1 = AppFrameworkConfig.isListLayoutSupportTransComponent("tenant1", "type1")

        then: "应该返回true"
        result1 == true

        when: "测试tenantId为空的情况"
        def result2 = AppFrameworkConfig.isListLayoutSupportTransComponent("", "type1")

        then: "应该调用无租户的方法"
        result2 == true

        when: "测试type为null的情况"
        def result3 = AppFrameworkConfig.isListLayoutSupportTransComponent("tenant1", null)

        then: "应该返回false"
        result3 == false

        when: "测试type不存在于灰度规则中的情况"
        def result4 = AppFrameworkConfig.isListLayoutSupportTransComponent("tenant1", "nonexistent")

        then: "应该调用无租户的方法"
        result4 == false

        when: "测试grayRule为null的情况"
        def result5 = AppFrameworkConfig.isListLayoutSupportTransComponent("tenant1", "type2")

        then: "应该调用无租户的方法"
        result5 == false

        when: "测试tenantId和type都为null的情况"
        def result6 = AppFrameworkConfig.isListLayoutSupportTransComponent(null, null)

        then: "应该返回false"
        result6 == false
    }

    @Unroll
    def "test isListLayoutSupportTransComponent with different inputs"() {
        given:
        def grayRule = Mock(GrayRule)
        AppFrameworkConfig.listLayoutSupportTransComponentGray = ["validType": grayRule]
        AppFrameworkConfig.listLayoutSupportTransComponent = ["validType2"]
        grayRule.isAllow(_ as String) >> grayRuleResult

        when:
        def result = AppFrameworkConfig.isListLayoutSupportTransComponent(tenantId, type)

        then:
        result == expectedResult

        where:
        tenantId  | type         | grayRuleResult | expectedResult
        "tenant1" | "validType"  | true           | true
        "tenant1" | "validType"  | false          | false
        ""        | "validType"  | true           | false  // 空租户ID
        null      | "validType"  | true           | false  // null租户ID
        "tenant1" | null         | true           | false  // null类型
        "tenant1" | ""           | true           | false  // 空类型
        "tenant1" | "invalid"    | true           | false  // 无效类型
        "tenant1" | "validType2" | true           | true  // 无效类型
        null      | "validType2" | true           | true  // 无效类型
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试HTML富文本字段在对象上的数量限制配置
     */
    @Unroll
    def "getHtmlRichTextFieldMaxLimitByObject with #describeApiName and #tenantId should return #expectedResult"() {
        given: "准备测试参数"
        // 设置AppFrameworkConfig的内部状态
        Map<String, Integer> htmlLimits = [
            "fspre": 50,
            "fsdemo": 50,
            "fs123456789": 50,
            "T-fs123456789": 50
        ]
        
        Map<String, Integer> objHtmlLimits = [
            "FSContact": 8,
            "FSAccount": 8,
            "FSCustomObject": 8,
            "object_a": 50
        ]
        
        Whitebox.setInternalState(AppFrameworkConfig, "htmlRichTextLimit", htmlLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "objectHtmlRichTextFieldMaxCount", objHtmlLimits)
        
        when: "调用HTML富文本字段最大限制计算方法"
        def result = AppFrameworkConfig.getHtmlRichTextFieldMaxLimitByObject(describeApiName, tenantId)
        
        then: "验证结果与预期一致"
        result == expectedResult
        
        where: "不同参数组合场景"
        describeApiName  | tenantId         | expectedResult
        "object_a"       | "fspre"          | 50
        "object_a"       | "fsdemo"         | 50
        "object_a"       | "fs123456789"    | 50
        "object_a"       | "T-fs123456789"  | 50
        "FSContact"      | "fspre"          | 50
        "FSContact"      | "fsdemo"         | 50
        "FSContact"      | "T-fs123456789"  | 50
        "FSAccount"      | "fspre"          | 50
        "FSAccount"      | "fsdemo"         | 50
        "FSAccount"      | "fs123456789"    | 50
        "FSCustomObject" | "fspre"          | 50
        "FSCustomObject" | "fsdemo"         | 50
        "FSCustomObject" | "T-fs123456789"  | 50
    }
    
    /**
     * GenerateByAI
     * 测试内容描述：测试协同富文本字段在对象上的数量限制配置
     */
    @Unroll
    def "getRichTextFieldMaxLimitByObject with #describeApiName and #tenantId should return #expectedResult"() {
        given: "准备测试参数"
        // 设置AppFrameworkConfig的内部状态
        Map<String, Integer> cooperativeLimits = [
            "fspre": 50,
            "fsdemo": 50,
            "fs123456789": 50,
            "T-fs123456789": 50
        ]
        
        Map<String, Integer> objCooperativeLimits = [
            "FSContact": 8,
            "FSAccount": 8,
            "FSCustomObject": 8,
            "object_a": 50
        ]
        
        Whitebox.setInternalState(AppFrameworkConfig, "richTextLimit", cooperativeLimits)
        Whitebox.setInternalState(AppFrameworkConfig, "objectRichTextFieldMaxCount", objCooperativeLimits)

        
        when: "调用协同富文本字段最大限制计算方法"
        def result = AppFrameworkConfig.getRichTextFieldMaxLimitByObject(describeApiName, tenantId)
        
        then: "验证结果与预期一致"
        result == expectedResult
        
        where: "不同参数组合场景"
        describeApiName  | tenantId         | expectedResult
        "object_a"       | "fspre"          | 50
        "object_a"       | "fsdemo"         | 50
        "object_a"       | "fs123456789"    | 50
        "object_a"       | "T-fs123456789"  | 50
        "FSContact"      | "fspre"          | 50
        "FSContact"      | "fsdemo"         | 50
        "FSContact"      | "T-fs123456789"  | 50
        "FSAccount"      | "fspre"          | 50
        "FSAccount"      | "fsdemo"         | 50
        "FSAccount"      | "fs123456789"    | 50
        "FSCustomObject" | "fspre"          | 50
        "FSCustomObject" | "fsdemo"         | 50
        "FSCustomObject" | "T-fs123456789"  | 50
    }

    def "test isGrayDuplicatedSupportDistance"() {
        given:
        def fsGrayReleaseBiz = Mock(FsGrayReleaseBiz)
        // 使用 Groovy metaClass 替代 FieldUtils.removeFinalModifier

        Whitebox.setInternalState(UdobjGrayConfig.INSTANCE, "UDOBJ_GRAY", fsGrayReleaseBiz)
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_SUPPORT_DISTANCE, tenantId) >> isAllowDuplicatedSearchSupportDistance
        fsGrayReleaseBiz.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_NOT_USE_REDIS, tenantId) >> isAllowDuplicatedSearchNotUseRedis

        when:
        def result = AppFrameworkConfig.isGrayDuplicatedSupportDistance(tenantId)

        then:
        result == expected

        where:
        tenantId | isAllowDuplicatedSearchSupportDistance | isAllowDuplicatedSearchNotUseRedis | expected
        "74255"  | true                                  | true                               | true
        "74255"  | true                                  | false                              | false
        "74255"  | false                                 | true                               | false
        "74255"  | false                                 | false                              | false
    }
}
