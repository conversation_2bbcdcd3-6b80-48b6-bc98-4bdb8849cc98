package com.facishare.paas.common.util;

import com.facishare.crm.userdefobj.CrmActionEnum;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * Created by yusb on 2017/8/18.
 */
//TODO: 这个
@Deprecated
public class UdobjConstants {


    /**
     * 字段相关的常数
     */
    //负责人、所属部门的API_NAME和LABEL
//    public static final String OWNER_API_LABEL = "负责人";
    public static final String OWNER_API_NAME = "owner";

//    public static final String DEPARTMENT_LABEL = "所属部门";
//    public static final String DEPARTMENT_API_NAME = "department";

    //    public static final String OWNER_DEPARTMENT_LABEL = "负责人所在部门";
    public static final String OWNER_DEPARTMENT_API_NAME = "owner_department";

    private static final String RELEVANT_TEAM_LABEL = I18NKey.constant_relevant_team;//"相关团队";
    public static final String RELEVANT_TEAM_API_NAME = "relevant_team";

    public static String getRelevantTeamLabel() {
        return I18N.text(RELEVANT_TEAM_LABEL);
    }


    private static final String TEAM_MEMBER_EMPLOYEE_LABEL = I18NKey.constant_team_member;//"成员员工";
    public static final String TEAM_MEMBER_EMPLOYEE_API_NAME = "teamMemberEmployee";

    public static String getTeamMemberEmployeeLabel() {
        return I18N.text(TEAM_MEMBER_EMPLOYEE_LABEL);
    }

    private static final String TEAM_MEMBER_ROLE_LABEL = I18NKey.constant_team_member_role;//"成员角色";
    public static final String TEAM_MEMBER_ROLE_API_NAME = "teamMemberRole";

    public static String getTeamMemberRoleLabel() {
        return I18N.text(TEAM_MEMBER_ROLE_LABEL);
    }

    private static final String TEAM_MEMBER_PERMISSION_TYPE_LABEL = I18NKey.constant_team_member_permission;//"成员权限类型";
    public static final String TEAM_MEMBER_PERMISSION_TYPE_API_NAME = "teamMemberPermissionType";

    public static String getTeamMemberPermissionTypeLabel() {
        return I18N.text(TEAM_MEMBER_PERMISSION_TYPE_LABEL);
    }

    private static final String LOCK_RULE_LABEL = I18NKey.constant_lock_rule;//"锁定规则";
    public static final String LOCK_RULE_API_NAME = "lock_rule";
    public static final String LOCK_RULE_DEFAULT_API_NAME = "default_lock_rule";

    public static String getLockRuleLabel() {
        return I18N.text(LOCK_RULE_LABEL);
    }

    private static final String LOCK_STATUS_LABEL = I18NKey.constant_lock_status;//"锁定状态";
    public static final String LOCK_STATUS_API_NAME = "lock_status";
    public static final String LOCK_STATUS_VALUE_UNLOCK = "0";
    public static final String LOCK_STATUS_VALUE_LOCK = "1";

    public static String getLockStatusLabel() {
        return I18N.text(LOCK_STATUS_LABEL);
    }

    private static final String LOCK_USER_LABEL = I18NKey.constant_lock_user; //"加锁人";
    public static final String LOCK_USER_API_NAME = "lock_user";

    public static String getLockUserLabel() {
        return I18N.text(LOCK_USER_LABEL);
    }

    private static final String LIFE_STATUS_LABEL = I18NKey.constant_life_status;//"生命状态";
    public static final String LIFE_STATUS_API_NAME = "life_status";
    private static final String LIFE_STATUS_BEFORE_INVALID_LABEL = "作废前生命状态"; // ignoreI18n

    public static String getLifeStatusBeforeInvalidLabel() {
        return I18NExt.getOrDefault(I18NKey.LIFE_STATUS_BEFORE_INVALID_LABEL, LIFE_STATUS_BEFORE_INVALID_LABEL);
    }

    public static final String LIFE_STATUS_BEFORE_INVALID_API_NAME = "life_status_before_invalid";

    public static String getLifeStatusLabel() {
        return I18N.text(LIFE_STATUS_LABEL);
    }

    public static final String LIFE_STATUS_VALUE_INEFFECTIVE = "ineffective";
    public static final String LIFE_STATUS_VALUE_INEFFECTIVE_LABEL = "未生效"; // ignoreI18n

    public static final String LIFE_STATUS_VALUE_UNDER_REVIEW = "under_review";
    public static final String LIFE_STATUS_VALUE_UNDER_REVIEW_LABEL = "审核中"; // ignoreI18n

    public static final String LIFE_STATUS_VALUE_NORMAL = "normal";
    public static final String LIFE_STATUS_VALUE_NORMAL_LABEL = "正常"; // ignoreI18n

    public static final String LIFE_STATUS_VALUE_IN_CHANGE = "in_change";
    public static final String LIFE_STATUS_VALUE_IN_CHANGE_LABEL = "变更中"; // ignoreI18n

    public static final String LIFE_STATUS_VALUE_INVALID = "invalid";
    public static final String LIFE_STATUS_VALUE_INVALID_LABEL = "作废"; // ignoreI18n

    /**
     * 业务场景常数
     */
    //定义n种有button的业务场景
//    public static final String APP_SCENE_UDOBJ_DETAIL = "ud_obj_detail";
//    public static final String APP_SCENE_UDOBJ_DETAIL_REF = "ud_obj_detail_ref";
//    public static final String APP_SCENE_UDOBJ_DETAIL_REF_MASTER = "ud_obj_detail_ref_master";
//    public static final String APP_SCENE_UDOBJ_RELEVANT_TEAM = "ud_obj_relevant_team";
//    public static final String APP_SCENE_UDOBJ_SALE_RECORD = "ud_obj_sale_record";
//    public static final String APP_SCENE_UDOBJ_DATA_LIST_HEADER = "ud_obj_data_list_header";
//    public static final String APP_SCENE_UDOBJ_MOBILE_LIST = "ud_obj_mobile_list";
//
//    public static final String APP_SCENE_OLDOBJ_DETAIL = "old_obj_detail";
//    public static final String APP_SCENE_OLDOBJ_DETAIL_REF = "old_obj_detail_ref";

    //自定义对象，新增字段限制
    public static final Integer CUSTOM_FIELD_TOTAL_COUNT = 500;
    public static final Integer CUSTOM_REF_FIELD_COUNT = 10;
    public static final Integer CUSTOM_CAlCULATE_FIELD_COUNT = 40;
    public static final Integer CUSTOM_IMAGE_FIELD_COUNT = 20;
    public static final Integer CUSTOM_FILE_FIELD_COUNT = 50;
    public static final Integer CUSTOM_AUTO_NUMBER_FIELD_COUNT = 10;

    public static final String SUPPORT_ACTION_SOURCE_TYPE_JAVA_SPRING = "java_spring";
    //自定义对象支持的所有CustomAction操作
    public static final String SUPPORT_ACTION_CLASS_NAME_EXT = "CustomAction";
    public static final List<String> SUPPORT_ACTION_CODE = Lists.newArrayList(
            CrmActionEnum.CHANGE_OWNER.getActionCode(), CrmActionEnum.ADD_TEAM_MEMBER.getActionCode(),
            CrmActionEnum.EDIT_TEAM_MEMBER.getActionCode(), CrmActionEnum.DELETE_TEAM_MEMBER.getActionCode(),
            CrmActionEnum.LOCK.getActionCode(), CrmActionEnum.UNLOCK.getActionCode()
    );

    //定义两种有button的页面场景
    public static final String APP_SCENE_DETAIL = "detail";
//    public static final String APP_SCENE_DETAIL_REF = "detail_ref";

    public static final String RENDER_TYPE_EMPLOYEE_NEST = "employee_nest";


    //定义场景和操作的map
//    public static Map<String, List<String>> APP_SCENE_DEFAULT_ACTION_MAP = Maps.newHashMap();
//    public static Map<String, List<String>> APP_SCENE_CUSTOM_ACTION_MAP = Maps.newHashMap();
//    public static Map<String, List<String>> APP_SCENE_COORDINATION_ACTION_MAP = Maps.newHashMap();
//
//    static {
//        //自定义对象的详情页的操作:编辑、作废、更换负责人、发起流程、打印、锁定、解锁
//        APP_SCENE_COORDINATION_ACTION_MAP.put(APP_SCENE_UDOBJ_DETAIL, Lists.newArrayList(
//                CrmActionEnum.SALE_RECORD.getActionCode(),
//                CrmActionEnum.DIAL.getActionCode(),
//                CrmActionEnum.SEND_MAIL.getActionCode(),
//                CrmActionEnum.DISCUSS.getActionCode(),
//                CrmActionEnum.SCHEDULE.getActionCode(),
//                CrmActionEnum.REMIND.getActionCode()));
//        APP_SCENE_DEFAULT_ACTION_MAP.put(APP_SCENE_UDOBJ_DETAIL, Lists.newArrayList(CrmActionEnum.UPDATE.getActionCode(), CrmActionEnum.INVALID.getActionCode()));
//        APP_SCENE_CUSTOM_ACTION_MAP.put(APP_SCENE_UDOBJ_DETAIL, Lists.newArrayList(CrmActionEnum.CHANGE_OWNER.getActionCode(),
//                CrmActionEnum.START_BPM.getActionCode(), CrmActionEnum.PRINT.getActionCode(), CrmActionEnum.LOCK.getActionCode(),
//                CrmActionEnum.UNLOCK.getActionCode()));
//        //自定义对象的详情页的关联对象的操作:新建、智能表单、关联、解除关联
//        APP_SCENE_DEFAULT_ACTION_MAP.put(APP_SCENE_UDOBJ_DETAIL_REF, Lists.newArrayList(CrmActionEnum.CREATE.getActionCode()));
//        APP_SCENE_CUSTOM_ACTION_MAP.put(APP_SCENE_UDOBJ_DETAIL_REF, Lists.newArrayList(CrmActionEnum.INTELLIGENTFORM.getActionCode(), CrmActionEnum.BULK_RELATE.getActionCode(), CrmActionEnum.BULK_DISRELATE.getActionCode()));
//        //自定义对象相关团队的操作：
//        APP_SCENE_DEFAULT_ACTION_MAP.put(APP_SCENE_UDOBJ_RELEVANT_TEAM, Lists.newArrayList());
//        APP_SCENE_CUSTOM_ACTION_MAP.put(APP_SCENE_UDOBJ_RELEVANT_TEAM, Lists.newArrayList(CrmActionEnum.ADD_TEAM_MEMBER.getActionCode(), CrmActionEnum.EDIT_TEAM_MEMBER.getActionCode(), CrmActionEnum.DELETE_TEAM_MEMBER.getActionCode()));
//        //自定义对象销售记录的按钮：
//        APP_SCENE_DEFAULT_ACTION_MAP.put(APP_SCENE_UDOBJ_SALE_RECORD, Lists.newArrayList());
//        APP_SCENE_CUSTOM_ACTION_MAP.put(APP_SCENE_UDOBJ_SALE_RECORD, Lists.newArrayList(CrmActionEnum.ADD_EVENT.getActionCode()));
//        //自定义对象getDataHeader的按钮：
//        APP_SCENE_DEFAULT_ACTION_MAP.put(APP_SCENE_UDOBJ_DATA_LIST_HEADER, Lists.newArrayList(CrmActionEnum.BATCH_IMPORT.getActionCode(), CrmActionEnum.BATCH_EXPORT.getActionCode()));
//        APP_SCENE_CUSTOM_ACTION_MAP.put(APP_SCENE_UDOBJ_DATA_LIST_HEADER, Lists.newArrayList());
//        //自定义对象手机端列表页layout的按钮：
//        APP_SCENE_DEFAULT_ACTION_MAP.put(APP_SCENE_UDOBJ_MOBILE_LIST, Lists.newArrayList(CrmActionEnum.CREATE.getActionCode()));
//        APP_SCENE_CUSTOM_ACTION_MAP.put(APP_SCENE_UDOBJ_MOBILE_LIST, Lists.newArrayList(CrmActionEnum.INTELLIGENTFORM.getActionCode()));
//        //老对象的详情页的操作:// TODO: 17/2/21  老对象的详情页的操作 可能需要从.net端获得。
//        APP_SCENE_DEFAULT_ACTION_MAP.put(APP_SCENE_OLDOBJ_DETAIL, Lists.newArrayList(CrmActionEnum.UPDATE.getActionCode(), CrmActionEnum.INVALID.getActionCode()));
//        APP_SCENE_CUSTOM_ACTION_MAP.put(APP_SCENE_OLDOBJ_DETAIL, Lists.newArrayList(CrmActionEnum.CHANGE_OWNER.getActionCode()));
//        //老对象的详情页的关联对象的操作:新建、智能表单、关联、解除关联
//        APP_SCENE_DEFAULT_ACTION_MAP.put(APP_SCENE_OLDOBJ_DETAIL_REF, Lists.newArrayList(CrmActionEnum.CREATE.getActionCode()));
//        APP_SCENE_CUSTOM_ACTION_MAP.put(APP_SCENE_OLDOBJ_DETAIL_REF, Lists.newArrayList(CrmActionEnum.INTELLIGENTFORM.getActionCode(), CrmActionEnum.BULK_RELATE.getActionCode(), CrmActionEnum.BULK_DISRELATE.getActionCode()));
//    }


    public static final String LAYOUT_TYPE_EDIT = "edit";
    public static final String LAYOUT_TYPE_ADD = "add";
    public static final String LAYOUT_TYPE_LIST = "list";
    public static final String LAYOUT_TYPE_DETAIL = "detail";
    public static final String TARGET_DISPLAY_NAME = "target_display_name";

    /**
     * 加锁解锁的策略枚举值 是否联动处理从对象 是否不处理从对象
     */
    public enum LOCK_STRATEGY {

        NOT_CASCADE_DETAIL_OBJ("0"), CASCADE_DETAIL_OBJ("1");
        private String value;

        LOCK_STRATEGY(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public int getIntValue() {
            return Integer.parseInt(value);
        }
    }

    /**
     * 更换负责人的策略枚举值 将原负责人从团队成员中移除或者是更改原负责人为普通成员
     */
    public enum CHANGE_OWNER_STRATEGY {
        REMOVE_ORIGINAL_OWNER_FROM_RELEVANT_TEAM("1"), SET_ORIGINAL_OWNER_TO_NORMAL_MEMBER("2");
        private String value;

        CHANGE_OWNER_STRATEGY(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}
