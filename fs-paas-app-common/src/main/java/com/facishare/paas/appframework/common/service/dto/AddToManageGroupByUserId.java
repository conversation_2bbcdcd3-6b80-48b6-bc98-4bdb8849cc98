package com.facishare.paas.appframework.common.service.dto;

import com.facishare.paas.appframework.core.rest.InnerAPIResult;
import lombok.Builder;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/7/13
 */
public interface AddToManageGroupByUserId {

    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String userId;
        private String parentApiName;
        private String appId;
        private String type;
        private String apiName;
    }

    class RestResult extends InnerAPIResult<Result> {
    }

    class Result {

    }
}
