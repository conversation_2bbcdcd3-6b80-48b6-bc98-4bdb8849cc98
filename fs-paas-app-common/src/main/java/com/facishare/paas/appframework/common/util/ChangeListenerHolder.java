package com.facishare.paas.appframework.common.util;

import com.facishare.paas.appframework.common.service.dto.SendMsgBySession;
import com.facishare.paas.appframework.common.service.proxy.QinXinFlowMessageProxy;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.github.autoconf.api.IChangeListener;
import com.github.autoconf.api.IConfig;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * Created by zhaooju on 2025/3/2
 */
@Slf4j
@Component
@DependsOn(value = {"qinXinFlowMessageProxy", "redissonClient"})
public final class ChangeListenerHolder implements ApplicationContextAware {

    public static final String CONFIG_NOTIFY_DEFAULT_SESSION_ID = "8e96b84c295a4f9a8f75b59b92d0efc4";
    private static QinXinFlowMessageProxy qinXinFlowMessageProxy;
    private static RedissonClient redissonClient;

    public static final String APP_NAME = ConfigHelper.getProcessInfo().getAppName();
    public static final String SERVICE_IP = ConfigHelper.getProcessInfo().getIp();
    public static final String PROFILE = ConfigHelper.getProcessInfo().getProfile();

    /**
     * 创建一个 IChangeListener 实例，只需要实现核心的变更处理逻辑
     *
     * @param handler 处理变更的函数式接口
     * @return IChangeListener 实例
     */
    public static IChangeListener create(ChangeHandler handler) {
        return new IChangeListener() {
            private String configName;
            private boolean loadFailFlag;

            @Override
            public void changed(IConfig config) {
                configName = config.getName();
                if (log.isDebugEnabled()) {
                    log.debug("start load config:{}. content: {}", configName, config.getString());
                }
                handler.handle(config);
                log.warn("success load config:{}", configName);
                if (!loadFailFlag) {
                    return;
                }
                asyncSendMessage(ConfigChangeType.RECOVER, null);
            }

            @Override
            public void onException(Exception e) {
                asyncSendMessage(ConfigChangeType.FAIL, e.getMessage());
            }

            private void asyncSendMessage(ConfigChangeType type, String message) {
                loadFailFlag = ConfigChangeType.FAIL == type;
                try {
                    ParallelUtils.createBackgroundTask()
                            .submit(() -> {
                                ConfigMessage configMessage = ConfigMessage.of(configName, message, type);
                                String lockKey = configMessage.lockKey();
                                if (needSendMessage(lockKey)) {
                                    sendMsg(configMessage.message(), getNotifySessionId());
                                }
                            }).run();
                } catch (Exception e) {
                    log.warn("asyncSendMessage fail! config:{}", configName, e);
                }

            }
        };
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        try {
            qinXinFlowMessageProxy = applicationContext.getBean("qinXinFlowMessageProxy", QinXinFlowMessageProxy.class);
            redissonClient = applicationContext.getBean("redissonClient", RedissonClient.class);
        } catch (BeansException e) {
            log.error("get QinXinFlowMessageProxy bean fail!", e);
            throw new IllegalStateException("Failed to initialize ChangeListenerHolder", e);
        }
    }

    private static String getNotifySessionId() {
        String defaultSessionId = AppFrameworkConfig.getLoadConfigFailNotifySessionId();
        if (!Strings.isNullOrEmpty(defaultSessionId)) {
            return defaultSessionId;
        }
        return CONFIG_NOTIFY_DEFAULT_SESSION_ID;
    }

    private static void sendMsg(String msg, String sessionId) {
        if (Objects.isNull(qinXinFlowMessageProxy)) {
            log.warn("qinXinFlowMessageProxy is null!");
            return;
        }
        SendMsgBySession.Arg arg = SendMsgBySession.Arg.builder()
                .content(msg)
                .senderId("E.fs.7576")
                .sessionId(sessionId)
                .build();
        User user = User.systemUser("1");
        Map<String, String> headers = RestUtils.buildFlowHeaders(user);
        try {
            qinXinFlowMessageProxy.sendMsgBySession(arg, headers);
        } catch (Exception e) {
            log.warn("sendMsgBySession fail! msg:{}", msg, e);
        }
    }

    public static boolean needSendMessage(String key) {
        if (Objects.isNull(redissonClient)) {
            log.warn("redissonClient is null!");
            return true;
        }
        try {
            RLock lock = getLock(key);
            return lock.tryLock(0, 60, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.warn("tryLock fail, key:{}", key, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.warn("tryLock fail, key:{}", key, e);
        }
        return true;
    }

    public static RLock getLock(String key) {
        return redissonClient.getLock(key);
    }

    /**
     * 变更处理的函数式接口
     */
    @FunctionalInterface
    public interface ChangeHandler {
        void handle(IConfig config);
    }

    @Data
    @AllArgsConstructor
    public static class ConfigMessage {
        private String configName;
        private String appName;
        private String message;
        private String serviceIp;
        private String profile;
        private ConfigChangeType changeType;

        public static ConfigMessage of(String configName, String message, ConfigChangeType changeType) {
            return new ConfigMessage(configName, APP_NAME, message, SERVICE_IP, PROFILE, changeType);
        }

        public String message() {
            if (changeType == ConfigChangeType.RECOVER) {
                return recoverMessage();
            }
            return failMessage();
        }

        public String lockKey() {
            if (changeType == ConfigChangeType.RECOVER) {
                return recoverLockKey();
            }
            return failLockKey();
        }

        private String failMessage() {
            return String.format("Config loading failed! appName: %s, profile: %s, serviceIp: %s, config: %s, Reason: %s",
                    appName, profile, serviceIp, configName, message);
        }

        private String recoverMessage() {
            return String.format("Config loading recovered! appName: %s, config: %s", appName, configName);
        }

        private String failLockKey() {
            return String.format("paas_config_lock_fail_%s_%s", appName, configName);
        }

        private String recoverLockKey() {
            return String.format("paas_config_lock_recover_%s_%s", appName, configName);
        }
    }

    private enum ConfigChangeType {
        RECOVER, FAIL
    }
}
