package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.FindTodoCount;
import com.facishare.paas.appframework.common.service.dto.FindTodoList;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

@Slf4j
@Service
public class MessageBoxServiceImpl implements MessageBoxService {
    @Autowired
    private MessageBoxServiceProxy messageBoxServiceProxy;

    @Override
    public FindTodoCount.Result findTodoCount(User user, FindTodoCount.Arg arg) {
        Map<String, String> headers = RestUtils.buildHeaders(user);
        return messageBoxServiceProxy.findTodoCount(headers, arg);
    }

    @Override
    public FindTodoList.Result findTodoList(User user, FindTodoList.Arg arg) {
        Map<String, String> headers = RestUtils.buildHeaders(user);
        return messageBoxServiceProxy.findTodoList(headers, arg);
    }
}
