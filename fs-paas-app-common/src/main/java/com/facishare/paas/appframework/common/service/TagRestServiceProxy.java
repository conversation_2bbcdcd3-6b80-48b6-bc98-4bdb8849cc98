package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.FindAllTags;
import com.facishare.rest.core.annotation.GET;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.QueryParamsMap;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * 标签服务接口
 *
 * <AUTHOR>
 * @date 2020/2/19 6:07 下午
 */
@RestResource(value = "TAG", desc = "CRM Rest API Call", contentType = "application/json",
        codec = "com.facishare.paas.appframework.metadata.util.CRMRestServiceCodec")
public interface TagRestServiceProxy {
    @GET(value = "/userMarketingTag/searchTags")
    FindAllTags.Result findAllTags(@HeaderMap Map<String, String> headers,
                                   @QueryParamsMap Map<String, String> queryParaMap);

}
