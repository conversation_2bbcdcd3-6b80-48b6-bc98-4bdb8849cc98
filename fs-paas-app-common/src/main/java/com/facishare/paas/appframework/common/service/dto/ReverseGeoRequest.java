package com.facishare.paas.appframework.common.service.dto;

import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 逆地址解析请求参数
 */
@Data
@NoArgsConstructor
public class ReverseGeoRequest {
    /**
     * 经度
     */
    @NotNull
    private Double longitude;

    /**
     * 纬度
     */
    @NotNull
    private Double latitude;

    /**
     * 是否启用缓存
     */
    private Boolean cache;

    /**
     * 是否返回POI列表
     */
    private Boolean returnPois;

    /**
     * 语言
     */
    private String language;

    /**
     * 业务名称
     */
    private String bizName;

    /**
     * 平台
     */
    private String platform;
} 