package com.facishare.paas.appframework.common.service.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import java.util.List;

/**
 * Create by baoxinxue
 */

public interface QueryUserExtInfoByIds {
    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String appId;
        private String userId;
        @Singular
        @SerializedName("idList")
        private List<String> ids;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<UserInfoExt> result;
        private Boolean success = false;

        public boolean isSuccess() {
            return Boolean.TRUE.equals(success);
        }
    }
}
