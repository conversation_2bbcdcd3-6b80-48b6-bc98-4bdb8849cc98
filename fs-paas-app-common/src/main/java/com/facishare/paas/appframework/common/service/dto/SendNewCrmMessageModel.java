package com.facishare.paas.appframework.common.service.dto;

import com.facishare.paas.appframework.common.service.CRMNotificationService;
import com.facishare.paas.appframework.common.service.CRMNotificationServiceImpl;
import com.facishare.paas.appframework.common.service.model.KeyValueItem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

public interface SendNewCrmMessageModel {
    @Data
    @Builder
    class RemindRecordItem {
        String uuid;
        int ei;
        SendNewCrmMessageModel.Arg remindRecordItem;
    }

    @Data
    @Builder
    class Arg {
        private String sourceId;
        private String senderId;
        private Integer type;
        private Set<Integer> receiverIDs;
        private List<DepartmentReceiver> departments;
        private boolean remindSender;
        private String title;
        private InternationalItem titleInfo;
        private String fullContent;
        private InternationalItem fullContentInfo;
        private int urlType;
        private Map<String, String> urlParameter;
        private String appId;
        private String lastSummary;
        private InternationalItem lastSummaryInfo;
        private String innerPlatformWebUrl;
        private String innerPlatformMobileUrl;
        private String outPlatformUrl;
        // 860 prm 支持外部通道（公众号）CRM 通知增加参数，wiki：https://wiki.firstshare.cn/pages/viewpage.action?pageId=131808626
        private List<KeyValueItem> bodyForm;
        private List<String> extraChannelList;
        private Map<String, Object> extraDataMap;
        private Map<String, List<Map<String, String>>> templateIdKeyListMap;
    }

    @Data
    class Result {
        private String message;
        private String code;
        private ResultData data;

        public boolean isSuccess() {
            return data != null && data.getCode() == 200;
        }
    }

    @Data
    class ResultData {
        private int code;
        private String msg;
        private String content;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class DepartmentReceiver {
        private int departmentId;
        private boolean includeLowDepartment;
    }

}
