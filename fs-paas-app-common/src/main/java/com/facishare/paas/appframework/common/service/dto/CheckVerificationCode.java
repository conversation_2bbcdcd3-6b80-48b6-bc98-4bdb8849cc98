package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * wiki: http://git.firstshare.cn/Infrastructure/fs-register/wikis/validatecode
 * Created by fengjy in 2020/2/4 16:35
 */
public interface CheckVerificationCode {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String phone;
        String smsCode;
        String areaCode;

        String fieldApiName;
        String describeApiName;

        //默认返回中国区号 +86
        public String getAreaCode() {
            return StringUtils.isEmpty(areaCode) ? "+86" : areaCode;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {
        boolean success;
        String errorMessage;
    }
}
