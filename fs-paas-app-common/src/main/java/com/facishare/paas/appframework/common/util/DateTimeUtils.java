package com.facishare.paas.appframework.common.util;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.timezone.DateTimeFormat;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.facishare.paas.timezone.config.TimeZoneConfig;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

/**
 * Created by liyiguang on 2017/9/20.
 */
@Slf4j
public abstract class DateTimeUtils {

    // TODO: 2022/10/18 加一个判断多时区无关属性返回时区的方法

    private static LocalDateTime getLocalDateTime(String time) {
        int colonCount = StringUtils.countMatches(time, ":");
        if (colonCount == 3) {
            return LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss:SSS"));
        } else if (colonCount == 2) {
            return LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } else {
            return LocalDateTime.parse(time, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm"));
        }
    }

    public static long parseISOLocateDateTime(String time, String fieldType) {
        ZoneId timeZone = TimeZoneContextHolder.getUserTimeZone();
        return parseISOLocateDateTime(time, fieldType, timeZone);
    }

    public static long parseISOLocateDateTime(String time, String fieldType, ZoneId zoneId) {
        try {
            if (NumberUtils.isCreatable(time)) {
                return new BigDecimal(time).longValue();
            }
            if (!IFieldType.DATE_TIME.equals(fieldType) && time.contains("-") && time.contains(":")) {
                fieldType = IFieldType.DATE_TIME;
            }
            if (isGrayTimeZone()) {
                String type = fieldType;
                return DateTimeFormat.getOrElseThrow(type, () -> new ValidateException(I18N.text(I18NKey.unsupport_date_type, type)))
                        .convertToTimestamp(time, zoneId);
            }

            switch (fieldType) {
                case IFieldType.TIME:
                    LocalTime localTime = LocalTime.parse(time, DateTimeFormatter.ISO_TIME);
                    LocalDateTime localDateTime = localTime.atDate(LocalDate.of(1970, 1, 1));
                    return ZonedDateTime.of(localDateTime, ZoneId.systemDefault()).toInstant().toEpochMilli();
                case IFieldType.DATE:
                    LocalDate date;
                    if (time.contains("T")) {
                        date = LocalDate.parse(time, DateTimeFormatter.ISO_DATE_TIME);
                    } else {
                        date = LocalDate.parse(time, DateTimeFormatter.ISO_DATE);
                    }
                    return date.atStartOfDay(ZoneId.systemDefault()).toEpochSecond() * 1000;
                case IFieldType.DATE_TIME:
                    LocalDateTime dateTime;
                    if (time.contains("T")) {
                        dateTime = LocalDateTime.parse(time, DateTimeFormatter.ISO_DATE_TIME);
                    } else {
                        dateTime = getLocalDateTime(time);
                    }
                    return ZonedDateTime.of(dateTime, ZoneId.systemDefault()).toInstant().toEpochMilli();
                default:
                    throw new ValidateException(I18N.text(I18NKey.unsupport_date_type, fieldType));
            }
        } catch (Exception e) {
            log.warn("parseISOLocateDateTime error,time:{},fieldType:{}", time, fieldType, e);
            throw new ValidateException(I18N.text(I18NKey.datetime_convert_abnormal, time));
        }
    }

    public static boolean isGrayTimeZone() {
        return Optional.ofNullable(RequestContextManager.getContext())
                .map(RequestContext::getTenantId)
                .map(TimeZoneConfig.INSTANCE::isGray)
                .orElse(false);
    }

}
