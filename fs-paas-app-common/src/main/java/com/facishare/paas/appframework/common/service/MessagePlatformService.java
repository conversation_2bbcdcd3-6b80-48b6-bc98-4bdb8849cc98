package com.facishare.paas.appframework.common.service;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.service.model.CRMNotification;
import com.facishare.paas.appframework.common.service.model.NewCrmNotification;
import com.facishare.paas.appframework.core.model.User;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public interface MessagePlatformService {
    void sendTextMessage(User user, CRMNotification arg);

    void sendPlatFormMessage(User user, NewCrmNotification arg);

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class ReceiverChannelData {
        String appId;
        String subAppId;
        String objectApiName;
        String objectId;
        Integer ei;
        Integer env;
        Integer platformType;
        String outTenantId;

        public String toJSONString() {
            return JSON.toJSONString(this);
        }
    }

    enum ReceiverChannelType {
        NOTIFICATION(1, "应用通知"),// ignoreI18n
        BIZ_GROUP(2, "业务群"),// ignoreI18n
        SERVICE_ACCOUNT(3, "服务号"),// ignoreI18n
        EXTERNAL_PLATFORM(4, "外部平台"),// ignoreI18n
        FXIAOKE_CS(5, "纷享客服"),// ignoreI18n
        EXTERNAL_SERVICE(6, "外部服务");// ignoreI18n

        private int value;
        private String label;

        ReceiverChannelType(int value, String label) {
            this.value = value;
            this.label = label;
        }

        public int getValue() {
            return value;
        }
    }

    enum GenerateUrlType {
        NO_GENERATE(0, "标识无需消息平台生成"),// ignoreI18n
        OBJECT_DETAIL(1, "标识对象详情页Url"),// ignoreI18n
        FLOW_DETAIL(2, "标识业务流详情页Url，objectId 未业务流实例Id");// ignoreI18n

        private int value;
        private String label;

        GenerateUrlType(int value, String label) {
            this.value = value;
            this.label = label;
        }

        public int getValue() {
            return value;
        }
    }
}
