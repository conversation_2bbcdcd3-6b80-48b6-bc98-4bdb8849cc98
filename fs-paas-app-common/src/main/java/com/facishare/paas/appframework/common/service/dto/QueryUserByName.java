package com.facishare.paas.appframework.common.service.dto;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

public interface QueryUserByName {
    @Data
    @Builder
    class Arg {
        private OrgContext context;
        private List<String> nicknames;
        private Integer userStatus;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<UserInfo> result = new ArrayList<>();
        private boolean success;
    }
}
