package com.facishare.paas.appframework.common.service.model;

import com.google.common.collect.ImmutableList;
import lombok.Data;

import java.util.Collection;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/7/31
 */
@Data
public class ManageGroupContainer {
    private final boolean allSupport;
    private final List<ManageGroup> manageGroups;

    public ManageGroupContainer(boolean allSupport, Collection<ManageGroup> manageGroups) {
        this.allSupport = allSupport;
        this.manageGroups = ImmutableList.copyOf(manageGroups);
    }
}
