package com.facishare.paas.appframework.common.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

public interface SendTextMessage {

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        String uuid;
        int ei;
        int receiverChannelType;
        String receiverChannelData;
        Set<Integer> receiverIds;
        List<SendNewCrmMessageModel.DepartmentReceiver> departments;
        String title;
        InternationalItem titleInfo;
        String messageContent;
        InternationalItem messageContentInfo;
        String lastSummary;
        InternationalItem lastSummaryInfo;
        String innerPlatformWebUrl;
        String innerPlatformMobileUrl;
        String outPlatformUrl;
        String objectApiName;
        String objectId;
        int generateUrlType;
        Map<String, Object> extraDataMap;
        /**
         * 下游人员 ID 集合
         */
        Set<Integer> outEmployees;
    }

    @Data
    class Result {
        String code;
        String message;
        ResultData data;

        public boolean isSuccess() {
            return Objects.equals(code, "0");
        }

        @Data
        public static class ResultData {
            Integer code;
            String msg;
            String content;
        }
    }

}
