package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.QueryPhoneNumberInformation;
import com.facishare.rest.core.annotation.*;

import java.util.Map;

@RestResource(value = "PAAS-MOBILE-PATH-V2", desc = "手机号归属地查询", contentType = "application/json")
public interface PhoneNumberInfoV2Proxy {
    @GET(value = "/api/v2/mobile/batch-query", desc = "手机号归属地查询服务")
    QueryPhoneNumberInformation.CodeV2 queryBatchQueryPhoneNumberInformation(@QueryParam("mobiles") String mobiles, @QueryParam("language") String language);

    @GET(value = "/api/v2/mobile/query", desc = "手机号归属地查询服务")
    QueryPhoneNumberInformation.CodeV2Single queryPhoneNumberInformation(@QueryParam("mobile") String mobile,
                                                                         @QueryParam("language") String language,
                                                                         @QueryParam("municipalityIgnoreCity") String municipalityIgnoreCity);
}
