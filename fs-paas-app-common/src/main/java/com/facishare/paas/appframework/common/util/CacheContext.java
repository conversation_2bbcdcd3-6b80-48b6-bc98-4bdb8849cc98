package com.facishare.paas.appframework.common.util;

import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.google.common.collect.Maps;

import java.util.Map;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReadWriteLock;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * Created by zhouwr on 2023/4/13.
 */
public class CacheContext {

    private static final ThreadLocal<CacheContext> THREAD_LOCAL = ThreadLocal.withInitial(() -> new CacheContext());

    private final Map<String, Object> cacheData = Maps.newHashMap();

    private final ReadWriteLock lock = new ReentrantReadWriteLock();
    private final Lock readLock = lock.readLock();
    private final Lock writeLock = lock.writeLock();

    static {
        RequestContextManager.addContextRemoveListener(it -> clearContext());
    }

    public static CacheContext getContext() {
        return THREAD_LOCAL.get();
    }

    public static void setContext(CacheContext cacheContext) {
        THREAD_LOCAL.set(cacheContext);
    }

    public static void clearContext() {
        THREAD_LOCAL.remove();
    }

    public boolean containsCache(String key) {
        readLock.lock();
        try {
            return cacheData.containsKey(key);
        } finally {
            readLock.unlock();
        }
    }

    public <V> V getCache(String key) {
        readLock.lock();
        try {
            return (V) cacheData.get(key);
        } finally {
            readLock.unlock();
        }
    }

    public <V> V removeCache(String key) {
        writeLock.lock();
        try {
            return (V) cacheData.remove(key);
        } finally {
            writeLock.unlock();
        }
    }

    public void setCache(String key, Object value) {
        writeLock.lock();
        try {
            cacheData.put(key, value);
        } finally {
            writeLock.unlock();
        }
    }
}
