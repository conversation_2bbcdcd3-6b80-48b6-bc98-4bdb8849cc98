package com.facishare.paas.appframework.common.service.model;

import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/5/12
 * @Description :
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ObjectExtensionConfig {
	@Builder.Default
	Set<String> showModuleName = Sets.newHashSet("RelatedList", "WebDetail");
	// 字段的层级关系
	private List<ConfigItem> configItems;

	@Data
	@NoArgsConstructor
	@AllArgsConstructor
	@Builder
	public static class ConfigItem {
		private String pluginProvider;
		private List<String> methods;
		private Map<String, List<String>> moduleName;

		public static ConfigItem fromJson(String json) {
			return JacksonUtils.fromJson(json, ConfigItem.class);
		}
	}
}
