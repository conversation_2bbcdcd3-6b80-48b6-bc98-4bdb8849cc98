package com.facishare.paas.appframework.common.util;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;

/**
 * 预定义对象APIName Mapping
 * <p>
 * Created by l<PERSON>yiguang on 2017/8/16.
 */
public enum ObjectAPINameMapping {

    //AccountAddr("AccountAddrObj", "location", 39, false),
    AccountAtt("AccountAttObj", "attach", 24, false),
    //AccountFinInfo("AccountFinInfoObj", "invoice", 40, false),
    AccountCost("AccountCostObj", "cost", 26, false),
    AccountMainData("AccountMainDataObj", "AccountMainDataQuery", 0, true), //客户主数据
    KeywordServingPlan("KeywordServingPlanObj", "KeywordServingPlanObj", 0, true), //关键词投放计划
    TermServingLinesObj("TermServingLinesObj", "TermServingLinesObj", 0, true), //关键词投放明细
    Account("AccountObj", "customer", 2, true),
    Contact("ContactObj", "contact", 3, true),
    Contract("ContractObj", "contract", 16, true),
    HighSeas("HighSeasObj", "highseas", 18, true),
    InvoiceApplication("InvoiceApplicationObj", "tradebill", 9, true),
    Leads("LeadsObj", "salesclue", 1, true),
    LeadsPool("LeadsPoolObj", "salescluepool", 17, false),
    MarketingEvent("MarketingEventObj", "marketingevent", 20, true),
    Opportunity("OpportunityObj", "opportunity", 8, true),
    //Payment("PaymentObj", "tradepayment", 5),
    //Product("ProductObj", "product", 4),
    Refund("RefundObj", "traderefund", 6, true),
    ReturnedGoodsInvoice("ReturnedGoodsInvoiceObj", "returnorder", 12, true),
    ReturnedGoodsInvoiceProduct("ReturnedGoodsInvoiceProductObj", "returnproduct", 27, true),
    SaleAction("SaleActionObj", "saleaction", 7, false),
    SaleActionStage("SaleActionStageObj", "saleactionstage", 0, false),
    SalesOrder("SalesOrderObj", "customerorder", 11, true),
    SalesOrderProduct("SalesOrderProductObj", "orderproduct", 28, true),
    Visiting("VisitingObj", "visit", 13, false),
    BizQuery("BizQueryObj", "BizQuery", 0, false), //工商信息
    NotSFAObject("NotSFAObject", "NotSFAObject", 0, false);
    String apiName;
    String oldAPIName;
    int type;
    boolean isStandard;

    public String getApiName() {
        return apiName;
    }

    public String getOldAPIName() {
        return oldAPIName;
    }

    public int getType() {
        return type;
    }

    public boolean isStandard() {
        return isStandard;
    }

    ObjectAPINameMapping(String apiName, String oldAPIName, int type, boolean isStandard) {
        this.apiName = apiName;
        this.oldAPIName = oldAPIName;
        this.type = type;
        this.isStandard = isStandard;
    }

    private static final Map<String, ObjectAPINameMapping> apiNameMap = Maps.newHashMap();
    private static final Map<Integer, ObjectAPINameMapping> typeMap = Maps.newHashMap();
    private static final Map<String, ObjectAPINameMapping> oldAPINameMap = Maps.newHashMap();

    static {
        for (ObjectAPINameMapping mapping : ObjectAPINameMapping.values()) {
            apiNameMap.put(mapping.apiName, mapping);
            typeMap.put(mapping.type, mapping);
            oldAPINameMap.put(mapping.oldAPIName, mapping);
        }
    }

    public static String toOldAPIName(String apiName) {
        return apiNameMap.get(apiName).oldAPIName;
    }

    /**
     * 获取对象apiName
     *
     * @param oldAPIName sfa老对象的名称
     * @return 对象的apiname, 如果不是老对象返回 null
     */
    public static String toApiName(String oldAPIName) {
        ObjectAPINameMapping objectAPINameMapping = oldAPINameMap.get(oldAPIName.toLowerCase());
        return objectAPINameMapping == null ? null : objectAPINameMapping.apiName;
    }

    public static String toObjectType(String apiName) {
        //老对象转换为Type
        ObjectAPINameMapping mapping = apiNameMap.get(apiName);
        return mapping != null ? String.valueOf(mapping.type) : apiName; //自定对象没有type类型，直接返回APIName
    }

    public static int toIntObjectType(String apiName) {
        //老对象转换为Type
        ObjectAPINameMapping mapping = apiNameMap.get(apiName);
        return mapping != null ? mapping.type : 0;
    }

    public static boolean isSFAObject(String apiName) {
        ObjectAPINameMapping object = of(apiName);
        return object != NotSFAObject;
    }

    public static boolean isSFANotStandardObject(String apiName) {
        ObjectAPINameMapping object = of(apiName);
        return object != NotSFAObject && !object.isStandard();
    }

    public static ObjectAPINameMapping of(String apiName) {
        ObjectAPINameMapping ret = apiNameMap.get(apiName);
        if (ret == null) {
            return NotSFAObject;
        }
        return ret;
    }

    public static ObjectAPINameMapping of(int type) {
        ObjectAPINameMapping ret = typeMap.get(type);
        if (ret == null) {
            return NotSFAObject;
        }
        return ret;
    }

    public static boolean isAccountDetailObject(String apiName) {
        ObjectAPINameMapping ret = apiNameMap.get(apiName);
        return getAccountDetailObjects().contains(ret);
    }

    public static List<ObjectAPINameMapping> getAccountDetailObjects() {
        //return Lists.newArrayList(AccountAddr, AccountAtt, AccountFinInfo);
        return Lists.newArrayList(AccountAtt);
    }

    public static String getStatusFieldAPIName(String apiName) {
        //ObjectAPINameMapping object = of(apiName);
        switch (apiName) {
            case "AccountObj":
                return "account_status";
            case "ContactObj":
                return "contact_status";
            case "LeadsObj":
                return "leads_status";
//            case Payment:
//                return "payment_status";
//            case Product:
//                return "product_status";
            case "SalesOrderObj":
                return "order_status";
            default:
                return "status";
        }
    }

    public static String getModuleCode(String apiName) {
        if ("AccountObj".equals(apiName)) {
            return "account";
        }
        if ("ContactObj".equals(apiName)) {
            return "contact";
        }
        if ("ProductObj".equals(apiName)) {
            return "product";
        }
        if ("SalesOrderObj".equals(apiName)) {
            return "sales_order";
        }
        if ("PaymentObj".equals(apiName)) {
            return "payment";
        }
        if ("LeadsObj".equals(apiName)) {
            return "leads";
        }
        if ("InvoiceApplicationObj".equals(apiName)) {
            return "invoice_application";
        }
        if ("ContractObj".equals(apiName)) {
            return "contract";
        }
        if ("OpportunityObj".equals(apiName)) {
            return "opportunity";
        }
        if ("RefundObj".equals(apiName)) {
            return "refund";
        }
        if ("VisitingObj".equals(apiName)) {
            return "visiting";
        }
        if ("LeadsPoolObj".equals(apiName)) {
            return "leads_pool";
        }
        if ("ReturnedGoodsInvoiceObj".equals(apiName)) {
            return "returned_goods_invoice";
        }
        return null;
    }

    public static List<String> NOT_SUPPORT_OLD_OBJ = Lists.newArrayList(
            AccountAtt.getApiName(),
            AccountCost.getApiName(),
            HighSeas.getApiName(),
            LeadsPool.getApiName(),
            SaleAction.getApiName(),
            SaleActionStage.getApiName()
    );


    public static ObjectAPINameMapping getObjectAPINameMapping(String apiName) {
        return apiNameMap.get(apiName);
    }
}
