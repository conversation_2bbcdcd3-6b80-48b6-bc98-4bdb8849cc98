package com.facishare.paas.appframework.common.service.proxy;

import com.facishare.paas.appframework.common.service.dto.ImportView;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(
        value = "DataLoader",
        desc = "DataLoader服务", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface DataLoaderProxy {

    @POST(value = "/bulkimport/excelParse", desc = "excel模版解析")
    ImportView.Result parser(@Body ImportView.Arg arg, @HeaderMap Map<String, String> headers);

}
