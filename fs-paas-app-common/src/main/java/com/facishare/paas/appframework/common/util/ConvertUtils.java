package com.facishare.paas.appframework.common.util;

import com.alibaba.druid.util.StringUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.math.NumberUtils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/12/26 11:26 上午
 */
@Slf4j
public class ConvertUtils {


    public static List<String> batchConvertIntegerToString(Collection<Integer> ints) {
        if (null == ints || ints.size() == 0) {
            return new ArrayList<>();
        }
        return ints.stream().map(String::valueOf).collect(Collectors.toList());
    }

    public static List<Integer> batchConvertStringToInteger(Collection<String> strList) {
        if (null == strList || strList.size() == 0) {
            return new ArrayList<>();
        }
        return strList.stream().map(ConvertUtils::convertStringToInteger).collect(Collectors.toList());
    }

    /**
     * 将字符串转为整数
     */
    public static Integer convertStringToInteger(String str) {
        return StringUtils.stringToInteger(str);
    }

    /**
     * 将字符串列表转换为整数列表
     */
    public static List<Integer> convertStringCollectionToIntegerList(Collection<String> strList) {
        if (CollectionUtils.empty(strList)) {
            if (strList == null) {
                log.warn("strList is null");
            }
            return Lists.newArrayList();
        }
        return strList.stream()
                .filter(ConvertUtils::isEffectiveId)
                .map(ConvertUtils::convertStringToInteger).collect(Collectors.toList());
    }

    public static String integerToString(Integer i) {
        if (null == i) {
            return null;
        }
        return String.valueOf(i);
    }

    public static boolean isEffectiveId(String id) {
        return NumberUtils.isDigits(id) && !"0".equals(id);
    }

    public static String removeSpaceForPhoneNumber(Object mobile) {
        if (Objects.isNull(mobile)) {
            return "";
        }
        String mobileStr = String.valueOf(mobile);
        return mobileStr.replaceAll(" ","");
    }
}