package com.facishare.paas.appframework.common.util;

import java.util.concurrent.TimeUnit;

/**
 * Created by liyiguang on 2017/8/31.
 */
public abstract class ThreadUtils {
    public static void sleepSilently(int value, TimeUnit timeUnit) {
        try {
            timeUnit.sleep(value);
        } catch (InterruptedException ignore) {
            Thread.currentThread().interrupt();
        }
    }
}
