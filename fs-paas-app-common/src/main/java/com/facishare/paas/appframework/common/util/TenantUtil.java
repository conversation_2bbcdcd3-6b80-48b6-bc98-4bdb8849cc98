package com.facishare.paas.appframework.common.util;

import com.alibaba.fastjson.JSON;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.Range;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.TenantUtil.Tag.*;

@Slf4j
public class TenantUtil {

    public enum Tag {
        All("all_envs", ""),
        AUDIT_LOG_ALL("audit_log_all_envs", ""),
        CALC_CRITERIA("calc_criteria_envs", "calc_criteria_append_ei"),
        MAX_COUNT("max_count_envs", "max_count_append_ei"),
        OPTIONAL_FEATURES("optional_features_envs", "optional_features_append_ei"),
        CARD_TEMPLATES("card_templates_enves", "card_templates_append_ei"),
        FILTER_VALIDATE("filter_validate_envs", "filter_validate_append_ei"),
        SCENE_ORDER("scene_order_envs", "scene_order_append_ei");

        @Getter
        private final String configKey;
        @Getter
        private final String manualConfigKey;

        Tag(String configKey, String manualConfigKey) {
            this.configKey = configKey;
            this.manualConfigKey = manualConfigKey;
        }
    }

    private static final Map<String, List<TenantEnvInfo>> NEW_EI_ENV_FOR_TAG = Maps.newHashMap();
    private static final Map<String, ManualTenantInfo> MANUAL_EI_FOR_TAG = Maps.newHashMap();
    private static List<TenantEnvInfo> ALL_ENVS = Lists.newArrayList();
    private static List<TenantEnvInfo> AUDIT_LOG_ALL_ENVS = Lists.newArrayList();

    private static GrayRule NEED_CALC_CRITERIA;

    private static Map<String, Tag> MAP_TAG = Maps.newHashMap();

    static {
        ConfigFactory.getConfig("fs-paas-appframework-config", config -> {
            log.warn("TenantUtil:reload config fs-paas-appframework-config");

            String rawConfig = config.get("all_envs", "[]");
            String logRawConfig = config.get("audit_log_all_envs", "[]");
            ALL_ENVS = JSON.parseArray(rawConfig, TenantEnvInfo.class);
            AUDIT_LOG_ALL_ENVS = JSON.parseArray(logRawConfig, TenantEnvInfo.class);
            MAP_TAG = Arrays.stream(values()).collect(Collectors.toMap(Tag::getConfigKey, x -> x, (x1, x2) -> x1));

            for (Tag tag : Tag.values()) {
                NEW_EI_ENV_FOR_TAG.put(tag.getConfigKey(), JSON.parseArray(config.get(tag.getConfigKey(), "[]"), TenantEnvInfo.class));
                if (StringUtils.isNotEmpty(tag.getManualConfigKey())) {
                    MANUAL_EI_FOR_TAG.put(tag.getManualConfigKey(), ManualTenantInfo.of(config, tag.getManualConfigKey()));
                }
            }
            NEED_CALC_CRITERIA = new GrayRule(config.get("need_calc_criteria_ei", "deny"));
        });
    }

    /**
     * 新建计算字段时已存历史数据重算逻辑，不关心历史数据数量
     *
     * @param tenantId
     * @return
     */
    public static boolean needCalcCriteria(String tenantId) {
        return NEED_CALC_CRITERIA.isAllow(tenantId);
    }

    /**
     * 新建计算字段时已存历史数据重算逻辑
     *
     * @param tenantId
     * @return
     */
    public static boolean isCalcCriteria(String tenantId) {
        return needCalcCriteria(tenantId) || TenantUtil.isManualEiElseNewTenantForTag(tenantId, CALC_CRITERIA);
    }

    /**
     * 新建统计字段时统计对象最大数据量
     *
     * @param tenantId
     * @return
     */
    public static boolean isMaxCount(String tenantId) {
        return TenantUtil.isManualEiElseNewTenantForTag(tenantId, MAX_COUNT);
    }

    /**
     * 可选功能开关，校验企业是否在此指定企业之后
     *
     * @param tenantId
     * @return
     */
    public static boolean isOptionalFeatures(String tenantId) {
        return TenantUtil.isManualEiElseNewTenantForTag(tenantId, OPTIONAL_FEATURES);
    }

    public static boolean isCardTemplates(String tenantId) {
        return TenantUtil.isManualEiElseNewTenantForTag(tenantId, CARD_TEMPLATES);
    }

    public static boolean judgmentGrayByConfigKey(String tenantId, String configKey) {
        if (StringUtils.isBlank(configKey)) {
            return true;
        }
        Tag tag = MAP_TAG.get(configKey);
        if (Objects.isNull(tag)) {
            return false;
        }
        return isManualEiElseNewTenantForTag(tenantId, tag);
    }

    public static boolean isFilterValidate(String tenantId) {
        return TenantUtil.isManualEiElseNewTenantForTag(tenantId, FILTER_VALIDATE);
    }

    public static boolean isSceneOrder(String tenantId) {
        return TenantUtil.isManualEiElseNewTenantForTag(tenantId, SCENE_ORDER);
    }

    /**
     * 企业ID 是递增的，校验企业是否在制定企业之后创建
     *
     * @param tenantId 当前企业id
     */
    public static boolean isNewTenant(String tenantId) {
        return isNewTenant(tenantId, ALL_ENVS);
    }

    public static boolean isManualEiElseNewTenantForTag(String tenantId, Tag tag) {
        ManualTenantInfo manualTenantInfo = MANUAL_EI_FOR_TAG.get(tag.getManualConfigKey());
        if (Objects.nonNull(manualTenantInfo)) {
            if (manualTenantInfo.getNewTenant().isAllow(tenantId)) {//手动，该企业强制灰度
                return true;
            }
            if (manualTenantInfo.getOldTenant().isAllow(tenantId)) {//手动，该企业走源逻辑
                return false;
            }
        }
        return isNewTenant(tenantId, NEW_EI_ENV_FOR_TAG.getOrDefault(tag.getConfigKey(), Lists.newArrayList()));
    }


    public static boolean isNewAuditLogTenant(String tenantId) {
        return isNewTenant(tenantId, AUDIT_LOG_ALL_ENVS);
    }

    private static boolean isNewTenant(String tenantId, List<TenantEnvInfo> tenantEnvInfos) {
        if (ObjectUtils.isEmpty(tenantId)) {
            return false;
        }
        if (CollectionUtils.empty(tenantEnvInfos)) {
            return false;
        }
        long tenant = Long.parseLong(tenantId);
        for (TenantEnvInfo info : tenantEnvInfos) {
            if (Range.between(info.getMinEI(), info.getNowEI()).contains(tenant)) {
                return false;
            }
        }
        return true;
    }

    @Data
    public static class TenantEnvInfo {
        String env;
        long minEI;
        long nowEI;
    }

    @Data
    public static class ManualTenantInfo {
        GrayRule oldTenant;
        GrayRule newTenant;

        public ManualTenantInfo(GrayRule oldTenantRule, GrayRule newTenantRule) {
            this.oldTenant = oldTenantRule;
            this.newTenant = newTenantRule;
        }

        public static ManualTenantInfo of(IConfig config, String manualEiConfigKey) {
            GrayRule deny = new GrayRule("deny");
            Map<String, GrayRule> fsGrayRule = AppFrameworkConfig.getFsGrayRule(config, manualEiConfigKey);
            return new ManualTenantInfo(fsGrayRule.getOrDefault("oldTenant", deny), fsGrayRule.getOrDefault("newTenant", deny));
        }
    }


}
