package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.model.FileBatchPack;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * 文件批量打包代理接口
 * 
 * <AUTHOR>
 */
@RestResource(
    value = "FileBatchPack",
    desc = "文件批量打包接口", // ignoreI18n
    contentType = "application/json")
public interface FileBatchPackProxy {

  @POST(value = "/api/batch/pack", desc = "rest批量打包（结构）")
  FileBatchPack.Result batchPack(@HeaderMap Map<String, String> header, @Body FileBatchPack.Arg arg);

}
