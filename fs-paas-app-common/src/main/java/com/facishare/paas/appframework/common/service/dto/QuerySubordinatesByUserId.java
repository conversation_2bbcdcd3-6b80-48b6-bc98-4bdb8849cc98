package com.facishare.paas.appframework.common.service.dto;

import lombok.Builder;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * Created by renlb on 2018/4/9.
 */
public interface QuerySubordinatesByUserId {

    @Data
    @Builder
    class Arg{
        private OrgContext context;
        private String userId;
        private Integer subordinateUserStatus;
        private boolean cascade;
    }

    @Data
    class Result{
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private List<UserInfo> result = new ArrayList<>();
        private boolean success;
    }
}
