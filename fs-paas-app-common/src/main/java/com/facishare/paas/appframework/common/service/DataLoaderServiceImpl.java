package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.ImportView;
import com.facishare.paas.appframework.common.service.proxy.DataLoaderProxy;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
@Slf4j
public class DataLoaderServiceImpl implements DataLoaderService {

    @Autowired
    private DataLoaderProxy dataLoaderProxy;

    @Override
    public ImportView.Result parse(User user, String filePath, Boolean multiSheets) {
        ImportView.Arg arg = new ImportView.Arg();
        arg.setFilePath(filePath);
        arg.setMultiSheets(multiSheets);

        Map<String, String> headers = RestUtils.buildHeaders(user);
        try {
            return dataLoaderProxy.parser(arg, headers);
        } catch (Exception e) {
            log.error("DataLoaderProxy parse excel error,user:{},filePath:{},multiSheets:{}", user, filePath, multiSheets);
            return ImportView.Result.builder().build();
        }
    }
}
