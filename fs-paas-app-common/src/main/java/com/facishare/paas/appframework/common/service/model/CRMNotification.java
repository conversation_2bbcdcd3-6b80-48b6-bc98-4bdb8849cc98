package com.facishare.paas.appframework.common.service.model;

import lombok.Builder;
import lombok.Data;

import java.util.Set;

/**
 * Created by liyigua<PERSON> on 2018/3/28.
 */
@Data
@Builder
public class CRMNotification {

    public static final int CUSTOM_REMIND_RECORD_TYPE = 92;
    public static final int CUSTOM_REMIND_DETAIL_RECORD_TYPE = 115;
    public static final int CUSTOM_REMIND_JOB_SCHEDULE_TYPE = 118;
    public static final int PAY_CALLBACK_REMIND_TYPE = 71;

    private String sender;
    private String content;
    private Integer remindRecordType;
    private String content2Id;
    private Set<Integer> receiverIds;
    private String title;
    private String dataId;
    private String fixContent2ID;
    private Integer outEI;
    private String appId;
    private String objectApiName;

    public int getRemindRecordTypeWithDefaultValue() {
        if (remindRecordType == null) {
            return CUSTOM_REMIND_RECORD_TYPE;
        }
        return remindRecordType;
    }
}
