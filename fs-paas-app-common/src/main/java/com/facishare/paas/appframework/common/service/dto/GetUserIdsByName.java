package com.facishare.paas.appframework.common.service.dto;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018/7/30 15:14
 */
public interface GetUserIdsByName {

    @Data
    @Builder
    class Arg {
        private String tenantId;
        private String appId;
        private String userId;
        private String name;
        private Integer status;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private Set<String> result;
        private boolean success;
    }
}
