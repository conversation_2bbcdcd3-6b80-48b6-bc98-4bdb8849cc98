package com.facishare.paas.appframework.common.service;

import com.facishare.paas.appframework.common.service.dto.*;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "PlatServiceProxy",
        desc = "plat rest api",
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC")
public interface PlatServiceProxy {
    @POST(value = "/enterpriseSettings/api/getOpenOrganization", desc = "查询是否开启多组织")
    OrganizationStatus.Result openOrganization(@Body OrganizationStatus.Arg arg);


    @POST(value = "/enterpriseSettings/api/getEnterpriseName", desc = "查询企业名称")
    FindEnterpriseName.Result findEnterpriseName(@Body FindEnterpriseName.Arg arg);

    @POST(value = "/Management/ManageGroups/getObjListByManageGroup", desc = "查询小组可用对象")
    FindManageGroupObjects.RestResult findManageGroupObjects(@HeaderMap Map<String, String> header, @Body FindManageGroupObjects.Arg arg);

    @POST(value = "/Management/ManageGroups/getObjListWithParentByManageGroup", desc = "查询业务信息时接入（需要返回父类的信息）")
    GetObjListWithParentByManageGroup.RestResult getObjListWithParentByManageGroup(@HeaderMap Map<String, String> header, @Body GetObjListWithParentByManageGroup.Arg arg);

    @POST(value = "/Management/ManageGroups/innerAddObjToManageGroupByUserId", desc = "添加业务信息时添加到分管小组中")
    AddToManageGroupByUserId.RestResult addToManageGroupByUserId(@HeaderMap Map<String, String> header, @Body AddToManageGroupByUserId.Arg arg);

    @POST(value = "/Management/ManageGroups/deleteManageContent", desc = "删除分管小组中的数据")
    DeleteManageContent.RestResult deleteManageContent(@HeaderMap Map<String, String> header, @Body DeleteManageContent.Arg arg);
}
