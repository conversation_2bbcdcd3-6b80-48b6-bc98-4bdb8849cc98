package com.facishare.paas.appframework.common.service.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface WhereUsed {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        @JsonProperty("describe_api_name")
        private String describeApiName;

        @JsonProperty("invoker_value")
        private String invokerValue;

        @JsonProperty("invoker_type")
        private String invokerType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Menu {
        @JsonProperty("label")
        private String label;
        @JsonProperty("invoker_type")
        private String invokerType;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Entity {
        @JsonProperty("invoker_type")
        private String invokerType;
        @JsonProperty("invoker_label")
        private String invokerLabel;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Result {

        @JsonProperty("menu")
        private List<Menu> menuList;

        @JsonProperty("data")
        private List<Entity> entityList;
    }
}
