package com.facishare.paas.appframework.common.util;

import com.google.common.collect.Maps;
import lombok.ToString;
import lombok.experimental.Delegate;

import java.io.Serializable;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by liyiguang on 2017/9/28.
 */
@ToString
public class DocumentBaseEntity implements Map<String, Object>, Serializable {
    private static final long serialVersionUID = -5835869283952852832L;

    @Delegate
    protected Map<String, Object> data;

    public DocumentBaseEntity() {
        this.data = Maps.newLinkedHashMap();
    }

    public DocumentBaseEntity(Map<String, Object> data) {
        Objects.requireNonNull(data);
        this.data = data;
    }

    public void projectField(List<String> fieldProjection) {
        if (CollectionUtils.empty(fieldProjection)) {
            return;
        }
        keySet().removeIf(it -> !fieldProjection.contains(it));
    }
}
