package com.facishare.paas.appframework.flow.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Created by zhouwr on 2022/12/19
 */
public interface FreeApprovalDefValidate {
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    @Builder
    class Arg {
        private Map<String, Object> freeApprovalDef;
    }

    @Data
    class Result {
        private int code;
        private String message;

        public boolean failed() {
            return code != 0;
        }
    }
}
