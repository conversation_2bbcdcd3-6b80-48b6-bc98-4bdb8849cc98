package com.facishare.paas.appframework.flow;

import com.facishare.paas.appframework.flow.dto.CanMoveToStagesOfObjects;
import com.facishare.paas.appframework.flow.dto.HasStageInstance;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(value = "PAAS-StagePropeller", desc = "流程推进器", contentType = "application/json")
public interface StageThrusterProxy {

    @POST(value = "rest/instance/canMoveToStagesOfObjects", desc = "获取可以变更的阶段")
    CanMoveToStagesOfObjects.Result canMoveToStagesOfObjects(@Body CanMoveToStagesOfObjects.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "rest/instance/hasStageInstance", desc = "查询单选字段是否只读")
    HasStageInstance.Result hasStageInstance(@Body HasStageInstance.Arg arg, @HeaderMap Map<String, String> header);
}