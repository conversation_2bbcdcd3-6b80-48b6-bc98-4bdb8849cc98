package com.facishare.paas.appframework.config;

import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Maps;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public interface OptionalFeaturesService {
    String RELATED_TEAM_SWITCH = "is_related_team_enabled";
    String GLOBAL_SEARCH_SWITCH = "is_global_search_enabled";
    String FOLLOW_UP_DYNAMIC_SWITCH = "is_follow_up_dynamic_enabled";
    String MODIFY_RECORD_SWITCH = "is_modify_record_enabled";
    String MULTI_FIELD_SORT = "multi_field_sort";
    String CROSS_OBJECT_FILTER_BUTTON = "cross_object_filter_button";
    String OPTIONAL_FEATURES = "optional_features";

    OptionalFeaturesSwitchDTO findOptionalFeaturesSwitch(String tenantId, IObjectDescribe describe);

    OptionalFeaturesSwitchDTO udobjOptionalFeaturesSwitchResources(String tenantId);

    Map<String, OptionalFeaturesSwitchDTO> batchQueryOptionalFeaturesSwitch(String tenantId, List<IObjectDescribe> describes);

    /**
     * 将OptionalFeaturesSwitchDTO转换为Map<String, Boolean>格式
     *
     * @param optionalFeaturesSwitch 功能开关DTO
     * @return 功能开关Map
     */
    default Map<String, Boolean> convertToMap(OptionalFeaturesSwitchDTO optionalFeaturesSwitch) {
        Map<String, Boolean> optionalFeaturesSwitchMap = Maps.newHashMap();
        if (Objects.isNull(optionalFeaturesSwitch)) {
            return optionalFeaturesSwitchMap;
        }
        optionalFeaturesSwitchMap.put(RELATED_TEAM_SWITCH, optionalFeaturesSwitch.getIsRelatedTeamEnabled());
        optionalFeaturesSwitchMap.put(GLOBAL_SEARCH_SWITCH, optionalFeaturesSwitch.getIsGlobalSearchEnabled());
        optionalFeaturesSwitchMap.put(FOLLOW_UP_DYNAMIC_SWITCH, optionalFeaturesSwitch.getIsFollowUpDynamicEnabled());
        optionalFeaturesSwitchMap.put(MODIFY_RECORD_SWITCH, optionalFeaturesSwitch.getIsModifyRecordEnabled());
        optionalFeaturesSwitchMap.put(MULTI_FIELD_SORT, optionalFeaturesSwitch.getMultiFieldSort());
        optionalFeaturesSwitchMap.put(CROSS_OBJECT_FILTER_BUTTON, optionalFeaturesSwitch.getCrossObjectFilterButton());
        return optionalFeaturesSwitchMap;
    }

    /**
     * 创建一个所有功能都禁用的Map
     *
     * @return 功能开关Map，所有功能都为false
     */
    default Map<String, Boolean> createDisabledFeaturesMap() {
        Map<String, Boolean> optionalFeaturesSwitchMap = Maps.newHashMap();
        optionalFeaturesSwitchMap.put(RELATED_TEAM_SWITCH, false);
        optionalFeaturesSwitchMap.put(GLOBAL_SEARCH_SWITCH, false);
        optionalFeaturesSwitchMap.put(FOLLOW_UP_DYNAMIC_SWITCH, false);
        optionalFeaturesSwitchMap.put(MODIFY_RECORD_SWITCH, false);
        optionalFeaturesSwitchMap.put(MULTI_FIELD_SORT, false);
        optionalFeaturesSwitchMap.put(CROSS_OBJECT_FILTER_BUTTON, false);
        return optionalFeaturesSwitchMap;
    }

    /**
     * 根据开关key获取开关状态
     *
     * @param switchKey 开关key
     * @param optionalFeaturesSwitchDTO 功能开关DTO
     * @return 开关状态，如果开关为null则返回true
     */
    default boolean isSwitchEnabled(String switchKey, OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO) {
        if (Objects.isNull(optionalFeaturesSwitchDTO)) {
            return true; // 如果没有开关配置，默认为开启状态
        }
        switch (switchKey) {
            case GLOBAL_SEARCH_SWITCH:
                return optionalFeaturesSwitchDTO.getIsGlobalSearchEnabled();
            case FOLLOW_UP_DYNAMIC_SWITCH:
                return optionalFeaturesSwitchDTO.getIsFollowUpDynamicEnabled();
            case RELATED_TEAM_SWITCH:
                return optionalFeaturesSwitchDTO.getIsRelatedTeamEnabled();
            case MODIFY_RECORD_SWITCH:
                return optionalFeaturesSwitchDTO.getIsModifyRecordEnabled();
            case MULTI_FIELD_SORT:
                return optionalFeaturesSwitchDTO.getMultiFieldSort();
            case CROSS_OBJECT_FILTER_BUTTON:
                return optionalFeaturesSwitchDTO.getCrossObjectFilterButton();
            default:
                return false;
        }
    }
}
