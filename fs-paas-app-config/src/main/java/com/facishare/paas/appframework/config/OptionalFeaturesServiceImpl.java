package com.facishare.paas.appframework.config;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.common.util.TenantUtil;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.metadata.api.action.ActionContext;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.MasterDetail;
import com.facishare.paas.metadata.api.service.IObjectDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Slf4j
@Service("optionalFeaturesService")
public class OptionalFeaturesServiceImpl implements OptionalFeaturesService {
    @Autowired
    private ConfigService configService;
    @Autowired
    private IObjectDescribeService objectDescribeService;

    @Override
    public OptionalFeaturesSwitchDTO findOptionalFeaturesSwitch(String tenantId, IObjectDescribe describe) {
        if (Objects.isNull(describe) || !AppFrameworkConfig.isOptionalFeaturesSupport(tenantId)) {
            OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO = OptionalFeaturesSwitchDTO.mackDefaultByDescribe(tenantId, describe);
            if (Objects.nonNull(describe)) {
                processingSwitchByDescribe(tenantId, describe, optionalFeaturesSwitchDTO);
            }
            return optionalFeaturesSwitchDTO;
        }
        String optionalFeaturesKey = buildSwitchKey(describe.getApiName(), OPTIONAL_FEATURES);
        String optionalFeatures = configService.findTenantConfig(User.systemUser(tenantId), optionalFeaturesKey);
        OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO = convertConfigToOptionalFeaturesSwitchDTO(tenantId, describe, optionalFeatures);
        processingSwitchByDescribe(tenantId, describe, optionalFeaturesSwitchDTO);
        return optionalFeaturesSwitchDTO;
    }

    @Override
    public OptionalFeaturesSwitchDTO udobjOptionalFeaturesSwitchResources(String tenantId) {
        if (AppFrameworkConfig.isOptionalFeaturesSupport(tenantId)) {
            if (!TenantUtil.isOptionalFeatures(tenantId)) {
                return OptionalFeaturesSwitchDTO.builder().build();
            }
            return OptionalFeaturesSwitchDTO.builder()
                    .isRelatedTeamEnabled(!AppFrameworkConfig.isCloseRelatedTeamSwitchObject(AppFrameworkConfig.UDOBJ))
                    .isGlobalSearchEnabled(!AppFrameworkConfig.isCloseGlobalSearchSwitchObject(AppFrameworkConfig.UDOBJ))
                    .isFollowUpDynamicEnabled(!AppFrameworkConfig.isCloseFollowUpDynamicSwitchObject(AppFrameworkConfig.UDOBJ))
                    .isModifyRecordEnabled(!AppFrameworkConfig.isCloseModifyRecordSwitchObject(AppFrameworkConfig.UDOBJ))
                    .crossObjectFilterButton(false)
                    .build();
        }
        return OptionalFeaturesSwitchDTO.builder().build();
    }

    @Override
    public Map<String, OptionalFeaturesSwitchDTO> batchQueryOptionalFeaturesSwitch(String tenantId, List<IObjectDescribe> describes) {
        if (CollectionUtils.empty(describes) || !AppFrameworkConfig.isOptionalFeaturesSupport(tenantId)) {
            return Maps.newHashMap();
        }
        StopWatch stopWatch =  StopWatch.create("batchQueryOptionalFeaturesSwitch");
        List<String> optionalFeaturesKeys = describes.stream()
                .map(IObjectDescribe::getApiName)
                .distinct()
                .map(apiName -> buildSwitchKey(apiName, OPTIONAL_FEATURES))
                .collect(Collectors.toList());
        Map<String, OptionalFeaturesSwitchDTO> optionalFeaturesSwitchMap = Maps.newHashMap();
        Map<String, String> configs = configService.queryTenantConfigs(User.systemUser(tenantId), optionalFeaturesKeys);
        stopWatch.lap("queryTenantConfigs");
        filledDetailDescribeIncludeFields(tenantId, describes);
        stopWatch.lap("filledDetailDescribeIncludeFields");
        for (IObjectDescribe describe : describes) {
            String describeApiName = describe.getApiName();
            OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO = convertConfigToOptionalFeaturesSwitchDTO(tenantId, describe, configs.get(buildSwitchKey(describeApiName, OPTIONAL_FEATURES)));
            processingSwitchByDescribe(tenantId, describe, optionalFeaturesSwitchDTO);
            optionalFeaturesSwitchMap.put(describeApiName, optionalFeaturesSwitchDTO);
        }
        stopWatch.lap("processingSwitchByDescribe");
        stopWatch.logSlow(1000);
        return optionalFeaturesSwitchMap;
    }

    private void filledDetailDescribeIncludeFields(String tenantId, List<IObjectDescribe> describes) {
        boolean notSupportDetailSearch = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EXCLUDE_DETAIL_IN_SEARCH, tenantId);
        if (notSupportDetailSearch) {
            return;
        }
        try {
            IActionContext actionContext = new ActionContext();
            actionContext.setEnterpriseId(tenantId);
            List<IObjectDescribe> detailDescribes = objectDescribeService.findDescribeByFieldTypes(tenantId,
                    Lists.newArrayList(IFieldType.MASTER_DETAIL), Lists.newArrayList(), actionContext);
            List<String> detailApiNames = detailDescribes.stream()
                    .map(IObjectDescribe::getApiName).collect(Collectors.toList());
            List<String> describeApiNames = describes.stream()
                    .map(IObjectDescribe::getApiName).collect(Collectors.toList());
            detailApiNames.retainAll(describeApiNames);
            describes.removeIf(x -> detailApiNames.contains(x.getApiName()));
            List<IObjectDescribe> retainedDescribes = detailDescribes.stream()
                    .filter(x -> detailApiNames.contains(x.getApiName()))
                    .collect(Collectors.toList());
            describes.addAll(retainedDescribes);
        } catch (MetadataServiceException e) {
            log.error("Error in filledDetailDescribeIncludeFields findDescribeByFieldTypes,tenantId:{},fieldTypes:{}", tenantId, IFieldType.MASTER_DETAIL, e);
        }
    }

    private String buildSwitchKey(String describeApiName, String switchFlag) {
        return describeApiName + "|" + switchFlag;
    }

    private OptionalFeaturesSwitchDTO convertConfigToOptionalFeaturesSwitchDTO(String tenantId, IObjectDescribe describe, String optionalFeatures) {
        if (StringUtils.isNotEmpty(optionalFeatures)) {
            return JSON.parseObject(optionalFeatures, OptionalFeaturesSwitchDTO.class);
        }
        return OptionalFeaturesSwitchDTO.mackDefaultByDescribe(tenantId, describe);
    }

    private void processingSwitchByDescribe(String tenantId, IObjectDescribe describe, OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO) {
        processingGlobalSearchSwitch(tenantId, describe, optionalFeaturesSwitchDTO);
    }

    private void processingGlobalSearchSwitch(String tenantId, IObjectDescribe describe, OptionalFeaturesSwitchDTO optionalFeaturesSwitchDTO) {
        boolean isSlave = describe.getFieldDescribes().stream().anyMatch(MasterDetail.class::isInstance);
        if (isSlave) {
            // 不在黑名单的企业从对象默认关闭全局搜索开关。
            optionalFeaturesSwitchDTO.setIsGlobalSearchEnabled(!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.EXCLUDE_DETAIL_IN_SEARCH, tenantId));
            optionalFeaturesSwitchDTO.setIsRelatedTeamEnabled(false);
            optionalFeaturesSwitchDTO.setCrossObjectFilterButton(false);
        }
    }
}
