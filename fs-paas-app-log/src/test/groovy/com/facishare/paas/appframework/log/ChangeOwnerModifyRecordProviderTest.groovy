package com.facishare.paas.appframework.log

import com.facishare.crm.userdefobj.DefObjConstants
import com.facishare.paas.I18N
import com.facishare.paas.appframework.core.i18n.I18NKey
import com.facishare.paas.appframework.log.dto.InternationalItem
import com.facishare.paas.appframework.log.dto.LogInfo
import com.facishare.paas.appframework.log.dto.TeamMemberInfo
import com.facishare.paas.appframework.metadata.ObjectDataExt
import com.facishare.paas.appframework.metadata.TeamMember
import com.facishare.paas.metadata.api.IObjectData
import com.facishare.paas.metadata.api.TeamRoleInfo
import com.facishare.paas.metadata.api.describe.IObjectDescribe
import spock.lang.Specification
import spock.lang.Unroll

/**
 * GenerateByAI
 * Test class for ChangeOwnerModifyRecordProvider
 */
class ChangeOwnerModifyRecordProviderTest extends Specification {
    ChangeOwnerModifyRecordProvider provider
    IObjectData oldData
    IObjectData newData
    IObjectDescribe objectDescribe
    Map<String, List<TeamMemberInfo.Member>> memberInfos
    List<TeamMemberInfo.Msg> msgList
    List<LogInfo.LintMessage> textMsg
    List<TeamRoleInfo> teamRoleInfos

    void setup() {
        provider = new ChangeOwnerModifyRecordProvider()
        oldData = Mock(IObjectData)
        newData = Mock(IObjectData)
        objectDescribe = Mock(IObjectDescribe)
        memberInfos = [:]
        msgList = []
        textMsg = []
        teamRoleInfos = []
    }

    /**
     * GenerateByAI
     * Test getApiName returns correct action type ID
     */
    def "getApiNameTest"() {
        when:
        def result = provider.getApiName()

        then:
        result == ActionType.ChangeOwner.getId()
    }

    /**
     * GenerateByAI
     * Test parseTeamMemberMsgs with different message scenarios
     */
    @Unroll
    def "parseTeamMemberMsgsTest"() {
        given:
        def msgs = [
            new TeamMemberInfo.Msg(
                operationType: operationType,
                msgMap: new TeamMemberInfo.MsgMap(
                    operation: operation,
                    role: TeamMemberInfo.Info.builder()
                        .internationalValue(roleI18nValue ? InternationalItem.builder()
                            .internationalKey(roleI18nValue)
                            .defaultInternationalValue(roleI18nValue)
                            .build() : null)
                        .value(roleI18nValue)
                        .build()
                )
            )
        ]

        when:
        def result = provider.parseTeamMemberMsgs(msgs)

        then:
        result.size() == 1
        result[0].operationLabel == ActionType.getNameById(operationType)
        if (operation) {
            if (roleI18nValue) {
                result[0].msgMap.operation == I18N.text(operation, roleI18nValue)
            } else {
                result[0].msgMap.operation == I18N.text(operation, I18N.text(I18NKey.constant_normal_staff))
            }
        }

        where:
        operationType | operation | roleI18nValue
        "ChangeOwner" | "op1"     | "role1"
        "ChangeOwner" | "op2"     | null
        "ChangeOwner" | null      | null
    }

    /**
     * GenerateByAI
     * Test logTeamMember when owner changes and old owner remains in team
     */
    def "logTeamMemberTestWhenOwnerChangesAndOldOwnerInTeam"() {
        given:
        def oldOwnerId = "old123"
        def newOwnerId = "new456"
        def oldOwnerName = "Old Owner"
        def newOwnerName = "New Owner"
        def permissionKey = DefObjConstants.DATA_PRIVILEGE_PERMISSION.READANDWRITE.getI18NLabel()
        
        oldData.getOwner() >> [oldOwnerId]
        newData.getOwner() >> [newOwnerId]
        newData.getName() >> "TestData"
        newData.getId() >> "123"
        objectDescribe.getDisplayName() >> "TestObject"
        objectDescribe.getApiName() >> "TestApiName"

        def teamMember = Mock(TeamMember)
        teamMember.getEmployee() >> oldOwnerId
        teamMember.getMemberType() >> TeamMember.MemberType.EMPLOYEE
        def permission = GroovyMock(TeamMember.Permission)
        permission.getLabelKey() >> permissionKey
        teamMember.getPermission() >> permission

        def objectDataExt = Mock(ObjectDataExt)
        objectDataExt.getTeamMembers() >> [teamMember]
        ObjectDataExt.metaClass.static.of = { IObjectData data -> objectDataExt }

        memberInfos[TeamMember.MemberType.EMPLOYEE.getValue()] = [
            new TeamMemberInfo.Member(id: oldOwnerId, name: oldOwnerName),
            new TeamMemberInfo.Member(id: newOwnerId, name: newOwnerName)
        ]

        when:
        provider.logTeamMember(oldData, newData, objectDescribe, memberInfos, msgList, textMsg, teamRoleInfos)

        then:
        textMsg.size() == 3
        textMsg[0].text == "TestObject"
        textMsg[1].text == "TestData"
        textMsg[2].internationalText.internationalKey == I18NKey.EDIT_OWNER_LOG_CHANGE_OWNER
        msgList.size() == 1
        msgList[0].operationType == ActionType.ChangeOwner.getId()

        cleanup:
        ObjectDataExt.metaClass = null
    }

    /**
     * GenerateByAI
     * Test logTeamMember when owner changes and old owner is removed from team
     */
    def "logTeamMemberTestWhenOwnerChangesAndOldOwnerRemoved"() {
        given:
        def oldOwnerId = "old123"
        def newOwnerId = "new456"
        def oldOwnerName = "Old Owner"
        def newOwnerName = "New Owner"
        
        oldData.getOwner() >> [oldOwnerId]
        newData.getOwner() >> [newOwnerId]
        newData.getName() >> "TestData"
        newData.getId() >> "123"
        objectDescribe.getDisplayName() >> "TestObject"
        objectDescribe.getApiName() >> "TestApiName"

        def objectDataExt = Mock(ObjectDataExt)
        objectDataExt.getTeamMembers() >> []
        ObjectDataExt.metaClass.static.of = { IObjectData data -> objectDataExt }

        memberInfos[TeamMember.MemberType.EMPLOYEE.getValue()] = [
            new TeamMemberInfo.Member(id: oldOwnerId, name: oldOwnerName),
            new TeamMemberInfo.Member(id: newOwnerId, name: newOwnerName)
        ]

        when:
        provider.logTeamMember(oldData, newData, objectDescribe, memberInfos, msgList, textMsg, teamRoleInfos)

        then:
        textMsg.size() == 3
        textMsg[0].text == "TestObject"
        textMsg[1].text == "TestData"
        textMsg[2].internationalText.internationalKey == I18NKey.EDIT_OWNER_LOG_CHANGE_OWNER
        msgList.size() == 1
        msgList[0].operationType == ActionType.ChangeOwner.getId()

        cleanup:
        ObjectDataExt.metaClass = null
    }

    /**
     * GenerateByAI
     * Test logTeamMember when owner has not changed
     */
    def "logTeamMemberTestWhenOwnerNotChanged"() {
        given:
        def ownerId = "owner123"
        oldData.getOwner() >> [ownerId]
        newData.getOwner() >> [ownerId]

        when:
        provider.logTeamMember(oldData, newData, objectDescribe, memberInfos, msgList, textMsg, teamRoleInfos)

        then:
        textMsg.size() == 0
        msgList.size() == 0
    }
}
