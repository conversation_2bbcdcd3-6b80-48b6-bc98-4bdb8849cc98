package com.facishare.paas.appframework.log.dto;

import com.facishare.paas.appframework.metadata.TeamMember;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.Set;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TeamMemberInfo {

    private List<Msg> msgList;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Msg {
        private String operationLabel;
        private String operationType;
        private Member member;
        private MsgMap msgMap;
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Member {
        private String oldId;
        private String oldName;
        private String name;
        private String id;
        private String oldOutTenantName;
        private String outTenantName;

        public Set<String> allIds() {
            Set<String> result = Sets.newHashSet();
            if (!Strings.isNullOrEmpty(oldId)) {
                result.add(oldId);
            }
            if (!Strings.isNullOrEmpty(id)) {
                result.add(id);
            }
            return result;
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MsgMap {
        private Info role;
        private Info permission;
        private String memberType;
        private String operation;

        public TeamMember.MemberType memberType() {
            return TeamMember.MemberType.of(memberType);
        }

        public boolean matchMemberType(TeamMember.MemberType target) {
            return target == memberType();
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Info {
        private InternationalItem internationalLabel;
        private String label;
        private InternationalItem internationalOldValue;
        private String oldValue;
        private InternationalItem internationalValue;
        private String value;

        public String convertToI18NLabel() {
            String result = null;
            if (Objects.nonNull(internationalLabel)) {
                result = internationalLabel.getInternationalValue();
            }
            if (StringUtils.isBlank(result)) {
                result = label;
            }
            return result;
        }


        public String convertToI18NValue() {
            String result = null;
            if (Objects.nonNull(internationalValue)) {
                result = internationalValue.getInternationalValue();
            }
            if (StringUtils.isBlank(result)) {
                result = value;
            }
            return result;
        }

        public String convertTo18NOldValue() {
            String result = null;
            if (Objects.nonNull(internationalOldValue)) {
                result = internationalOldValue.getInternationalValue();
            }
            if (StringUtils.isBlank(result)) {
                result = oldValue;
            }
            return result;
        }
    }
}
