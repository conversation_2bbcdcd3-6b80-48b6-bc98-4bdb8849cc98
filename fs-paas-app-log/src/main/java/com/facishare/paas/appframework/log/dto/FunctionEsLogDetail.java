package com.facishare.paas.appframework.log.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class FunctionEsLogDetail implements Serializable {


    //日志id
    private String id;
    //租户id
    private long tenantId;
    //函数apiName
    private String functionApiName;
    //绑定对象
    private String bindingApiName;

    //绑定对象Name
    private String bindingObjName;

    //命名空间
    private String nameSpace;
    //fs_paas_functionlog主键id
    private String logId;
    //一次调用产生
    private String traceId;
    //日志 级别
    private long logLevel;
    //日志行号
    private long lineNumber;
    //函数运行生成的详细信息
    private String content;
    //日志自增号码,解决同时记录日志时间相同导致乱序 和限制记录日志的数量
    private Integer incrSerialNumber;
    //操作时间
    private long createTime;

}
