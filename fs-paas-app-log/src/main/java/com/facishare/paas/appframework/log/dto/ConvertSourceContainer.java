package com.facishare.paas.appframework.log.dto;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Builder
@Data
public class ConvertSourceContainer {
    private IObjectDescribe sourceObjectDescribe;
    private Map<String, IObjectDescribe> sourceDetailDescribes;
    private List<IObjectData> sourceObjectDataList;
    private Map<String, List<IObjectData>> sourceDetailList;
    private String eventId;
    private Map<String, String> targetSourceApiNameMap;
    private Map<String, String> targetSourceIdMap;

}
