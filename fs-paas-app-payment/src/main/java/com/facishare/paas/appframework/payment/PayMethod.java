package com.facishare.paas.appframework.payment;

import com.google.common.base.Strings;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
public enum PayMethod {
    ALIPAY("1", "支付宝"),
    WEIXIN("2", "微信"),
    BANK("3", "银行支付"),
    WALLET("4", "企业余额"),
    APPLEPAY("5", "applepay"),
    EMPTY("-1", null);
    String value;
    String label;

    PayMethod(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getLabel() {
        return label;
    }

    public String getValue() {
        return value;
    }

    private static Map<String, PayMethod> map;

    static {
        map = Stream.of(values()).collect(Collectors.toMap(PayMethod::getValue, x -> x));
    }

    public static PayMethod parse(String value) {
        return map.getOrDefault(value, EMPTY);
    }

    public static PayMethod parseByName(String name) {
        try {
            return PayMethod.valueOf(name);
        } catch (Exception e) {
            log.warn("can not find pay method:{}", name);
        }
        return PayMethod.EMPTY;
    }
}
