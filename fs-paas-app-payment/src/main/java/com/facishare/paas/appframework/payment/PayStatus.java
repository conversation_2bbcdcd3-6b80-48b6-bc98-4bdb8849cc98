package com.facishare.paas.appframework.payment;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;

import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum PayStatus {
    FINISH(1, I18NKey.FINISH),
    INIT(2, I18NKey.INIT),
    EMPTY(-1, "");

    private int value;
    private String label;

    PayStatus(int value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getLabel() {
        return I18N.text(label);
    }

    public int getValue() {
        return value;
    }

    private static Map<Integer, PayStatus> map;

    static {
        map = Stream.of(values()).collect(Collectors.toMap(PayStatus::getValue, x -> x));
    }

    public static PayStatus parse(int value) {
        return map.getOrDefault(value, EMPTY);
    }
}
