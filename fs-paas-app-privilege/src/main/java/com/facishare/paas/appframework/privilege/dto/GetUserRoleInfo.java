package com.facishare.paas.appframework.privilege.dto;

import com.facishare.paas.auth.common.constant.AuthConstant;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * Created by zhouwr on 2017/10/17
 */
public interface GetUserRoleInfo {
    @Data
    class Arg {
        private AuthContext authContext;
        private String userId;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errMessage;
        private List<RoleInfo> result = new ArrayList<RoleInfo>();
        private boolean success;
    }

    @Data
    class RoleInfo {
        private String roleCode;
        private String roleName;
        private Integer roleType;
        private String appId;
        private String tenantId;
        private String description;
        private Boolean delFlag = false;

        public boolean isCrmRole() {
            return Objects.equals(AuthConstant.RoleType.DEFAULT, roleType)
                    || Objects.equals(AuthConstant.RoleType.CUSTOMIZED, roleType);
        }

        public boolean isOutRole() {
            return !isCrmRole();
        }
    }
}
