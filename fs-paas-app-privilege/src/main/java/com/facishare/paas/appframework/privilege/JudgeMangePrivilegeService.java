package com.facishare.paas.appframework.privilege;


import com.facishare.crm.valueobject.SessionContext;
import com.facishare.organization.adapter.api.permission.model.CheckFunctionCodeAndGetManageDepartments;
import com.facishare.paas.appframework.privilege.dto.JudgeManageRangeInfo;
import com.facishare.paas.appframework.privilege.dto.JudgeMangePrivilegeResult;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2019/3/27 15:40
 */
public interface JudgeMangePrivilegeService {
    /**
     * 判断用户是否有管理权限
     *
     * @param sessionContext
     * @return 是否有管理权限、管理部门ids、管理员工
     * <p>
     * NPE 注意：在无管理员权限时，result中的部门链表为空，所管理员工为空。
     * 管理权限问题：若管理员为全公司管理员，部门链表返回99999(Integer)
     */
    JudgeMangePrivilegeResult judgeMangePrivilege(SessionContext sessionContext);

    /**
     * 判断用户是否有管理权限
     *
     * @param sessionContext
     * @return 是否有管理权限、管理部门ids、管理员工
     * <p>
     * NPE 注意：在无管理员权限时，result中的部门链表为空，所管理员工为空。
     * 管理权限问题：若管理员为全公司管理员，部门链表返回99999(Integer)
     */
    JudgeMangePrivilegeResult judgeMangePrivilegeGray(SessionContext sessionContext);

    /**
     * 查询该部门下的所有员工
     *
     * @param departmentIds
     * @param sessionContext
     * @return 员工列表
     */
    List<String> getEmployeeByDepartment(List<Integer> departmentIds, SessionContext sessionContext);

    /**
     * 查询是否有目标权限
     *
     * @param sessionContext
     * @param targetFunction 权限码
     * @return 是否有目标权限
     */
    CheckFunctionCodeAndGetManageDepartments.Result checkTargetFunction(SessionContext sessionContext, String targetFunction);

    /**
     * 查询是否有目标权限
     *
     * @param sessionContext
     * @param targetFunction
     * @return
     */
    JudgeManageRangeInfo.Result getUserMangeRangeInfo(SessionContext sessionContext, String targetFunction);

}
