package com.facishare.paas.appframework.privilege.dto;

import com.facishare.crm.userdefobj.DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION;
import lombok.Builder;
import lombok.Data;

import static com.facishare.crm.userdefobj.DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.NO_PERMISSION;
import static com.facishare.crm.userdefobj.DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PRIVATE;
import static com.facishare.crm.userdefobj.DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PUBLIC_READONLY;
import static com.facishare.crm.userdefobj.DefObjConstants.DATA_PRIVILEGE_OBJECTDATA_PERMISSION.PUBLIC_READ_WRITE_DELETE;

/**
 * Created by yusb on 2017/4/24.
 */
@Data
@Builder
public class EntityOpennessPojo {
    private String id;
    private String entityId;
    private Integer permission;
    private Integer scope;
    private String appId;
    private String tenantId;
    private Boolean delFlag;
    private String creator;
    private Long createTime;
    private String modifier;
    private Long modifyTime;

    static final public Integer SCOPE_ALL = 0; //全公司
    static final public Integer SCOPE_DEPT = 1; //本部门
    static final public Integer SCOPE_PRIVATE = 2; //私有

    static final public Integer PERMISSION_READONLY = 1; //只读
    static final public Integer PERMISSION_READ_OR_WRITE = 2; //读写

    public DATA_PRIVILEGE_OBJECTDATA_PERMISSION getPermissionFromEntityOpennessPojo() {
        if (EntityOpennessPojo.SCOPE_PRIVATE.equals(getScope()) &&
                EntityOpennessPojo.PERMISSION_READ_OR_WRITE.equals(getPermission())) {
            return PRIVATE;
        } else if (EntityOpennessPojo.SCOPE_ALL.equals(getScope()) &&
                EntityOpennessPojo.PERMISSION_READONLY.equals(getPermission())) {
            return PUBLIC_READONLY;
        } else if (EntityOpennessPojo.SCOPE_ALL.equals(getScope()) &&
                EntityOpennessPojo.PERMISSION_READ_OR_WRITE.equals(getPermission())) {
            return PUBLIC_READ_WRITE_DELETE;
        }
        return NO_PERMISSION;
    }
}
