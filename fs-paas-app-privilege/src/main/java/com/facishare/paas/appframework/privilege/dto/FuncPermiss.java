package com.facishare.paas.appframework.privilege.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.collect.Maps;
import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2018/3/8.
 */
public interface FuncPermiss {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class PassBaseResult<T> {
        private Integer errCode;
        private String errMessage;
        private T result;
        private Boolean success;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class RoleFuncPermissArg {
        private AuthContext authContext;
        private String roleCode;
    }


    @Data
    @AllArgsConstructor
    class RolesEntitysFieldPermissArg {
        private AuthContext authContext;
        private List<String> roleCodes;
        private Collection<String> entitys;
    }

    @Data
    @AllArgsConstructor
    class RolesFuncPermissArg {
        private AuthContext authContext;
        private List<String> roleCodes;
    }

    @Data
    class UpdateRoleFuncPermissArg {
        private AuthContext authContext;
        private String roleCode;
        @JsonProperty("funcCode")
        @SerializedName("funcCode")
        private List<String> funcCodes;
    }


    @Data
    class RolesFuncPermissResult {
        private Integer errCode;
        private String errMessage;
        private Map<String, List<String>> result = Maps.newHashMap();
        private Boolean success;
    }

    @Data
    class UserEntitysFieldPermissResponse {
        private Integer errCode;
        private String errMessage;
        private Map<String, Map<String, Integer>> result = new HashMap<String, Map<String, Integer>>();
        private Boolean success;
    }

    @Data
    class Result {
        private Integer errCode;
        private String errMessage;
        private List<FunctionPojo> result;
        private Boolean success;
    }

    @Data
    class BaseResult {
        private Integer errCode;
        private String errKey;
        private String errMessage;
        private String errDescription;
        private Object result;
        private Boolean success;
    }


    @Data
    class FunctionPojo {
        private String id;
        private String appId;
        private String tenantId;
        private String funcName;
        private String funcCode;
        private String funcOrder;
        private String parentCode;
        private String levelCode;
        private Integer funcType;
        private Boolean isEnabled;
    }

}
