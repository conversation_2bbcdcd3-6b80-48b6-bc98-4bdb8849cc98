package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * Created by l<PERSON><PERSON> on 2019/11/12
 */
public interface FindViews {
    @Data
    class Arg {
        private AuthContext authContext;

        @Builder
        public Arg(AuthContext context, String entityId, List<String> recordTypeIds, List<String> roleCodes, String viewType) {
            this.authContext = context;
            this.entityId = entityId;
            this.recordTypeIds = recordTypeIds;
            this.roleCodes = roleCodes;
            this.viewType = viewType;
        }

        private String entityId;
        private List<String> recordTypeIds;
        private List<String> roleCodes;
        private String viewType;
    }


    @Data
    @EqualsAndHashCode(callSuper = true)
    class Result extends BasePrivilegeResult {
        private List<FindView.RoleViewPojo> result;
    }
}
