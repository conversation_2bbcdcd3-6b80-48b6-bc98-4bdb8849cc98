package com.facishare.paas.appframework.privilege.model;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

import static com.facishare.paas.appframework.privilege.model.FunctionPrivilegeProviderDefinition.*;

@RestResource(
        value = "NCRM",
        desc = "NCRM服务", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface FunctionPrivilegeProviderProxy {

    @POST(value = "/API/v1/rest/object/FunctionPrivilegeProvider/service/getSupportedActionCodes", desc = "获取所有支持的操作码")
    GetSupportedActionCodes.RestResult getSupportedActionCodes(@Body GetSupportedActionCodes.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/API/v1/rest/object/FunctionPrivilegeProvider/service/getCustomInitRoleActionCodes", desc = "获取自定义需要初始化的角色以及其有权限的操作码")
    GetCustomInitRoleActionCodes.RestResult getCustomInitRoleActionCodes(@Body GetCustomInitRoleActionCodes.Arg arg, @HeaderMap Map<String, String> headers);

    @POST(value = "/API/v1/rest/object/FunctionPrivilegeProvider/service/isAdminInitByDefault", desc = "CRM管理员是否默认初始化")
    IsAdminInitByDefault.RestResult isAdminInitByDefault(@Body IsAdminInitByDefault.Arg arg, @HeaderMap Map<String, String> headers);

}
