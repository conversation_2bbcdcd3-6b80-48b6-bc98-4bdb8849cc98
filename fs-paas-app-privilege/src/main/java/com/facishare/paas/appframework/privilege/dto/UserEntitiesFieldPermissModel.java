package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2018/4/24.
 */
public interface UserEntitiesFieldPermissModel {
    @Data
    class Result {
        private Integer errCode;
        private String errMessage;
        private Map<String, Map<String, Integer>> result;
        private Boolean success;
    }

    @Data
    @AllArgsConstructor
    class Arg {
        private AuthContext authContext;
        private List<String> entitys;
    }

}
