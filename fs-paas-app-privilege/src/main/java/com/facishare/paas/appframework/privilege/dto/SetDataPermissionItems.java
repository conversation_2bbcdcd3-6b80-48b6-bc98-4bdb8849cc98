package com.facishare.paas.appframework.privilege.dto;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.appframework.privilege.dto.FindFeedRolePermission;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface SetDataPermissionItems {

    @Builder
    @Data
    class Arg {
        @J<PERSON><PERSON>ield(name = "CurrentEmployeeID")
        private String currentEmployeeID;
        @JSONField(name = "DataPermissionItems")
        private List<FindFeedRolePermission.DataPermissionItem> dataPermissionItems;
    }

    @Builder
    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private FindFeedRolePermission.Value value;
        private boolean success;
        private String message;
        private int errorCode;
    }
}
