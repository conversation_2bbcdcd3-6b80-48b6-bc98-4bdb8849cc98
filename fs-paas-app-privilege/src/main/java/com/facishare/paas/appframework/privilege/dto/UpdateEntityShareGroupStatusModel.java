package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

public interface UpdateEntityShareGroupStatusModel {

  int ENABLE_STATUS = 1;
  int DISABLE_STATUS = 0;

  @Data
  @EqualsAndHashCode(callSuper = true)
  class Arg extends BasePrivilegeArg {
    private Set<String> groupIds;
    private Integer status;
  }

  @Data
  @EqualsAndHashCode(callSuper = true)
  class Result extends BasePrivilegeResult {
  }

}
