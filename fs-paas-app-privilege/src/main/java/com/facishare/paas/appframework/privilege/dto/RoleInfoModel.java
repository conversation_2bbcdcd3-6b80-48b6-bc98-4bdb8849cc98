package com.facishare.paas.appframework.privilege.dto;

import java.util.List;
import java.util.Map;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2018/2/27 10:24
 */
public interface RoleInfoModel {
    @Data
    @AllArgsConstructor
    class Arg {
        private AuthContext authContext;
        private String roleCode;

    }

    @Data
    class Result {
        private Integer errCode;
        private String errMessage;
        private Map<String, List<RoleInfoModel.UserRolePojo>> result;
        private boolean success;
    }

    @Data
    class UserRolePojo {
        private String tenantId;
        private String appId;
        private String roleCode;
        private String roleName;
        private Integer roleType;
        private Boolean delFlag;
        private String description;
    }

}
