package com.facishare.paas.appframework.privilege.dto;


import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * Created by luxin on 2018/1/16.
 */
public interface RoleListInfo {

    @Data
    @AllArgsConstructor
    class Arg {
        AuthContext authContext;
    }

    @Data
    class Result {
        int errCode;
        String errMessage;
        Map<String, List<Map<String, String>>> result;
        boolean success;
    }

}
