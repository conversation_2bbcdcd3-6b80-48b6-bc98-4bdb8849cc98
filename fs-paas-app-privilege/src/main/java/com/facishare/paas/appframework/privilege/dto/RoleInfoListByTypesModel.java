package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by luxin on 2018/5/31.
 */
public interface RoleInfoListByTypesModel {
    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        private String roleCode;
        private String roleName;
        private List<Integer> roleTypes;
    }

    @Data
    class Result {
        int errCode;
        String errMessage;
        RestResult result;
        boolean success;
    }

}
