package com.facishare.paas.appframework.privilege;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CacheContext;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.privilege.dto.*;
import com.facishare.paas.appframework.privilege.util.AuthContextExt;
import com.facishare.paas.auth.common.exception.AuthException;
import com.facishare.paas.auth.model.RolePojo;
import com.facishare.paas.auth.model.RoleViewPojo;
import com.facishare.paas.auth.model.UserRolePojo;
import com.fxiaoke.paas.auth.factory.RoleClient;
import com.fxiaoke.paas.auth.factory.ViewClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.privilege.util.Headers.PAAS_PRIVILEGE_HEADDER;

/**
 * Created by zhouwr on 2017/10/16
 */
@Slf4j
@Service("userRoleInfoService")
public class UserRoleInfoServiceImpl implements UserRoleInfoService {
    @Autowired
    private RoleClient roleClient;
    @Autowired
    private ViewClient viewClient;
    @Autowired
    private UserRoleInfoProxy proxy;

    @Override
    public List<String> getUsersByRole(User user, String roleCode) {
        AuthContext context = buildOldAuthContext(user);
        GetUsersByRole.Arg arg = new GetUsersByRole.Arg();
        arg.setAuthContext(context);
        arg.setRoleCode(roleCode);
        GetUsersByRole.Result result = proxy.getUsersByRole(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));

        if (!result.isSuccess()) {
            throw new PermissionError(result.getErrMessage());
        }

        return result.getResult().getUsers();
    }

    @Override
    public boolean isAdmin(User user) {
        if (user.isSupperAdmin()) {
            return true;
        }
        if (user.isOutUser()) {
            return false;
        }
        if (!user.getIsCrmAdmin().isPresent()) {
            Map<String, Boolean> results;
            try {
                results = roleClient.checkCrmManager(buildAuthContext(user), Sets.newHashSet(user.getUserId()));
            } catch (AuthException e) {
                throw new PermissionError(I18N.text(I18NKey.GET_ROLE_LIST_FAIL), e.getCode());
            }
            user.setIsCrmAdmin(Optional.of(CollectionUtils.nullToEmpty(results).getOrDefault(user.getUserId(), false)));
        }
        return user.getIsCrmAdmin().orElse(false);
    }

    @Override
    public List<String> getUserRole(User user) {
        try {
            return Lists.newArrayList(roleClient.queryRoleCodeByUserId(buildAuthContext(user)));
        } catch (AuthException e) {
            throw new PermissionError(I18N.text(I18NKey.GET_ROLE_LIST_FAIL), e.getCode());
        }
    }

    @Override
    public Optional<String> getDefaultRoleCode(User user) {
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.USER_DEFAULT_ROLE_CODE_LOCAL_CACHE_GRAY, user.getTenantId())) {
            return getRoleInfoByUser(user).stream().filter(GetRolesByUserId.UserRole::getDefaultRole).map(GetRolesByUserId.UserRole::getRoleCode).findFirst();
        }
        String cacheKey = buildCacheKey(user);
        CacheContext cacheContext = CacheContext.getContext();
        String cache = cacheContext.getCache(cacheKey);
        if (StringUtils.isNotBlank(cache)) {
            return Optional.of(cache);
        }
        // 判断是否缓存了空值
        if (cacheContext.containsCache(cacheKey)) {
            return Optional.empty();
        }
        Optional<String> defaultRoleCode = getRoleInfoByUser(user).stream()
                .filter(GetRolesByUserId.UserRole::getDefaultRole)
                .map(GetRolesByUserId.UserRole::getRoleCode)
                .findFirst();
        cacheContext.setCache(cacheKey, defaultRoleCode.orElse(null));
        return defaultRoleCode;
    }

    private String buildCacheKey(User user) {
        StringJoiner stringJoiner = new StringJoiner("_");
        stringJoiner.add("USER_DEFAULT_ROLE_CODE");
        stringJoiner.add(user.getTenantId());
        if (user.isOutUser()) {
            stringJoiner.add(user.getOutTenantId());
            stringJoiner.add(user.getOutUserId());
        } else {
            stringJoiner.add(user.getUserId());
        }
        return stringJoiner.toString();
    }

    @Override
    public List<GetRolesByUserId.UserRole> getRoleInfoByUser(User user) {
        if (user.isOutUser()) {
            try {
                List<UserRolePojo> userRolePojos = roleClient.queryUserRoleByUserIds(buildAuthContext(user), Sets.newHashSet(user.getOutUserId()));
                return userRolePojos.stream().map(this::convertToUserRole).collect(Collectors.toList());
            } catch (AuthException e) {
                throw new PermissionError(I18N.text(I18NKey.GET_ROLE_LIST_FAIL), e.getCode());
            }
        } else {
            AuthContext context = buildOldAuthContext(user);
            GetRolesByUserId.Arg arg = GetRolesByUserId.Arg.builder()
                    .context(context)
                    .user(user.getUserId())
                    .build();

            GetRolesByUserId.Result result = proxy.GetRolesByUserId(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
            if (!result.isSuccess()) {
                throw new PermissionError(result.getErrMessage());
            }
            return result.getResult();
        }
    }

    private GetRolesByUserId.UserRole convertToUserRole(UserRolePojo userRolePojo) {
        GetRolesByUserId.UserRole userRole = new GetRolesByUserId.UserRole();
        userRole.setDefaultRole(userRolePojo.getDefaultRole());
        userRole.setRoleCode(userRolePojo.getRoleCode());
        userRole.setAppId(userRolePojo.getAppId());
        userRole.setOrgId(userRolePojo.getOrgId());
        return userRole;
    }

    @Override
    public Optional<String> getMainRoleLayoutAPIName(User user, String objectAPIName, String recordType) {
        return getMainRoleLayoutAPIName(user, objectAPIName, recordType, LayoutTypes.DETAIL);
    }

    @Override
    public Optional<String> getMainRoleLayoutAPIName(User user, String objectAPIName, String recordType, String viewType) {
        try {
            List<RoleViewPojo> view = viewClient.findDefaultRoleView(buildAuthContext(user), objectAPIName, recordType, viewType);
            return view.stream().findFirst().map(RoleViewPojo::getViewId);
        } catch (AuthException e) {
            log.error("findDefaultRoleView error,user:{},objectAPIName:{},recordType:{},viewType:{}", user,
                    objectAPIName, recordType, viewType, e);
            throw new PermissionError(I18N.text(I18NKey.GET_ROLE_LIST_FAIL), e.getCode());
        }
    }

    @Override
    public List<RoleViewPojo> getMainRoleLayoutByRecordType(User user, String objectAPIName, String viewType) {
        try {
            return viewClient.findDefaultRoleView(buildAuthContext(user), objectAPIName, "", viewType);
        } catch (AuthException e) {
            log.error("findDefaultRoleView error,user:{},objectAPIName:{},recordType:{},viewType:{}", user,
                    objectAPIName, null, viewType, e);
            throw new PermissionError(I18N.text(I18NKey.GET_ROLE_LIST_FAIL), e.getCode());
        }
    }

    @Override
    public List<String> queryRoleUsersByRoles(User user, List<String> roles) {
        AuthContext context = buildOldAuthContext(user);

        QueryUsersByRoles.Arg arg = QueryUsersByRoles.Arg.builder()
                .context(context)
                .roles(roles)
                .build();

        QueryUsersByRoles.Result result = proxy.queryRoleUsersByRoles(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        List<String> ret = Lists.newArrayList();
        result.getResult().forEach((role, userList) -> {
            ret.addAll((ArrayList<String>) userList);
        });
        return ret;
    }

    @Override
    public List<String> queryUsers(User user, String roleCode, Set<String> userIdSet) {
        AuthContext context = buildOldAuthContext(user);
        QueryUsers.Arg arg = QueryUsers.Arg.builder()
                .authContext(context)
                .roleCode(roleCode)
                .users(userIdSet)
                .build();
        QueryUsers.Result result = proxy.queryUsers(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        if (!result.isSuccess()) {
            throw new PermissionError(result.getErrMessage());
        }
        return result.getResult().get("users");
    }

    @Override
    public Map<String, String> queryRoleNameByRoleCode(User user, List<String> roleIds) {
        List<GetUserRoleInfo.RoleInfo> result = queryRoleInfoByRoleCode(user, roleIds);
        return CollectionUtils.nullToEmpty(result).stream().collect(Collectors.toMap(GetUserRoleInfo.RoleInfo::getRoleCode, GetUserRoleInfo.RoleInfo::getRoleName, (x, y) -> y));
    }

    @Override
    public List<QueryRoleCodeByNames.RoleInfo> queryRoleCodeByNames(User user, List<String> roleNames) {
        if (CollectionUtils.empty(roleNames)) {
            return Lists.newArrayList();
        }
        QueryRoleCodeByNames.Arg arg = QueryRoleCodeByNames.Arg.builder()
                .authContext(AuthContext.buildByUser(user))
                .roleNames(roleNames)
                .build();
        QueryRoleCodeByNames.Result result = proxy.queryRoleCodeByNames(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        if (!result.isSuccess()) {
            throw new PermissionError(result.getErrMessage());
        }
        if (Objects.isNull(result.getResult())) {
            return Lists.newArrayList();
        }
        return CollectionUtils.nullToEmpty(result.getResult().getRoles());
    }

    @Override
    public List<GetUserRoleInfo.RoleInfo> queryRoleInfoByRoleCode(User user, List<String> roleIds) {
        if (CollectionUtils.empty(roleIds)) {
            return Lists.newArrayList();
        }
        QueryRoleInfoWithCodes.Arg arg = QueryRoleInfoWithCodes.Arg.builder()
                .authContext(AuthContext.buildByUser(user))
                .roleCodes(roleIds)
                .build();
        QueryRoleInfoWithCodes.Result result = proxy.queryRoleInfoWithCodes(arg, PAAS_PRIVILEGE_HEADDER.buildHeader(user.getTenantId()));
        if (!result.isSuccess()) {
            throw new PermissionError(result.getErrMessage());
        }
        return CollectionUtils.nullToEmpty(result.getRoles());
    }


    public List<UserRoleInfo> queryAllRoleInfoByCodes(User user, List<String> roleCodes) {
        if (CollectionUtils.empty(roleCodes)) {
            return Lists.newArrayList();
        }
        List<RolePojo> result = roleClient.queryAllRoleInfo(buildAuthContext(user), Sets.newHashSet(roleCodes), false);
        return result.stream().map(this::convertToUserRole).collect(Collectors.toList());
    }

    private UserRoleInfo convertToUserRole(RolePojo rolePojo) {
        UserRoleInfo userRole = new UserRoleInfo();
        userRole.setRoleCode(rolePojo.getRoleCode());
        userRole.setRoleName(rolePojo.getRoleName());
        userRole.setAppId(rolePojo.getAppId());
        userRole.setDelFlag(rolePojo.getDelFlag());
        userRole.setRoleType(rolePojo.getRoleType());
        return userRole;
    }

    @Deprecated
    private AuthContext buildOldAuthContext(User user) {
        return AuthContext.builder()
                .appId(AuthContextExt.getAppId(user))
                .tenantId(user.getTenantId())
                .userId(user.isOutUser() ? user.getOutUserId() : user.getUserId())
                .outerTenantId(user.isOutUser() ? user.getOutTenantId() : null)
                .properties(AuthContextExt.buildProperties(user))
                .identityType(RequestUtil.getOutIdentityType())
                .build();
    }

    private com.facishare.paas.auth.model.AuthContext buildAuthContext(User user) {
        return AuthContextExt.of(user).getAuthContext();
    }
}
