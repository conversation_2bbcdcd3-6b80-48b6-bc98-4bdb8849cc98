package com.facishare.paas.appframework.privilege.model;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.privilege.dto.Permissions;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderDefinition.BatchCheckBusinessPrivilege;
import com.facishare.paas.appframework.privilege.model.DataPrivilegeProviderDefinition.CheckBusinessPrivilege;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Sets;
import lombok.Builder;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Builder
public class RemoteDataPrivilegeProvider implements DataPrivilegeProvider {
    private DataPrivilegeProviderProxy proxy;
    private String describeApiName;

    @Override
    public String getApiName() {
        return "REMOTE";
    }

    @Override
    public Map<String, Map<String, Permissions>> checkBusinessPrivilege(User user, Map<String, Permissions> dataPrivilegeMap, List<IObjectData> dataList, List<String> actionCodes) {
        BatchCheckBusinessPrivilege.Arg arg = BatchCheckBusinessPrivilege.Arg
                .builder()
                .actionCodes(Sets.newHashSet(actionCodes))
                .dataPrivilegeMap(dataPrivilegeMap)
                .dataList(CollectionUtils.nullToEmpty(dataList).stream().map(a -> ObjectDataExt.of(a).toMap()).collect(Collectors.toList()))
                .describeApiName(describeApiName)
                .build();
        Map<String, String> header = RestUtils.buildHeaders(user, describeApiName);
        BatchCheckBusinessPrivilege.RestResult result = proxy.batchCheckBusinessPrivilege(arg, header);
        return result.getData().getResult();
    }

    @Override
    public Map<String, Permissions> checkBusinessPrivilege(User user, Map<String, Permissions> dataPrivilegeMap, List<IObjectData> dataList, String actionCode) {
        CheckBusinessPrivilege.Arg arg = CheckBusinessPrivilege.Arg
                .builder()
                .actionCode(actionCode)
                .dataPrivilegeMap(dataPrivilegeMap)
                .dataList(CollectionUtils.nullToEmpty(dataList).stream().map(a -> ObjectDataExt.of(a).toMap()).collect(Collectors.toList()))
                .describeApiName(describeApiName)
                .build();
        Map<String, String> header = RestUtils.buildHeaders(user, describeApiName);
        CheckBusinessPrivilege.RestResult result = proxy.checkBusinessPrivilege(arg, header);
        return result.getData().getResult();
    }
}
