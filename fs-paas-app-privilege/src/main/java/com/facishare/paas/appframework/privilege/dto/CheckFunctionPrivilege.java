package com.facishare.paas.appframework.privilege.dto;

import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import java.util.List;
import java.util.Map;

/**
 * Created by liyiguang on 2017/8/16.
 */
public interface CheckFunctionPrivilege {
    @Data
    @Builder
    class Arg {
        AuthContext authContext;
        @Singular
        @SerializedName("funcCodeList")
        List<String> funcCodeLists;
    }

    @Data
    @Builder
    class Result {
        int errCode;
        String errMessage;
        Map<String, Boolean> result;
        boolean success;
    }
}
