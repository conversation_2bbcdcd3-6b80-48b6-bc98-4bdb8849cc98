package com.facishare.paas.appframework.privilege.dto;

import com.google.common.collect.Lists;
import lombok.Data;

import java.util.Collections;
import java.util.List;
import java.util.Set;

@Data
public class TemporaryPermissionInfo {
    String tenantId;
    String appId;
    String entityId;
    String dataId;
    Set<String> owners;
    Integer permission;
    String sceneName;
    String scene;

    public List<String> getOwners() {
        if (owners == null) {
            return Collections.emptyList();
        }
        return Lists.newArrayList(owners);
    }
}

