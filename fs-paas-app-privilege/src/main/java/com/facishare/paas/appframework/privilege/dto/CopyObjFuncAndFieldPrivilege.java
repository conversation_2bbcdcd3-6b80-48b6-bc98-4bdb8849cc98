package com.facishare.paas.appframework.privilege.dto;

import lombok.Builder;
import lombok.Data;


public interface CopyObjFuncAndFieldPrivilege {

    @Data
    @Builder
    class Arg {
        private AuthContext authContext;
        // 原对象apiName
        private String oldEntityId;
        // 目标对象apiName
        private String newEntityId;
        private Boolean copyField;
    }

    @Data
    class Result {
        private int errCode;
        private String errMessage;
        private boolean success;
    }
}
