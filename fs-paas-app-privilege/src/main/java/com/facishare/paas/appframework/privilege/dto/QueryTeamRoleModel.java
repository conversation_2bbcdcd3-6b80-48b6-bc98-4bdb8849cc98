package com.facishare.paas.appframework.privilege.dto;

import lombok.*;

import java.util.List;


public class QueryTeamRoleModel {

  @EqualsAndHashCode(callSuper = true)
  @Data
  @Builder
  @NoArgsConstructor
  @AllArgsConstructor
  public static class Arg extends BasePrivilegeArg {

    private String lang;

  }


  @EqualsAndHashCode(callSuper = true)
  @Data
  public static class Result extends BasePrivilegeResult {
    private List<TeamRolePojo> result;
  }
}
