package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.core.model.User;

import java.util.List;

/**
 * Created by luxin on 2018/1/15.
 */
public interface UserDefinedButtonService {

    /**
     * 新建按钮功能权限
     *
     * @param describeApiName 自定义对象apiName
     */
    Boolean createUserDefinedButton(User user, String describeApiName, String buttonApiName, String buttonName, List<String> roles);


    /**
     * 给roles增加自定义按钮的权限,并删原来具有自定按钮权限的角色的该自定义按钮权限
     */
    Boolean updateUserDefinedButtonPrivilegeRoles(User user, String describeApiName, String buttonApiName, List<String> addRoles, List<String> delRoles);

    /**
     * 删除自定义按钮
     */
    Boolean deleteUserDefinedButton(User user, String describeApiName, String buttonApiName);


    /**
     * 获得具有目标自定按钮权限的角色列表
     */
    List<String> getHavePrivilegeRolesByUserDefinedButton(User user, String describeApiName, String buttonApiName);

    /**
     * 更新自定义按钮的按钮名称
     */
    Boolean updateFuncButtonName(User user, String describeApiName, String buttonApiName, String buttonName);

}
