package com.facishare.paas.appframework.privilege;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.privilege.util.AppIdUtil;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.support.GDSHandler;
import com.fxiaoke.enterpriserelation2.arg.BatchGetRelationDownstreamAndOwnerArg;
import com.fxiaoke.enterpriserelation2.arg.GetMapperObjectOwnerIdArg;
import com.fxiaoke.enterpriserelation2.arg.UpstreamAndDownstreamOuterTenantIdOutArg;
import com.fxiaoke.enterpriserelation2.common.HeaderObj;
import com.fxiaoke.enterpriserelation2.common.RestResult;
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationObjService;
import com.fxiaoke.enterpriserelation2.service.EnterpriseRelationService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service("enterpriseRelationService")
public class EnterpriseRelationServiceImpl implements EnterpriseRelationServiceProxy {

    @Autowired
    private EnterpriseRelationService enterpriseRelationService;
    @Autowired
    private GDSHandler gdsHandler;

    private EnterpriseRelationObjService enterpriseRelationObjService;

    @Autowired
    public void setEnterpriseRelationObjService(EnterpriseRelationObjService enterpriseRelationObjService) {
        this.enterpriseRelationObjService = enterpriseRelationObjService;
    }

    @Override
    public Map<String, RelationDownstreamResult> getRelationDownstreamInfo(String tenantId, Set<String> partnerIdList) {
        return getRelationDownstreamInfo(tenantId, Utils.PARTNER_API_NAME, partnerIdList);
    }

    @Override
    public Map<String, RelationDownstreamResult> getRelationDownstreamInfo(String tenantId,
                                                                           String objectApiName,
                                                                           Collection<String> objectDataIds) {
        BatchGetRelationDownstreamAndOwnerArg arg = new BatchGetRelationDownstreamAndOwnerArg();
        arg.setObjectApiName(objectApiName);
        arg.setUpstreamTenantId(Integer.parseInt(tenantId));
        arg.setCrmDataIds(Lists.newArrayList(objectDataIds));
        RestResult<Map<String, RelationDownstreamResult>> result = enterpriseRelationService.batchGetRelationDownstreamAndOwner(HeaderObj.newInstance(Integer.parseInt(tenantId)), arg);

        if (Objects.isNull(result) || !result.isSuccess() || Objects.isNull(result.getData())) {
            log.error("enterpriseRelationService batchGetRelationDownstream error,tenantId:{},result:{}",
                    tenantId, result);
            return Maps.newHashMap();
        }
        return result.getData();
    }

    @Override
    public void fillOutEnterpriseInfo(User user, List<IObjectData> dataList, IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        if (!describeExt.isPRMEnabled() || user.isOutUser()) {
            return;
        }

        List<IObjectData> partnerList = dataList
                .stream()
                .filter(a -> !Strings.isNullOrEmpty(ObjectDataExt.of(a).getPartnerId()))
                .collect(Collectors.toList());

        Set<String> partnerIdList = partnerList.stream()
                .map(a -> ObjectDataExt.of(a).getPartnerId())
                .collect(Collectors.toSet());
        if (CollectionUtils.empty(partnerIdList)) {
            return;
        }

        //更换合作伙伴，获取外部企业和外部负责人填充
        Map<String, RelationDownstreamResult> result = getRelationDownstreamInfo(user.getTenantId(), partnerIdList);

        partnerList.forEach(a -> {
            ObjectDataExt dataExt = ObjectDataExt.of(a);
            RelationDownstreamResult item = result.get(dataExt.getPartnerId());
            if (item == null) {
                return;
            }
            Integer outTenantId = item.getDownstreamOuterTenantId();
            if (outTenantId == null) {
                outTenantId = 0;
            }
            dataExt.setOutTenantId(String.valueOf(outTenantId));
            String ownerId = String.valueOf(item.getRelationOwnerOuterUid());
            if (Strings.isNullOrEmpty(ownerId) || Objects.equals("null", ownerId)) {
                return;
            }
            dataExt.setOutOwner(Lists.newArrayList(ownerId));
            // 同步外部相关团队负责人
            dataExt.synchronizeOutTeamMemberOwner(String.valueOf(outTenantId), ownerId);
        });
    }

    /**
     * 获取合作伙伴或者客户新信息
     */
    @Override
    public String getUpstreamMapperObjectId(User outUser, String apiName) {
        HeaderObj header = HeaderObj.newInstance(
                Integer.valueOf(outUser.getTenantId()),
                Long.valueOf(outUser.getOutTenantId()),
                Long.valueOf(outUser.getOutUserId()));

        UpstreamAndDownstreamOuterTenantIdOutArg arg = new UpstreamAndDownstreamOuterTenantIdOutArg();
        arg.setDownstreamOuterTenantId(Long.valueOf(outUser.getOutTenantId()));
        arg.setObjectApiName(apiName);
        arg.setUpstreamEa(gdsHandler.getEAByEI(outUser.getTenantId()));
        RestResult<String> restResult = enterpriseRelationService.getMapperObjectId(header, arg);
        return restResult.getData();
    }

    @Override
    public String getMapperObjectOwnerId(User user, String appId) {
        if (!user.isOutUser()) {
            return null;
        }

        HeaderObj header = HeaderObj.newInstance(
                user.getTenantIdInt(), Long.valueOf(user.getOutTenantId()), Long.valueOf(user.getOutUserId()));

        GetMapperObjectOwnerIdArg arg = new GetMapperObjectOwnerIdArg();
        arg.setFsAppId(appId);
        arg.setDownstreamOuterTenantId(Long.valueOf(user.getOutTenantId()));
        arg.setUpstreamTenantId(user.getTenantIdInt());
        try {
            log.info("getMapperObjectOwnerId, user:{}, appId:{}.", user, appId);
            RestResult<Integer> restResult = enterpriseRelationService.getMapperObjectOwnerId(header, arg);
            if (Objects.nonNull(restResult) && restResult.isSuccess() && Objects.nonNull(restResult.getData())) {
                return String.valueOf(restResult.getData());
            } else {
                log.warn("upstreamOwnerId not found, user:{}, appId:{}, result:{}.", user, appId, restResult);
            }
        } catch (Exception e) {
            log.error("find upstreamOwnerId fail, user:{}, appId:{}.", user, appId, e);
        }
        return null;
    }

    @Override
    public String getDefaultDataOwner(User outUser, String apiName) {
        String appId = AppIdUtil.getAppIdWithNoDefault();
        Integer tenantId = outUser.getTenantIdInt();
        Long outTenantId = Long.valueOf(outUser.getOutTenantId());
        Long outUserId = Long.valueOf(outUser.getOutUserId());
        HeaderObj header = HeaderObj.newInstance(tenantId, outTenantId, outUserId);
        GetMapperObjectOwnerIdArg arg = new GetMapperObjectOwnerIdArg();
        arg.setFsAppId(appId);
        arg.setUpstreamTenantId(tenantId);
        arg.setDownstreamOuterTenantId(outTenantId);
        arg.setObjectApiName(apiName);
        RestResult<Integer> res = enterpriseRelationObjService.getObjectDataOwnerId(header, arg);
        return Optional.of(res).filter(RestResult::isSuccess).map(RestResult::getData).map(String::valueOf).orElse(null);
    }

    @Override
    public Optional<String> getDefaultDataOwnerByUser(User user) {
        if (!user.isOutUser()) {
            return Optional.empty();
        }
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUT_USER_CREATE_DATA_OWNER_ID_CALC_EI, user.getTenantId())
                || UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DEFAULT_VALUE_SUPPORT_OWNER_GRAY, user.getTenantId())) {
            // 设置一个默认数据负责人为下游用户
            return Optional.ofNullable(getDefaultDataOwner(user, null));
        }
        return Optional.empty();
    }
}
