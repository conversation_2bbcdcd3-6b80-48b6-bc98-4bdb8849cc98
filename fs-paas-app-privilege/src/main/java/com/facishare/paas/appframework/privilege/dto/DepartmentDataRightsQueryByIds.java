package com.facishare.paas.appframework.privilege.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface DepartmentDataRightsQueryByIds {
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        DepartmentDataRightsContext context;
        List<String> ids;
        int scene;
    }

}
