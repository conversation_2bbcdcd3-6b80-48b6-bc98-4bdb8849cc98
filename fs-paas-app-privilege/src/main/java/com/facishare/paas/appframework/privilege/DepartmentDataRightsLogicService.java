package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.dto.DepartmentDataRights;
import com.facishare.paas.appframework.privilege.dto.PageInfo;

import java.util.List;

/**
 * Created by fengjy on 2020/11/18.
 */
public interface DepartmentDataRightsLogicService {

    void upsert(User user, List<String> objectAPINames, List<String> departmentIds, int scene, int type);

    Tuple<PageInfo, List<DepartmentDataRights>> query(User user, List<String> objectAPINames, List<String> departmentIds, int scene, int page, int size);

    List<DepartmentDataRights> findByIds(User user,int scene, List<String> ids);

    void disableByIds(User user,int scene,List<String> ids);

    void deleteByIds(User user,int scene,List<String> ids);

    void enable(User user,int scene, List<String> ids);
}
