package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.enterpriserelation2.result.RelationDownstreamResult;

import java.util.*;

public interface EnterpriseRelationServiceProxy {
    /**
     * 默认使用合作伙伴作为对象 apiName 请使用 {@link #getRelationDownstreamInfo(String, String, Collection)}
     */
    @Deprecated
    Map<String, RelationDownstreamResult> getRelationDownstreamInfo(String tenantId, Set<String> partnerIdList);

    /**
     * @param tenantId      企业 id
     * @param objectApiName 对象 apiName
     * @param objectDataIds 数据 id
     * @return 数据对应的下游企业信息
     */
    Map<String, RelationDownstreamResult> getRelationDownstreamInfo(String tenantId,
                                                                    String objectApiName,
                                                                    Collection<String> objectDataIds);

    void fillOutEnterpriseInfo(User user, List<IObjectData> dataList, IObjectDescribe describe);

    String getUpstreamMapperObjectId(User outUser, String apiName);

    /**
     * @param outUser 下游用户信息
     * @param appId   应用 Id
     * @return
     */
    String getMapperObjectOwnerId(User outUser, String appId);

    /**
     * 下游用户创建数据时由互联平台分配默认数据负责人
     *
     * @param outUser 下游用户信息
     * @param apiName 对象 API（留口，暂时没有用，用客户还是合作伙伴还是什么是由互联决定）
     * @return 负责人 Id
     */
    String getDefaultDataOwner(User outUser, String apiName);

    Optional<String> getDefaultDataOwnerByUser(User user);
}
