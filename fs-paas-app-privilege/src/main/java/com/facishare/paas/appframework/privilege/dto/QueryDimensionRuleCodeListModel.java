package com.facishare.paas.appframework.privilege.dto;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Set;

public class QueryDimensionRuleCodeListModel {

  @Data
  @EqualsAndHashCode(callSuper = true)
  public static class Arg extends BasePrivilegeArg {

    private Set<String> receiveIds;

    private Integer receiveType;

    private String receiveTenantId;

  }


  @Data
  @EqualsAndHashCode(callSuper = true)
  public static class Result extends BasePrivilegeResult {
    private Set<String> result;
  }

}
