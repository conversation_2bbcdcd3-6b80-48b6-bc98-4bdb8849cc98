package com.facishare.paas.appframework.privilege.util;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.auth.model.AuthContext;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Map;

public class AuthContextExt {

    @Getter
    private AuthContext authContext;

    private AuthContextExt(AuthContext authContext) {
        this.authContext = authContext;
    }

    public static AuthContextExt of(User user) {
        return of(user, false);
    }

    public static AuthContextExt of(User user, boolean allAppScope) {
        AuthContext authContext = AuthContext.builder()
                .appId(getAppId(user))
                .tenantId(user.getTenantId())
                .userId(user.getUserIdOrOutUserIdIfOutUser())
                .outerTenantId(user.isOutUser() ? user.getOutTenantId() : null)
                .filterAppId(!allAppScope)
                .properties(buildProperties(user))
                .identityType(RequestUtil.getOutIdentityType())
                .build();
        return new AuthContextExt(authContext);
    }

    public static AuthContextExt of(User user, boolean allAppScope, String appId) {
        AuthContext authContext = AuthContext.builder()
                .appId(getAppId(user))
                .tenantId(user.getTenantId())
                .userId(user.getUserIdOrOutUserIdIfOutUser())
                .outerTenantId(user.isOutUser() ? user.getOutTenantId() : null)
                .filterAppId(!allAppScope)
                .properties(buildProperties(user.getTenantId(), appId, true))
                .identityType(RequestUtil.getOutIdentityType())
                .build();
        return new AuthContextExt(authContext);
    }

    public static Map<String, String> buildProperties(User user) {
        if (!AppFrameworkConfig.isGrayFuncPermissionAPPID(user.getTenantId()) || !user.isOutUser()) {
            return null;
        }

        String bizAppId = RequestUtil.getAppId();
        Map<String, String> properties = Maps.newHashMap();
        properties.put("bizAppId", bizAppId);
        return properties;
    }

    public static Map<String, String> buildProperties(String tenantId, String appId, boolean isOutUser) {
        if (!AppFrameworkConfig.isGrayFuncPermissionAPPID(tenantId) || !isOutUser) {
            return null;
        }

        Map<String, String> properties = Maps.newHashMap();
        properties.put("bizAppId", appId);
        return properties;
    }

    public static String getAppId(User user) {
        if (user.isOutUser()) {
            if (AppFrameworkConfig.isGrayFuncPermissionAPPID(user.getTenantId())) {
                return PrivilegeConstants.APP_ID;
            }
            return RequestUtil.getAppId();
        }
        return AppIdUtil.getAppId(user);
    }

    public static String getAppId(String tenantId, String appId, boolean isOutUser) {
        if (isOutUser) {
            if (AppFrameworkConfig.isGrayFuncPermissionAPPID(tenantId)) {
                return PrivilegeConstants.APP_ID;
            }
            return appId;
        }
        return AppIdUtil.getAppId(null, appId);
    }
}
