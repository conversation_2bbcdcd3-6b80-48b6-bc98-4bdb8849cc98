package com.facishare.paas.appframework.privilege;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FunctionPrivilegeProxy.HeaderUtil 单元测试
 * 
 * <AUTHOR> Generated
 */
@ExtendWith(MockitoExtension.class)
class FunctionPrivilegeProxyHeaderUtilTest {

  /**
   * GenerateByAI
   * 测试内容描述：测试buildHeaders方法，正常企业ID场景
   */
  @Test
  @DisplayName("正常场景 - 测试buildHeaders方法正常企业ID")
  void testBuildHeaders_Success() {
    // 准备测试数据
    String enterpriseId = "123456";
    
    // 执行被测试方法
    Map<String, String> result = FunctionPrivilegeProxy.HeaderUtil.buildHeaders(enterpriseId);
    
    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("123456", result.get("x-fs-ei"));
    assertTrue(result.containsKey("x-fs-ei"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildHeaders方法，多种企业ID格式
   */
  @ParameterizedTest
  @ValueSource(strings = {
    "123456",
    "enterprise_001", 
    "ENTERPRISE-123",
    "999999999999",
    "a1b2c3d4e5f6",
    "test.enterprise.id"
  })
  @DisplayName("参数化测试 - buildHeaders方法多种企业ID格式")
  void testBuildHeaders_VariousFormats(String enterpriseId) {
    // 执行被测试方法
    Map<String, String> result = FunctionPrivilegeProxy.HeaderUtil.buildHeaders(enterpriseId);
    
    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals(enterpriseId, result.get("x-fs-ei"));
    assertTrue(result.containsKey("x-fs-ei"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildHeaders方法，空字符串场景
   */
  @Test
  @DisplayName("边界场景 - 测试buildHeaders方法空字符串")
  void testBuildHeaders_EmptyString() {
    // 准备测试数据
    String enterpriseId = "";
    
    // 执行被测试方法
    Map<String, String> result = FunctionPrivilegeProxy.HeaderUtil.buildHeaders(enterpriseId);
    
    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("", result.get("x-fs-ei"));
    assertTrue(result.containsKey("x-fs-ei"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildHeaders方法，null值场景
   */
  @Test
  @DisplayName("边界场景 - 测试buildHeaders方法null值")
  void testBuildHeaders_NullValue() {
    // 准备测试数据
    String enterpriseId = null;
    
    // 执行被测试方法
    Map<String, String> result = FunctionPrivilegeProxy.HeaderUtil.buildHeaders(enterpriseId);
    
    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertNull(result.get("x-fs-ei"));
    assertTrue(result.containsKey("x-fs-ei"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildHeaders方法，特殊字符场景
   */
  @Test
  @DisplayName("边界场景 - 测试buildHeaders方法特殊字符")
  void testBuildHeaders_SpecialCharacters() {
    // 准备测试数据
    String enterpriseId = "企业ID-123!@#$%^&*()";
    
    // 执行被测试方法
    Map<String, String> result = FunctionPrivilegeProxy.HeaderUtil.buildHeaders(enterpriseId);
    
    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("企业ID-123!@#$%^&*()", result.get("x-fs-ei"));
    assertTrue(result.containsKey("x-fs-ei"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildHeaders方法，长字符串场景
   */
  @Test
  @DisplayName("边界场景 - 测试buildHeaders方法长字符串")
  void testBuildHeaders_LongString() {
    // 准备测试数据
    String enterpriseId = "very_long_enterprise_id_that_exceeds_normal_length_123456789012345678901234567890";
    
    // 执行被测试方法
    Map<String, String> result = FunctionPrivilegeProxy.HeaderUtil.buildHeaders(enterpriseId);
    
    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals(enterpriseId, result.get("x-fs-ei"));
    assertTrue(result.containsKey("x-fs-ei"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildHeaders方法，返回Map的可变性
   */
  @Test
  @DisplayName("正常场景 - 测试buildHeaders方法返回Map可变性")
  void testBuildHeaders_MapMutability() {
    // 准备测试数据
    String enterpriseId = "123456";
    
    // 执行被测试方法
    Map<String, String> result = FunctionPrivilegeProxy.HeaderUtil.buildHeaders(enterpriseId);
    
    // 验证Map是可变的
    assertDoesNotThrow(() -> {
      result.put("additional-header", "additional-value");
    });
    
    // 验证添加后的状态
    assertEquals(2, result.size());
    assertEquals("123456", result.get("x-fs-ei"));
    assertEquals("additional-value", result.get("additional-header"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildHeaders方法，多次调用独立性
   */
  @Test
  @DisplayName("正常场景 - 测试buildHeaders方法多次调用独立性")
  void testBuildHeaders_MultipleCallsIndependence() {
    // 准备测试数据
    String enterpriseId1 = "123456";
    String enterpriseId2 = "789012";
    
    // 执行被测试方法
    Map<String, String> result1 = FunctionPrivilegeProxy.HeaderUtil.buildHeaders(enterpriseId1);
    Map<String, String> result2 = FunctionPrivilegeProxy.HeaderUtil.buildHeaders(enterpriseId2);
    
    // 验证两次调用返回不同的Map实例
    assertNotSame(result1, result2);
    
    // 验证内容正确
    assertEquals("123456", result1.get("x-fs-ei"));
    assertEquals("789012", result2.get("x-fs-ei"));
    
    // 修改一个Map不影响另一个
    result1.put("test", "value1");
    result2.put("test", "value2");
    
    assertEquals("value1", result1.get("test"));
    assertEquals("value2", result2.get("test"));
    assertNotEquals(result1.get("test"), result2.get("test"));
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试buildHeaders方法，Unicode字符场景
   */
  @Test
  @DisplayName("边界场景 - 测试buildHeaders方法Unicode字符")
  void testBuildHeaders_UnicodeCharacters() {
    // 准备测试数据
    String enterpriseId = "企业🏢ID-测试123";
    
    // 执行被测试方法
    Map<String, String> result = FunctionPrivilegeProxy.HeaderUtil.buildHeaders(enterpriseId);
    
    // 验证结果
    assertNotNull(result);
    assertEquals(1, result.size());
    assertEquals("企业🏢ID-测试123", result.get("x-fs-ei"));
    assertTrue(result.containsKey("x-fs-ei"));
  }
}
