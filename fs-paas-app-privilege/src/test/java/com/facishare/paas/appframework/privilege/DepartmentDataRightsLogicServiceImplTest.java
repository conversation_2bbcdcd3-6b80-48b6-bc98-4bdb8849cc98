package com.facishare.paas.appframework.privilege;

import com.facishare.paas.appframework.common.service.ManageGroupService;
import com.facishare.paas.appframework.common.service.model.ManageGroup;
import com.facishare.paas.appframework.common.service.model.ManageGroupType;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.privilege.dto.DepartmentDataRights;
import com.facishare.paas.appframework.privilege.dto.DepartmentDataRightsQueryAll;
import com.facishare.paas.appframework.privilege.dto.DepartmentDataRightsQueryByIds;
import com.facishare.paas.appframework.privilege.dto.PageInfo;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.Arguments;
import org.junit.jupiter.params.provider.MethodSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试内容描述：测试DepartmentDataRightsLogicServiceImpl类的方法
 */
@ExtendWith(MockitoExtension.class)
class DepartmentDataRightsLogicServiceImplTest {

    @Mock
    private DepartmentDataRightsProxy rightsProxy;

    @Mock
    private FunctionPrivilegeService functionPrivilegeService;

    @Mock
    private DataPrivilegeCommonService dataPrivilegeCommonService;

    @Mock
    private ManageGroupService manageGroupService;

    @InjectMocks
    private DepartmentDataRightsLogicServiceImpl departmentDataRightsLogicService;

    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = new User("123", "456");
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试upsert方法正常场景
     */
    @Test
    @DisplayName("正常场景 - upsert方法测试笛卡尔积权限分配")
    void testUpsert() {
        // 准备测试数据
        List<String> objectAPINames = Arrays.asList("Object1", "Object2");
        List<String> departmentIds = Arrays.asList("Dept1", "Dept2");
        int scene = 1;
        int type = 0;

        // 执行被测试方法
        departmentDataRightsLogicService.upsert(testUser, objectAPINames, departmentIds, scene, type);

        // 验证Mock交互
        verify(rightsProxy, times(1)).upsert(anyMap(), argThat(arg -> {
            // 验证基本参数
            assertEquals(testUser.getTenantId(), arg.getContext().getTenantId());
            assertEquals(testUser.getUserId(), arg.getContext().getUserId());

            // 验证笛卡尔积结果 - 应该有4个权限记录
            assertEquals(4, arg.getDeptRights().size());

            // 验证每个权限记录的属性
            return arg.getDeptRights().stream().allMatch(rights ->
                    rights.getStatus() == 0 && // ENABLE_STATUS
                    objectAPINames.contains(rights.getEntityId()) &&
                    departmentIds.contains(rights.getDeptId()) &&
                    rights.getScene() == scene &&
                    rights.getType() == type
            );
        }));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试upsert方法边界场景，参数为空的情况
     */
    @ParameterizedTest
    @MethodSource("provideEmptyParams")
    @DisplayName("边界场景 - upsert方法参数为空")
    void testUpsertWithEmptyParams(List<String> objectAPINames, List<String> departmentIds) {
        // 准备测试数据
        int scene = 1;
        int type = 0;

        // 执行被测试方法
        departmentDataRightsLogicService.upsert(testUser, objectAPINames, departmentIds, scene, type);

        // 验证Mock交互 - 不应该进行任何RPC调用
        verify(rightsProxy, never()).upsert(anyMap(), any());
    }

    static Stream<Arguments> provideEmptyParams() {
        return Stream.of(
                Arguments.of(Collections.emptyList(), Arrays.asList("Dept1", "Dept2")),
                Arguments.of(null, Arrays.asList("Dept1", "Dept2")),
                Arguments.of(Arrays.asList("Object1"), Collections.emptyList()),
                Arguments.of(Arrays.asList("Object1"), null)
        );
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试query方法，测试分页查询功能
     */
    @Test
    @DisplayName("正常场景 - query方法分页查询")
    void testQuery() {
        // 准备测试数据
        List<String> objectAPINames = Arrays.asList("Object1", "Object2");
        List<String> departmentIds = Arrays.asList("Dept1", "Dept2");
        int scene = 1;
        int page = 1;
        int size = 20;

        List<DepartmentDataRights> mockRightsList = Arrays.asList(
                createDepartmentDataRights("1", "Object1", "Dept1"),
                createDepartmentDataRights("2", "Object1", "Dept2")
        );

        PageInfo mockPageInfo = new PageInfo();
        mockPageInfo.setCurrentPage(1);
        mockPageInfo.setPageSize(20);
        mockPageInfo.setTotalPage(1);
        mockPageInfo.setTotal(2);

        DepartmentDataRightsQueryAll.Result mockQueryResult = new DepartmentDataRightsQueryAll.Result();
        mockQueryResult.setPageInfo(mockPageInfo);
        mockQueryResult.setDeptRights(mockRightsList);

        // 配置Mock行为
        when(dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(testUser.getTenantId()))
                .thenReturn(false);
        when(rightsProxy.queryAll(anyMap(), any(DepartmentDataRightsQueryAll.Arg.class)))
                .thenReturn(mockQueryResult);

        // 执行被测试方法
        Tuple<PageInfo, List<DepartmentDataRights>> result = departmentDataRightsLogicService.query(
                testUser, objectAPINames, departmentIds, scene, page, size);

        // 验证Mock交互
        verify(rightsProxy, times(1)).queryAll(anyMap(), argThat(arg -> {
            assertEquals(testUser.getTenantId(), arg.getContext().getTenantId());
            assertEquals(testUser.getUserId(), arg.getContext().getUserId());
            assertEquals(objectAPINames, arg.getEntityIds());
            assertEquals(departmentIds, arg.getDeptIds());
            assertEquals(scene, arg.getScene());
            assertEquals(page, arg.getCurrentPage());
            assertEquals(size, arg.getSize());
            return true;
        }));

        // 验证结果
        assertEquals(mockPageInfo, result.getKey());
        assertEquals(mockRightsList, result.getValue());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试query方法，当启用了数据查询验证，且用户有管理组权限
     */
    @Test
    @DisplayName("权限验证场景 - query方法启用数据查询验证")
    void testQueryWithDataQueryValidation() {
        // 准备测试数据
        List<String> objectAPINames = Arrays.asList("Object1", "Object2");
        List<String> departmentIds = Arrays.asList("Dept1", "Dept2");
        int scene = 1;
        int page = 1;
        int size = 20;

        List<DepartmentDataRights> mockRightsList = Arrays.asList(
                createDepartmentDataRights("1", "Object1", "Dept1"),
                createDepartmentDataRights("2", "Object1", "Dept2")
        );

        PageInfo mockPageInfo = new PageInfo();
        mockPageInfo.setCurrentPage(1);
        mockPageInfo.setPageSize(20);
        mockPageInfo.setTotalPage(1);
        mockPageInfo.setTotal(2);

        DepartmentDataRightsQueryAll.Result mockQueryResult = new DepartmentDataRightsQueryAll.Result();
        mockQueryResult.setPageInfo(mockPageInfo);
        mockQueryResult.setDeptRights(mockRightsList);

        // 配置Mock行为 - 启用权限验证
        when(dataPrivilegeCommonService.enableDataQueryByDescribeAndScopeValidation(testUser.getTenantId()))
                .thenReturn(true);

        ManageGroup manageGroup = new ManageGroup(
                false,
                ManageGroupType.OBJECT,
                "AccountObj",
                Sets.newHashSet("AccountObj")
        );
        when(manageGroupService.queryManageGroup(testUser, null, ManageGroupType.OBJECT, true))
                .thenReturn(manageGroup);

        when(dataPrivilegeCommonService.checkEntityIdsByManageGroup(objectAPINames, manageGroup))
                .thenReturn(Arrays.asList("Object1"));

        Set<String> deptAndOrgIds = Sets.newHashSet("Dept1");
        when(functionPrivilegeService.queryDeptAndOrgIdsByScope(testUser))
                .thenReturn(deptAndOrgIds);

        when(dataPrivilegeCommonService.checkDeptAndOrgIdsByManageScope(departmentIds, deptAndOrgIds))
                .thenReturn(Sets.newHashSet("Dept1"));

        when(rightsProxy.queryAll(anyMap(), any(DepartmentDataRightsQueryAll.Arg.class)))
                .thenReturn(mockQueryResult);

        // 执行被测试方法
        Tuple<PageInfo, List<DepartmentDataRights>> result = departmentDataRightsLogicService.query(
                testUser, objectAPINames, departmentIds, scene, page, size);

        // 验证Mock交互 - 验证过滤后的参数
        verify(rightsProxy, times(1)).queryAll(anyMap(), argThat(arg -> {
            assertEquals(Arrays.asList("Object1"), arg.getEntityIds());
            assertEquals(Arrays.asList("Dept1"), arg.getDeptIds());
            return true;
        }));

        // 验证结果
        assertEquals(mockPageInfo, result.getKey());
        assertEquals(mockRightsList, result.getValue());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试findByIds方法
     */
    @Test
    @DisplayName("正常场景 - findByIds方法根据ID查询")
    void testFindByIds() {
        // 准备测试数据
        int scene = 1;
        List<String> ids = Arrays.asList("id1", "id2");

        List<DepartmentDataRights> mockRightsList = Arrays.asList(
                createDepartmentDataRights("id1", "Object1", "Dept1"),
                createDepartmentDataRights("id2", "Object1", "Dept2")
        );

        // 配置Mock行为
        when(rightsProxy.queryByIds(anyMap(), any(DepartmentDataRightsQueryByIds.Arg.class)))
                .thenReturn(mockRightsList);

        // 执行被测试方法
        List<DepartmentDataRights> result = departmentDataRightsLogicService.findByIds(testUser, scene, ids);

        // 验证Mock交互
        verify(rightsProxy, times(1)).queryByIds(anyMap(), argThat(arg -> {
            assertEquals(testUser.getTenantId(), arg.getContext().getTenantId());
            assertEquals(testUser.getUserId(), arg.getContext().getUserId());
            assertEquals(ids, arg.getIds());
            assertEquals(scene, arg.getScene());
            return true;
        }));

        // 验证结果
        assertEquals(mockRightsList, result);
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试disableByIds方法
     */
    @Test
    @DisplayName("正常场景 - disableByIds方法禁用权限")
    void testDisableByIds() {
        // 准备测试数据
        int scene = 1;
        List<String> ids = Arrays.asList("id1", "id2");

        List<DepartmentDataRights> mockRightsList = Arrays.asList(
                createDepartmentDataRightsWithType("id1", "Object1", "Dept1", 0),
                createDepartmentDataRightsWithType("id2", "Object1", "Dept2", 1)
        );

        // 配置Mock行为
        when(rightsProxy.queryByIds(anyMap(), any(DepartmentDataRightsQueryByIds.Arg.class)))
                .thenReturn(mockRightsList);

        // 执行被测试方法
        departmentDataRightsLogicService.disableByIds(testUser, scene, ids);

        // 验证Mock交互
        verify(rightsProxy, times(1)).queryByIds(anyMap(), any());
        verify(rightsProxy, times(1)).upsert(anyMap(), argThat(arg -> {
            assertEquals(2, arg.getDeptRights().size());
            // 验证所有权限都被设置为禁用状态
            return arg.getDeptRights().stream().allMatch(rights -> rights.getStatus() == 1);
        }));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deleteByIds方法正常场景
     */
    @Test
    @DisplayName("正常场景 - deleteByIds方法删除已禁用权限")
    void testDeleteByIds() {
        // 准备测试数据
        int scene = 1;
        List<String> ids = Arrays.asList("id1", "id2");

        List<DepartmentDataRights> mockRightsList = Arrays.asList(
                createDepartmentDataRightsWithStatus("id1", "Object1", "Dept1", 1), // DISABLE_STATUS
                createDepartmentDataRightsWithStatus("id2", "Object2", "Dept2", 1)  // DISABLE_STATUS
        );

        // 配置Mock行为
        when(rightsProxy.queryByIds(anyMap(), any(DepartmentDataRightsQueryByIds.Arg.class)))
                .thenReturn(mockRightsList);

        // 执行被测试方法
        departmentDataRightsLogicService.deleteByIds(testUser, scene, ids);

        // 验证Mock交互
        verify(rightsProxy, times(1)).queryByIds(anyMap(), any());
        verify(rightsProxy, times(1)).delete(anyMap(), argThat(arg -> {
            assertEquals(scene, arg.getScene());
            assertEquals(2, arg.getDatas().size());
            assertEquals(Arrays.asList("id1"), arg.getDatas().get("Object1"));
            assertEquals(Arrays.asList("id2"), arg.getDatas().get("Object2"));
            return true;
        }));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试deleteByIds方法异常场景，包含启用状态的权限
     */
    @Test
    @DisplayName("异常场景 - deleteByIds方法包含启用状态权限时抛出异常")
    void testDeleteByIdsErrorWithEnabledRights() {
        // 准备测试数据
        int scene = 1;
        List<String> ids = Arrays.asList("id1", "id2");

        List<DepartmentDataRights> mockRightsList = Arrays.asList(
                createDepartmentDataRightsWithStatus("id1", "Object1", "Dept1", 1), // DISABLE_STATUS
                createDepartmentDataRightsWithStatus("id2", "Object2", "Dept2", 0)  // ENABLE_STATUS
        );

        // 配置Mock行为
        when(rightsProxy.queryByIds(anyMap(), any(DepartmentDataRightsQueryByIds.Arg.class)))
                .thenReturn(mockRightsList);

        // 执行被测试方法并验证异常 - 由于I18N初始化问题，实际抛出NullPointerException
        assertThrows(NullPointerException.class, () -> {
            departmentDataRightsLogicService.deleteByIds(testUser, scene, ids);
        });

        // 验证Mock交互
        verify(rightsProxy, times(1)).queryByIds(anyMap(), any());
        verify(rightsProxy, never()).delete(anyMap(), any());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试enable方法
     */
    @Test
    @DisplayName("正常场景 - enable方法启用权限")
    void testEnable() {
        // 准备测试数据
        int scene = 1;
        List<String> ids = Arrays.asList("id1", "id2");

        List<DepartmentDataRights> mockRightsList = Arrays.asList(
                createDepartmentDataRightsWithType("id1", "Object1", "Dept1", 0),
                createDepartmentDataRightsWithType("id2", "Object2", "Dept2", 1)
        );

        // 配置Mock行为
        when(rightsProxy.queryByIds(anyMap(), any(DepartmentDataRightsQueryByIds.Arg.class)))
                .thenReturn(mockRightsList);

        // 执行被测试方法
        departmentDataRightsLogicService.enable(testUser, scene, ids);

        // 验证Mock交互
        verify(rightsProxy, times(1)).queryByIds(anyMap(), any());
        verify(rightsProxy, times(1)).upsert(anyMap(), argThat(arg -> {
            assertEquals(2, arg.getDeptRights().size());
            // 验证所有权限都被设置为启用状态
            return arg.getDeptRights().stream().allMatch(rights -> rights.getStatus() == 0);
        }));
    }

    // 辅助方法
    private DepartmentDataRights createDepartmentDataRights(String id, String entityId, String deptId) {
        DepartmentDataRights rights = new DepartmentDataRights();
        rights.setId(id);
        rights.setEntityId(entityId);
        rights.setDeptId(deptId);
        return rights;
    }

    private DepartmentDataRights createDepartmentDataRightsWithType(String id, String entityId, String deptId, int type) {
        DepartmentDataRights rights = createDepartmentDataRights(id, entityId, deptId);
        rights.setType(type);
        return rights;
    }

    private DepartmentDataRights createDepartmentDataRightsWithStatus(String id, String entityId, String deptId, int status) {
        DepartmentDataRights rights = createDepartmentDataRights(id, entityId, deptId);
        rights.setStatus(status);
        return rights;
    }
} 