package com.facishare.paas.appframework.privilege;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.PermissionError;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.auth.common.exception.AuthException;
import com.facishare.paas.auth.model.AuthContext;
import com.fxiaoke.paas.auth.factory.FieldClient;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.util.List;
import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * GenerateByAI
 * 测试内容描述：测试FieldPrivilegeServiceImpl类的字段权限相关方法
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class FieldPrivilegeServiceImplTest {

    @Mock
    private FieldClient fieldClient;

    @InjectMocks
    private FieldPrivilegeServiceImpl fieldPrivilegeService;

    private User testUser;

    @BeforeEach
    void setUp() {
        testUser = User.builder()
            .tenantId("123456")
            .userId("78910")
            .build();
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试listUserEntitiesFiledPrivilege方法，正常获取用户实体字段权限
     */
    @Test
    @DisplayName("正常场景 - 测试listUserEntitiesFiledPrivilege方法")
    void testListUserEntitiesFiledPrivilege_NormalCase() {
        // 准备测试数据
        String tenantId = "123456";
        String userId = "78910";
        List<String> apiNames = Lists.newArrayList("TestObject1", "TestObject2");
        
        Map<String, Map<String, Integer>> expectedResult = Maps.newHashMap();
        Map<String, Integer> fieldPermissions = Maps.newHashMap();
        fieldPermissions.put("field1", 1);
        fieldPermissions.put("field2", 2);
        expectedResult.put("TestObject1", fieldPermissions);
        
        // 配置Mock行为
        when(fieldClient.userEntityIdsFieldPermission(any(AuthContext.class), anySet()))
            .thenReturn(expectedResult);
        
        // 执行被测试方法
        Map<String, Map<String, Integer>> result = fieldPrivilegeService.listUserEntitiesFiledPrivilege(tenantId, userId, apiNames);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(fieldClient).userEntityIdsFieldPermission(any(AuthContext.class), anySet());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试listUserEntitiesFiledPrivilege方法，AuthException异常场景
     */
    @Test
    @DisplayName("异常场景 - 测试listUserEntitiesFiledPrivilege方法AuthException")
    void testListUserEntitiesFiledPrivilege_AuthException() {
        try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
            
            // 准备测试数据
            String tenantId = "123456";
            String userId = "78910";
            List<String> apiNames = Lists.newArrayList("TestObject1");
            
            // 配置Mock行为
            when(fieldClient.userEntityIdsFieldPermission(any(AuthContext.class), anySet()))
                .thenThrow(new AuthException(403, "权限验证失败"));
            i18nMock.when(() -> I18N.text(I18NKey.PERMISSION_ERROR)).thenReturn("权限错误");
            
            // 执行并验证异常
            PermissionError exception = assertThrows(PermissionError.class, () -> {
                fieldPrivilegeService.listUserEntitiesFiledPrivilege(tenantId, userId, apiNames);
            });
            
            assertEquals("权限错误", exception.getMessage());
            assertEquals(403, exception.getErrorCode());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getUserFieldPrivilege方法（String参数版本），正常获取用户字段权限
     */
    @Test
    @DisplayName("正常场景 - 测试getUserFieldPrivilege方法String参数版本")
    void testGetUserFieldPrivilege_StringParams_NormalCase() {
        // 准备测试数据
        String tenantId = "123456";
        String userId = "78910";
        String apiName = "TestObject";
        
        Map<String, Integer> expectedResult = Maps.newHashMap();
        expectedResult.put("field1", 1);
        expectedResult.put("field2", 2);
        
        // 配置Mock行为
        when(fieldClient.userFieldPermission(any(AuthContext.class), eq(apiName)))
            .thenReturn(expectedResult);
        
        // 执行被测试方法
        Map<String, Integer> result = fieldPrivilegeService.getUserFieldPrivilege(tenantId, userId, apiName);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(fieldClient).userFieldPermission(any(AuthContext.class), eq(apiName));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getUserFieldPrivilege方法（User参数版本），正常获取用户字段权限
     */
    @Test
    @DisplayName("正常场景 - 测试getUserFieldPrivilege方法User参数版本")
    void testGetUserFieldPrivilege_UserParam_NormalCase() {
        // 准备测试数据
        String apiName = "TestObject";
        
        Map<String, Integer> expectedResult = Maps.newHashMap();
        expectedResult.put("field1", 1);
        expectedResult.put("field2", 2);
        
        // 配置Mock行为
        when(fieldClient.userFieldPermission(any(AuthContext.class), eq(apiName)))
            .thenReturn(expectedResult);
        
        // 执行被测试方法
        Map<String, Integer> result = fieldPrivilegeService.getUserFieldPrivilege(testUser, apiName);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(fieldClient).userFieldPermission(any(AuthContext.class), eq(apiName));
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getUserFieldPrivilege方法，AuthException异常场景
     */
    @Test
    @DisplayName("异常场景 - 测试getUserFieldPrivilege方法AuthException")
    void testGetUserFieldPrivilege_AuthException() {
        try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
            
            // 准备测试数据
            String apiName = "TestObject";
            
            // 配置Mock行为
            when(fieldClient.userFieldPermission(any(AuthContext.class), eq(apiName)))
                .thenThrow(new AuthException(403, "权限验证失败"));
            i18nMock.when(() -> I18N.text(I18NKey.PERMISSION_ERROR)).thenReturn("权限错误");
            
            // 执行并验证异常
            PermissionError exception = assertThrows(PermissionError.class, () -> {
                fieldPrivilegeService.getUserFieldPrivilege(testUser, apiName);
            });
            
            assertEquals("权限错误", exception.getMessage());
            assertEquals(403, exception.getErrorCode());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getUserNoExportFieldPrivilege方法，正常获取不支持导出的字段权限
     */
    @Test
    @DisplayName("正常场景 - 测试getUserNoExportFieldPrivilege方法")
    void testGetUserNoExportFieldPrivilege_NormalCase() {
        // 准备测试数据
        Set<String> objectApiNames = Sets.newHashSet("TestObject1", "TestObject2");
        
        Map<String, Set<String>> expectedResult = Maps.newHashMap();
        expectedResult.put("TestObject1", Sets.newHashSet("field1", "field2"));
        expectedResult.put("TestObject2", Sets.newHashSet("field3"));
        
        // 配置Mock行为
        when(fieldClient.notSupportExportFields(any(AuthContext.class), anyList()))
            .thenReturn(expectedResult);
        
        // 执行被测试方法
        Map<String, Set<String>> result = fieldPrivilegeService.getUserNoExportFieldPrivilege(testUser, objectApiNames);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(fieldClient).notSupportExportFields(any(AuthContext.class), anyList());
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试getUserNoExportFieldPrivilege方法，AuthException异常场景
     */
    @Test
    @DisplayName("异常场景 - 测试getUserNoExportFieldPrivilege方法AuthException")
    void testGetUserNoExportFieldPrivilege_AuthException() {
        try (MockedStatic<I18N> i18nMock = mockStatic(I18N.class)) {
            
            // 准备测试数据
            Set<String> objectApiNames = Sets.newHashSet("TestObject1");
            
            // 配置Mock行为
            when(fieldClient.notSupportExportFields(any(AuthContext.class), anyList()))
                .thenThrow(new AuthException(403, "权限验证失败"));
            i18nMock.when(() -> I18N.text(I18NKey.PERMISSION_ERROR)).thenReturn("权限错误");
            
            // 执行并验证异常
            PermissionError exception = assertThrows(PermissionError.class, () -> {
                fieldPrivilegeService.getUserNoExportFieldPrivilege(testUser, objectApiNames);
            });
            
            assertEquals("权限错误", exception.getMessage());
            assertEquals(403, exception.getErrorCode());
        }
    }

    /**
     * GenerateByAI
     * 测试内容描述：测试外部用户场景的buildAuthContext方法
     */
    @Test
    @DisplayName("边界场景 - 测试外部用户的字段权限获取")
    void testGetUserFieldPrivilege_OutUser() {
        // 准备测试数据
        User outUser = User.builder()
            .tenantId("123456")
            .userId("78910")
            .outUserId("20001")
            .outTenantId("10001")
            .build();
        String apiName = "TestObject";
        
        Map<String, Integer> expectedResult = Maps.newHashMap();
        expectedResult.put("field1", 1);
        
        // 配置Mock行为
        when(fieldClient.userFieldPermission(any(AuthContext.class), eq(apiName)))
            .thenReturn(expectedResult);
        
        // 执行被测试方法
        Map<String, Integer> result = fieldPrivilegeService.getUserFieldPrivilege(outUser, apiName);
        
        // 验证结果
        assertEquals(expectedResult, result);
        verify(fieldClient).userFieldPermission(any(AuthContext.class), eq(apiName));
    }
} 