package com.facishare.paas.appframework.privilege.util;

import com.facishare.paas.appframework.privilege.model.ObjectPrivilegeInfo;
import com.google.common.reflect.TypeToken;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FunctionInfoConstants 单元测试
 * 
 * <AUTHOR> Generated
 */
@ExtendWith(MockitoExtension.class)
class FunctionInfoConstantsTest {

  /**
   * GenerateByAI
   * 测试内容描述：测试静态初始化块的执行
   */
  @Test
  @DisplayName("正常场景 - 测试静态初始化块")
  void testStaticInitialization() {
    // 由于静态初始化在类加载时就已经执行，这里只验证基本访问
    assertDoesNotThrow(() -> {
      // 触发静态初始化（通过访问静态字段）
      Map<String, ObjectPrivilegeInfo> mapping = FunctionInfoConstants.apiNameObjectPrivilegeInfoMapping;

      // 验证结果
      assertNotNull(mapping);
      // 由于静态初始化只执行一次，我们主要验证映射不为null
      // 实际的映射内容可能已经在类加载时初始化了
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试映射字段的基本功能
   */
  @Test
  @DisplayName("正常场景 - 测试映射字段基本功能")
  void testMappingFieldBasicFunctionality() {
    // 由于静态初始化的复杂性，这里只测试基本的访问不抛出异常
    assertDoesNotThrow(() -> {
      // 验证静态字段不为null
      assertNotNull(FunctionInfoConstants.apiNameObjectPrivilegeInfoMapping);

      // 验证是Map类型
      assertTrue(FunctionInfoConstants.apiNameObjectPrivilegeInfoMapping instanceof Map);

      // 验证可以进行基本的Map操作
      Map<String, ObjectPrivilegeInfo> mapping = FunctionInfoConstants.apiNameObjectPrivilegeInfoMapping;
      assertNotNull(mapping.keySet());
      assertNotNull(mapping.values());
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试TypeToken的使用
   */
  @Test
  @DisplayName("正常场景 - 测试TypeToken功能")
  void testTypeTokenUsage() {
    // 这个测试主要验证TypeToken的创建不会抛出异常
    assertDoesNotThrow(() -> {
      // 创建一个新的TypeToken实例来测试
      TypeToken<java.util.List<ObjectPrivilegeInfo>> typeToken = new TypeToken<java.util.List<ObjectPrivilegeInfo>>(){};
      assertNotNull(typeToken.getType());
    });
  }

  /**
   * GenerateByAI
   * 测试内容描述：测试Gson解析功能
   */
  @Test
  @DisplayName("正常场景 - 测试Gson解析")
  void testGsonParsing() {
    // 准备测试数据
    String validJson = "[\n" +
      "  {\n" +
      "    \"apiName\": \"TestObject\",\n" +
      "    \"objectName\": \"测试对象\"\n" +
      "  }\n" +
      "]";
    
    // 测试Gson解析
    assertDoesNotThrow(() -> {
      Gson gson = new GsonBuilder().create();
      TypeToken<java.util.List<ObjectPrivilegeInfo>> typeToken = new TypeToken<java.util.List<ObjectPrivilegeInfo>>(){};
      java.util.List<ObjectPrivilegeInfo> result = gson.fromJson(validJson, typeToken.getType());
      assertNotNull(result);
    });
  }
}
