package com.facishare.paas.appframework.coordination.dto;

import io.protostuff.Tag;
import lombok.Data;

import java.util.List;

/**
 * Created by yuanjl on 2019/5/13
 */
public interface FeedsExternalResourceDto {
  @Data
  class MergeResourcesArg {
    @Tag(1)
    Integer source;
    @Tag(2)
    List<String> abandonedResourceIds;
    @Tag(3)
    MergeTarget mergeTarget;
  }

  @Data
  class MergeTarget {
    @Tag(1)
    String id;
    @Tag(2)
    String data;
  }

  @Data
  class MergeResourcesResult {
    @Tag(1)
    Boolean success;
  }
}
