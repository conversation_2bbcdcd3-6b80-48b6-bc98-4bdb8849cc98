package com.facishare.paas.appframework.metadata.dto.auth;

import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.Singular;

import java.util.List;

/**
 * Created by liyiguang on 2017/11/24.
 */
public interface BatchFindRecordType {

    @EqualsAndHashCode(callSuper = true)
    @Data
    @Builder
    class Arg extends BaseAuthArg {
        @Singular
        private List<String> entityIds;
        @Singular
        private List<String> roleCodes;
    }

}
