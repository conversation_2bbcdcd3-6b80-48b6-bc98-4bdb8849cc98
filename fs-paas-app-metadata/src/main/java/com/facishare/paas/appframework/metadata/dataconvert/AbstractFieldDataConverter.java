package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.common.exception.CrmDefObjCheckedException;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.multiRegion.MultiRegionDateTimeFormatUtils;
import com.facishare.paas.timezone.DateTimeFormatUtils;
import com.ibm.icu.util.ULocale;
import lombok.Data;
import org.apache.commons.lang.StringUtils;

/**
 * Created by linqiuying on 17/5/18.
 */
@Data
public abstract class AbstractFieldDataConverter {
    private IFieldDescribe fieldDescribe;
    private IObjectData objectData;
    private IObjectDataProxyService dataService;

    public abstract String convertFieldData(SessionContext sessionContext) throws CrmDefObjCheckedException, MetadataServiceException;

    protected String format(Object o, String type, SessionContext sessionContext) {
        if (StringUtils.isNotBlank(sessionContext.getRegion())) {
            return MultiRegionDateTimeFormatUtils.formatForRegion(o, sessionContext.getTimeZone(), type, new ULocale(sessionContext.getRegion()));
        }
        if (sessionContext.isDateFormatIncludeTimezoneInfo()) {
            return DateTimeFormatUtils.formatWithTimezoneInfo(o, sessionContext.getTimeZone(), type);
        }
        return DateTimeFormatUtils.format(o, sessionContext.getTimeZone(), type);
    }
}
