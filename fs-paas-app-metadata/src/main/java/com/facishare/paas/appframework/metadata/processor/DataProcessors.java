package com.facishare.paas.appframework.metadata.processor;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by zhouwr on 2018/3/6
 */
@Component("dataProcessors")
public class DataProcessors implements DataProcessor<IObjectDescribe, List<IObjectData>> {

    @Autowired
    private FileAttachmentProcessor fileAttachmentProcessor;

    @Autowired
    private CustomImageProcessor customImageProcessor;

    @Autowired
    private SfaImageProcessor sfaImageProcessor;

    private Map<String, DataProcessor> customProcessorMap = Maps.newHashMap();

    private Map<String, DataProcessor> sfaProcessorMap = Maps.newHashMap();

    @PostConstruct
    public void init() {
        customProcessorMap.put(IFieldType.FILE_ATTACHMENT, fileAttachmentProcessor);
        customProcessorMap.put(IFieldType.IMAGE, customImageProcessor);
        customProcessorMap.put(IFieldType.SIGNATURE, customImageProcessor);

        sfaProcessorMap.put(IFieldType.FILE_ATTACHMENT, fileAttachmentProcessor);
        sfaProcessorMap.put(IFieldType.IMAGE, sfaImageProcessor);
    }

    @Override
    public void process(IObjectDescribe describe, List<IObjectData> dataList) {
        if (CollectionUtils.empty(dataList)) {
            return;
        }
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        for (IObjectData objectData : dataList) {
            Map<String, Object> dataMap = Maps.newHashMap(ObjectDataExt.of(objectData).toMap());
            for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                if (Objects.isNull(entry.getValue())) {
                    continue;
                }
                describeExt.getFieldDescribeSilently(entry.getKey()).ifPresent(x -> {
                    DataProcessor dataProcessor = getDataProcessor(describe, x);
                    if (dataProcessor != null) {
                        dataProcessor.process(x, objectData);
                    }
                });
            }
        }
    }

    private DataProcessor getDataProcessor(IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe) {
        return customProcessorMap.get(fieldDescribe.getType());
    }
}
