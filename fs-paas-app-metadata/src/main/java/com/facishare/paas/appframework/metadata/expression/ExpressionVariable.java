package com.facishare.paas.appframework.metadata.expression;

/**
 * 表达式中用的变量
 * <p>
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2018/4/23.
 */
public interface ExpressionVariable {
    String getName();

    String getBindingName();

    Object getBindingValue(boolean nullAsZero, boolean isFormula, ExpressionDataType returnType, boolean useValue, String tenantId);

    ExpressionDataType getDataType();

    void setValue(Object object);
}
