package com.facishare.paas.appframework.metadata.initscene;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.util.SearchTemplateUtil;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.impl.search.Filter;
import com.facishare.rest.core.util.JsonUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.lang.reflect.Type;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class ApprovalTaskInitSceneProvider extends DefaultSceneProvider {


    @Override
    public String getApiName() {
        return Utils.APPROVAL_TASK_API_NAME;
    }

    @Override
    public List<ISearchTemplate> getDefaultSearchTemplateList(User user, String apiName, String extendAttribute) {
        Map<String, String> filterMaps = Maps.newLinkedHashMap();
        filterMaps.put("in_progress", "[{\"isIndex\": false, \"fieldNum\": 0, \"operator\": \"EQ\", \"connector\": \"AND\", \"field_name\": \"object_describe_api_name\", \"field_values\": [\"ApprovalTaskObj\"], \"isObjectReference\": false}, {\"isIndex\": false, \"fieldNum\": 0, \"operator\": \"EQ\", \"connector\": \"AND\", \"field_name\": \"state\", \"field_values\": [\"in_progress\"], \"isObjectReference\": false}, {\"isIndex\": false, \"fieldNum\": 0, \"operator\": \"NIN\", \"connector\": \"AND\", \"field_name\": \"dealed_persons\", \"field_values\": [\"${current_user}\"], \"isObjectReference\": false}, {\"isIndex\": false, \"fieldNum\": 0, \"operator\": \"IN\", \"connector\": \"AND\", \"field_name\": \"relevant_team.teamMemberEmployee\", \"field_values\": [\"${current_user}\"], \"isObjectReference\": false}]");
        filterMaps.put("pass", "[ { \"field_name\": \"object_describe_api_name\", \"field_values\": [ \"ApprovalTaskObj\"], \"operator\": \"EQ\", \"connector\": \"AND\", \"fieldNum\": 0, \"isObjectReference\": false, \"isIndex\": false}, { \"field_name\": \"dealed_persons\", \"field_values\": [ \"${current_user}\"], \"operator\": \"IN\", \"connector\": \"AND\", \"fieldNum\": 0, \"isObjectReference\": false, \"isIndex\": false}]");

        List<ISearchTemplate> defaultSearchTemplates = Lists.newArrayList();

        Type filterType = new TypeToken<List<Filter>>() {
        }.getType();

        filterMaps.forEach((api_name, filterString) -> {
            ISearchTemplate template = SearchTemplateUtil.getSearchTemplate(
                    user.getTenantId(),
                    apiName,
                    getLabel(api_name),
                    "in_progress".equals(api_name),
                    JsonUtil.fromJson(filterString, filterType));
            template.setApiName(api_name);
            defaultSearchTemplates.add(template);
        });
        return defaultSearchTemplates;
    }

    public String getLabel(String api_name) {
        Map<String, String> labelMapping = Maps.newHashMap();
        labelMapping.put("in_progress", I18N.text(I18NKey.APPROVAL_NEED_DEAL_WITH));//待办
        labelMapping.put("pass", I18N.text(I18NKey.APPROVAL_HAVE_DONE));//已办
        return labelMapping.get(api_name);
    }
}



