package com.facishare.paas.appframework.metadata.publicobject.module;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2023/12/19
 */
@Slf4j
public enum PublicFieldType {
    PRIVATE_FIELD("private_field", I18NKey.PRIVATE_FIELD) {
        @Override
        public boolean verify(IObjectDescribe objectDescribe, String fieldName) {
            return !ObjectDescribeExt.of(objectDescribe)
                    .getPublicField(fieldName)
                    .isPresent();
        }

        @Override
        protected boolean canConvert(PublicFieldType newPublicFieldType) {
            return true;
        }

        @Override
        protected String belongTenant(String tenantId) {
            return tenantId;
        }

        @Override
        public boolean dependentOn(PublicFieldType publicFieldType) {
            return true;
        }

    },
    PUBLIC_FIELD_PRIVATE_DATA("public_field_private_data", I18NKey.PUBLIC_FIELD_PRIVATE_DATA) {
        @Override
        public boolean verify(IObjectDescribe objectDescribe, String fieldName) {
            return ObjectDescribeExt.of(objectDescribe)
                    .getPublicFieldPrivateData(fieldName)
                    .isPresent();
        }

        @Override
        protected String belongTenant(String tenantId) {
            return IFieldDescribe.PUBLIC_FIELD_PRIVATE_DATA_BELONG_TENANT;
        }

        @Override
        public boolean dependentOn(PublicFieldType publicFieldType) {
            return PRIVATE_FIELD != publicFieldType;
        }
    },
    PUBLIC_FIELD_PUBLIC_DATA("public_field_public_data", I18NKey.PUBLIC_FIELD_PUBLIC_DATA) {
        @Override
        public boolean verify(IObjectDescribe objectDescribe, String fieldName) {
            return ObjectDescribeExt.of(objectDescribe)
                    .getPublicFieldPublicData(fieldName)
                    .isPresent();
        }

        @Override
        protected String belongTenant(String tenantId) {
            return null;
        }

        @Override
        public boolean dependentOn(PublicFieldType publicFieldType) {
            return PUBLIC_FIELD_PUBLIC_DATA == publicFieldType;
        }
    },
    ;
    @Getter
    private final String type;

    private final String i18nKey;

    PublicFieldType(String type, String i18nKey) {
        this.type = type;
        this.i18nKey = i18nKey;
    }

    private static final Map<String, PublicFieldType> map = Stream.of(values())
            .collect(Collectors.toMap(PublicFieldType::getType, Function.identity()));

    public static PublicFieldType fromType(String type) {
        return map.get(type);
    }

    public static PublicFieldType fromFieldDescribe(IObjectDescribe objectDescribe, String fieldName) {
        return Stream.of(values()).filter(it -> it.verify(objectDescribe, fieldName)).findFirst()
                .orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR)));
    }

    public abstract boolean verify(IObjectDescribe objectDescribe, String fieldName);

    protected boolean canConvert(PublicFieldType newPublicFieldType) {
        return this == newPublicFieldType;
    }

    protected abstract String belongTenant(String tenantId);

    public String getName() {
        return I18NExt.text(i18nKey);
    }

    public String getI18nKey() {
        return i18nKey;
    }

    /**
     * 私有字段可以依赖字段
     * 公共字段私有数据可以依赖公共字段私有数据和公共字段共有数据
     * 公共字段共有数据只可以依赖公共字段共有数据
     *
     * @param publicFieldType
     * @return
     */
    public abstract boolean dependentOn(PublicFieldType publicFieldType);
}
