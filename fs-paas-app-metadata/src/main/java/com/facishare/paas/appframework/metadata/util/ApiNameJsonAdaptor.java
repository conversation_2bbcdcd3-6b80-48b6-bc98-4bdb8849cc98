package com.facishare.paas.appframework.metadata.util;

import com.facishare.crm.common.exception.CrmCheckedException;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.github.autoconf.ConfigFactory;
import com.google.common.collect.BiMap;
import com.google.common.collect.HashBiMap;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.*;

import static com.facishare.crm.common.exception.CRMErrorCode.FS_CRM_COMMON_RUNTIME_CONFIG_CENTER_FILE_NULL;

/**
 * Created by lilei on 2017/7/21.
 */
@Slf4j
public class ApiNameJsonAdaptor {


    // 定义两种apiName转换算法模式:  第1种:  "abc" -> "bcd" ; 第2种: "abc": -> "bcd":
    private static HashMap<String,String> describes_compactTrieStr_Mode1_Map = new HashMap<String,String>();
    private static HashMap<String,String> describes_compactTrieStr_Mode2_Map = new HashMap<String,String>();
    // 定义两种方向:describe中的老名字转换成新名字;describe中的新名字转换成老名字。
    private static HashMap<String, HashMap> describes_apiNameConvert_Mode2_o2n_Map = Maps.newHashMap();
    private static HashMap<String, HashMap> describes_apiNameConvert_Mode2_n2o_Map = Maps.newHashMap();
    private static HashMap<String, HashMap> describes_apiNameConvert_Mode1_n2o_Map = Maps.newHashMap();
    private static HashMap<String, HashMap> describes_apiNameConvert_Mode1_o2n_Map = Maps.newHashMap();
    // 定义支持的describeApiName列表和支持的自定义字段所有的枚举。
    private static String[] describesApiNames;
    private static String UDMap_50_str; //UDMap_50_str=UDSText,UDMText,UDInt
    private static String UDMap_20_str; //UDMap_20_str=UDDec,UDMoney,UDDate,UDSSel,UDMSel,UDImg,UDCSSel,UDMail,UDTel
    static {
        ConfigFactory.getInstance().getConfig("fs-crm-java-apiName-o2n", config -> {

            try {
                //jsonConvert_describesApiNames=ProductObj,SalesOrderObj,SalesOrderProductObj,AccountObj,ContactObj,LeadsObj,PaymentObj
                String jsonConvert_describesApiNames = config.get("jsonConvert_describesApiNames").trim();
                UDMap_50_str = config.get("UDMap_50_str").trim();
                UDMap_20_str = config.get("UDMap_20_str").trim();
                describesApiNames = jsonConvert_describesApiNames.split(",");
            }catch (Exception e){
                log.warn("ApiNameJsonAdaptor reload config fail", e);
            }

            //为每个describe都生成两种模式的匹配树str。以便后面根据不同的使用场景来使用。
            for(String describeApiName : describesApiNames){
                // 为了使用CompactTrie的算法,必须针对不同的describe生成特定格式的CompactTrieStr。
                // mode1的compactTrieStrMode1:"product_status":@@"Status":||"category":@@"Category":
                // mode12的compactTrieStrMode2:"product_status"@@"Status"||"category"@@"Category"
                String compactTrieStrMode1 = generateCompactTrieStr(config.get(describeApiName).trim(), UDMap_50_str, UDMap_20_str,1);
                String compactTrieStrMode2 = generateCompactTrieStr(config.get(describeApiName).trim(), UDMap_50_str, UDMap_20_str,2);
                describes_compactTrieStr_Mode1_Map.put(describeApiName, compactTrieStrMode1);
                describes_compactTrieStr_Mode2_Map.put(describeApiName,compactTrieStrMode2);
            }
            initApiNameMapping();
        });
    }

    private static void initApiNameMapping() {
        //为每个describe都生成三种场景的转换apiName转换mapping。
        for(String describeApiName : describesApiNames){
            describes_apiNameConvert_Mode1_o2n_Map.put(describeApiName,getMapFromStr(describes_compactTrieStr_Mode1_Map.get(describeApiName), "||", "@@"));
            describes_apiNameConvert_Mode1_n2o_Map.put(describeApiName,ReverseHashMap(getMapFromStr(describes_compactTrieStr_Mode1_Map.get(describeApiName), "||", "@@")));
            describes_apiNameConvert_Mode2_o2n_Map.put(describeApiName,getMapFromStr(describes_compactTrieStr_Mode2_Map.get(describeApiName), "||", "@@"));
            describes_apiNameConvert_Mode2_n2o_Map.put(describeApiName,ReverseHashMap(getMapFromStr(describes_compactTrieStr_Mode2_Map.get(describeApiName), "||", "@@")));
        }
    }

    private static HashMap<String,String> ReverseHashMap(HashMap<String,String> map){
        HashMap<String,String> result = Maps.newHashMap();
        map.forEach((x,y)-> result.put(y,x));
        return result;
    }

    public static String convertApiNamedJson(String str,String apiName,HashMap<String, HashMap> map) throws CrmCheckedException {
        if(map.size() == 0){
            throw new CrmCheckedException(FS_CRM_COMMON_RUNTIME_CONFIG_CENTER_FILE_NULL,"fs-crm-java-apiName-o2n config has error");
        }
        Map<String, String> apiNameMapping = map.get(apiName);
        if(apiNameMapping==null){
            throw new CrmCheckedException(FS_CRM_COMMON_RUNTIME_CONFIG_CENTER_FILE_NULL,"fs-crm-java-apiName-o2n config has error, doesn't have information"
                    + "for ApiName:"+apiName+" in jsonConvert_describesApiNames part.");
        }
        CompactTrie trieTree = new CompactTrie(apiNameMapping.keySet());
        String replaceResult = trieTree.replaceAll(str, apiNameMapping);
        return replaceResult;
    }

    /**
     *
     * @param str 拥有新apiName的字符串
     * @param describeApiName 对象apiName
     * @param mode 1: "old" -> "new" 模式 2: "old": -> "new": 注意是带着双引号一起替换。
     * @return
     * @throws CrmCheckedException
     */
    public static String convertToOldApiNamedJson(String str,String describeApiName,int mode) throws CrmCheckedException {
        return convertApiNamedJson(str,describeApiName, (mode==1? describes_apiNameConvert_Mode1_n2o_Map : describes_apiNameConvert_Mode2_n2o_Map));
    }

    /**
     *
     * @param str 拥有老apiName的字符串
     * @param describeApiName 对象apiName
     * @param mode 模式 1: "old" -> "new" 2: "old": -> "new":注意是带着双引号一起替换。
     * @return
     * @throws CrmCheckedException
     */
    public static String convertToNewApiNamedJson(String str,String describeApiName,int mode) throws CrmCheckedException {
        return convertApiNamedJson(str,describeApiName, (mode==1?describes_apiNameConvert_Mode1_o2n_Map:describes_apiNameConvert_Mode2_o2n_Map));
    }

    public static String convert(String str,String apiName,Map<String,String> extensionCovertMap) throws CrmCheckedException {
        Map<String, String> apiNameMapping = describes_apiNameConvert_Mode2_o2n_Map.get(apiName);
        if (null == apiNameMapping){
            apiNameMapping = Maps.newHashMap();
        }
        apiNameMapping.putAll(extensionCovertMap);

        if(CollectionUtils.empty(apiNameMapping)){
            throw new CrmCheckedException(FS_CRM_COMMON_RUNTIME_CONFIG_CENTER_FILE_NULL,"fs-crm-java-apiName-o2n config has error, doesn't have information"
                    + "for ApiName:"+apiName+" in jsonConvert_describesApiNames part.");
        }
        CompactTrie trieTree = new CompactTrie(apiNameMapping.keySet());
        String replaceResult = trieTree.replaceAll(str, apiNameMapping);
        return replaceResult;

    }


    public static HashMap getOldApiNameToNewApiNameMap(String describeApiName){
        return describes_apiNameConvert_Mode1_o2n_Map.get(describeApiName);
    }


    /**
     * 根据双层分隔符将字符串转换为map
     *
     * @param str
     * @param outerSeparator
     * @param innerSeperator
     * @return
     */
    private static HashMap<String, String> getMapFromStr(String str, String outerSeparator, String innerSeperator) {
        HashMap<String, String> map = new HashMap<String, String>();
        if (StringUtils.isNotEmpty(str) && StringUtils.isNotEmpty(outerSeparator) && StringUtils.isNotEmpty(innerSeperator)) {
            List<String> pairList = Arrays.asList(StringUtils.split(str, outerSeparator));
            for (String pair : pairList) {
                try {
                    String[] pairArray = StringUtils.split(pair, innerSeperator);
                    if (pairArray != null && pairArray.length == 2) {
                        map.put(pairArray[0], pairArray[1]);
                    }
                } catch (Exception e) {
                    log.error("Error when getMapFromStr: " + pair, e);
                }
            }
        }
        return map;
    }

    public static String generateCompactTrieStr(String Obj_str,String UDMap_50_str,String UDMap_20_str,int mode){
        return generateUDStr(UDMap_50_str,UDMap_20_str,mode)+"||"+convertMapStr2CompactTrieStr(Obj_str,mode);
    }

    //将形为"product_status":@@"Status":||"category":@@"Category":的str,转成
    public static String convertMapStr2CompactTrieStr(String str,int mode){
        StringBuilder result= new StringBuilder();
        String [] splitStr = str.split(",");
        for(int i = 0; i < splitStr.length; i++){
            String key = splitStr[i].split(":")[0];
            String key_new = splitStr[i].split(":")[1];
            //"key":@@"key_new":||
            if(mode == 1){
                result.append("\"").append(key).append("\"@@\"").append(key_new).append("\"");
            }
            else {
                result.append("\"").append(key).append("\":@@\"").append(key_new).append("\":").append("||");
                result.append("\"").append(key).append("\" :@@\"").append(key_new).append("\" :").append("||");
                result.append("\"").append(key).append("\"  :@@\"").append(key_new).append("\"  :");
            }
            if(i != splitStr.length -1){
                result.append("||");
            }
        }
        return result.toString().replace("\r\n","").replace("\n","").trim();
    }

    public static String generateUDStr(String UDMap_50_str,String UDMap_20_str,int mode){
        String result_tmp = "";
        String [] split50 = UDMap_50_str.split(",");
        String [] split20 = UDMap_20_str.split(",");
        for(int i = 0 ; i < 51; i++){
            for(String UDmap_str : split50){
                result_tmp += ((UDmap_str+i) +":" + (String.valueOf(UDmap_str+i+"__c"+",")));
            }
        }
        for(int i = 0 ; i < 21 ;i++){
            for(String UDmap_str : split20){
                result_tmp += ((UDmap_str+i) +":" + (String.valueOf(UDmap_str+i+"__c"+",")));
            }
        }
        result_tmp = result_tmp.replace("\r\n","").replace("\n","").trim();
        String result = convertMapStr2CompactTrieStr(result_tmp,mode);
        return result;
    }

}
