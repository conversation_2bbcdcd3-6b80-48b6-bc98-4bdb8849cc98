package com.facishare.paas.appframework.metadata.button;

import com.facishare.paas.appframework.core.rest.BaseAPIResult;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.LayoutButtonExt;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.collect.Lists;
import lombok.*;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public interface GetSpecialButtons {

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg {
        String describeApiName;
    }

    @Data
    class Result {
        List<Map> buttons;

        public List<IButton> convertIButtons() {
            if (CollectionUtils.empty(buttons)) {
                return Lists.newArrayList();
            }
            return buttons.stream().map(x -> LayoutButtonExt.of(x).getButton()).collect(Collectors.toList());
        }

        public static Result of(List<IButton> ibuttons) {
            Result result = new Result();
            result.setButtons(CollectionUtils.nullToEmpty(ibuttons).stream().map(x -> LayoutButtonExt.of(x).toMap()).collect(Collectors.toList()));
            return result;
        }
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class RestResult extends BaseAPIResult {
        Result data;
    }

}
