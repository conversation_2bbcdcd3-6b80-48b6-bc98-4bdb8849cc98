package com.facishare.paas.appframework.metadata.richtext;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

public interface RichTextService {
    void convertPathForHtmlRichText(User user, IObjectData objectData, IObjectDescribe describe);
    void convertPathForCooperativeRichText(User user, IObjectData objectData, IObjectDescribe describe);
}
