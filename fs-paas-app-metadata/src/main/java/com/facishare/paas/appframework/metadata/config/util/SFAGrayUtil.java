package com.facishare.paas.appframework.metadata.config.util;

import com.fxiaoke.release.FsGrayRelease;
import com.fxiaoke.release.FsGrayReleaseBiz;

public class SFAGrayUtil {
    private static final FsGrayReleaseBiz gray = FsGrayRelease.getInstance("sfa");
    private static final String ORDER_PROMOTION = "order_promotion";
    private static final String AVAILABLE_RANGE_LIMIT_IGNORE = "available_range_limit_ignore";
    /**
     * 灰度允许产品导入时更新产品的负责人
     */
    private static final String ALLOW_PRODUCT_IMPORT_UPDATE_OWNER = "allow_product_import_update_owner";

    public static Boolean isGrayOrderPromotion(String tenantId) {
        return gray.isAllow(ORDER_PROMOTION, tenantId);
    }

    public static Boolean isIgnoreAvailableRangeLimit(String tenantId) {
        return gray.isAllow(AVAILABLE_RANGE_LIMIT_IGNORE, tenantId);
    }

    public static Boolean allowProductImportUpdateOwner(String tenantId) {
        return gray.isAllow(ALLOW_PRODUCT_IMPORT_UPDATE_OWNER,tenantId);
    }
}
