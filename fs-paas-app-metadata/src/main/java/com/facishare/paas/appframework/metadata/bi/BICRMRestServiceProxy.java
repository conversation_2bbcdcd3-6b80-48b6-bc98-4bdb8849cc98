package com.facishare.paas.appframework.metadata.bi;

import com.facishare.paas.appframework.metadata.dto.CrossFilterSupportedObjects;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * BI-CRM服务接口代理
 * 
 * <AUTHOR>
 */
@RestResource(
        value = "BI-CRM",
        desc = "BI-CRM服务", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.metadata.util.CRMRestServiceCodec")
public interface BICRMRestServiceProxy {

    /**
     * 获取支持跨对象筛选的对象列表
     *
     * @param headers 请求头，包含租户ID、用户ID等信息
     * @param arg 请求参数，包含CRM对象名称列表
     * @return 验证结果，包含验证通过的CRM对象名称列表
     */
    @POST(value = "/api/v1/obj/validate", desc = "获取支持跨对象筛选的对象列表")
    CrossFilterSupportedObjects.RestResult getCrossFilterSupportedObjects(@HeaderMap Map<String, String> headers,
                                          @Body CrossFilterSupportedObjects.Arg arg);
} 
