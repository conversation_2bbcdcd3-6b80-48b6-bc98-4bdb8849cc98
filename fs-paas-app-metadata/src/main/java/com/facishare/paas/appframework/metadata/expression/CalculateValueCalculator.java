package com.facishare.paas.appframework.metadata.expression;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.AppBusinessException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.expression.ExpressionService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Number;
import com.facishare.paas.metadata.api.describe.Percentile;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Map;

/**
 * Created by zhouwr on 2019/3/21
 */
@Slf4j
public class CalculateValueCalculator extends AbstractFieldCalculator {

    private IFieldDescribe field;

    public CalculateValueCalculator(ExpressionService service, IObjectDescribe objectDescribe,
                                    IFieldDescribe field, boolean ignoreInvalidVariable) {
        super(service, createExpression(objectDescribe, field, ignoreInvalidVariable), objectDescribe);
        this.field = field;
    }

    @Override
    public void doCalculate(IObjectData data, Map<String, Object> globalVariableData, Map<String, Map<String, IObjectData>> objectDataMap,
                            Map<String, Object> extData, boolean isLookupDependencyForOthers) {
        try {
            super.doCalculate(data, globalVariableData, objectDataMap, extData, isLookupDependencyForOthers);

            if (result != null && result instanceof BigDecimal) {
                BigDecimal ret = (BigDecimal) result;
                result = ret.toPlainString();
            }
        } catch (AppBusinessException e) {
            log.warn("calculate field value failed:{},tenantId:{},objectApiName:{},fieldApiName:{},fieldDescribe:{}",
                    e.getMessage(), objectDescribe.getTenantId(), objectDescribe.getApiName(), field.getApiName(), field);
            result = NA;
        } catch (Exception e) {
            log.error("calculate field value error:{},tenantId:{},objectApiName:{},fieldApiName:{},fieldDescribe:{},expression:{},globalVariableData:{},objectDataMap:{}",
                    e.getMessage(), objectDescribe.getTenantId(), objectDescribe.getApiName(), field.getApiName(),
                    field, expression, globalVariableData, objectDataMap, e);
            result = NA;
        }
    }

    @Override
    public String key() {
        return field.getApiName();
    }

    public static Expression createExpression(IObjectDescribe describe, IFieldDescribe field) {
        return createExpression(describe, field, false);
    }

    public static Expression createExpression(IObjectDescribe describe, IFieldDescribe field, boolean ignoreInvalidVariable) {
        int decimalPlaces = 2;
        if (field instanceof Number) {
            decimalPlaces = ((Number) field).getDecimalPlaces();
        }

        if (field instanceof Percentile) {
            decimalPlaces = 4;
        }

        Expression expression = Expression.builder()
                .expression(field.getExpression() == null ? "" : field.getExpression())
                .describe(describe)
                .returnType(field.getType())
                .decimalPlaces(decimalPlaces)
                .nullAsZero(field.getDefaultToZero() == null ? true : field.getDefaultToZero())
                .useValue(Boolean.TRUE.equals(field.getIsUseValue()))
                .fieldName(field.getApiName())
                .expressionLabel(I18N.text(I18NKey.FIELD_SPECIFY, describe.getDisplayName() + "." + field.getLabel()))
                .ignoreInvalidVariable(ignoreInvalidVariable)
                .build();
        return expression;
    }
}
