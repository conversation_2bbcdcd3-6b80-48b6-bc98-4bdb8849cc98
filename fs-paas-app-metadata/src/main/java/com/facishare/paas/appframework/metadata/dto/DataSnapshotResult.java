package com.facishare.paas.appframework.metadata.dto;

import com.google.common.collect.Maps;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

@Data
@NoArgsConstructor
public class DataSnapshotResult {
    private ObjectDataSnapshot snapshot;
    private Map<String, Object> diffMap;

    public static DataSnapshotResult of(ObjectDataSnapshot snapshot, Map<String, Object> diffMap) {
        DataSnapshotResult result = new DataSnapshotResult();
        result.setSnapshot(snapshot);
        result.setDiffMap(diffMap);
        return result;
    }

    public Map<String, Object> getMergedMasterSnapshot() {
        Map<String, Object> result = Maps.newHashMap(snapshot.getMasterSnapshot());
        result.putAll(diffMap);
        return result;
    }
}
