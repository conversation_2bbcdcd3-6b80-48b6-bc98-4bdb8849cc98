package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import static com.facishare.crm.openapi.Utils.BATCH_API_NAME;

/**
 * 物料-版本
 * create by <PERSON><PERSON><PERSON> on 2019/03/31
 */
@Component
public class BatchObject<PERSON>rovider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return BATCH_API_NAME;
    }

    @Override
    protected int getDuplicateJudgmentType(IObjectDescribe objectDescribe) {
        return UNSUPPORT_UPDATE_IMPORT;
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_UPDATE_IMPORT;
    }
}
