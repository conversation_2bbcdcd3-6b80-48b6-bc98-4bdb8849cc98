package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.ImportObjectModule;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2021/03/16
 */
public interface ImportObjectProvider {

    String getObjectModule();

    List<ImportObjectModule.ImportModule> getImportObjectModule(User user, ImportModuleContext context);

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor(staticName = "of")
    class ImportModuleContext {
        private Boolean management;
    }
}
