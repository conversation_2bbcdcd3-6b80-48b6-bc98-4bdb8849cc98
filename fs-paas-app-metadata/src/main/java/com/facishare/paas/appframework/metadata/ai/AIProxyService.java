package com.facishare.paas.appframework.metadata.ai;

import com.alibaba.fastjson.JSONObject;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.dto.ChatComplete;
import com.facishare.paas.appframework.metadata.dto.ai.AIDto;
import com.facishare.paas.appframework.metadata.dto.ai.AIProxy;
import com.facishare.paas.appframework.metadata.dto.ai.DataQuery;
import com.facishare.paas.appframework.metadata.dto.ai.GenerateSearchQuery;
import com.facishare.paas.metadata.api.search.ISearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/2/28
 * @Description :
 */
@Slf4j
@Service
public class AIProxyService {

    String FORMULA_BIZ_NAME = "paas_formula_generate";

    @Resource
    private AiRestServiceProxy aiRestServiceProxy;

    AIProxy.ChatResult chatComplete(User user, AIProxy.ChatArg arg) {
        ChatComplete.Result result = aiRestServiceProxy.chatCompleteLongTime(user.getTenantId(), user.getUserId(), FORMULA_BIZ_NAME, arg);
        if (result.getErrCode() == 0) {
            log.debug("answer:{}", result.getResult().getMessage());
        } else {
            log.info("result: {}", result);
        }
        return AIProxy.ChatResult.builder()
                .message(result.getResult().getMessage())
                .build();
    }

    AIDto.Ret<List<JSONObject>> queryFields(User user, AIProxy.TopNArg arg) {
        return aiRestServiceProxy.queryFields(user.getTenantId(), user.getUserId(), FORMULA_BIZ_NAME, arg);
    }

    public SearchTemplateQueryExt generateSearchQuery(User user, GenerateSearchQuery.Arg arg) {
        DataQuery.Result result = aiRestServiceProxy.generateSearchQuery(user.getTenantId(), user.getUserId(), buildGenerateSearchQueryArg(arg));
        if (!result.isSuccess()) {
            log.warn("generateSearchQuery fail! message:{}", result.getErrMessage());
            throw new ValidateException(I18NExt.text(I18NKey.NATURAL_LANGUAGE_TO_SEARCHQUERY_FAIL));
        }
        String objectApiName = result.getResult().getObjectApiName();
        if (Objects.equals(objectApiName, arg.getBindingObjectApiName())) {
            log.warn("bindingObjectApiName not effective！describeApiName:{}, bindingObjectApiName:{}", objectApiName, arg.getBindingObjectApiName());
        }
        ISearchTemplateQuery searchTemplateQuery = result.searchTemplateQuery();
        if (Objects.isNull(searchTemplateQuery)) {
            log.warn("searchTemplateQuery is null");
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        return SearchTemplateQueryExt.of(searchTemplateQuery);
    }

    private DataQuery.Arg buildGenerateSearchQueryArg(GenerateSearchQuery.Arg arg) {
        DataQuery.Parameters parameters = DataQuery.Parameters.builder()
                .execute(false)
                .limitedObjectList(Lists.newArrayList(arg.getBindingObjectApiName()))
                .build();
        return DataQuery.Arg.builder()
                .userInput(arg.getUserInput())
                .bindingObjectApiName(arg.getBindingObjectApiName())
                .sessionId(arg.getSessionId())
                .history(arg.getHistory())
                .parameters(parameters)
                .build();
    }
}
