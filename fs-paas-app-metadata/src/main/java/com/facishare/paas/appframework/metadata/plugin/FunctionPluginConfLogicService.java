package com.facishare.paas.appframework.metadata.plugin;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.MtFunctionPluginConf;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR> <PERSON><PERSON><PERSON>ui
 * @Data : 2025/4/25
 * @Description : 对象管理后台 页面扩展, 记录对象的页面和适用的APL函数绑定关系
 */
public interface FunctionPluginConfLogicService {
	/**
	 * @param user 用户信息
	 * @return 所有管理后台可见的绑定关系(最初迁移时部分类型虽然迁移但是用户不可见)
	 */
	List<MtFunctionPluginConf> findAllForList(String refObjApi, User user);

	Optional<MtFunctionPluginConf> findByApiName(User user, String apiName);


	// 供运行时查询可用的绑定关系

	/**
	 * @param objectApiName 业务对象 apiname
	 * @param requestCode   {@link MtFunctionPluginConf#moduleName} 请求路径
	 */
	Optional<MtFunctionPluginConf> findAvailableRuntime(User user,
														String objectApiName, String requestCode);

	/**
	 * 创建绑定关系
	 *
	 * @param user   用户信息
	 * @param config 绑定关系
	 * @return 创建后的绑定关系
	 */
	MtFunctionPluginConf create(User user, MtFunctionPluginConf config);

	/**
	 * 更新绑定关系
	 *
	 * @param user   用户信息
	 * @param config 绑定关系
	 * @return 更新后的绑定关系
	 */
	MtFunctionPluginConf update(User user, MtFunctionPluginConf config);

	/**
	 * 启用绑定关系
	 *
	 * @param user    用户信息
	 * @param apiName 接口名称
	 * @return 启用后的绑定关系
	 */
	MtFunctionPluginConf enable(User user, String apiName);

	/**
	 * 禁用绑定关系
	 *
	 * @param user    用户信息
	 * @param apiName 接口名称
	 * @return 禁用后的绑定关系
	 */
	MtFunctionPluginConf disable(User user, String apiName);

	void deleteAll(User user, String refObjApi);

	/**
	 * 删除绑定关系
	 *
	 * @param user    用户信息
	 * @param apiName 接口名称
	 */
	void delete(User user, String apiName);

	List<MtFunctionPluginConf> brush(String tenantId, List<MtFunctionPluginConf> configs);
}
