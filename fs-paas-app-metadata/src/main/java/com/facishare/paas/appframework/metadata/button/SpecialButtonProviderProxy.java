package com.facishare.paas.appframework.metadata.button;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(
        value = "NCRM",
        desc = "NCRM服务", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface SpecialButtonProviderProxy {

    @POST(value = "/API/v1/rest/object/SpecialButtonProvider/service/getSpecialButtons", desc = "获取预设对象的特殊按钮")
    GetSpecialButtons.RestResult getSpecialButtons(@Body GetSpecialButtons.Arg arg, @HeaderMap Map<String, String> headers);

}
