package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/07/26
 */
public interface ImportFieldDataConverter {

    /**
     * 转换器支持的字段类型
     *
     * @return
     */
    List<String> getSupportedFieldTypes();

    /**
     * 将指定字段的数据转成前端展现的格式
     *
     * @param objectData    数据集合
     * @param fieldDescribe 字段描述
     * @param user          用户信息
     * @return
     */
    ConvertResult convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, User user);

    /**
     * 将指定字段的数据转换成前段展现的格式
     *
     * @param objectData
     * @param objectDescribe
     * @param fieldDescribe
     * @param user
     * @return
     */
    ConvertResult convertFieldData(IObjectData objectData, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, User user);
}
