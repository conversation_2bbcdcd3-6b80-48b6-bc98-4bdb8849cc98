package com.facishare.paas.appframework.metadata.dto;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.function.dto.FindRelationBySource;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Objects;
import java.util.function.UnaryOperator;

/**
 * create by z<PERSON><PERSON> on 2021/07/14
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ImportConfig {
    public static final String IMPORT_RELATION_TYPE = "Describe.import.version";
    public static final String IMPORT_PRE_PROCESSING_VALUE_TEMPLATE = "%s.%s.%s";
    public static final String INSERT = "insert";
    public static final String UPDATE = "update";

    public static final String IMPORT_PRE_PROCESSING = "importPreProcessing";
    public static final String IMPORT = "import";

    public static final String IMPORT_INSERT = "insertImport";
    public static final String IMPORT_UPDATE = "updateImport";

    @JsonProperty("describe_api_name")
    private String describeApiName;
    @JsonProperty("function_api_name")
    private String functionApiName;
    /**
     * insert 新建导入
     * update 更新导入
     */
    @JsonProperty("import_type")
    private String importType;
    @JsonProperty("function_name")
    private String functionName;
    private String remark;
    /**
     * import 导入前验证函数
     * importPreProcessing 导入预处理函数
     */
    @JsonProperty("define_type")
    private String defineType;

    public static ImportConfig from(FindRelationBySource.FuncRelationInfo info, IUdefFunction function,
                                    String importType, boolean importPreProcessing) {
        Objects.requireNonNull(info, "funcRelationInfo");
        Objects.requireNonNull(function, "function");
        Objects.requireNonNull(importType, "importType");
        String defineType = getDefineTypeByIsImportPre(importPreProcessing);
        return builder()
                .describeApiName(function.getBindingObjectApiName())
                .functionApiName(function.getApiName())
                .functionName(function.getFunctionName())
                .remark(function.getRemark())
                .importType(importType)
                .defineType(defineType)
                .build();
    }

    public static ImportConfig empty(String describeApiName, String importType, boolean importPreProcessing) {
        String defineType = getDefineTypeByIsImportPre(importPreProcessing);
        return builder()
                .describeApiName(describeApiName)
                .functionApiName("")
                .functionName("")
                .remark("")
                .importType(importType)
                .defineType(defineType)
                .build();
    }

    @JsonProperty("define_type")
    public String getDefineType() {
        if (IMPORT_PRE_PROCESSING.equals(defineType)) {
            return IMPORT_PRE_PROCESSING;
        }
        return IMPORT;
    }

    public FindRelationBySource.FuncRelationInfo toFuncRelationInfo(UnaryOperator<String> unaryOperator) {
        String value = getFuncRelationValue(describeApiName, importType, getDefineType());
        return FindRelationBySource.FuncRelationInfo.builder()
                .type(IMPORT_RELATION_TYPE)
                .name(getImportRelationMessage(describeApiName, importType, unaryOperator))
                .value(value)
                .funcApiName(functionApiName)
                .build();
    }

    public String toFuncRelationValue() {
        return getFuncRelationValue(describeApiName, importType, getDefineType());
    }

    private static String getFuncRelationValue(String describeApiName, String importType, String defineType) {
        return String.format(IMPORT_PRE_PROCESSING_VALUE_TEMPLATE, describeApiName, defineType, importType);
    }

    public static String getFuncRelationValue(String describeApiName, String importType, Boolean importPreProcessing) {
        String defineType = getDefineTypeByIsImportPre(importPreProcessing);
        return getFuncRelationValue(describeApiName, importType, defineType);
    }

    private static String getDefineTypeByIsImportPre(Boolean importPreProcessing) {
        return BooleanUtils.isTrue(importPreProcessing) ? IMPORT_PRE_PROCESSING : IMPORT;
    }

    public static String getImportRelationMessage(String describeApiName, String importType,
                                                  UnaryOperator<String> unaryOperator) {
        Objects.requireNonNull(unaryOperator);
        String displayName = unaryOperator.apply(describeApiName);
        displayName = Strings.isNullOrEmpty(displayName) ? describeApiName : displayName;
        String i18nKey = UPDATE.equalsIgnoreCase(importType) ? I18NKey.FUNC_RELATION_UPDATE_IMPORT :
                I18NKey.FUNC_RELATION_INSERT_IMPORT;
        return I18NExt.getOrDefault(i18nKey, i18nKey, displayName);
    }
}
