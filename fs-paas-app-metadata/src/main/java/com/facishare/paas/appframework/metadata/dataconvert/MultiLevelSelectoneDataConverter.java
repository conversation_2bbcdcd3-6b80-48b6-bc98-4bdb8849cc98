package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.metadata.api.IMultiLevelSelectOption;
import com.facishare.paas.metadata.impl.describe.MultiLevelSelectOneFieldDescribe;
import com.google.common.base.Strings;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * Created by linqiuying on 17/5/18.
 */
public class MultiLevelSelectoneDataConverter extends AbstractFieldDataConverter {
  @Override
  public String convertFieldData(SessionContext sessionContext) {
    List<IMultiLevelSelectOption> options = ((MultiLevelSelectOneFieldDescribe)getFieldDescribe()).getSelectOptions();
    String oldData = getObjectData().get(getFieldDescribe().getApiName(),String.class);
    if (CollectionUtils.isEmpty(options) || Strings.isNullOrEmpty(oldData)) {
      return "";
    }

    for (IMultiLevelSelectOption option : options) {
      if (option.getValue().equals(oldData)) {
        return option.getLabel();
      }
    }
    return oldData;
  }
}
