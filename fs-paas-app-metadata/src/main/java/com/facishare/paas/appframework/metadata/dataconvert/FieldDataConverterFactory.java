package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.common.exception.CRMErrorCode;
import com.facishare.crm.common.exception.CrmDefObjCheckedException;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.service.IObjectDataProxyService;
import com.google.common.collect.Maps;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.Map;

import static com.facishare.paas.metadata.api.describe.IFieldType.*;

/**
 * 字段转换器Bean工厂
 *
 * <AUTHOR>
 * @date 17/5/18
 */
@Slf4j
public class FieldDataConverterFactory {
    private static final Map<String, Class<? extends AbstractFieldDataConverter>> CONVERT_MAP = Maps.newHashMap();

    static {
        //CONVERT_MAP.put(EMPLOYEE, EmployeeDataConverter.class);
        CONVERT_MAP.put(MULTI_LEVEL_SELECT_ONE, MultiLevelSelectoneDataConverter.class);
        CONVERT_MAP.put(RECORD_TYPE, RecordTypeDataConverter.class);
        //CONVERT_MAP.put(DEPARTMENT, DepartmentDataConverter.class);
        CONVERT_MAP.put(SELECT_ONE, SelectOneDataConverter.class);
        CONVERT_MAP.put(SELECT_MANY, SelectManyDataConverter.class);
        CONVERT_MAP.put(OBJECT_REFERENCE, ObjectReferrenceDataConverter.class);
        CONVERT_MAP.put(DATE, DateDataConverter.class);
        CONVERT_MAP.put(DATE_TIME, DateTimeDataConverter.class);
        CONVERT_MAP.put(TIME, TimeDataConverter.class);
        CONVERT_MAP.put(NUMBER, NumberFieldDataConverter.class);
        CONVERT_MAP.put(CURRENCY, CurrencyFieldDataConverter.class);
        CONVERT_MAP.put(EMAIL, EmailFieldDataConverter.class);
        CONVERT_MAP.put(LOCATION, LocationFieldDataConverter.class);
        CONVERT_MAP.put(TEXT, TextFieldDataConverter.class);
        CONVERT_MAP.put(LONG_TEXT, LongTextFieldDataConverter.class);
        CONVERT_MAP.put(BIG_TEXT, BigTextFieldDataConverter.class);
        CONVERT_MAP.put(RICH_TEXT, RichTextFieldDataConverter.class);
        CONVERT_MAP.put(PERCENTILE, PercentileFieldDataConverter.class);
        CONVERT_MAP.put(TRUE_OR_FALSE, TrueOrFalseDataConverter.class);
        CONVERT_MAP.put(URL, UrlFieldDataConverter.class);
        CONVERT_MAP.put(PHONE_NUMBER, PhoneNumberFieldDataConverter.class);
        CONVERT_MAP.put(FORMULA, FormulaFieldDataConverter.class);
        CONVERT_MAP.put(AUTO_NUMBER, FormulaFieldDataConverter.class);
        CONVERT_MAP.put(COUNT, CountDataConverter.class);
        CONVERT_MAP.put(QUOTE, CountDataConverter.class);
        CONVERT_MAP.put(IMAGE, ImageFieldDataConverter.class);
        CONVERT_MAP.put(FILE_ATTACHMENT, AttachFieldDataConverter.class);
        CONVERT_MAP.put(WHAT_LIST_DATA, WhatListDataFieldDataConverter.class);
        CONVERT_MAP.put(HTML_RICH_TEXT, RichTextFieldDataConverter.class);
        CONVERT_MAP.put(SIGNATURE, SignatureDataConverter.class);
        CONVERT_MAP.put(TAG, TagFieldDataConverter.class);
    }

    public static AbstractFieldDataConverter getFieldDataConverter(@NonNull IFieldDescribe fieldDescribe,
                                                                   IObjectData objectData,
                                                                   IObjectDataProxyService dataService) throws CrmDefObjCheckedException {
        String fieldType = fieldDescribe.getType();
        if (CONVERT_MAP.containsKey(fieldType)) {
            Class<? extends AbstractFieldDataConverter> validatorClass = CONVERT_MAP.get(fieldType);
            try {
                AbstractFieldDataConverter fieldDataConverter = validatorClass.newInstance();
                fieldDataConverter.setFieldDescribe(fieldDescribe);
                fieldDataConverter.setObjectData(objectData);
                fieldDataConverter.setDataService(dataService);
                log.debug("Leaving getFieldDataConvert: get result Converter:{}", fieldDataConverter);
                return fieldDataConverter;
            } catch (InstantiationException | IllegalAccessException e) {
                throw new CrmDefObjCheckedException(CRMErrorCode.CRM_METHOD_FAILED, "");
            }
        } else {
            return NonHandleFieldDataConverter.instance();
        }
    }
}
