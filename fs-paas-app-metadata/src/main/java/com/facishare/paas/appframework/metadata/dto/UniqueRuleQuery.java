package com.facishare.paas.appframework.metadata.dto;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NonNull;
import lombok.RequiredArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * create by z<PERSON><PERSON> on 2019/05/19
 */
public interface UniqueRuleQuery {
    @Data
    @RequiredArgsConstructor(staticName = "of")
    class Arg {
        @NonNull
        private List<UniqueRuleData> uniqueRuleData;

        public static Arg from(IObjectData data) {
            UniqueRuleData uniqueRuleData = UniqueRuleData.of(0, data);
            return of(Lists.newArrayList(uniqueRuleData));
        }

        public static Arg fromList(List<IObjectData> dataList) {
            List<UniqueRuleData> uniqueRuleDataList = Lists.newArrayList();
            for (int i = 0; i < dataList.size(); i++) {
                IObjectData objectData = dataList.get(i);
                uniqueRuleDataList.add(UniqueRuleData.of(i, objectData));
            }
            return of(uniqueRuleDataList);
        }

    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class Result {
        private List<UniqueRuleResult> uniqueRuleData;

        public static Result createEmpty() {
            return UniqueRuleQuery.Result.of(Collections.emptyList());
        }

        public boolean isEmpty() {
            return CollectionUtils.empty(uniqueRuleData);
        }
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class UniqueRuleData {
        private int rowNo;
        private IObjectData data;
    }

    @Data
    @AllArgsConstructor(staticName = "of")
    class UniqueRuleResult {
        private int rowNo;
        private Set<String> duplicateIds;
    }
}
