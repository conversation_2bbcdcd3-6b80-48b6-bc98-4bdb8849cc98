package com.facishare.paas.appframework.metadata.dto.tools;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Map;

public interface BrushDescribe {
    @EqualsAndHashCode(callSuper = true)
    @Data
    @SuperBuilder
    @NoArgsConstructor
    @AllArgsConstructor
    class Arg extends InitTools.Arg {
        private Map<String, Object> describeJson;
        private Map<String, Object> describeAttribute;
        /**
         * 对象附加属性
         */
        private Map<String, Object> describeExtraAttribute;
        private Map<String, Map<String, Object>> fields;
        /**
         * 字段扩展属性
         */
        private Map<String, Map<String, Object>> fieldExtraAttribute;
    }
}
