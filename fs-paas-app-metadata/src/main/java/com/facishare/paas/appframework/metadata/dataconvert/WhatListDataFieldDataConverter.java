package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.common.exception.CrmDefObjCheckedException;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.SpringUtil;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by linqiuying on 17/6/9.
 */
public class WhatListDataFieldDataConverter extends AbstractFieldDataConverter {

    private DescribeLogicService describeLogicService = (DescribeLogicService) SpringUtil.getContext().getBean("describeLogicService");

    @Override
    public String convertFieldData(SessionContext sessionContext) throws CrmDefObjCheckedException {
        final List<Map<String, Object>> list = getObjectData().get(getFieldDescribe().getApiName(), List.class);
        if (CollectionUtils.isEmpty(list)) {
            return StringUtils.EMPTY;
        }
        Set<String> apiNames = Sets.newHashSet();

        list.stream().forEach(e -> apiNames.add(String.valueOf(e.get("describe_api_name"))));

        final List<IObjectDescribe> describeListWithoutFields = describeLogicService.findDescribeListWithoutFields(getObjectData().getTenantId(), Lists.newArrayList(apiNames));

        final Map<String, String> apiName2DisplayName = describeListWithoutFields.stream().collect(Collectors.toMap(IObjectDescribe::getApiName, IObjectDescribe::getDisplayName));

        return Joiner.on(";")
                .join(list.stream().map(e -> String.format("%s:%s", apiName2DisplayName.get(e.get("describe_api_name")), e.get("name")))
                        .filter(StringUtils::isNotBlank).collect(Collectors.toList()));
    }
}
