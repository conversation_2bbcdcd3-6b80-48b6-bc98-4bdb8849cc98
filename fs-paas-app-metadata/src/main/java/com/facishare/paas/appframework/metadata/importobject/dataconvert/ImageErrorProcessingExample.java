package com.facishare.paas.appframework.metadata.importobject.dataconvert;

/**
 * CRM Excel导入图片错误处理示例
 * 
 * 本示例展示了ImageImportDataConverter如何处理Excel导入服务生成的图片错误标识
 * 
 * <AUTHOR> Assistant
 * @since 2025-01-29
 */
public class ImageErrorProcessingExample {

    /**
     * 错误格式规范示例
     */
    public static class ErrorFormatExamples {
        
        // 单个图片错误示例
        public static final String SINGLE_ERROR = "[IMG_ERROR:paas.udobj.image_max_size]产品图片.jpg";
        
        // 多个图片错误示例（用|分隔）
        public static final String MULTIPLE_ERRORS = 
            "正常图片1.jpg|[IMG_ERROR:paas.udobj.image_max_size]超大图片.jpg|[IMG_ERROR:paas.udobj.image_format_unsupported]不支持格式.bmp|正常图片2.png";
        
        // 只有错误标识，没有原始值
        public static final String ERROR_WITHOUT_ORIGINAL = "[IMG_ERROR:paas.udobj.image_not_found]";
        
        // 复杂场景：混合错误
        public static final String COMPLEX_SCENARIO = 
            "[IMG_ERROR:paas.udobj.image_upload_failed]上传失败.jpg|[IMG_ERROR:paas.udobj.image_data_empty]|正常图片.png";
    }

    /**
     * 支持的错误类型和对应的多语言key
     */
    public static class SupportedErrorTypes {
        
        // 图片上传失败
        public static final String UPLOAD_FAILED = "paas.udobj.image_upload_failed";
        
        // 图片数据为空
        public static final String DATA_EMPTY = "paas.udobj.image_data_empty";
        
        // 文件大小超限
        public static final String MAX_SIZE = "paas.udobj.image_max_size";
        
        // 格式不支持
        public static final String FORMAT_UNSUPPORTED = "paas.udobj.image_format_unsupported";
        
        // 提取失败
        public static final String EXTRACTION_FAILED = "paas.udobj.image_extraction_failed";
        
        // 图片未找到
        public static final String NOT_FOUND = "paas.udobj.image_not_found";
        
        // 处理异常
        public static final String PROCESSING_ERROR = "paas.udobj.image_processing_error";
    }

    /**
     * 处理结果示例
     */
    public static class ProcessingResultExamples {
        
        /**
         * 单个错误的处理结果
         * 输入：[IMG_ERROR:paas.udobj.image_max_size]产品图片.jpg
         * 输出：[产品图片字段] 文件大小超过限制
         */
        public static final String SINGLE_ERROR_RESULT = "[产品图片字段] 文件大小超过限制";

        /**
         * 多个错误的处理结果（简化的逗号分隔格式）
         * 输入：[IMG_ERROR:paas.udobj.image_max_size]图片1.jpg|[IMG_ERROR:paas.udobj.image_format_unsupported]图片2.bmp
         * 输出：[产品图片字段] 文件大小超过限制,图片格式不支持
         */
        public static final String MULTIPLE_ERRORS_RESULT = "[产品图片字段] 文件大小超过限制,图片格式不支持";
    }

    /**
     * 处理流程说明
     */
    public static class ProcessingFlow {
        
        /**
         * 1. 错误检测阶段
         * - 使用正则表达式检测单元格值是否包含[IMG_ERROR:xxx]格式
         * - 模式：.*\[IMG_ERROR:[^\]]+\].*
         */
        
        /**
         * 2. 错误解析阶段
         * - 提取多语言key：\[IMG_ERROR:([^\]]+)\](.*)
         * - 提取原始值：用于后续处理
         */
        
        /**
         * 3. 消息渲染阶段
         * - 使用I18NExt.text(key)获取当前语言环境的错误提示
         * - 自动适配中文、英文等多种语言
         */
        
        /**
         * 4. 报告生成阶段
         * - 单个错误：[字段名] 错误消息
         * - 多个错误：[字段名] 错误消息1,错误消息2,错误消息3
         */
        
        /**
         * 5. 原始值处理阶段
         * - 提取有效图片路径，移除错误标识
         * - 继续正常的图片导入流程
         */
    }

    /**
     * 集成说明
     */
    public static class IntegrationNotes {
        
        /**
         * 集成点：ImageImportDataConverter.convertFieldData()方法
         * 
         * 在以下位置添加错误处理：
         * 1. 基本验证之后（空值、水印、数量检查）
         * 2. TNpath/Npath格式检测之前
         * 3. 确保不干扰现有业务逻辑
         */
        
        /**
         * 向后兼容性：
         * - 如果单元格值不包含[IMG_ERROR:xxx]格式，完全按照原有逻辑处理
         * - 如果包含错误标识但原始值为空，则跳过该图片
         * - 如果包含错误标识且有原始值，则使用原始值继续处理
         */
        
        /**
         * 性能考虑：
         * - 使用预编译的正则表达式模式
         * - 只在检测到错误时才进行详细解析
         * - 避免不必要的字符串操作
         */
    }

    /**
     * 使用示例
     */
    public static class UsageExample {
        
        /**
         * Excel导入服务生成错误标识的场景：
         * 
         * 1. 用户在Excel中插入了一张超大的图片
         * 2. Excel导入服务检测到图片大小超限
         * 3. 将单元格值格式化为：[IMG_ERROR:paas.udobj.image_max_size]原始文件名.jpg
         * 4. CRM端的ImageImportDataConverter检测到错误标识
         * 5. 提取多语言key并渲染为用户可读的错误信息
         * 6. 生成简化的导入报告：[产品图片] 文件大小超过限制
         * 7. 用户看到友好的错误提示，知道具体的问题所在
         */
    }
}
