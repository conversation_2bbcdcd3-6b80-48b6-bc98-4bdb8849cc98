package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IUdefAction;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.Area;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.service.impl.UdefActionService;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by linqy on 2018/01/11
 */
@Slf4j
@Service("postActionService")
public class PostActionServiceImpl implements PostActionService {
    @Autowired
    private UdefActionService actionService;

    @Autowired
    private DescribeLogicService describeLogicService;

    @Override
    public IUdefAction createAction(User user, IUdefAction action) {
        action.setTenantId(user.getTenantId());
        try {
            IUdefAction create = actionService.create(action);
            return create;
        } catch (MetadataServiceException e) {
            log.error("createAction error,user:{},action:{}", user, action, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public List<IUdefAction> bulkCreateAction(User user, List<IUdefAction> actionList) {
        if (CollectionUtils.empty(actionList)) {
            return new ArrayList<>();
        }
        actionList.forEach(x -> x.setTenantId(user.getTenantId()));
        try {
            return actionService.bulkCreate(actionList);
        } catch (MetadataServiceException e) {
            log.error("createCustomButton error,user:{},action:{}", user, actionList, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public List<IUdefAction> bulkUpdateAction(User user, List<IUdefAction> actionList) {
        if (CollectionUtils.empty(actionList)) {
            return Lists.newArrayList();
        }
        try {
            return actionService.bulkUpdate(actionList);
        } catch (MetadataServiceException e) {
            log.error("bulkUpdateAction error,user:{},action:{}", user, actionList, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public List<IUdefAction> findActionList(User user, List<String> idList, String describeApiName) {
        if (CollectionUtils.empty(idList)) {
            return Lists.newArrayList();
        }
        return actionService.findUdefActionByIdList(idList, user.getTenantId(), describeApiName);
    }

    @Override
    public List<IUdefAction> findActionListForDesigner(User user, IUdefButton button, String describeApiName) {
        if (Objects.isNull(button)) {
            return Lists.newArrayList();
        }
        List<String> idList = button.getActions();
        if (CollectionUtils.empty(idList)) {
            return Lists.newArrayList();
        }
        List<IUdefAction> udefActionList = actionService.findUdefActionByIdList(idList, user.getTenantId(), describeApiName);
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(button.getTenantId(), describeApiName);
        dealTownInfo(button, udefActionList, objectDescribe);
        return udefActionList;
    }

    @Override
    public List<IUdefAction> findActionList(User user, IUdefButton button, String describeApiName) {
        if (Objects.isNull(button)) {
            return Lists.newArrayList();
        }
        List<String> idList = button.getActions();
        if (CollectionUtils.empty(idList)) {
            return Lists.newArrayList();
        }
        List<IUdefAction> udefActionList = actionService.findUdefActionByIdList(idList, user.getTenantId(), describeApiName);
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopyIfGray(button.getTenantId(), describeApiName);
        dealTownInfo(button, udefActionList, objectDescribe);
        removeDisableField(udefActionList, objectDescribe);
        return udefActionList;
    }

    private void removeDisableField(List<IUdefAction> udefActionList, IObjectDescribe objectDescribe) {
        List<String> activeField = ObjectDescribeExt.of(objectDescribe).getActiveFieldDescribes().stream()
                .map(x -> x.getApiName()).collect(Collectors.toList());
        udefActionList.forEach(action -> {
            String actionParameter = action.getActionParamter();
            if (StringUtils.isBlank(actionParameter)) {
                return;
            }
            Map<String, List<Map<String, String>>> actionParameters = JacksonUtils.fromJson(actionParameter, Map.class);
            if (CollectionUtils.empty(actionParameters)) {
                return;
            }
            List<Map<String, String>> fields = actionParameters.get("fields");
            if (CollectionUtils.empty(fields)) {
                return;
            }
            //删除禁用的字段
            fields.removeIf(field -> !activeField.contains(field.get("field")));
            if (CollectionUtils.empty(fields)) {
                return;
            }
            actionParameters.put("fields", fields);
            action.setActionParamter(JacksonUtils.toJson(actionParameters));
        });

    }

    private void dealTownInfo(IUdefButton button, List<IUdefAction> udefActionList, IObjectDescribe objectDescribe) {
        List<IParamForm> paramForms = ParamForm.fromList(button.getParamForm());
        //按钮中配了国家省市区并且到区一级才会处理乡镇
        List<String> districtParams = paramForms.stream()
                .filter(x -> Objects.equals(objectDescribe.getApiName(), x.getObjectApiName()))
                .filter(x -> IFieldType.DISTRICT.equals(x.getType()))
                .map(IParamForm::convertToFieldApiName)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(districtParams)) {
            return;
        }
        List<String> groupFieldApiNames = paramForms.stream()
                .filter(x -> Objects.equals(objectDescribe.getApiName(), x.getObjectApiName()))
                .filter(x -> IFieldType.GROUP.equals(x.getType()))
                .map(IParamForm::convertToFieldApiName)
                .collect(Collectors.toList());

        if (CollectionUtils.empty(groupFieldApiNames)) {
            return;
        }
        Map<String, String> districtAndTownMap = ObjectDescribeExt.of(objectDescribe).getGroupFields().stream()
                .filter(x -> groupFieldApiNames.contains(x.getApiName()))
                .filter(x -> x instanceof Area)
                .map(x -> (Area) x)
                .filter(x -> BooleanUtils.isTrue(x.getIsSupportTown()))
                .collect(Collectors.toMap(Area::getAreaDistrictFieldApiName, Area::getAreaTownFieldApiName));
        filledActionParameters(udefActionList, districtAndTownMap);
        Map<String, String> townAndVillageMap = ObjectDescribeExt.of(objectDescribe).getGroupFields().stream()
                .filter(x -> groupFieldApiNames.contains(x.getApiName()))
                .filter(x -> x instanceof Area)
                .map(x -> (Area) x)
                .filter(x -> BooleanUtils.isTrue(x.getIsSupportTown()) && BooleanUtils.isTrue(x.getIsSupportVillage()))
                .collect(Collectors.toMap(Area::getAreaTownFieldApiName, Area::getAreaVillageFieldApiName));
        filledActionParameters(udefActionList, townAndVillageMap);

    }

    private static void filledActionParameters(List<IUdefAction> udefActionList, Map<String, String> areaMap) {
        if (CollectionUtils.empty(areaMap)) {
            return;
        }
        udefActionList.forEach(action -> {
            String actionParameter = action.getActionParamter();
            if (StringUtils.isBlank(actionParameter)) {
                return;
            }
            Map<String, List<Map<String, String>>> actionParameters = JacksonUtils.fromJson(actionParameter, Map.class);
            if (CollectionUtils.empty(actionParameters)) {
                return;
            }
            List<Map<String, String>> fields = actionParameters.get("fields");
            if (CollectionUtils.empty(fields)) {
                return;
            }
            List<String> fieldApiNames = Lists.newArrayList();
            fields.forEach(field -> {
                String fieldApiName = field.get("field");
                if (StringUtils.isNotBlank(fieldApiName)) {
                    fieldApiNames.add(fieldApiName);
                }
            });
            areaMap.forEach((parentField, childField) -> {
                if (fieldApiNames.contains(parentField) && !fieldApiNames.contains(childField)) {
                    Map<String, String> paramMap = Maps.newHashMap();
                    paramMap.put("field", childField);
                    paramMap.put("value", String.format("$form_%s$", childField));
                    paramMap.put("var_type", "variable");
                    fields.add(paramMap);
                }
            });
            actionParameters.put("fields", fields);
            action.setActionParamter(JacksonUtils.toJson(actionParameters));
        });
    }

    @Override
    public boolean bulkDeleteAction(User user, List<String> idList, String describeApiName) {
        if (CollectionUtils.empty(idList)) {
            return true;
        }
        try {
            return actionService.bulkDelete(idList, user.getTenantId(), describeApiName);
        } catch (MetadataServiceException e) {
            log.error("bulkDeleteAction error,user:{},idlist:{}", user, idList, e);
            throw new MetaDataBusinessException(e.getMessage());
        }
    }

    @Override
    public Map<String, List<IUdefAction>> findActionByApiNameListAndType(User user, List<String> apiNameList, String actionType) {
        Map<String, List<IUdefAction>> map = actionService.findActionsByDescribeApiNamesAndType(user.getTenantId(), apiNameList, actionType);
        return CollectionUtils.empty(map) ? Maps.newHashMap() : map;
    }

    @Override
    public List<IUdefAction> findActionListByStage(User user, IUdefButton button, String describeApiName, String actionStage) {
        List<IUdefAction> actions = findActionList(user, button, describeApiName);
        if (CollectionUtils.empty(actions)) {
            return Lists.newArrayList();
        }
        String stage = Strings.isNullOrEmpty(actionStage) ? "pre" : actionStage;
        return actions.stream()
                .map(UdefActionExt::of)
                .peek(x -> x.computeActionStage(button))
                .filter(x -> Objects.equals(stage, x.getStage()))
                .map(UdefActionExt::getUdefAction)
                .collect(Collectors.toList());
    }
}
