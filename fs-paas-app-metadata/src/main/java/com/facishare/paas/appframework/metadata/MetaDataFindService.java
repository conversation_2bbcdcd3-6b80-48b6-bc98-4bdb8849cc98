package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.layout.component.ISummaryComponentInfo;
import com.facishare.paas.appframework.metadata.query.SearchQueryContext;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.ISearchQuery;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.*;
import com.facishare.paas.metadata.dao.pg.entity.metadata.RelevantTeam;
import com.facishare.paas.metadata.impl.arg.FindInvalidDataListArg;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import lombok.Builder;
import lombok.Data;
import lombok.NonNull;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.UnaryOperator;

/**
 * 元数据服务接口
 * <p>
 * Created by by yusb on 2017/12/14.
 */
public interface MetaDataFindService {

    /**
     * 查询个对象
     *
     * @param tenantId
     * @param id
     * @param describe
     * @return
     */
    IObjectData findObjectData(String tenantId, String id, IObjectDescribe describe);

    IObjectData findObjectData(User user, String id, IObjectDescribe describe);

    IObjectData findObjectData(IActionContext context, String id, IObjectDescribe describe);

    /**
     * 获取数据
     *
     * @param user    用户身份，不进行数据权限校验
     * @param id      数据id
     * @param apiName 数据的apiName
     * @return
     */
    IObjectData findObjectData(User user, String id, String apiName);

    IObjectData findObjectData(IActionContext context, String id, String apiName);

    IObjectData findObjectDataIgnoreFormula(User user, String id, String objectApiName);

    IObjectData findObjectDataIgnoreRelevantTeam(User user, String id, String objectApiName);

    IObjectData findObjectDataIgnoreAll(User user, String id, String objectApiName);

    IObjectData findObjectDataIncludeDeleted(User user, String id, String objectApiName);

    IObjectData getObjectDataIncludeDeleted(IActionContext context, String id, String objectApiName);

    IObjectData findObjectDataIgnoreStatus(User user, String id, String objectApiName);

    IObjectData findObjectDataIgnoreStatusAndFormula(User user, String id, String objectApiName);

    IObjectData findObjectDataIncludeDeletedIgnoreFormula(User user, String id, String objectApiName);

    IObjectData findMasterObjectData(User user, IObjectData detailData, IObjectDescribe detailDescribe);

    /**
     * 接口会议管理员的身份查询数据，对于没有查询到的数据会返回 null，使用的时候需要注意判空
     *
     * @param id
     * @return
     */
    IObjectData findObjectDataIncludeNull(String tenantId, String id, IObjectDescribe describe);

    Map<String, IObjectData> findMasterObjectData(User user, List<IObjectData> detailDataList, IObjectDescribe detailDescribe);

    /**
     * 批量接口比较特殊，需要注意使用场景，老对象没有走数据权限校验，自定义对象不会返回相关团队
     */
    List<IObjectData> findObjectDataByIds(String tenantId, List<String> ids, String objectApiName);

    List<IObjectData> findObjectDataByIdsIgnoreFormula(String tenantId, List<String> ids, String objectApiName);

    List<IObjectData> findObjectDataByIdsIgnoreRelevantTeam(String tenantId, List<String> ids, String objectApiName);

    /**
     * 查询数据
     * 忽略权限校验、不补充lookup、不查询相关团队
     * <p>
     * 查询结果
     * 不需要实时计算
     */
    List<IObjectData> findObjectDataByIdsIgnoreAll(String tenantId, List<String> ids, String objectApiName);

    List<IObjectData> findObjectDataByIdsIgnoreAllExceptFormula(String tenantId, List<String> ids, String objectApiName);

    List<IObjectData> findObjectDataByIdsIgnoreAllAndExtraInfo(String tenantId, List<String> ids, String objectApiName);

    List<IObjectData> findObjectDataByIdsIgnoreAllAndExtraInfoExceptFormula(String tenantId, List<String> ids, String objectApiName);

    List<IObjectData> findObjectDataByIds(IActionContext context, List<String> ids, String objectApiName);

    List<IObjectData> findObjectDataByIdsExcludeInvalid(IActionContext context, List<String> ids, String objectApiName);

    List<IObjectData> findObjectDataByIdsExcludeInvalidIgnoreAll(String tenantId, List<String> ids, String objectApiName);

    List<IObjectData> findObjectDataByIdsIncludeDeleted(User user, List<String> ids, String objectApiName);

    List<IObjectData> findObjectDataByIdsIncludeDeletedIgnoreFormula(User user, List<String> ids, String objectApiName);

    /**
     * 根据数据id查询数据,不处理计算字段
     *
     * @param user                  上下文中的人员
     * @param ids                   查询的数据id
     * @param objectApiName         查询的对象
     * @param needAllMultiLangValue 是否保留开起多语字段的所有语言信息
     * @return 查询的数据
     */
    List<IObjectData> findObjectDataByIdsIncludeDeletedIgnoreFormula(User user, List<String> ids, String objectApiName, boolean needAllMultiLangValue);

    List<IObjectData> findObjectDataByIdsIncludeDeletedIgnoreAll(User user, List<String> ids, String objectApiName);

    //获取主对象的某条数据的对于所有从对象的所有从数据。
    List<IObjectData> findDetailObjectDataListIgnoreFormula(IObjectData masterObjectData, User user);

    List<IObjectData> findObjectDataByIdsWithQueryContext(QueryContext queryContext, List<String> ids, String objectApiName);

    //获取主对象的某条数据的对于某从对象的所有从数据。
    List<IObjectData> findDetailObjectDataList(IObjectDescribe detailDescribe, IObjectData masterObjectData, User user);

    List<IObjectData> findDetailObjectDataList(User user, IObjectDescribe detailDescribe, List<IObjectData> masterDataList);

    List<IObjectData> findDetailObjectDataList(IActionContext context, IObjectDescribe detailDescribe, List<IObjectData> masterDataList, UnaryOperator<SearchTemplateQueryExt> function);

    List<IObjectData> findDetailObjectDataList(IActionContext context, IObjectDescribe detailDescribe, IObjectData masterObjectData);

    List<IObjectData> findDetailObjectDataListIgnoreFormula(IObjectDescribe detailDescribe, IObjectData masterObjectData, User user);

    //获取已放入回收站的主对象的某条数据的对于所有从对象的所有从数据。
    List<IObjectData> findDetailIncludeInvalidObjectDataListIgnoreFormula(IObjectData masterObjectData, User user);

    //获取已放入回收站的主对象的某条数据的对于某从对象的所有从数据。
    List<IObjectData> findDetailIncludeInvalidObjectDataListIgnoreFormula(IObjectDescribe detailDescribe, IObjectData masterObjectData, User user);

    //获取主对象某条数据的全部从对象的数据。Map中key为从对象的apiName,value为此从对象下的所有从数据。
    Map<String, List<IObjectData>> findDetailObjectDataList(List<IObjectDescribe> detailDescribeList, IObjectData masterObjectData, User user);

    List<IObjectData> findDataWithWhere(User user, String apiName, List<IFilter> filters, List<Wheres> whereList, int offset, int limit);

    QueryResult<IObjectData> findBySearchQuery(User user, IObjectDescribe describe, String apiName,
                                               SearchTemplateQuery searchTemplateQuery);

    QueryResult<IObjectData> findBySearchQuery(User user, IObjectDescribe describe, String apiName,
                                               SearchTemplateQuery searchTemplateQuery, boolean isSimple);

    QueryResult<IObjectData> findBySearchQuery(User user, IObjectDescribe describe, String apiName,
                                               SearchTemplateQuery searchTemplateQuery, boolean isSimple,
                                               boolean esSearchSkipRecentUpdateCheck);

    QueryResult<IObjectData> findBySearchQuery(User user, String objectApiName, SearchTemplateQuery searchTemplateQuery);

    QueryResult<IObjectData> findBySearchQuery(IActionContext context, String objectApiName, SearchTemplateQuery searchTemplateQuery);

    QueryResult<IObjectData> findBySearchQuery(User user, IObjectDescribe describe, String apiName,
                                               SearchTemplateQuery searchTemplateQuery, boolean isSimple,
                                               boolean esSearchSkipRecentUpdateCheck, boolean skipRelevantTeam);

    QueryResult<IObjectData> findBySearchQuery(User user, IObjectDescribe describe, String apiName,
                                               SearchTemplateQuery searchTemplateQuery, boolean isSimple,
                                               boolean esSearchSkipRecentUpdateCheck, boolean skipRelevantTeam, boolean searchRichTextExtra);

    QueryResult<IObjectData> findBySearchQuery(QueryContext searchQueryContext,
                                               String describeApiName,
                                               SearchTemplateQuery searchTemplateQuery);

    QueryResult<IObjectData> findBySearchQueryIgnoreAll(User user, String apiName, SearchTemplateQuery searchTemplateQuery);

    QueryResult<IObjectData> findByQueryWithContext(QueryContext queryContext, String describeApiName,
                                                    SearchTemplateQuery searchTemplateQuery);

    /**
     * 查询数据补充字段相关信息
     *
     * @param isIncludeLookup     是否补充lookUp
     * @param isIncludeQuoteValue 是否补充quote
     * @param isFillExtendField   是否补充国家、省市区、人员
     * @param isFillMaskField     是否补充掩码字段
     * @return 补充过的数据
     */
    QueryResult<IObjectData> findBySearchQueryIfFillExtendFieldInfo(User user, IObjectDescribe describe, SearchTemplateQuery searchTemplateQuery,
                                                                    boolean isIncludeLookup, boolean isIncludeQuoteValue, boolean isFillExtendField,
                                                                    boolean isFillMaskField);

    QueryResult<IObjectData> findBySearchQueryWithFieldsIgnoreAll(User user, String apiName, SearchTemplateQuery searchTemplateQuery, List<String> fieldList);

    QueryResult<IObjectData> findBySearchQueryWithDeleted(User user, IObjectDescribe describe, SearchTemplateQuery searchTemplateQuery);

    QueryResult<IObjectData> findBySearchQueryWithDeleted(User user, IObjectDescribe describe, SearchTemplateQuery searchTemplateQuery, boolean skipRelevantTeam);

    QueryResult<IObjectData> findBySearchQueryWithDeleted(User user, IObjectDescribe describe, SearchTemplateQuery searchTemplateQuery, boolean skipRelevantTeam, boolean searchRichTextExtra);

    QueryResult<IObjectData> findBySearchQueryWithDeleted(IActionContext context, IObjectDescribe describe, SearchTemplateQuery searchTemplateQuery);

    QueryResult<IObjectData> findBySearchQuery(User user, String objectApiName, ISearchQuery searchQuery);

    QueryResult<IObjectData> findDetailObjectDataWithPage(User user, String masterApiName, String masterDataId,
                                                          IObjectDescribe detailDescribe, int pageNum,
                                                          int pageSize, List<IFilter> filters);

    QueryResult<IObjectData> findDetailObjectDataBatchWithPage(User user, String masterApiName, List<String> masterDataIds,
                                                               IObjectDescribe detailDescribe, int pageNum,
                                                               int pageSize, List<IFilter> filters);

    QueryResult<IObjectData> findObjectDataWithFieldsAndPage(User user, String masterApiName, Set<String> masterDataIds, IObjectDescribe detailDescribe, int pageNum, int pageSize, List<String> fieldList);

    QueryResult<IObjectData> findBySearchTemplateQueryWithFields(IActionContext context, String objectApiName, SearchTemplateQuery searchTemplateQuery, List<String> fieldList);

    QueryResult<IObjectData> findDetailObjectDataWithPageIncludeInvalid(User user, String masterApiName, String masterDataId,
                                                                        IObjectDescribe detailDescribe, int pageNum,
                                                                        int pageSize, List<IFilter> filters);

    QueryResult<IObjectData> findDetailObjectDataBatchWithPageIncludeInvalid(User user, String masterApiName, List<String> masterDataIds,
                                                                             IObjectDescribe detailDescribe, int pageNum,
                                                                             int pageSize, List<IFilter> filters);

    QueryResult<IObjectData> findRelatedObjectDataWithPage(User user, String masterDataId,
                                                           IObjectDescribe relatedDescribe, String lookupFieldName,
                                                           int pageNum, int pageSize, List<IFilter> filters);

    QueryResult<IObjectData> findRelatedObjectDataWithFields(User user, String masterDataId,
                                                             IObjectDescribe relatedDescribe, List<String> lookupFieldNames,
                                                             int pageNum, int pageSize, List<IFilter> filters);

    QueryResult<IObjectData> findRelatedObjectDataWithFields(User user, String masterDataId,
                                                             IObjectDescribe relatedDescribe, List<String> lookupFieldNames,
                                                             int pageNum, int pageSize, List<IFilter> filters, boolean isSimple);

    IRelatedListQuery.QueryResult<IObjectData> findRelatedObjectData(IRelatedListQuery relatedListQuery, User user);

    List<IObjectData> getDataProjectionDataList(List<IObjectData> dataList, List<String> includeFields);

    /**
     * 建议使用带type参数的方法来查询筛选模板，当前方法在670之后效率不高
     * com.facishare.paas.appframework.metadata.MetaDataFindService#findSearchTemplateByIdAndType(com.facishare.paas.appframework.core.model.User, java.lang.String, java.lang.String, java.lang.String)
     *
     * @param user
     * @param templateId
     * @param objectApiName
     * @return
     */
    @Deprecated
    ISearchTemplate findSearchTemplate(User user, String templateId, String objectApiName);

    ISearchTemplate findSearchTemplateByIdAndType(User user, String templateId, String describeApiName, String sceneType);

    SearchTemplateQuery getSearchTemplateQuery(User user, ObjectDescribeExt objectDescribe, String templateId, String searchQueryInfo);

    SearchTemplateQuery getSearchTemplateQuery(User user, ObjectDescribeExt objectDescribe, String templateId, String searchQueryInfo, boolean isRelatedPage);

    SearchTemplateQuery getSearchTemplateQuery(User user, ObjectDescribeExt objectDescribe, String templateId,
                                               String searchQueryInfo, boolean isRelatedPage, boolean isAdmin);

    /**
     * 请使用带type的接口
     *
     * @param user
     * @param objectDescribe
     * @param templateId
     * @param searchQueryInfo
     * @param isIgnoreSceneFilter
     * @return
     */
    @Deprecated
    SearchTemplateQuery getSearchTemplateQueryWithIgnoreSceneFilter(User user, ObjectDescribeExt objectDescribe,
                                                                    String templateId, String searchQueryInfo,
                                                                    boolean isIgnoreSceneFilter);

    SearchTemplateQuery getSearchTemplateQueryWithIgnoreSceneFilter(User user, ObjectDescribeExt objectDescribe,
                                                                    String templateId, String searchTemplateType,
                                                                    String searchQueryInfo, boolean isRelatedPage,
                                                                    boolean isIgnoreSceneFilter, boolean isIgnoreSceneRecordType);

    SearchTemplateQuery getSearchTemplateQueryWithIgnoreSceneFilter(User user, ObjectDescribeExt objectDescribe,
                                                                    ISearchTemplate searchTemplate,
                                                                    String searchQueryInfo, boolean isRelatedPage,
                                                                    boolean isIgnoreSceneFilter, boolean isIgnoreSceneRecordType);

    SearchTemplateQuery buildDetailSearchTemplateQuery(User user, ObjectDescribeExt detailDescribe, IObjectData masterData);

    void handleDataRightsParameter(IObjectDescribe describe, ISearchTemplateQuery searchTemplateQuery, User user);

    Query findSearchQuery(User user, IObjectDescribe objectDescribe, String searchQueryInfo, SearchQueryContext context);

    IRelatedListQuery.QueryResult<IObjectData> findRelatedObjectData(IRelatedListQuery relatedListQuery, User
            user, IActionContext actionContext);

    QueryResult<IObjectData> findInvalidData(FindInvalidDataListArg arg);

    QueryResult<IObjectData> findDetailDataBySearchQuery(String tenantId,
                                                         String objectDescribeAPIName,
                                                         SearchTemplateQuery searchTemplateQuery,
                                                         IActionContext context);

    @Deprecated
    Map<String, List<IObjectData>> findRefObjectDataIfHasQuoteField(User user, List<IObjectData> dataList, IObjectDescribe objectDescribe);

    Map<String, List<IObjectData>> findRefObjectData(User user, List<IObjectData> dataList, IObjectDescribe objectDescribe);

    Map<String, List<IObjectData>> findRefObjectData(User user, List<IObjectData> dataList, IObjectDescribe objectDescribe, List<IFieldDescribe> refFieldList);

    Map<String, String> findObjectIdByName(User user, String objectApiName, List<String> nameList);

    void mergeWithDbData(String tenantId, String objectApiName, List<IObjectData> dataList);

    List<IObjectData> findEmployeeInfoByUserIds(String tenantId, List<String> userIds);

    List<IObjectData> findEmployeeInfoByUserIdsForCalculate(String tenantId, List<String> userIds);

    List<IObjectData> findEmployeeInfoByUserIdsIgnoreFormula(String tenantId, List<String> userIds);

    List<IObjectData> findDepartmentInfoByDepartIds(String tenantId, List<String> departIds);

    List<IObjectData> findDepartmentInfoByDepartIdsIgnoreFormula(String tenantId, List<String> departIds);

    List<String> findReferencedDataList(User user, String objectApiName, List<String> idList);

    List<IObjectData> findByIdsIncludeInvalid(List<String> ids,
                                              String tenantId,
                                              String objectDescribeAPIName,
                                              IActionContext context);

    /**
     * 批量查询相关团队
     *
     * @param tenantId
     * @param apiNameIdsMap：对象apiName和数据id
     * @return
     */
    Map<String, Map<String, List<RelevantTeam>>> batchFindTeamMember(String tenantId, Map<String, List<String>> apiNameIdsMap);

    /**
     * Simple查询
     *
     * @param tenantId
     * @param apiName
     * @param ids
     * @param fieldList
     * @param context
     * @return
     */
    List<IObjectData> findSimpleDataByIds(String tenantId, String apiName, List<String> ids, List<String> fieldList, IActionContext context);

    /**
     * 获取短id
     *
     * @param tenantId
     * @param describeApiName
     * @return
     */
    String getSeqId(String tenantId, String describeApiName);

    /**
     * 根据SearchQuery和指定分组字段，查询数据的聚合结果
     *
     * @param user
     * @param query
     * @param describeApiName
     * @param groupField      分组字段的apiname，如name
     * @return IObjectData中，只包括<聚合字段, IObjectData的数量>
     */
    List<IObjectData> findByAggregateSearchQuery(User user, SearchTemplateQuery query, String describeApiName, String groupField);


    List<IObjectData> aggregateFindBySearchQuery(User user, SearchTemplateQuery query, String describeApiName);

    /**
     * 根据查询条件和对象、字段等信息进行聚合
     *
     * @param tenantId        企业id
     * @param query           查询条件
     * @param describeApiName 对象api_name
     * @param groupField      聚合维度
     * @param aggFunction     聚合方式(sum、count)
     * @param aggField        聚合字段
     * @return 聚合值列表
     */
    List<IObjectData> aggregateFindBySearchQuery(String tenantId,
                                                 SearchTemplateQuery query,
                                                 String describeApiName,
                                                 String groupField,
                                                 String aggFunction,
                                                 String aggField);

    List<IObjectData> aggregateFindBySearchQuery(User user,
                                                 SearchTemplateQuery query,
                                                 String describeApiName,
                                                 String groupField,
                                                 List<AggFunctionArg> aggFunctionArgs);

    List<IObjectData> aggregateFindBySearchQueryWithGroupFields(User user,
                                                                SearchTemplateQuery query,
                                                                String describeApiName,
                                                                List<String> groupFields,
                                                                String aggFunction,
                                                                String aggField);


    List<IObjectData> aggregateFindBySearchQueryWithGroupFields(User user,
                                                                SearchTemplateQuery query,
                                                                String describeApiName,
                                                                List<String> groupFields,
                                                                List<AggFunctionArg> aggFunctionArgs);

    void queryDataAndHandle(User user, SearchTemplateQuery searchQuery, IObjectDescribe objectDescribe,
                            int dataBatchSize, int maxQueryCount, boolean skipRelevantTeam, Consumer<QueryResult<IObjectData>> consumer);


    void queryDataAndHandle(User user, SearchTemplateQuery searchQuery, String describeApiName,
                            int dataBatchSize, int maxQueryCount, boolean skipRelevantTeam, Consumer<QueryResult<IObjectData>> consumer);


    void queryDataAndHandle(QueryContext queryContext, SearchTemplateQuery searchQuery, IObjectDescribe objectDescribe,
                            int dataBatchSize, int maxQueryCount, Consumer<QueryResult<IObjectData>> consumer);

    void queryDataAndHandle(QueryContext queryContext, SearchTemplateQuery searchQuery, String describeApiName,
                            int dataBatchSize, int maxQueryCount, Consumer<QueryResult<IObjectData>> consumer);

    Map<String, String> querySummaryData(User user, String describeApiName, SearchTemplateQuery query, List<ISummaryComponentInfo> summaryInfos);

    boolean existData(String tenantId, String objectDescribeApiName);

    /**
     * 选项是否允许被删除
     * 1. 对象使用了选项
     *
     * @param user
     * @param objectDescribeApiName
     * @param fieldApiName
     * @param values
     */
    void checkOptionUsedByData(User user, String objectDescribeApiName, String fieldApiName, Set<String> values);

    boolean checkDataRelevantTeamOnlyExistOwner(User user, String objectDescribeApiName);

    /**
     * 选项是否允许被删除
     * 2. 阶段推进器等使用了选项但是并没有数据对象使用
     *
     * @param user
     * @param objectDescribeApiName
     * @param fieldApiName
     * @param values
     */
    void checkOptionUsedByReference(User user, String objectDescribeApiName, String fieldApiName, Set<String> values);

    void processFilterSupportDisplayName(String tenantId, IObjectDescribe objectDescribe, ISearchTemplateQuery searchQuery);

    void processFilterSupportDisplayName(String tenantId, IObjectDescribe objectDescribe, List<IFilter> filters);

    int countObjectDataWithoutFilter(String tenantId, String objectApiName, String searchSource);

    Integer countObjectDataFromDB(String tenantId, String objApiName, ISearchTemplateQuery query);

    void renderERVarFromFilterValues(User user, List<IFilter> filters);

    void handleRecentVisitScene(User user, String apiName, ISearchTemplate searchTemplate, SearchTemplateQueryExt queryExt);

    boolean hasIndex(String tenantId, String objectApiName, String fieldApiName);

    @Data
    @Builder
    class QueryContext {
        @NonNull
        private User user;
        /**
         * 不处理计算字段
         * 元数据不处理引用字段
         */
        @Builder.Default
        private boolean isSimple = false;
        /**
         * 跳过es查询的recentUpdate校验
         */
        @Builder.Default
        private boolean esSearchSkipRecentUpdateCheck = false;

        /**
         * 强制es查询走recentUpdate校验
         */
        @Builder.Default
        private boolean esRecentUpdateCheck = false;

        /**
         * 查询相关团队
         */
        @Builder.Default
        private boolean skipRelevantTeam = false;
        /**
         * es 分页查询优化的标记
         */
        @Builder.Default
        private boolean paginationOptimization = false;

        /**
         * 查询返回的字段（只select指定的字段）
         */
        @Builder.Default
        private List<String> projectionFields = null;

        /**
         * 查询是否返回作废数据
         */
        @Builder.Default
        private boolean includeInvalid = false;

        /**
         * 是否实时计算落地的计算字段
         */
        @Builder.Default
        private boolean calculateFormula = false;

        /**
         * 是否实时计算引用字段
         */
        @Builder.Default
        private boolean calculateQuote = false;

        /**
         * 是否实时计算统计字段
         */
        @Builder.Default
        private boolean calculateCount = false;

        /**
         * 是否将引用字段转换成页面展示的格式
         */
        @Builder.Default
        private boolean convertQuoteForView = false;

        /**
         * 是否补充各种字段的__r
         */
        @Builder.Default
        private boolean fillExtendInfo = false;

        /**
         * 是否处理引用字段的值(与fillExtendInfo配合使用，fillExtendInfo=true时这个参数才生效)
         */
        @Builder.Default
        private boolean notFillQuote = false;

        /**
         * 是否处理掩码字段(与fillExtendInfo配合使用，fillExtendInfo=true时这个参数才生效)
         */
        @Builder.Default
        private boolean notFillMask = false;

        /**
         * 是否返回富文本详细信息
         */
        @Builder.Default
        private boolean searchRichTextExtra = false;

        @Builder.Default
        private Boolean keepAllMultiLangValue;

        @Builder.Default
        private boolean validateFilterField = false;

        @Builder.Default
        private boolean needOptionLabel = false;

        /**
         * 查询超时时间（秒）
         */
        @Builder.Default
        private Integer timeoutNSecond = null;

        @Builder.Default
        private String statOnEmptyResult;

        @Builder.Default
        private boolean generateSignUrlAndEncryptSignature = true;

        public boolean onlySelectPreferredField() {
            return CollectionUtils.notEmpty(projectionFields);
        }
    }
}
