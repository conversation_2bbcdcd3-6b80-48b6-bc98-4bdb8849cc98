package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.common.exception.CrmDefObjCheckedException;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.appframework.common.util.DateTimeUtils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.multiRegion.MultiRegionDateTimeFormatUtils;
import com.facishare.paas.timezone.TimeZoneContext;
import com.ibm.icu.util.ULocale;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.text.SimpleDateFormat;

/**
 * Created by linqiuying on 17/5/19.
 */
@Slf4j
public class TimeDataConverter extends AbstractFieldDataConverter {
    @Override
    public String convertFieldData(SessionContext sessionContext) throws CrmDefObjCheckedException {
        Object o = getObjectData().get(getFieldDescribe().getApiName());
        if (o != null && !"".equals(o) && !"null".equals(o)) {
            if (o instanceof String) {
                return (String) o;
            }
            try {
                if (DateTimeUtils.isGrayTimeZone()) {
                    return format(o, IFieldType.TIME, sessionContext);
                }
                if (StringUtils.isNotBlank(sessionContext.getRegion())) {
                    return MultiRegionDateTimeFormatUtils.formatForRegion(o, TimeZoneContext.DEFAULT_TIME_ZONE, IFieldType.TIME, new ULocale(sessionContext.getRegion()));
                }
                SimpleDateFormat format;
                if (UdobjGrayConfig.isAllow("export_date_time_second_gray", String.valueOf(sessionContext.getEId()))) {
                    format = new SimpleDateFormat("HH:mm:ss");
                } else {
                    format = new SimpleDateFormat("HH:mm");
                }
                return format.format(o);
            } catch (Exception e) {
                log.error("datetimeDataCovert error, data is {}", o, e);
            }
        }
        return "";
    }
}
