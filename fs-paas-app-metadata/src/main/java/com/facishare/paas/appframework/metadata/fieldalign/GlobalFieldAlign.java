package com.facishare.paas.appframework.metadata.fieldalign;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import lombok.EqualsAndHashCode;
import lombok.ToString;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by <PERSON><PERSON><PERSON>ju on 2022/5/13
 */
@EqualsAndHashCode
@ToString
public class GlobalFieldAlign {
    @JsonProperty("field_align")
    private FieldAlign fieldAlign;
    @JsonProperty("detail_form_layout")
    private DetailFormLayout detailFormLayout;

    private GlobalFieldAlign(FieldAlign fieldAlign, DetailFormLayout detailFormLayout) {
        this.fieldAlign = fieldAlign;
        this.detailFormLayout = detailFormLayout;
    }

    @JsonCreator
    public static GlobalFieldAlign of(@JsonProperty("field_align") String fieldAlign, @JsonProperty("detail_form_layout") String detailFormLayout) {
        return new GlobalFieldAlign(FieldAlign.of(fieldAlign), DetailFormLayout.of(detailFormLayout));
    }

    /**
     * 校验是否和法
     * <p>
     * fieldAlign 为空,认为不合法
     *
     * @return false 不合法发
     */
    public boolean validateFieldAlign() {
        return Objects.nonNull(fieldAlign);
    }

    /**
     * 获取字段对齐方式,为空时获取默认的对齐方式--左对齐
     *
     * @return 返回 fieldAlign, 为空时返回 {@link FieldAlign.LEFT}
     */
    public FieldAlign getFieldAlign() {
        return Optional.ofNullable(fieldAlign).orElse(FieldAlign.LEFT);
    }

    public String getDetailFormLayout() {
        return Optional.ofNullable(detailFormLayout).map(DetailFormLayout::getType).orElse(null);
    }


    public enum FieldAlign {
        LEFT("left"),
        CENTER("center"),
        TOP_AND_BOTTOM("top_and_bottom"),
        WHOLE_LEFT("whole_left"),
        ;
        private final String type;

        FieldAlign(String type) {
            this.type = type;
        }

        private static final Map<String, FieldAlign> FIELD_ALIGN_MAP;

        static {
            FIELD_ALIGN_MAP = Stream.of(values()).collect(Collectors.toMap(FieldAlign::getType, Function.identity()));
        }

        @JsonCreator
        public static FieldAlign of(String type) {
            return FIELD_ALIGN_MAP.get(type);
        }

        @JsonValue
        public String getType() {
            return type;
        }
    }

    public enum DetailFormLayout {
        RESPONSIVE("responsive"),
        STATIC("static"),
        ;
        private final String type;

        DetailFormLayout(String type) {
            this.type = type;
        }

        private static final Map<String, DetailFormLayout> map;

        static {
            map = Stream.of(values()).collect(Collectors.toMap(DetailFormLayout::getType, Function.identity()));
        }

        @JsonCreator
        public static DetailFormLayout of(String type) {
            return map.get(type);
        }

        @JsonValue
        public String getType() {
            return type;
        }
    }
}
