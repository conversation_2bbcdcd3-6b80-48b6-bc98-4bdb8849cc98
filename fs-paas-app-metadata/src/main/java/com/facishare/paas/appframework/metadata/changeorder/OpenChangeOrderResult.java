package com.facishare.paas.appframework.metadata.changeorder;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import lombok.Data;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/5/11
 */
@Data
public class OpenChangeOrderResult {
    private final int code;
    private final String message;

    public static final int SUCCESS_CODE = 0;
    public static final int HAS_FLOW_DEFINITIONS_CODE = 1;
    public static final int FAIL_CODE = 2;

    private OpenChangeOrderResult(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public static OpenChangeOrderResult buildFail(String message) {
        return new OpenChangeOrderResult(FAIL_CODE, message);
    }

    public static OpenChangeOrderResult buildHasFlowDefinitions() {
        return new OpenChangeOrderResult(HAS_FLOW_DEFINITIONS_CODE, I18NExt.text(I18NKey.HAS_FLOW_DEFINITIONS));
    }

    public static OpenChangeOrderResult buildSuccess() {
        return new OpenChangeOrderResult(0, "");
    }

    public boolean success() {
        return SUCCESS_CODE == code;
    }
}
