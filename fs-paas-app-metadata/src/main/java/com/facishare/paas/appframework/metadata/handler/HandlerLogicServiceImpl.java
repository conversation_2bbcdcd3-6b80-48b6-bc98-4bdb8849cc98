package com.facishare.paas.appframework.metadata.handler;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.HandlerGrayConfig;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.repository.model.HandlerDefinition;
import com.facishare.paas.appframework.metadata.repository.model.HandlerRuntimeConfig;
import com.facishare.paas.metadata.dao.pg.config.NotSupportMetadataTransaction;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/1/5.
 */
@Service("handlerConfigLogicService")
public class HandlerLogicServiceImpl implements HandlerLogicService {
    @Autowired
    private HandlerRuntimeConfigService handlerRuntimeConfigService;
    @Autowired
    private HandlerDefinitionService handlerDefinitionService;

    @Override
    @NotSupportMetadataTransaction
    public List<HandlerInfo> findHandlerInfoForManagement(String tenantId, String objectApiName, String interfaceCode) {
        if (Strings.isNullOrEmpty(objectApiName) || Strings.isNullOrEmpty(interfaceCode)) {
            return Lists.newArrayList();
        }
        List<HandlerRuntimeConfig> configs = handlerRuntimeConfigService.findByObjectApiNameAndInterfaceCode(tenantId,
                objectApiName, interfaceCode, true, true);
        if (CollectionUtils.empty(configs)) {
            return Lists.newArrayList();
        }
        List<String> handlerApiNames = configs.stream().map(HandlerRuntimeConfig::getHandlerApiName).distinct().collect(Collectors.toList());
        List<HandlerDefinition> definitions = handlerDefinitionService.findByApiNames(tenantId, handlerApiNames, true, true);
        //过滤禁用的预置handler
        definitions.removeIf(x -> !x.provideByTenant() && !x.isActive());
        if (CollectionUtils.empty(definitions)) {
            return Lists.newArrayList();
        }
        Map<String, HandlerDefinition> definitionMap = definitions.stream().collect(Collectors.toMap(HandlerDefinition::getApiName, x -> x, (a, b) -> a));
        return configs.stream()
                .filter(config -> definitionMap.containsKey(config.getHandlerApiName()))
                .filter(config -> {
                    HandlerDefinition definition = definitionMap.get(config.getHandlerApiName());
                    //过滤禁用的预置handler
                    if (!definition.provideByTenant() && !config.isActive()) {
                        return false;
                    }
                    //过滤启用了灰度策略并且不在灰度名单中的Handler
                    if (definition.isEnableGrayConfig() && !HandlerGrayConfig.isHandlerInWhiteList(tenantId, config.getHandlerApiName(), objectApiName)) {
                        return false;
                    }
                    //过滤配置了隐藏属性的Handler
                    return !definition.hiddenInManagement();
                })
                .map(config -> HandlerInfo.of(definitionMap.get(config.getHandlerApiName()), config))
                .sorted(Comparator.comparingInt(HandlerInfo::getExecuteOrder))
                .collect(Collectors.toList());
    }

    @Override
    public List<SimpleHandlerDescribe> findHandlerDescribeByInterfaceCode(String tenantId, String objectApiName, String interfaceCode) {
        if (Strings.isNullOrEmpty(objectApiName) || Strings.isNullOrEmpty(interfaceCode)) {
            return Lists.newArrayList();
        }
        List<HandlerRuntimeConfig> configs = handlerRuntimeConfigService.findByObjectApiNameAndInterfaceCode(tenantId,
                objectApiName, interfaceCode, true, false);
        if (CollectionUtils.empty(configs)) {
            return Lists.newArrayList();
        }
        List<String> handlerApiNames = configs.stream().map(HandlerRuntimeConfig::getHandlerApiName).distinct().collect(Collectors.toList());
        List<HandlerDefinition> definitions = handlerDefinitionService.findByApiNames(tenantId, handlerApiNames, true, false);
        if (CollectionUtils.empty(definitions)) {
            return Lists.newArrayList();
        }
        String appId = RequestUtil.getAppId();
        Map<String, HandlerDefinition> definitionMap = definitions.stream().collect(Collectors.toMap(HandlerDefinition::getApiName, x -> x, (a, b) -> a));
        return configs.stream()
                .filter(config -> definitionMap.containsKey(config.getHandlerApiName()))
                .filter(config -> {
                    HandlerDefinition definition = definitionMap.get(config.getHandlerApiName());
                    if (!definition.defineByAppId(appId)) {
                        return false;
                    }
                    return !definition.isEnableGrayConfig() || HandlerGrayConfig.isHandlerInWhiteList(tenantId, config.getHandlerApiName(), objectApiName);
                })
                .map(config -> SimpleHandlerDescribe.of(definitionMap.get(config.getHandlerApiName()), config))
                .sorted(Comparator.comparingInt(SimpleHandlerDescribe::getExecuteOrder))
                .collect(Collectors.toList());
    }

    @Override
    public void batchUpsertHandlerDefinitionAndRuntimeConfig(User user, List<HandlerDefinition> definitions, List<HandlerRuntimeConfig> runtimeConfigs) {
        handlerDefinitionService.batchUpsert(user, definitions);
        handlerRuntimeConfigService.batchUpsert(user, runtimeConfigs);
    }

    @Override
    public void enableHandler(User user, HandlerRuntimeConfigUniqueKey uniqueKey) {
        if (Objects.isNull(uniqueKey) || !uniqueKey.check()) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        HandlerRuntimeConfig runtimeConfig = handlerRuntimeConfigService.findByUniqueKey(user.getTenantId(), uniqueKey);
        if (Objects.isNull(runtimeConfig)) {
            throw new ValidateException("Handler not found");
        }
        if (runtimeConfig.isActive()) {
            return;
        }
        runtimeConfig.setActive(true);
        handlerRuntimeConfigService.batchUpdate(user, Lists.newArrayList(runtimeConfig));
    }

    @Override
    public void disableHandler(User user, HandlerRuntimeConfigUniqueKey uniqueKey) {
        if (Objects.isNull(uniqueKey) || !uniqueKey.check()) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        HandlerRuntimeConfig runtimeConfig = handlerRuntimeConfigService.findByUniqueKey(user.getTenantId(), uniqueKey);
        //租户库没查到，到系统库查一下
        if (Objects.isNull(runtimeConfig) && !RequestUtil.isSystemTenant(user.getTenantId())) {
            runtimeConfig = handlerRuntimeConfigService.findByUniqueKey(HandlerGrayConfig.getSystemTenantId(user.getTenantId()), uniqueKey);
        }
        //租户库和系统库都没查到，抛异常
        if (Objects.isNull(runtimeConfig)) {
            throw new ValidateException("Handler not found");
        }
        //查询到的HandlerRuntimeConfig的状态和要更新的状态一致，直接返回
        if (!runtimeConfig.isActive()) {
            return;
        }
        //租户库的请求且没有在租户库查到HandlerRuntimeConfig则创建一个新的，否则直接更新is_active=false
        if (!Objects.equals(user.getTenantId(), runtimeConfig.getTenantId())) {
            runtimeConfig.setId(null);
            runtimeConfig.setTenantId(user.getTenantId());
            runtimeConfig.setActive(false);
            handlerRuntimeConfigService.batchCreate(user, Lists.newArrayList(runtimeConfig));
        } else {
            runtimeConfig.setActive(false);
            handlerRuntimeConfigService.batchUpdate(user, Lists.newArrayList(runtimeConfig));
        }
    }

    private DiffResult diffTenantHandler(User user, List<HandlerInfo> handlerList, String interfaceCode, String objectApiName) {
        if (CollectionUtils.empty(handlerList)) {
            return new DiffResult();
        }
        //从租户库和系统库中查询HandlerRuntimeConfig
        List<HandlerRuntimeConfig> dbRuntimeConfigs = handlerRuntimeConfigService.findByObjectApiNameAndInterfaceCode(user.getTenantId(),
                objectApiName, interfaceCode, true, true);
        //从租户库中查询HandlerDefinition
        List<String> dbDefinitionApiNames = dbRuntimeConfigs.stream().map(HandlerRuntimeConfig::getHandlerApiName).distinct().collect(Collectors.toList());
        List<HandlerDefinition> dbDefinitions = handlerDefinitionService.findByApiNames(user.getTenantId(), dbDefinitionApiNames,
                true, true);
        Map<String, HandlerDefinition> dbDefinitionMap = dbDefinitions.stream().collect(Collectors.toMap(HandlerDefinition::getApiName,
                x -> x, (a, b) -> a));
        List<HandlerInfo> dbHandlerList = dbRuntimeConfigs.stream()
                .filter(config -> dbDefinitionMap.containsKey(config.getHandlerApiName()))
                .map(config -> HandlerInfo.of(dbDefinitionMap.get(config.getHandlerApiName()), config))
                .sorted(Comparator.comparingInt(HandlerInfo::getExecuteOrder))
                .collect(Collectors.toList());

        //将数据库中查到的Handler合并到提交的Handler中
        HandlerInfo.merge(handlerList, dbHandlerList);

        //计算租户级Handler的executeOrder
        Map<String, List<HandlerInfo>> type2HandlerMap = handlerList.stream().collect(Collectors.groupingBy(HandlerInfo::getHandlerType));
        type2HandlerMap.forEach((handlerType, groupHandlerList) -> HandlerType.getByCode(handlerType).calculateTenantHandlerOrder(groupHandlerList));

        List<HandlerInfo> tenantHandlerList = CollectionUtils.nullToEmpty(handlerList).stream()
                .filter(x -> x.toDefinition(interfaceCode, objectApiName).provideByTenant())
                .collect(Collectors.toList());
        Map<HandlerRuntimeConfigUniqueKey, HandlerInfo> tenantHandlerMap = tenantHandlerList.stream()
                .collect(Collectors.toMap(x -> HandlerRuntimeConfigUniqueKey.of(objectApiName, interfaceCode, x.getApiName()),
                        x -> x, (a, b) -> a));
        Set<String> tenantDefinitionApiNames = tenantHandlerList.stream().map(HandlerInfo::getApiName).collect(Collectors.toSet());

        //更新租户库中的HandlerDefinition和HandlerRuntimeConfig
        List<HandlerDefinition> tenantDefinitions = tenantHandlerList.stream().map(x -> x.toDefinition(interfaceCode, objectApiName)).collect(Collectors.toList());
        List<HandlerRuntimeConfig> tenantRuntimeConfigs = tenantHandlerList.stream().map(x -> x.toConfig(interfaceCode, objectApiName)).collect(Collectors.toList());

        //找出并删除页面上删除的租户级的Handler
        List<HandlerRuntimeConfig> tenantRuntimeConfigList2Delete = dbRuntimeConfigs.stream()
                .filter(x -> dbDefinitionMap.containsKey(x.getHandlerApiName()))
                .filter(x -> dbDefinitionMap.get(x.getHandlerApiName()).provideByTenant())
                .filter(x -> !tenantHandlerMap.containsKey(HandlerRuntimeConfigUniqueKey.of(x)))
                .collect(Collectors.toList());
        List<HandlerDefinition> tenantDefinitions2Delete = dbDefinitions.stream()
                .filter(HandlerDefinition::provideByTenant)
                .filter(x -> !tenantDefinitionApiNames.contains(x.getApiName()))
                .collect(Collectors.toList());

        return DiffResult.builder()
                .definitions2Upsert(tenantDefinitions)
                .runtimeConfigs2Upsert(tenantRuntimeConfigs)
                .definitions2Delete(tenantDefinitions2Delete)
                .runtimeConfigs2Delete(tenantRuntimeConfigList2Delete)
                .build();
    }

    @Override
    public void batchUpsertTenantHandler(User user, List<HandlerInfo> handlerList, String interfaceCode, String objectApiName) {
        if (CollectionUtils.empty(handlerList)) {
            return;
        }
        //和数据库中的HandlerDefinition和HandlerRuntimeConfig进行比较，找出需要更新的和删除的
        DiffResult diffResult = diffTenantHandler(user, handlerList, interfaceCode, objectApiName);
        //更新数据库
        batchUpsertHandler(user, diffResult);
    }

    @Override
    public void batchUpsertHandler(User user, DiffResult diffResult) {
        handlerRuntimeConfigService.batchDelete(user, diffResult.getRuntimeConfigs2Delete());
        handlerDefinitionService.batchDelete(user, diffResult.getDefinitions2Delete());
        batchUpsertHandlerDefinitionAndRuntimeConfig(user, diffResult.getDefinitions2Upsert(), diffResult.getRuntimeConfigs2Upsert());
    }

}
