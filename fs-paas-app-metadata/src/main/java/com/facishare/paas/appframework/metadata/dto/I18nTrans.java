package com.facishare.paas.appframework.metadata.dto;

import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

public interface I18nTrans {


    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    class TransArg {
        private String name;
        private String customKey;
        @Builder.Default
        private List<String> preKeyList = Lists.newArrayList();
    }
}
