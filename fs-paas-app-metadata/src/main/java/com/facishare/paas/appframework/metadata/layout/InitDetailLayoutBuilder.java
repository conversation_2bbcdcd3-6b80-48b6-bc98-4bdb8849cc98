package com.facishare.paas.appframework.metadata.layout;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.strcuture.LayoutStructureExt;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.SimpleComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TabsComponent;
import com.facishare.paas.metadata.ui.layout.*;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Builder;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

@Builder
public class InitDetailLayoutBuilder extends InitLayoutHandler {

    private ILayout editLayout;
    private ILayout detailLayout;
    private IObjectDescribe describe;
    private User user;
    private DescribeLogicService describeLogicService;
    private OptionalFeaturesSwitchDTO optionalFeaturesSwitch;
    private BiFunction<User, String, String> getOriginalApiNameFunction;

    public ILayout getDetailLayout() {
        LayoutComponents.restoreComponentOrder(LayoutExt.of(editLayout));
        List<IComponent> orderComponents = ComponentOrder.order(LayoutExt.of(editLayout).getComponentsSilently());
        ILayout detailLayout = copyLayout(editLayout, LayoutTypes.DETAIL);
        detailLayout.setComponents(orderComponents);
        List<IComponent> components = buildComponents(detailLayout);
        detailLayout.setComponents(components);
        buildDetailLayoutStructure(detailLayout);
        LayoutExt.of(detailLayout).removeI18n();
        return detailLayout;
    }

    public void mergeEditLayoutFieldConfigToDetailLayout() {
        LayoutExt editLayoutExt = LayoutExt.of(editLayout);
        LayoutComponents.restoreComponentOrder(editLayoutExt);
        List<IComponent> orderComponents = ComponentOrder.order(editLayoutExt.getComponentsSilently());
        editLayoutExt.setComponents(orderComponents);
        // 获取需要复制的组件（表单，表格，文本框组件）
        List<IComponent> copyComponents = EditLayout.of(editLayout).getComponentsWithField();
        processComponentsWithField(editLayoutExt, copyComponents);
        LayoutExt detailLayoutExt = LayoutExt.of(detailLayout);
        List<IComponent> componentsRemoved = detailLayoutExt.removeRefComponentsWithEditLayout();
        List<String> componentNamesRemoved = componentsRemoved.stream().map(IComponent::getName).collect(Collectors.toList());
        List<String> copyComponentNames = copyComponents.stream().map(IComponent::getName).collect(Collectors.toList());
        copyComponents(copyComponents, detailLayoutExt, componentsRemoved, componentNamesRemoved);
//        topInfoComponentFieldFilter(detailLayoutExt);
        List<IComponent> components = detailLayoutExt.getComponentsSilently();
        // 表格拖拽问题修复
        Set<String> retainedComponentNames = components.stream().map(IComponent::getName).collect(Collectors.toSet());
        List<String> needClearComponentNames = componentNamesRemoved.stream()
                .filter(componentName -> !componentName.equals(ComponentExt.FORM_COMPONENT))
                .collect(Collectors.toList());
        retainedComponentNames.removeIf(needClearComponentNames::contains);
        LayoutStructure.clearLayoutStructureByComponentNames(detailLayoutExt, components, retainedComponentNames);
        processComponentNamesList(editLayoutExt, copyComponentNames, componentNamesRemoved);
    }

    private void topInfoComponentFieldFilter(LayoutExt detailLayoutExt) {
        // 摘要组件过滤隐藏的字段
        List<String> fieldApiNamesInLayout = Lists.newArrayList();
        detailLayoutExt.getFormComponent().ifPresent(formComponent -> {
            List<String> fieldApiNames = formComponent.getFieldSections().stream()
                    .map(IFieldSection::getFields)
                    .flatMap(Collection::stream)
                    .map(IFormField::getFieldName)
                    .collect(Collectors.toList());
            fieldApiNamesInLayout.addAll(fieldApiNames);
        });
        detailLayoutExt.getFormTables().forEach(formTable -> {
            List<String> fieldApiNames = formTable.getFields().stream()
                    .map(IFormField::getFieldName)
                    .collect(Collectors.toList());
            fieldApiNamesInLayout.addAll(fieldApiNames);
        });
        detailLayoutExt.retainTopInfoComponentFieldsByPredicate(x -> fieldApiNamesInLayout.contains(x.getFieldName()));
    }

    private void copyComponents(List<IComponent> copyComponents, LayoutExt detailLayoutExt, List<IComponent> componentsRemoved, List<String> componentNamesRemoved) {
        copyComponents.forEach(component -> {
            component.set(ComponentExt.IS_DISABLED, Boolean.TRUE);
            Optional<IComponent> componentOptional = componentsRemoved.stream()
                    .filter(x -> componentNamesRemoved.contains(component.getName()) && StringUtils.equals(component.getName(), x.getName()))
                    .findFirst();
            if (!componentOptional.isPresent()) {
                if (ComponentExt.FORM_COMPONENT.equals(component.getName())) {
                    // 还原引用布局相关配置
                    FormComponentExt.of((IFormComponent) component).setReferenceFieldConfig(editLayout.getName());
                }
                detailLayoutExt.addComponent(component);
            } else {
                IComponent copyComponent = componentOptional.get();
                // 引用新建编辑页布局的组件支持修改属性。暂支持修改 form_component 组件属性
                if (ComponentExt.FORM_COMPONENT.equals(copyComponent.getName())) {
                    List<IFieldSection> fieldSections = FormComponentExt.of((IFormComponent) component).getFieldSections();
                    FormComponentExt.of((IFormComponent) copyComponent).setFieldSections(fieldSections);
                    copyComponent.set(ComponentExt.IS_DISABLED, Boolean.TRUE);
                    FormComponentExt.of((IFormComponent) copyComponent).setReferenceFieldConfig(editLayout.getName());
                    detailLayoutExt.addComponent(copyComponent);
                } else {
                    detailLayoutExt.addComponent(component);
                }
            }
        });
    }

    private void processComponentNamesList(LayoutExt layoutExt, List<String> copyComponentNames, List<String> componentNamesRemoved) {
        List<String> textComponentNamesInTable = layoutExt.getTextComponentNamesInTable();
        List<String> addComponentNames = copyComponentNames.stream()
                .filter(x -> !textComponentNamesInTable.contains(x))
                .collect(Collectors.toList());
        componentNamesRemoved.removeIf(copyComponentNames::contains);
        LayoutExt detailLayoutExt = LayoutExt.of(detailLayout);
        detailLayoutExt.getTabsComponents().forEach(tabsComponent -> {
            List<List<String>> sourceComponentNamesList = tabsComponent.getComponents();
            LayoutExt.processComponentNames(sourceComponentNamesList, componentNamesRemoved, addComponentNames);
        });
        // 处理将详情页组件拖到外面的场景
        LayoutStructure.processComponentNamesList(detailLayoutExt, componentNamesRemoved, addComponentNames);
    }

    private void buildDetailLayoutStructure(ILayout detailLayout) {
        LayoutExt layout = LayoutExt.of(detailLayout);
        List<IComponent> components = layout.getComponentsSilently();
        components = getExcludeFormTableTextComponents(components);
        components = removeComponentsContainedInTabs(components);
        Map<String, Object> layoutStructure = Maps.newLinkedHashMap();
        Map<String, Object> row1 = Maps.newLinkedHashMap();
        row1.put(LayoutStructure.COLUMNS, Lists.newArrayList(ImmutableMap.of("width", "100%")));
        List<List<String>> components1 = Lists.newArrayList();
        components1.add(Lists.newArrayList(ComponentExt.HEAD_INFO_COMPONENT_NAME));
        row1.put(LayoutStructure.COMPONENTS, components1);

        Map<String, Object> row2 = Maps.newLinkedHashMap();
        row2.put(LayoutStructure.COLUMNS, Lists.newArrayList(ImmutableMap.of("width", "auto"), ImmutableMap.of("retractable", true, "width", "500px")));
        List<List<String>> components2 = Lists.newArrayList();
        List<String> childComponents2 = components.stream()
                .filter(x -> !ComponentExt.of(x).isMasterDetailComponent()
                        && !ComponentExt.TEAM_COMPONENT_NAME.equals(x.getName())
                        && !ComponentExt.SALE_LOG_COMPONENT_NAME.equals(x.getName())
                        && !ComponentExt.HEAD_INFO_COMPONENT_NAME.equals(x.getName()))
                .map(IComponent::getName)
                .collect(Collectors.toList());
        components2.add(childComponents2);
        List<String> childComponents3 = components.stream()
                .filter(x -> ComponentExt.TEAM_COMPONENT_NAME.equals(x.getName())
                        || ComponentExt.SALE_LOG_COMPONENT_NAME.equals(x.getName()))
                .map(IComponent::getName)
                .collect(Collectors.toList());
        components2.add(childComponents3);
        row2.put(LayoutStructure.COMPONENTS, components2);

        layoutStructure.put(LayoutStructure.LAYOUT, Lists.newArrayList(row1, row2));
        layoutStructure.put(LayoutStructureExt.LAYOUT_STRUCTURE_TYPE, 2);
        detailLayout.setLayoutStructure(layoutStructure);
    }

    //移除包含在页签中的组件
    private List<IComponent> removeComponentsContainedInTabs(List<IComponent> components) {
        List<String> tabComponentApiName = Lists.newArrayList();
        components.stream().filter(x -> ComponentExt.of(x).isTabs())
                .map(x -> ((TabsComponent) x).getComponents())
                .forEach(x -> x.forEach(tabComponentApiName::addAll));
        return components.stream().filter(x -> !tabComponentApiName.contains(x.getName())).collect(Collectors.toList());
    }

    private List<IComponent> buildComponents(ILayout layout) {
        LayoutExt layoutExt = LayoutExt.of(layout);
        //结果表单组件
        List<IComponent> resultComponents = Lists.newArrayList();

        List<IButton> buttons = CollectionUtils.nullToEmpty(layoutExt.getButtons());
        //过滤隐藏、禁用的按钮
        List<String> hiddenButtons = CollectionUtils.nullToEmpty(layoutExt.getHiddenButtons());
        buttons.removeIf(button -> !LayoutButtonExt.of(button).isActive() || hiddenButtons.contains(button.getName()));

        SimpleComponent simpleComponent = LayoutComponents.buildHeadInfoComponent();
        simpleComponent.setButtons(buttons);

        resultComponents.add(simpleComponent);
        //摘要信息
        LayoutComponents.addTopInfoComponentByLayout(layoutExt, resultComponents, Lists.newArrayList(), ObjectDescribeExt.of(describe));
        //摘要卡片
        LayoutComponents.addSummaryCardComponent(describe.getApiName(), resultComponents, Lists.newArrayList(), layoutExt);
        // 获取需要复制的组件（表单，表格，文本框组件）
        List<IComponent> copyComponents = EditLayout.of(layout).getComponentForDetailLayout();
        processComponentsWithField(layoutExt, copyComponents);
        resultComponents.addAll(copyComponents);
        // 补充相关组件和从组件
        List<IObjectDescribe> relatedDescribes = describeLogicService.findRelatedDescribes(layout.getTenantId(), describe.getApiName());
        List<IComponent> detailAndRefComponents = fillRelatedAndMasterComponents(relatedDescribes, ObjectDescribeExt.of(describe), LayoutExt.of(layout));
        resultComponents.addAll(detailAndRefComponents);
        //补充相关团队组件
        if (optionalFeaturesSwitch.getIsRelatedTeamEnabled()) {
            resultComponents.add(TeamComponentBuilder.builder().build().buildUserListComponentWithoutFieldsAndButtons());
        }
        if (optionalFeaturesSwitch.getIsModifyRecordEnabled()) {
            resultComponents.add(LayoutExt.buildModifyRecordComponent());
        }
        if (!AppFrameworkConfig.isCloseFollowUpDynamicSwitchObject(describe.getApiName())) {
            resultComponents.add(RelatedObjectGroupComponentBuilder.builder().build()
                    .buildRecordComponentWithoutFieldsAndButtons(describe.getApiName()));
        }
        resultComponents.add(buildTabComponent(resultComponents, true, copyComponents));
        return resultComponents;
    }

    private void processComponentsWithField(LayoutExt layoutExt, List<IComponent> needCopyComponents) {
        List<FormComponentExt> formComponents = layoutExt.getFormComponents();

        //提取表单组件中间的组件
        List<IComponent> bottomComponents = Lists.newArrayList();
        if (formComponents.size() > 1) {
            List<String> componentTypeList = needCopyComponents.stream().map(IComponent::getType).collect(Collectors.toList());
            int pre = componentTypeList.indexOf(IComponent.TYPE_FORM);
            int after = componentTypeList.lastIndexOf(IComponent.TYPE_FORM);
            bottomComponents.addAll(Lists.newArrayList(needCopyComponents.subList(pre + 1, after)));
            bottomComponents.removeIf(x -> ComponentExt.of(x).isFormType());
            needCopyComponents.removeAll(bottomComponents);
        }

        //合并表单组件，将所有的表单组件按照从上到下的顺序，合并到form_component组件中
        List<IFieldSection> fieldSectionList = Lists.newArrayList();
        layoutExt.getFormComponent().ifPresent(formComponent -> {
            formComponents.forEach(x -> fieldSectionList.addAll(x.getFieldSections()));
            if (!ComponentExt.FORM_COMPONENT.equals(formComponent.getName())) {
                formComponent.setName(ComponentExt.FORM_COMPONENT);
                formComponent.set("_id", ComponentExt.FORM_COMPONENT);
            }
            formComponent.setHeader(I18N.text(I18NKey.DETAIL_INFO));
            ComponentExt.of(formComponent).setNameI18nKey(I18NKey.DETAIL_INFO);
            formComponent.setFieldSections(fieldSectionList);
        });
        //移除其他表单组件
        needCopyComponents.removeIf(x -> ComponentExt.of(x).isFormType() && !ComponentExt.FORM_COMPONENT.equals(x.getName()));
        if (CollectionUtils.notEmpty(bottomComponents)) {
            int index = needCopyComponents.stream().map(IComponent::getType).collect(Collectors.toList()).indexOf(IComponent.TYPE_FORM);
            needCopyComponents.addAll(index + 1, bottomComponents);
        }
    }


    private List<IComponent> fillRelatedAndMasterComponents(List<IObjectDescribe> relatedDescribes, ObjectDescribeExt describe, LayoutExt layout) {
        List<IComponent> detailAndRefComponents = Lists.newArrayList();

        List<RelatedObjectDescribeStructure> detailObjects = describe.getDetailObjectDescribeStructuresOrderByFieldCreateTime(relatedDescribes);
        List<RelatedObjectDescribeStructure> lookupObjects = describe.getRelatedObjectDescribeStructuresOrderByFieldCreateTime(relatedDescribes);
        //根据配置中心的配置去掉不展示的关联对象
        lookupObjects.removeIf(x -> DefObjConstants.isReferenceObjectInvisible(describe.getApiName(), x.getRelatedObjectDescribe().getApiName()));

        //从对象
        List<IComponent> detailComponentList = MasterDetailGroupComponentBuilder.builder()
                .layoutExt(layout)
                .detailObjectsDescribeList(detailObjects).build()
                .getComponentListForNewDesigner(false);

        //关联对象
        List<IComponent> relateComponentList = RelatedObjectGroupComponentBuilder.builder().layout(layout).objectDescribe(describe)
                .user(user)
                .relatedObjectDescribeList(lookupObjects).build().getComponentListForDesigner(false);
        relateComponentList = layout.filterComponents(relateComponentList);
        //使用新版附件替换旧版附件
        LayoutComponents.replaceOldAttachComponent(layout, relateComponentList);
        detailAndRefComponents.addAll(relateComponentList);
        detailAndRefComponents.addAll(detailComponentList);
        return detailAndRefComponents;
    }

}
