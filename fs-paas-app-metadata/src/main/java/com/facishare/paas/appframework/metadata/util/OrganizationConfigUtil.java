package com.facishare.paas.appframework.metadata.util;

import com.github.autoconf.ConfigFactory;

import java.util.Arrays;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by shun on 2020/2/26
 */
public class OrganizationConfigUtil {
    private static Set<String> importDepartmentGrayEnterpriseIds = new HashSet<>();
    private static Set<String> importDimensionGrayEnterpriseIds = new HashSet<>();

    static {
        ConfigFactory.getConfig("fs-social-feed-profile", config -> {
            importDepartmentGrayEnterpriseIds = Arrays.stream(config.get("importDepartmentGrayEnterpriseIds", "").split(",")).collect(Collectors.toSet());
            importDimensionGrayEnterpriseIds = Arrays.stream(config.get("importDimensionGrayEnterpriseIds", "").split(",")).collect(Collectors.toSet());
        });
    }

    public static boolean isImportDepartmentGray(String enterpriseId) {
        if (importDepartmentGrayEnterpriseIds.contains("*")) {
            return true;
        }
        return importDepartmentGrayEnterpriseIds.contains(enterpriseId);
    }

    public static boolean isImportDimensionGray(String enterpriseId) {
        if (importDimensionGrayEnterpriseIds.contains("*")) {
            return true;
        }
        return importDimensionGrayEnterpriseIds.contains(enterpriseId);
    }
}
