package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.service.model.WhereUsed.Entity;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.WhereUsedUtil;
import com.facishare.paas.appframework.metadata.reference.RefFieldService;
import com.facishare.paas.appframework.metadata.reference.WhereUsedDisplayConf.EntityConf;
import com.facishare.paas.appframework.metadata.reference.WhereUsedDisplayConf.I18nConf;
import com.facishare.paas.reference.data.EntityReferenceArg;
import com.facishare.paas.reference.service.EntityReferenceService;
import com.facishare.paas.reference.service.EntityReferenceService.MatchType;
import com.google.common.collect.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;

@Service
public class EntityReferenceExtServiceImpl implements EntityReferenceExtService {

    private EntityReferenceService entityReferenceService;

    private DescribeLogicService describeLogicService;

    @Autowired
    public void setEntityReferenceService(EntityReferenceService entityReferenceService) {
        this.entityReferenceService = entityReferenceService;
    }

    @Autowired
    public void setDescribeLogicService(DescribeLogicService describeLogicService) {
        this.describeLogicService = describeLogicService;
    }


    /**
     * 查询引用（where_used）
     *
     * @param user        查询人
     * @param targetType  类型
     * @param targetValue 值
     * @param matchType   匹配模式
     * @param limit       返回结果数量
     * @return 关系
     */
    @Override
    public List<EntityReferenceArg> findWhereFieldIsUsed(User user, String targetType, String targetValue, MatchType matchType, Integer limit) {
        String defineType = WhereUsedUtil.REF_DEFINE_TYPE;
        return entityReferenceService.findByTargetTypeAndTargetValue(user.getTenantId(), targetType, targetValue, matchType, limit, defineType);
    }

    @Override
    public List<Entity> findWhereUsedEntity(User user, String objApi, String targetType, String targetValue) {
        List<EntityReferenceArg> erList = findWhereFieldIsUsed(user,
                targetType, String.join(WhereUsedUtil.TARGET_VALUE_DELIMITER, objApi, targetValue), MatchType.EQ, 2000);
        List<Entity> entityList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(erList)) {
            Map<String, Map<String, EntityConf>> confMapMap = RefFieldService.TYPE_TO_CONF_ITEM;
            for (EntityReferenceArg er : erList) {
                String[] invokerAndDisplay = er.getSourceType().split(Pattern.quote(WhereUsedUtil.REF_TYPE_DELIMITER_SOURCE_DISPLAY_TYPE));
                String invokerType = invokerAndDisplay[0];
                String displayType = invokerAndDisplay[1];

                String[] defaultParamLabelArr = er.getSourceLabel().split(Pattern.quote(WhereUsedUtil.SOURCE_LABEL_DELIMITER));

                Optional<I18nConf> i18nConfOpt = Optional.ofNullable(confMapMap.get(invokerType))
                        .map(confMap -> confMap.get(displayType))
                        .map(EntityConf::getI18nConf);
                if (!i18nConfOpt.isPresent()) {
                    continue;
                }
                I18nConf i18nConf = i18nConfOpt.get();
                String invokerLabel = null;
                if (CollectionUtils.notEmpty(i18nConf.getParamKeyI18n())) {
                    List<String> paramKeyI18nList = i18nConf.getParamKeyI18n();
                    Object[] sourceValueArr = er.getSourceValue().split(Pattern.quote(WhereUsedUtil.SOURCE_VALUE_DELIMITER));
                    List<String> paramLabelList = Lists.newArrayList();
                    // sourceValueArr paramKeyI18nList defaultParamLabelArr 三者相同 index 的值是一样对应的都是指向同一事物
                    for (int i = 0; i < sourceValueArr.length; i++) {
                        Object apiName = sourceValueArr[i]; // APIName 对应某一实体
                        String i18nKeyTemplate = paramKeyI18nList.get(i); // 该实体对应多语模板 比如某对象的字段 {0}.field.{1}.label 0,1表示 sourceValueArr相应索引位置的值
                        String label = defaultParamLabelArr[i]; // 该实体对应默认显示内容
                        if (StringUtils.isNotBlank(i18nKeyTemplate)) {
                            // 模板不为空，根据模板和 sourceValueArr 获得 最终的多语 key
                            String i18nKey = MessageFormat.format(i18nKeyTemplate, sourceValueArr);
                            label = I18NExt.getOrDefault(i18nKey, label);
                        }

                        if (i18nConf.isApiWrapperLabel()
                                && CollectionUtils.notEmpty(i18nConf.getApiToParamIndex())
                                && i18nConf.getApiToParamIndex().size() > i) {
                            Integer index = i18nConf.getApiToParamIndex().get(i);
                            if (Objects.nonNull(index) && index >= 0) {
                                label += WhereUsedUtil.API_WRAPPER_PREFIX + apiName + WhereUsedUtil.API_WRAPPER_SUFFIX;
                            }
                        }

                        paramLabelList.add(label);
                    }
                    invokerLabel = I18NExt.getOrDefault(i18nConf.getKey(), i18nConf.getDefaultVal(), paramLabelList.toArray());
                }
                Entity entity = Entity.builder()
                        .invokerType(invokerType)
                        .invokerLabel(invokerLabel)
                        .build();
                entityList.add(entity);
            }

        }
        return entityList;
    }
}