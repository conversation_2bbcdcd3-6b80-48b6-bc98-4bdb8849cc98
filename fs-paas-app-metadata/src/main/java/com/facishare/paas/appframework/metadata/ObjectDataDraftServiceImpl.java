package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.IObjectDataDraft;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.service.IDraftService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.search.DraftSearchTemplateQuery;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2019/11/11 4:32 下午
 */
@Slf4j
@Service("objectDataDraftService")
public class ObjectDataDraftServiceImpl implements ObjectDataDraftService {

    @Autowired
    private IDraftService draftService;

    @Override
    public IObjectDataDraft createDraft(IObjectDataDraft draft, User user) {
        try {
            IActionContext actionContext = ActionContextExt.of(user).getContext();
            if (user.isOutUser()) {
                draft.setAppId(RequestUtil.getAppId());
            }
            return draftService.create(draft, actionContext);
        } catch (MetadataServiceException e) {
            log.warn("Create draft fail, draft:{}, tenantId:{}, userId:{}", draft, user.getTenantId(), user.getUserId());
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public IObjectDataDraft updateDraft(IObjectDataDraft draft, User user) {
        try {
            if (user.isOutUser() && Strings.isNullOrEmpty(draft.getAppId())) {
                draft.setAppId(RequestUtil.getAppId());
            }
            return draftService.update(draft);
        } catch (MetadataServiceException e) {
            log.warn("Update draft fail, draft:{}, tenantId:{}", draft, draft.getTenantId());
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void deleteDraftById(String tenantId, String id) {
        try {
            draftService.delete(tenantId, id);
        } catch (MetadataServiceException e) {
            log.warn("Delete draft fail, tenantId:{}, draftId:{}", tenantId, id);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public List<IObjectDataDraft> findDraftByUser(User user) {
        try {
            IActionContext actionContext = ActionContextExt.of(user).getContext();
            if (user.isOutUser() && AppFrameworkConfig.isDraftFilterAppId(user.getTenantId(), RequestUtil.getAppId())) {
                actionContext.setDraftFilterAppId(true);
            }
            return draftService.findByTenantIdAndUserId(actionContext);
        } catch (MetadataServiceException e) {
            log.warn("Find draft fail, tenantId:{}, userId:{}", user.getTenantId(), user.getUserId());
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public QueryResult<IObjectDataDraft> findDraftByTemplate(User user, String describeApiName, String recordType, List<String> dataSources, int limit, int offset) {
        try {
            DraftSearchTemplateQuery query = new DraftSearchTemplateQuery();
            query.setLimit(limit);
            query.setOffset(offset);
            boolean onlyReturnTotalNum = limit == 1 && StringUtils.isAllEmpty(describeApiName, recordType);
            if (onlyReturnTotalNum) {
                query.setReturnTotalNum(true);
            }
            Map<String, Object> queryMap = Maps.newHashMap();
            if (RequestUtil.isMobileRequest() || RequestUtil.isMobileDeviceRequest()) {
                List<String> notSupportObjectsInDraft = AppFrameworkConfig.getNotSupportObjectsInMobileDraft();
                if (StringUtils.isNotEmpty(describeApiName) && notSupportObjectsInDraft.contains(describeApiName)) {
                    return new QueryResult<>();
                }
                queryMap.put("describe_api_name_n", notSupportObjectsInDraft);
            }
            if (StringUtils.isNotEmpty(describeApiName)) {
                queryMap.put("describe_api_name", describeApiName);
                queryMap.put("data.record_type", recordType);
            }
            if (CollectionUtils.empty(dataSources)) {
                dataSources = Lists.newArrayList("Add", "Edit");
            }
            queryMap.put("data_source", dataSources);
            if (user.isOutUser() && AppFrameworkConfig.isDraftFilterAppId(user.getTenantId(), RequestUtil.getAppId())) {
                queryMap.put("app_id", RequestUtil.getAppId());
            }
            query.setQueryMap(queryMap);
            IActionContext actionContext = ActionContextExt.of(user).getContext();
            return draftService.findDraftByTemplate(query, actionContext);
        } catch (MetadataServiceException e) {
            log.warn("findDraftByTemplate fail, tenantId:{}, userId:{}", user.getTenantId(), user.getUserId());
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public Optional<IObjectDataDraft> findDraftById(String tenantId, String draftId) {
        try {
            return Optional.ofNullable(draftService.findById(tenantId, draftId));
        } catch (MetadataServiceException e) {
            log.warn("Find draft fail, tenantId:{}, draftId:{}", tenantId, draftId);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void deleteDraftByIds(String tenantId, List<String> idList) {
        try {
            draftService.batchDeleteByIds(tenantId, idList);
        } catch (MetadataServiceException e) {
            log.warn("Batch delete draft fail, tenantId:{}, draftIds:{}", tenantId, idList);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public boolean deleteDraftByUser(User user) {
        try {
            IActionContext context = ActionContextExt.of(user).getContext();
            draftService.deleteDraftByUserId(context);
            return true;
        } catch (Exception e) {
            //错误降级，不弹出未知异常错误，只记录error日志
            log.error("Batch delete draft by uer fail, user:{}", user);
            return false;
        }
    }

    @Override
    public IObjectDataDraft findByBizTypeAndBizId(User user, String bizType, String bizId) {
        if (Strings.isNullOrEmpty(bizType) || Strings.isNullOrEmpty(bizId)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        IActionContext context = ActionContextExt.of(user).getContext();
        try {
            return draftService.findByBizTypeAndBizId(bizType, bizId, context);
        } catch (MetadataServiceException e) {
            log.warn("findByBizTypeAndBizId failed,user:{},bizType:{},bizId:{} ", user, bizType, bizId);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public void deleteByBizTypeAndBizIds(String tenantId, String bizType, List<String> bizIds) {
        if (Strings.isNullOrEmpty(bizType) || CollectionUtils.empty(bizIds)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        try {
            draftService.deleteByBizTypeAndBizId(bizType, bizIds, tenantId);
        } catch (MetadataServiceException e) {
            log.warn("deleteByBizTypeAndBizId failed,tenantId:{},bizType:{},bizIds:{} ", tenantId, bizType, bizIds);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public boolean existPreviousDraft(User user, long timestamp) {
        IActionContext actionContext = ActionContextExt.of(user).getContext();
        try {
            return draftService.checkExistUnusedDraftByLastModifiedTime(actionContext, timestamp);
        } catch (MetadataServiceException e) {
            log.warn("existPreviousDraft fail, tenantId:{}, userId:{}, appId:{}, timestamp:{}", user.getTenantId(), user.getUserId(), actionContext.getAppId(), timestamp);
        }
        return false;
    }

    @Override
    public boolean deleteDraftByTimestamp(User user, long timestamp) {
        IActionContext actionContext = ActionContextExt.of(user).getContext();
        try {
            int num = draftService.deleteDraftByLastModifiedTime(actionContext, timestamp);
            log.info("delete num:{}, timestamp:{}", num, timestamp);
            return num > 0;
        } catch (MetadataServiceException e) {
            log.warn("deleteDraftByTimestamp fail, tenantId:{}, userId:{}, appId:{}, timestamp:{}", user.getTenantId(), user.getUserId(), actionContext.getAppId(), timestamp);
        }
        return false;
    }
}
