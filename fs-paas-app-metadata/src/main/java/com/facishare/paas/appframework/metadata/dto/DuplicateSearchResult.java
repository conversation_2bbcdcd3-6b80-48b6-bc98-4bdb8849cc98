package com.facishare.paas.appframework.metadata.dto;

import com.facishare.paas.metadata.api.IObjectData;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Data
@Builder
public class DuplicateSearchResult {

    private String code;

    private List<DuplicateData> duplicateDataList;

    private String message;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class DuplicateData {
        private String apiName;
        private String ruleApiName;
        private String ruleLabel;
        private List<String> dataIds;

        public String getRuleLabel() {
            return Objects.isNull(ruleLabel) ? "默认查重规则" : ruleLabel; // ignoreI18n
        }
    }

    public boolean success() {
        return StringUtils.equals(this.code, "200");
    }

    public List<DuplicateSearchDataInfo> toDuplicateSearchDataInfo(IObjectData data) {
        if (!success()) {
            return Collections.emptyList();
        }
        return duplicateDataList.stream()
                .map(it -> DuplicateSearchDataInfo.of(it.getApiName(), data.getId(), it.getDataIds()))
                .collect(Collectors.toList());
    }


}
