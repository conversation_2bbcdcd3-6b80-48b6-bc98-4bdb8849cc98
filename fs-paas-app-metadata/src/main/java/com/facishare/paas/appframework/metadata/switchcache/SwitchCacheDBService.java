package com.facishare.paas.appframework.metadata.switchcache;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.MtSwitch;

import java.util.Collection;
import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/04/16
 */
public interface SwitchCacheDBService {

    List<MtSwitch> querySwitch(User user, String switchType, String objectApiName, Collection<String> switchNames);

    List<MtSwitch> bulkUpsert(User user, List<MtSwitch> switches);

    void modifySwitch(User user, Collection<SwitchInfo> switchInfos);
}
