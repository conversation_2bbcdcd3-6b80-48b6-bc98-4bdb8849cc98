package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.service.dto.ReferenceData;
import com.facishare.paas.appframework.core.model.User;

import java.util.List;

public interface ReferenceLogicService {

    List<ReferenceData> findReferenceByTarget(String tenantId, String targetType, String targetValue);

    List<ReferenceData> findReferenceByTarget(String tenantId, String targetType, String targetValue, boolean isPrefixMatch);

    List<ReferenceData> findReferenceByTarget(String tenantId, String targetType, String targetValue, boolean isPrefixMatch, int limit);

    void deleteAndCreateReference(String tenantId, List<ReferenceData> items);

    void deleteReference(String tenantId, String sourceType, String sourceValue);

    void batchDeleteReference(String tenantId, String sourceType, List<String> sourceValues);

    List<ReferenceData> queryByTargetList(String tenantId, List<String> targetValues);

    void deleteAndCreateRelation(User user, List<ReferenceData> list);
}
