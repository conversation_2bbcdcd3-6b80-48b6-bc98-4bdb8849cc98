package com.facishare.paas.appframework.metadata.options;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.MtOptionSet;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.*;

/**
 * create by <PERSON><PERSON><PERSON> on 2021/12/13
 */
public interface OptionSetLogicService {
    String OPTION_REFERENCE_SOURCE_TYPE = "Describe.Field";
    String OPTION_REFERENCE_TARGET_TYPE = "OptionSet";

    MtOptionSet create(User user, MtOptionSet options);

    MtOptionSet update(User user, MtOptionSet options, boolean onlyUpdateOptions);

    List<MtOptionSet> findAll(User user);

    Map<String, MtOptionSet> findByApiNames(User user, Collection<String> optionApiNames);

    Optional<MtOptionSet> find(User user, String optionApiName);

    MtOptionSet enable(User user, String optionApiName);

    MtOptionSet disable(User user, String optionApiName);

    void deleted(User user, String optionApiName);

    void checkCount(User user);

    OptionReference findReference(User user, String optionApiName);

    void saveReference(User user, String describeApiName, String fieldApiName, String optionApiName);

    void deleteReference(User user, String describeApiName, String fieldApiName, String optionApiName);

    void deleteReferenceByField(User user, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe);

    void validateAndUpdateOptionReference(User user, IObjectDescribe describe, List<IFieldDescribe> fieldDescribeList);

    void validateAndUpdateOptionReference(User user, IObjectDescribe oldDescribe, IObjectDescribe newDescribe);

    /**
     * 触发全量计算，并更新所有引用了选项集对象的描述版本
     *
     * @param user
     * @param optionApiName
     * @param onlyTouch
     */
    void triggerCalculate(User user, String optionApiName, boolean onlyTouch);

    void asyncTriggerCalculate(User user, Set<String> optionApiNames, boolean onlyTouch);

    IFieldDescribe fillSelectOption(User user, IFieldDescribe fieldDescribe);
}
