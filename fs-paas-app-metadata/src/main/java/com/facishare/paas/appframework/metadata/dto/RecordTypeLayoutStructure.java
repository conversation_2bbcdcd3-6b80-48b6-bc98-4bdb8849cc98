package com.facishare.paas.appframework.metadata.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * Created by zhaopx on 2017/10/27.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecordTypeLayoutStructure {
    Map detail_layout;
    Map list_layout;
    String record_type;
    Boolean not_match;
    Boolean support_manual_add;
    List<Map> batchButtons;
    List<Map> singleButtons;
    List<Map> allPageSummaryInfo;
}
