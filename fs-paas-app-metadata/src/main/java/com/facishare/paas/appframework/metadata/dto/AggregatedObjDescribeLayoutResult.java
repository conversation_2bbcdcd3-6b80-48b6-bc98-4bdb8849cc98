package com.facishare.paas.appframework.metadata.dto;

import com.facishare.paas.metadata.ui.layout.ILayout;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;

/**
 * Created by luxin on 2018/5/15.
 * 聚合对象的describe和layout数据
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class AggregatedObjDescribeLayoutResult {
    //字段apiName
    String fieldApiName;
    //字段label
    String fieldLabel;
    //对象列表
    Map objectDescribe;
    //布局
    ILayout layout;
}
