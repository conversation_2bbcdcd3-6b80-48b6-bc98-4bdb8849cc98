package com.facishare.paas.appframework.metadata.expression;


import com.facishare.paas.appframework.metadata.MtCurrency;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IGlobalVariableDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 值计算接口
 * <p>
 * Created by liyiguang on 2018/4/18.
 */
public interface ExpressionValueCalculator {

    Object NA = null;

    void init();

    String key();

    Set<String> getDependentObjectAPINames();

    Set<String> getDependentGlobalVariableAPINames();

    Set<String> getDependentFormVariableAPINames();

    Map<String, Set<String>> getDependentObjectDataIds(IObjectData data);

    default void bindDependentObjectDataId(IObjectData data, Map<String, MtCurrency> functionalExchangeRateMap) {
        bindDependentObjectDataId(data, functionalExchangeRateMap, null);
    }

    void bindDependentObjectDataId(IObjectData data, Map<String, MtCurrency> functionalExchangeRateMap, Map<String, String> exchangeRateMap);

    boolean canCalculate(Set<String> availableDataSet);

    List<String> getDependentCountFieldNames(IObjectDescribe masterDescribe);

    void doCalculate(IObjectData data, Map<String, Object> globalVariableData, Map<String, Map<String, IObjectData>> objectDataMap,
                     Map<String, Object> extData, boolean isLookupDependencyForOthers);

    void bindingVariableType(
            Map<String, IGlobalVariableDescribe> globalVariableDescribes,
            Map<String, IObjectDescribe> describes,
            Map<String, String> formVariables);

    Object getResult();

    Expression getExpression();

    List<IFieldDescribe> getDependentCalcFields(boolean includeFormulaDefault);

    boolean dependExchangeRateFunction();

    boolean isDependentField(String fieldName);
}
