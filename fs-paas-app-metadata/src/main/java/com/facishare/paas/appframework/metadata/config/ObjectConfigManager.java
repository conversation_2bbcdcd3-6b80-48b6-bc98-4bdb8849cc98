package com.facishare.paas.appframework.metadata.config;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.JacksonUtils;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.bi.BIService;
import com.facishare.paas.appframework.metadata.config.util.ObjectDataCloneUtils;
import com.fxiaoke.common.MapUtils;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Created by fengjy in 2020/6/23 16:43
 */
@Component
@Slf4j
public class ObjectConfigManager {
    public static final String MODULE = "module";
    public static final String SUB_MODULES = "submodules";

    private final Map<String, List<ConfigModule>> objectConfigs = Maps.newConcurrentMap();
    private final Map<String, Map<String, List<ConfigModule>>> fieldConfigs = Maps.newConcurrentMap();
    private final Map<String, Map<String, List<ConfigModule>>> ruleConfigs = Maps.newConcurrentMap();
    private final Map<String, Map<String, List<ConfigModule>>> layoutConfigs = Maps.newConcurrentMap();
    private final Map<String, Map<String, List<ConfigModule>>> layoutRuleConfigs = Maps.newConcurrentMap();
    private final Map<String, BusinessFilters> businessFilterConfigs = Maps.newConcurrentMap();
    @Resource
    private BIService biService;

    @PostConstruct
    public void init() {
        //SFA配置文件
        ConfigFactory.getConfig("fs-paas-config-sfa", this::reload);
        ConfigFactory.getConfig("fs-paas-filter-config-sfa", this::reload);
        //快消的配置文件
        ConfigFactory.getConfig("fs-paas-config-fmcg", this::reload);
        ConfigFactory.getConfig("fs-paas-filter-config-fmcg", this::reload);
        //社交配置文件
        ConfigFactory.getConfig("fs-paas-config-social", this::reload);
        ConfigFactory.getConfig("fs-paas-filter-config-social", this::reload);
        //服务通相关的配置文件
        ConfigFactory.getConfig("fs-paas-config-server", this::reload);
        ConfigFactory.getConfig("fs-paas-filter-config-server", this::reload);
        //订货同相关配置文件
        ConfigFactory.getConfig("fs-paas-config-dht", this::reload);
        ConfigFactory.getConfig("fs-paas-filter-config-dht", this::reload);
        //库存相关配置文件
        ConfigFactory.getConfig("fs-paas-config-stock", this::reload);
        ConfigFactory.getConfig("fs-paas-filter-config-stock", this::reload);
        //营销通配置文件
        ConfigFactory.getConfig("fs-paas-config-marketing", this::reload);
        ConfigFactory.getConfig("fs-paas-filter-config-marketing", this::reload);
        //互联平台
        ConfigFactory.getConfig("fs-paas-config-er", this::reload);
        //流程配置
        ConfigFactory.getConfig("fs-paas-config-flow", this::reload);
    }


    private void updateConfig(String json) {
        Config currentConfig = JacksonUtils.fromJson(json, Config.class);
        if (Objects.isNull(currentConfig)) {
            log.warn("reload updateConfig currentConfig is null");
            return;
        }

        Map<String, List<ConfigModule>> objectConfig = currentConfig.getObjectConfig();
        Map<String, Map<String, List<ConfigModule>>> fieldConfig = currentConfig.getFieldConfig();
        Map<String, Map<String, List<ConfigModule>>> ruleConfig = currentConfig.getRuleConfig();
        Map<String, Map<String, List<ConfigModule>>> layoutConfig = currentConfig.getLayoutConfig();
        Map<String, Map<String, List<ConfigModule>>> layoutRuleConfig = currentConfig.getLayoutRuleConfig();
        Map<String, BusinessFilters> businessFilterConfig = currentConfig.getBusinessFilterConfig();


        if (!CollectionUtils.isEmpty(objectConfig)) {
            objectConfigs.putAll(objectConfig);
        }
        if (!CollectionUtils.isEmpty(fieldConfig)) {
            fieldConfigs.putAll(fieldConfig);
        }
        if (!CollectionUtils.isEmpty(ruleConfig)) {
            ruleConfigs.putAll(ruleConfig);
        }
        if (!CollectionUtils.isEmpty(layoutConfig)) {
            layoutConfigs.putAll(layoutConfig);
        }
        if (!CollectionUtils.isEmpty(layoutRuleConfig)) {
            layoutRuleConfigs.putAll(layoutRuleConfig);
        }
        if (!CollectionUtils.isEmpty(businessFilterConfig)) {
            businessFilterConfigs.putAll(businessFilterConfig);
        }
    }

    public Map<String, Object> searchObjectConfig(String tenantId, String objectAPIName) {
        return searchConfig(tenantId, objectConfigs.get(objectAPIName));
    }

    /**
     * 查询配置
     */
    public static Map<String, Object> searchConfig(String tenantId, List<ConfigModule> configModules) {

        if (CollectionUtils.isEmpty(configModules)) {
            return new HashMap<>();
        }

        ConfigModule configModule = null;
        for (ConfigModule currentModule : configModules) {
            //先根据企业id 搜索配置,如果搜索到直接break
            if (currentModule.getTenants() != null && currentModule.getTenants().contains(tenantId)) {
                configModule = currentModule;
                break;
            }
            //同时保留default
            if (ConfigModule.DEFAULT_GROUP.equals(currentModule.getGroup())) {
                configModule = currentModule;
            }
        }
        //如果没有搜索到,通常configModule 的 group 是default , 判空安全一点.
        return ObjectUtils.isEmpty(configModule) || Objects.isNull(configModule.getData()) ? new HashMap<>() : configModule.getData();
    }

    /**
     * 查询字段配置
     */
    public Map<String, Map<String, Object>> searchFieldConfig(String tenantId, String objectAPIName, List<String> fieldAPINames) {
        Map<String, List<ConfigModule>> fieldConfig = fieldConfigs.get(objectAPIName);
        Map<String, Map<String, Object>> result = new HashMap<>();

        if (MapUtils.isNullOrEmpty(fieldConfig)) {
            return result;
        }
        log.debug("field config content:{}", JSON.toJSONString(fieldConfig));
        for (String fieldAPIName : fieldAPINames) {
            Map<String, Object> config = searchConfig(tenantId, fieldConfig.get(fieldAPIName));
            if (!MapUtils.isNullOrEmpty(config)) {
                result.put(fieldAPIName, config);
            }
        }

        return result;
    }

    /**
     * 规则相关配置
     */
    public Map<String, Map<String, Object>> searchRuleConfig(String tenantId, String objectAPIName) {
        Map<String, List<ConfigModule>> ruleConfig = ruleConfigs.get(objectAPIName);
        return filterConfig(tenantId, ruleConfig);
    }

    /**
     * 布局相关配置
     */
    public Map<String, Map<String, Object>> searchLayoutConfig(String tenantId, String objectAPIName) {
        Map<String, List<ConfigModule>> layoutConfig = layoutConfigs.get(objectAPIName);
        return filterConfig(tenantId, layoutConfig);
    }

    /**
     * 布局规则相关配置
     */
    public Map<String, Map<String, Object>> searchLayoutRuleConfig(String tenantId, String objectAPIName) {
        Map<String, List<ConfigModule>> layoutRuleConfig = layoutRuleConfigs.get(objectAPIName);
        return filterConfig(tenantId, layoutRuleConfig);
    }

    private Map<String, Map<String, Object>> filterConfig(String tenantId, Map<String, List<ConfigModule>> config) {
        Map<String, Map<String, Object>> result = new HashMap<>();

        if (MapUtils.isNullOrEmpty(config)) {
            return result;
        }

        config.forEach((key, value) -> {
            Map<String, Object> tenantConfig = searchConfig(tenantId, value);
            if (!ObjectUtils.isEmpty(tenantConfig)) {
                result.put(key, tenantConfig);
            }
        });
        return result;
    }


    public Map<String, Object> searchBusinessConfig(String tenantId, String apiName) {
        BusinessFilters businessFilterConfig = businessFilterConfigs.get(apiName);
        Map<String, Object> optionalFeaturesSwtchMap = Maps.newHashMap();
        boolean disabled = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OBJECT_OPTIONAL_FEATURES_DISABLED, apiName);
        optionalFeaturesSwtchMap.put(OptionalFeaturesService.RELATED_TEAM_SWITCH, disabled ? 0 : 1);
        optionalFeaturesSwtchMap.put(OptionalFeaturesService.GLOBAL_SEARCH_SWITCH, disabled ? 0 : 1);
        optionalFeaturesSwtchMap.put(OptionalFeaturesService.FOLLOW_UP_DYNAMIC_SWITCH, disabled ? 0 : 1);
        optionalFeaturesSwtchMap.put(OptionalFeaturesService.MODIFY_RECORD_SWITCH, disabled ? 0 : 1);
        // 跨对象筛选开关，沿用上面的逻辑
        boolean supportCrossObjectFilter = biService.supportCrossObjectFilter(User.systemUser(tenantId), apiName);
        optionalFeaturesSwtchMap.put(OptionalFeaturesService.CROSS_OBJECT_FILTER_BUTTON, !supportCrossObjectFilter ? 0 : 1);
        if (businessFilterConfig == null) {
            return optionalFeaturesSwtchMap;
        }
        List<ConfigModule> objectBusinessConfig = businessFilterConfig.getBusiness();
        if (CollectionUtils.isEmpty(objectBusinessConfig)) {
            return optionalFeaturesSwtchMap;
        }
        Map<String, Object> businessConfig = ObjectDataCloneUtils.clone(searchConfig(tenantId, objectBusinessConfig));
        businessConfig.putAll(optionalFeaturesSwtchMap);
        return businessConfig;
    }

    public Map<String, Object> searchFilterConfig(String tenantId, String apiName) {
        BusinessFilters businessFilterConfig = businessFilterConfigs.get(apiName);
        if (businessFilterConfig == null) {
            return new HashMap<>();
        }
        Map<String, Object> objectConfig = new HashMap<>();
        Map<String, Object> subObjectConfig = new HashMap<>();
        Map<String, Map<String, List<ConfigModule>>> objectFilterConfig = businessFilterConfig.getFilters();
        if (CollectionUtils.isEmpty(objectFilterConfig)) {
            return new HashMap<>();
        }
        Map<String, List<ConfigModule>> objectModule = objectFilterConfig.get(MODULE);
        if (!CollectionUtils.isEmpty(objectModule)) {
            objectModule.forEach((key, value) -> {
                Map<String, Object> commonConfig = searchConfig(tenantId, value);
                if (!CollectionUtils.isEmpty(commonConfig)) {
                    objectConfig.put(key, commonConfig);
                }
            });
        }
        Map<String, List<ConfigModule>> objectSubModule = objectFilterConfig.get(SUB_MODULES);
        if (!CollectionUtils.isEmpty(objectSubModule)) {
            objectSubModule.forEach((key, value) -> {
                Map<String, Object> subFilterConfig = searchConfig(tenantId, value);
                if (!CollectionUtils.isEmpty(subFilterConfig)) {
                    subObjectConfig.put(key, subFilterConfig);
                }
            });
            if (!CollectionUtils.isEmpty(subObjectConfig)) {
                objectConfig.put(SUB_MODULES, subObjectConfig);
            }
        }
        if (CollectionUtils.isEmpty(objectConfig)) {
            return new HashMap<>();
        }
        return objectConfig;
    }


    @Data
    static class Config {
        ConcurrentHashMap<String, List<ConfigModule>> objectConfig;
        ConcurrentHashMap<String, Map<String, List<ConfigModule>>> fieldConfig;
        ConcurrentHashMap<String, Map<String, List<ConfigModule>>> ruleConfig;
        ConcurrentHashMap<String, Map<String, List<ConfigModule>>> layoutConfig;
        ConcurrentHashMap<String, Map<String, List<ConfigModule>>> layoutRuleConfig;
        ConcurrentHashMap<String, BusinessFilters> businessFilterConfig;
    }


    @Data
    static class ConfigModule {
        public static final String DEFAULT_GROUP = "default";
        private String group;
        private Map<String, Object> data;
        private Set<String> tenants;
    }

    @Data
    static class BusinessFilters {
        public List<ConfigModule> business;
        public Map<String, Map<String, List<ConfigModule>>> filters;
    }

    private void reload(IConfig config) {
        if (ObjectUtils.isEmpty(config.getString())) {
            log.warn("reload updateConfig is null,configName:{}", config.getName());
            return;
        }

        try {
            updateConfig(config.getString());
        } catch (Exception e) {
            log.error("reload updateConfig error,config name:{} ", config.getName(), e);
        }
    }
}