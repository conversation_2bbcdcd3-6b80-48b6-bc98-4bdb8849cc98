package com.facishare.paas.appframework.metadata.ocr;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class IdentifyFieldMapping {
    private String sourceApiName;
    private String targetApiName;


    public static List<IdentifyFieldMapping> convertFieldMapping(List identifyFieldMappingList) {
        if (CollectionUtils.empty(identifyFieldMappingList)) {
            return null;
        }
        List<IdentifyFieldMapping> fieldMappings = Lists.newArrayList();
        for (Object o : identifyFieldMappingList) {
            if (o instanceof Map) {
                Map<String, String> fieldMapping = (Map<String, String>) o;
                IdentifyFieldMapping identifyFieldMapping = new IdentifyFieldMapping();
                String source_api_name = fieldMapping.get("s");
                String target_api_name = fieldMapping.get("t");
                identifyFieldMapping.setSourceApiName(source_api_name);
                identifyFieldMapping.setTargetApiName(target_api_name);
                fieldMappings.add(identifyFieldMapping);
            }
        }
        return fieldMappings;
    }
}
