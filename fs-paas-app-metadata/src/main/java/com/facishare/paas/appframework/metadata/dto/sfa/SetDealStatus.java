package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;

/**
 * Created by renlb on 2018/7/18.
 */
public interface SetDealStatus {
    @Data
    @Builder
    class Arg {
        @JSONField(name = "CustomerID")
        String accountId;
        @JSONField(name = "DealStatus")
        int dealStatus;
    }
}
