package com.facishare.paas.appframework.metadata;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.dto.IncrementNumberBatchExecute;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefFunction;
import com.facishare.paas.metadata.api.describe.AutoNumber;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * create by zhaoju on 2020/04/22
 */
@Slf4j
@Service
public class FunctionAutoNumberImpl implements FunctionAutoNumber {
    @Autowired
    private FunctionLogicService functionLogicService;
    @Autowired
    private AutoNumberLogicService autoNumberLogicService;

    @Override
    public List<IObjectData> incrementNumberByFunction(IObjectDescribe describe, AutoNumber autoNumber,
                                                       List<IObjectData> dataList, User user) {
        IUdefFunction uDefFunction = functionLogicService.findUDefFunction(user, autoNumber.getFuncApiName(), autoNumber.getDescribeApiName());
        if (uDefFunction == null) {
            log.warn("incrementNumberByFunction fail, function not exit. tenantId:{}, describeApiName:{}, fieldApiName:{}, user:{}",
                    user.getTenantId(), autoNumber.getDescribeApiName(), autoNumber.getApiName(), user);
            throw new ValidateException(I18N.text(I18NKey.NOT_FIND_FUNCTION, autoNumber.getFuncApiName()));
        }

        // 调用函数，拿到生成自增编号的规则
        List<IncrementNumberBatchExecute.ObjectInfo> objectInfos = dataList.stream().map(data -> IncrementNumberBatchExecute.ObjectInfo.builder()
                .data(ObjectDataExt.of(data).toMap())
                .id(IdGenerator.get())
                .build()).collect(Collectors.toList());
        // key 为 data index(id)，value 为函数返回的 编号规则
        Map<String, IncrementNumberBatchExecute.IncrementNumber> incrementNumberMap = functionLogicService.incrementNumberBatchExecute(user, autoNumber.getDescribeApiName(), autoNumber.getFuncApiName(), objectInfos);

        // 对 incrementNumber 分组，并对同组的操作在 auto_number 表中取号
        // key 编号规则，value 相同编号规则下的 index 集合（数据集合）
        Map<IncrementNumberBatchExecute.IncrementNumber, Set<String>> incrementNumberIndexMap = incrementNumberMap.entrySet().stream()
                .filter(entry -> entry.getValue().isRulesType())
                .collect(Collectors.groupingBy(Map.Entry::getValue, Collectors.mapping(Map.Entry::getKey, Collectors.toSet())));
        // 根据规则取号 key 规则， value 对应规则的最终 编号
        Map<IncrementNumberBatchExecute.IncrementNumber, AtomicLong> incrementNumberLongMap = autoNumberLogicService.incrementNumber(describe, autoNumber, incrementNumberIndexMap, user);

        List<IObjectData> resultList = Lists.newArrayList();
        objectInfos.forEach(objectInfo -> {
            ObjectDataExt objectDataExt = ObjectDataExt.of(objectInfo.getData());
            IncrementNumberBatchExecute.IncrementNumber incrementNumber = incrementNumberMap.get(objectInfo.getId());
            // 直接使用返回值的方式
            if (!incrementNumber.isRulesType()) {
                objectDataExt.set(autoNumber.getApiName(), validateAndGet(incrementNumber, 0, autoNumber, user));
                resultList.add(objectDataExt);
                return;
            }

            // 根据最终的编号，求出 当前 n 条数据，每条数据对应的编号
            long currentNumber = getCurrentNumber(incrementNumberIndexMap, incrementNumberLongMap, incrementNumber);
            objectDataExt.set(autoNumber.getApiName(), validateAndGet(incrementNumber, currentNumber, autoNumber, user));
            resultList.add(objectDataExt);
        });
        return resultList;
    }

    private long getCurrentNumber(Map<IncrementNumberBatchExecute.IncrementNumber, Set<String>> incrementNumberIndexMap,
                                  Map<IncrementNumberBatchExecute.IncrementNumber, AtomicLong> incrementNumberLongMap,
                                  IncrementNumberBatchExecute.IncrementNumber incrementNumber) {
        return incrementNumberLongMap.get(incrementNumber).addAndGet(incrementNumber.getSteppingNumber())
                - incrementNumberIndexMap.getOrDefault(incrementNumber, Collections.emptySet()).size() * incrementNumber.getSteppingNumber();
    }

    private String validateAndGet(IncrementNumberBatchExecute.IncrementNumber incrementNumber, long currentNumber, AutoNumber autoNumber, User user) {
        String result = incrementNumber.getAutoNumber(currentNumber);
        if (Strings.isNullOrEmpty(result)) {
            log.warn("autoNumber value is empty, ei:{}, objectApiName:{}, fieldApiName:{}, value:{} " +
                            "incrementNumber:{}, user:{}", user.getTenantId(), autoNumber.getDescribeApiName(),
                    autoNumber.getApiName(), result, incrementNumber, user);
            throw new ValidateException(I18N.text(I18NKey.FUNC_INCREMENT_NUMBER_FAIL));
        }
        if (result.length() > 100) {
            log.warn("autoNumber value too long, ei:{}, objectApiName:{}, fieldApiName:{}, value:{} " +
                            "incrementNumber:{}, user:{}", user.getTenantId(), autoNumber.getDescribeApiName(),
                    autoNumber.getApiName(), result, incrementNumber, user);
            throw new ValidateException(I18N.text(I18NKey.FUNC_INCREMENT_NUMBER_VALUE_TOO_LONG));
        }
        return result;
    }

}
