package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;

import java.util.Optional;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/03/26
 */
public interface ObjectImportInitProvider {

    /**
     * 默认为对象ApiName
     * 预置对象请参考{@link ObjectImportEnum}
     * 联合导入需要定义新的objectCode时需要在{@link ObjectImportEnum}中添加
     * @return
     */
    default String getObjectCode(){
        return null;
    }

    Optional<ImportObject> getImportObject(IObjectDescribe objectDescribe, IUniqueRule uniqueRule);
}
