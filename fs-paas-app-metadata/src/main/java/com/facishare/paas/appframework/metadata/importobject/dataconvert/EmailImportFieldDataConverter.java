package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * create by zhao<PERSON> on 2019/07/27
 */
@Slf4j
@Component
public class EmailImportFieldDataConverter extends BaseImportFieldDataConverter {
    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.EMAIL);
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, User user) {
        String valueStr = getStringValue(objectData, fieldDescribe.getApiName());
        if (Strings.isNullOrEmpty(valueStr)) {
            return ConvertResult.buildSuccess(null);
        }

        return doValidate(objectData, fieldDescribe, user, valueStr, this::getValidateErrorMessage);
    }

    private String getValidateErrorMessage(IFieldDescribe fieldDescribe) {
        return I18N.text(I18NKey.FORMAT_ERROR, fieldDescribe.getLabel());
    }
}
