package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.quote.FillQuoteFieldValueArg;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Quote;

import java.util.List;
import java.util.Map;

public interface QuoteValueService {
    void fillQuoteFieldValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe, boolean isConvertLocation);

    void fillQuoteFieldValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe,
                             Map<String, List<IObjectData>> refObjectDataMap, boolean isConvertLocation);

    void fillQuoteFieldValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe,
                             Map<String, List<IObjectData>> refObjectDataMap, boolean isConvertLocation, List<Quote> quoteList);

    void fillQuoteFieldValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe,
                             Map<String, List<IObjectData>> refObjectDataMap, boolean isConvertLocation, List<Quote> quoteList, IObjectData masterData);

    void fillQuoteFieldValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe,
                             Map<String, List<IObjectData>> refObjectDataMap, boolean isConvertLocation, List<Quote> quoteList,
                                         IObjectData masterData,boolean isForCalculate);

    void fillQuoteFieldValue(User user, List<IObjectData> objectDataList, IObjectDescribe describe,
                             Map<String, List<IObjectData>> refObjectDataMap, boolean isConvertLocation, List<Quote> quoteList,
                             IObjectData masterData, boolean isForCalculate, FillQuoteFieldValueArg fillQuoteFieldValueArg);

    /**
     * 调用函数前需简要补充__q虚拟字段，『单独灰度功能』
     * @param user
     * @param objectData
     * @param details
     */
    void fillQuoteValueVirtualField(User user, IObjectData objectData, Map<String, List<IObjectData>> details);

    void fillQuoteValueVirtualField(User user, IObjectDescribe objectDescribe, Map<String, IObjectDescribe> detailObjectDescribes,
                                    IObjectData objectData, Map<String, List<IObjectData>> details);
}
