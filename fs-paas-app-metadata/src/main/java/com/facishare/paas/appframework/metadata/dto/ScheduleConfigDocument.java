package com.facishare.paas.appframework.metadata.dto;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectArchiveRuleInfo;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import org.apache.commons.lang3.StringUtils;
import org.joda.time.DateTime;
import org.joda.time.DateTimeZone;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import java.util.List;
import java.util.Map;
import java.util.Objects;

public class ScheduleConfigDocument extends DocumentBasedBean {
    private static final String TYPE = "type";
    private static final String EXECUTE_TIME = "executeTime";
    private static final String WEEKLY = "weekly";
    private static final String MONTHLY = "monthly";
    private static final String ONCE_EXECUTE_TIME = "onceExecuteTime";

    private static final String TIME_ZONE = "time_zone";

    private final transient DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern("HH:mm:ss");

    public ScheduleConfigDocument() {
        super();
    }

    public ScheduleConfigDocument(Map scheduleConfigMap) {
        super(scheduleConfigMap);
    }

    public static ScheduleConfigDocument of(Map scheduleConfigMap) {
        return new ScheduleConfigDocument(scheduleConfigMap);
    }

    public String getType() {
        return (String) get(TYPE);
    }

    public void setType(String type) {
        set(TYPE, type);
    }

    public String getExecuteTime() {
        return (String) get(EXECUTE_TIME);
    }

    public void setExecuteTime(String executeTime) {
        set(EXECUTE_TIME, executeTime);
    }

    public List<Integer> getWeekly() {
        return get(WEEKLY, List.class);
    }

    public void setWeekly(List<Integer> weekly) {
        set(WEEKLY, weekly);
    }

    public List<Integer> getMonthly() {
        return get(MONTHLY, List.class);
    }

    public void setMonthly(List<Integer> monthly) {
        set(MONTHLY, monthly);
    }

    public long getOnceExecuteTime() {
        Object obj = get(ONCE_EXECUTE_TIME);
        return Objects.isNull(obj) ? 0L : (long) obj;
    }

    public void setOnceExecuteTime(long onceExecuteTime) {
        set(ONCE_EXECUTE_TIME, onceExecuteTime);
    }

    public Map toScheduleConfigDTOMap(String timeZone) {
        ScheduleConfigDTO scheduleConfigDTO = new ScheduleConfigDTO();
        if (StringUtils.equalsAny(getType(), "once", "immediately")) {
            scheduleConfigDTO.setExecuteTime(getOnceExecuteTime());
        } else {
            DateTime dateTime = DateTime.parse(getExecuteTime(), dateTimeFormatter.withZone(DateTimeZone.forID(timeZone)));
            scheduleConfigDTO.setExecuteTime(dateTime.getMillis());
        }
        scheduleConfigDTO.setType(getType());
        scheduleConfigDTO.setWeekly(getWeekly());
        scheduleConfigDTO.setMonthly(getMonthly());
        return scheduleConfigDTO.getContainerDocument();
    }

    public static void convert2ScheduleConfigDTO(IObjectArchiveRuleInfo rule) {
        Map scheduleConfig = rule.getScheduleConfig();
        if (CollectionUtils.empty(scheduleConfig)) {
            return;
        }
        String timeZone = rule.get(TIME_ZONE, String.class);
        rule.setScheduleConfig(of(scheduleConfig).toScheduleConfigDTOMap(timeZone));
    }
}
