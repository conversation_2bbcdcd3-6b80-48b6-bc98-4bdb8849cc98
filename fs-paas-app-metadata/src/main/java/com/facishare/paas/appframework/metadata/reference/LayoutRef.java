package com.facishare.paas.appframework.metadata.reference;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.ref.RefMessage.ActionType;
import com.facishare.paas.appframework.core.model.ref.RefMessage.CreateArg;
import com.facishare.paas.appframework.core.model.ref.RefMessage.DeleteArg;
import com.facishare.paas.appframework.core.model.ref.RefMessage.Ref;
import com.facishare.paas.appframework.core.util.WhereUsedUtil;
import com.facishare.paas.appframework.core.util.WhereUsedUtil.SOURCE_MATCH_TYPE;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.component.*;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.ui.layout.component.FormComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.MultiTableComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.impl.ui.layout.component.TopInfoComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.reference.service.EntityReferenceService;
import com.fxiaoke.functions.utils.Maps;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

@Slf4j
public class LayoutRef {

    private String[] skipComponentTypes = {"tabs", "simple", "text", "navigation", "approval_component", "stage_component",
            "bpm_component", "what"};

    /**
     * 应用方（source）类型，对应配置文件
     */
    public static final String REF_TYPE_VAL = "layout";

    /**
     * 对象.布局
     */
    public static final String REF_DISPLAY_TYPE_VAL = "o.l";

    private List<CreateArg> createArgs = Lists.newArrayList();
    private List<DeleteArg> deleteArgs = Lists.newArrayList();
    ;

    /**
     * 用来去重
     */
    private final Map<String, Set<String>> distinctUsedObj2Field = Maps.newHashMap();

    private LayoutRef() {
    }

    public static Ref buildRefByLayout(ActionType actionType, ILayout sourceLayout, IObjectDescribe sourceObjDesc,
                                       Map<String, IObjectDescribe> refObjDesMap) {
        LayoutRef layoutRef = new LayoutRef();
        return layoutRef.buildRefs(actionType, sourceLayout, sourceObjDesc, refObjDesMap);
    }

    public static List<Ref> buildRefByLayout(ActionType actionType, List<ILayout> sourceLayoutList, IObjectDescribe sourceObjDesc,
                                             Map<String, IObjectDescribe> refObjDesMap) {
        List<Ref> refList = Lists.newArrayList();
        for (ILayout layout : sourceLayoutList) {
            refList.add(buildRefByLayout(actionType, layout, sourceObjDesc, refObjDesMap));
        }
        return refList;
    }

    private Ref buildRefs(ActionType actionType, ILayout sourceLayout, IObjectDescribe sourceObjDesc, Map<String, IObjectDescribe> refObjDesMap) {
        if (Objects.equals(actionType, ActionType.CREATE) || Objects.equals(actionType, ActionType.DELETE_AND_CREATE)) {

            List<IComponent> components = LayoutExt.of(sourceLayout).getComponentsSilently();
            if (Objects.equals(sourceLayout.getLayoutType(), ListLayoutExt.LIST_LAYOUT) && ListLayoutExt.of(sourceLayout).getFirstListComponent().isPresent()) {
                // 列表页布局
                components.add(ListLayoutExt.of(sourceLayout).getFirstListComponent().get().getComponent());
            }
            try {
                for (IComponent component : components) {
                    if (skipComponent(component)) {
                        continue;
                    }
                    getCreateRefsByComponentWhere(sourceLayout, component, sourceObjDesc);
                    getCreateRefsByComponentFormField(sourceLayout, component, sourceObjDesc, refObjDesMap);
                    getCreateRefsByListComponent(sourceLayout, component, sourceObjDesc, refObjDesMap);
                    getCreateRefsByAbListComponent(sourceLayout, component, sourceObjDesc);
                }
                // 布局 移动端独立配置
                if (LayoutExt.of(sourceLayout).isEnableMobileLayout()) {
                    LayoutExt mobileLayoutExt = LayoutExt.of(sourceLayout.getMobileLayout());
                    for (IComponent component : mobileLayoutExt.getComponentsSilently()) {
                        if (skipComponent(component)) {
                            continue;
                        }
                        // 组件显示条件
                        getCreateRefsByComponentWhere(sourceLayout, component, sourceObjDesc);
                        // 列表组件
                        getCreateRefsByListComponent(sourceLayout, component, sourceObjDesc, refObjDesMap);
                        getCreateRefsByComponentFormField(sourceLayout, component, sourceObjDesc, refObjDesMap);
                    }
                }
            } catch (Exception e) {
                log.warn("getComponents fail, tenantId:{}, objAPI:{}, layoutAPI:{}", sourceObjDesc.getTenantId(), sourceObjDesc.getApiName(), sourceLayout.getName());
            }
        }
        if (Objects.equals(actionType, ActionType.DELETE) || Objects.equals(actionType, ActionType.DELETE_AND_CREATE)) {
            buildDefaultDeleteArg(sourceObjDesc, sourceLayout, null, null);
        }
        return Ref.builder().action(actionType.getCode()).createArgs(createArgs).deleteArgs(deleteArgs).tenantId(sourceObjDesc.getTenantId()).build();
    }

    private boolean skipComponent(IComponent component) {

        return StringUtils.equalsAny(component.getType(), skipComponentTypes);
    }

    /**
     * 组件显示条件处理
     *
     * @param sourceLayout
     * @param sourceObjDesc
     */
    private void getCreateRefsByComponentWhere(ILayout sourceLayout, IComponent component, IObjectDescribe sourceObjDesc) {

        List<Wheres> wheresList = ComponentExt.of(component).getWheres();

        if (CollectionUtils.empty(wheresList)) {
            return;
        }

        IObjectDescribe targetObjDesc = sourceObjDesc;
        for (Wheres wheres : wheresList) {
            if (CollectionUtils.empty(wheres.getFilters())) {
                continue;
            }
            for (IFilter filter : wheres.getFilters()) {
                // 只记录该对象上字段
                if (StringUtils.contains(filter.getFieldName(), ".")) {
                    continue;
                }
                // 只记录 filter 的 fieldName
                // 暂不记录 filter 的 fieldValues 中的存在的变量
                IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(filter.getFieldName());
                appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
            }
        }
    }

    private void getCreateRefsByComponentFormField(ILayout sourceLayout, IComponent component, IObjectDescribe sourceObjDesc,
                                                   Map<String, IObjectDescribe> refObjDesMap) {
        IObjectDescribe targetObjDesc = sourceObjDesc;
        if (component instanceof TopInfoComponent) {
            List<IFormField> fields = ((TopInfoComponent) component).getFieldSections();
            if (CollectionUtils.empty(fields)) {
                return;
            }
            for (IFormField field : fields) {
                IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(field.getFieldName());
                appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
            }
        } else if (component instanceof FormComponent) {
            List<IFieldSection> fieldSectionList = ((FormComponent) component).getFieldSections();
            if (CollectionUtils.empty(fieldSectionList)) {
                return;
            }
            for (IFieldSection fieldSection : fieldSectionList) {
                List<IFormField> fields = fieldSection.getFields();
                if (CollectionUtils.empty(fields)) {
                    return;
                }
                for (IFormField field : fields) {
                    IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(field.getFieldName());
                    appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
                }
            }
        } else if (ComponentExt.of(component).isFormTable()) {

            List<IFormField> fields = FormTable.of(component).getFields();
            if (CollectionUtils.empty(fields)) {
                return;
            }
            for (IFormField field : fields) {
                IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(field.getFieldName());
                appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
            }
        } else if (ComponentExt.of(component).isSummaryKeyInfo()) {
            // 关键信息组件（summary_info）支持 使用查找关联对象字段
            List<IFormField> fields = SummaryKeyComponentInfo.of(component).getFieldSections();
            if (CollectionUtils.empty(fields)) {
                return;
            }
            for (IFormField field : fields) {
                IFieldDescribe targetFieldDesc = null;
                if (StringUtils.contains(field.getFieldName(), ".")) {
                    String[] parts = StringUtils.split(field.getFieldName(), ".");
                    String lookupFieldApi = StringUtils.substringBeforeLast(parts[0], "__r");
                    String targetObjApi = FieldDescribeExt.of(sourceObjDesc.getFieldDescribe(lookupFieldApi)).getRefObjTargetApiName();
                    targetObjDesc = refObjDesMap.get(targetObjApi);
                    targetFieldDesc = targetObjDesc.getFieldDescribe(parts[1]);
                } else {
                    targetFieldDesc = targetObjDesc.getFieldDescribe(field.getFieldName());
                }
                appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
            }
        }
    }

    /**
     * 记录汇总字段信息
     * type: master_detail | relatedlist | related_list_form
     *
     * @param sourceLayout
     * @param component
     * @param sourceObjDesc
     * @param refObjDesMap
     */
    private void getCreateRefsByListComponent(ILayout sourceLayout, IComponent component, IObjectDescribe sourceObjDesc,
                                              Map<String, IObjectDescribe> refObjDesMap) {
        String[] componentTypes = {
                MultiTableComponent.TYPE_MULTI_TABLE, ListComponentExt.RELATED_LIST_TYPE,
                RelatedListFormComponentExt.RELATED_LIST_FORM_TYPE, ListComponentExt.COMPONENT_TYPE_LIST,
                ComponentExt.TILE_RELATED_LIST
        };
        if (!StringUtils.equalsAny(component.getType(), componentTypes)) {
            return;
        }
        ListComponentExt componentExt = ListComponentExt.of(component);
        IObjectDescribe targetObjDesc = null;
        if (Objects.equals(ListComponentExt.COMPONENT_TYPE_LIST, component.getType())) {
            // 列表页布局 target 是 本对象
            targetObjDesc = sourceObjDesc;
        } else {
            targetObjDesc = refObjDesMap.get(componentExt.getRefObjectApiName());
            //相关对象列表 从对象列表
            if (Objects.isNull(targetObjDesc)) {
                return;
            }
            IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(componentExt.getRelatedListFieldApiName());
            appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
            // 相关（从）列表页布局 target 是 相关对象
        }
        // 当前页汇总使用字段
        for (String summaryFieldName : componentExt.getSummaryFieldNames()) {
            IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(summaryFieldName);
            appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
        }
        // 快速筛选使用字段 ：通用列表页 、 选数据列表页
        List<IFiltersComponentInfo> filtersInfo = componentExt.getFiltersComponentInfo();
        if (CollectionUtils.notEmpty(filtersInfo)) {
            for (IFiltersComponentInfo info : filtersInfo) {
                List<String> fields = info.getFields();
                if (CollectionUtils.notEmpty(fields)) {
                    for (String field : fields) {
                        IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(field);
                        appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
                    }

                }
            }
        }
        // 移动端筛选字段：自定义
        List<String> items = componentExt.getSideListFilterItems();
        if (CollectionUtils.notEmpty(items)) {
            for (String item : items) {
                IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(item);
                appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
            }
        }

        // 阶段推进器视图 未在布局中先不记录字段依赖关系
        // 视图使用字段
        List<IViewComponentInfo> viewList = componentExt.getViewInfos();
        if (CollectionUtils.notEmpty(viewList)) {
            for (IViewComponentInfo view : viewList) {
                // 日历视图 卡片显示字段 、地图视图 气泡显示字段
                List<String> fields = view.getFields();
                if (CollectionUtils.notEmpty(fields)) {
                    for (String field : fields) {
                        IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(field);
                        appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
                    }
                }
                // 日历视图 时间维度字段
                List<String> timeDimension = view.getTimeDimension();
                if (CollectionUtils.notEmpty(timeDimension)) {
                    for (String field : timeDimension) {
                        IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(field);
                        appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
                    }
                }
                // 地图视图 地图定位字段
                String locationField = view.getLocationField();
                if (StringUtils.isNotBlank(locationField)) {
                    IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(locationField);
                    appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
                }
                // 地图视图 气泡颜色控制字段
                IMapViewBubbleInfo bubbleInfo = view.getBubbleInfo();
                if (Objects.nonNull(bubbleInfo)) {
                    String bubbleColorField = bubbleInfo.getField();
                    if (StringUtils.isNotBlank(bubbleColorField)) {
                        IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(bubbleColorField);
                        appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
                    }
                }
            }
        }
    }

    /**
     * 移动端摘要布局
     * 下面只处理其中的一种，且优先级为 高级布局 > 通用布局 > 多列布局
     * 第 1 种 new_layout.row_sections 高级布局
     * 第 2 种 include_fields 通用布局
     * 第 3 种 field_section 多列布局
     * <p>
     * 显示图片使用字段：
     * show_image
     *
     * @param sourceLayout
     * @param component
     * @param sourceObjDesc
     */
    private void getCreateRefsByAbListComponent(ILayout sourceLayout, IComponent component, IObjectDescribe sourceObjDesc) {
        if (!Objects.equals(sourceLayout.getLayoutType(), LayoutTypes.LIST) || !Objects.equals(component.getType(), TableComponentExt.COMPONENT_TYPE_TABLE)) {
            return;
        }
        IObjectDescribe targetObjDesc = sourceObjDesc;
        TableComponentExt tableComponentExt = TableComponentExt.of(((TableComponent) component));
        List<String> fieldList = tableComponentExt.getFieldListAnyway();
        if (CollectionUtils.notEmpty(fieldList)) {
            for (String field : fieldList) {
                IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(field);
                appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
            }
        }

        String showImageField = tableComponentExt.getShowImage();
        if (StringUtils.isNotBlank(showImageField)) {
            IFieldDescribe targetFieldDesc = targetObjDesc.getFieldDescribe(showImageField);
            appendCreateArg(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc);
        }
    }

    private void buildDefaultDeleteArg(IObjectDescribe sourceObjDesc, ILayout sourceLayout,
                                       IObjectDescribe targetObjDesc, IFieldDescribe targetFieldDesc) {
        if (Objects.isNull(deleteArgs)) {
            deleteArgs = Lists.newArrayList();
        }

        if (ObjectUtils.anyNull(sourceObjDesc, sourceLayout)) {
            return;
        }
        DeleteArg deleteArg = DeleteArg.builder()
                .refType(REF_TYPE_VAL)
                .archivedSourceDisplayTypes(RefFieldService.ARCHIVED_TYPE_MAP.get(REF_TYPE_VAL))
                .sourceMatchType(SOURCE_MATCH_TYPE.EQ.name())
                .sourceValue(WhereUsedUtil.buildSourceValue(sourceObjDesc.getApiName(), sourceLayout.getName()))
                .build();
        deleteArgs.add(deleteArg);

    }


    private void appendCreateArg(IObjectDescribe sourceObjDesc, ILayout sourceLayout,
                                 IObjectDescribe targetObjDesc, IFieldDescribe targetFieldDesc) {
        if (Objects.isNull(createArgs)) {
            createArgs = Lists.newArrayList();
        }

        if (ObjectUtils.anyNull(sourceObjDesc, sourceLayout, targetObjDesc, targetFieldDesc)) {
            return;
        }

        // 大前提： targetFieldDesc 必须是自定义字段 因为只有自定义字段才能查被用在何处
        if (!FieldDescribeExt.of(targetFieldDesc).isCustomField()) {
            return;
        }

        // 去重 ：！！！目前 source 内容都一致，不需要根据source去重
        Set<String> distinctField = distinctUsedObj2Field.computeIfAbsent(targetObjDesc.getApiName(), fields -> Sets.newHashSet());
        if (distinctField.contains(targetFieldDesc.getApiName())) {
            // 存在直接跳过
            return;
        } else {
            // 不存在则加入
            distinctField.add(targetFieldDesc.getApiName());
        }


        CreateArg createArg = CreateArg.builder()
                .refType(REF_TYPE_VAL)
                .sourceDisplayType(REF_DISPLAY_TYPE_VAL)
                .sourceValue(WhereUsedUtil.buildSourceValue(sourceObjDesc.getApiName(), sourceLayout.getName()))
                .sourceLabel(WhereUsedUtil.buildSourceLabel(sourceObjDesc.getDisplayName(), sourceLayout.getDisplayName()))
                .targetType(EntityReferenceService.DESCRIBE_FIELD_TYPE)
                .targetValue(WhereUsedUtil.buildTargetValue(targetObjDesc.getApiName(), targetFieldDesc.getApiName()))
                .targetLabel(WhereUsedUtil.buildTargetLabel(targetObjDesc.getDisplayName(), targetFieldDesc.getLabel()))
                .build();
        createArgs.add(createArg);
    }
}
