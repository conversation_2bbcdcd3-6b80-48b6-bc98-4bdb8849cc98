package com.facishare.paas.appframework.metadata.qixin;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.webpage.customer.api.model.arg.GetTenantAppStatusArg;
import com.facishare.webpage.customer.api.model.result.GetTenantAppStatusResult;
import com.facishare.webpage.customer.api.service.PaaSAppRestService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("qiXinAppLogicService")
@Slf4j
public class QiXinAppLogicServiceImpl implements QiXinAppLogicService {

    @Autowired
    private PaaSAppRestService paaSAppRestService;


    @Override
    public boolean isHiddenQiXinEntrance(User user) {
        if (user.isOutUser()) {
            //下游默认不支持
            return false;
        }
        GetTenantAppStatusArg arg = new GetTenantAppStatusArg();
        arg.setTenantId(user.getTenantIdInt());
        arg.setEmployeeId(Integer.parseInt(user.getUserId()));
        arg.setAppId(RequestUtil.getAppId());
        try {
            GetTenantAppStatusResult result = paaSAppRestService.getTenantPaaSAppStatusByAppId(user.getTenantId(), arg);
            return result.isHiddenApplicationEntry();
        } catch (Exception e) {
            log.warn("getTenantPaaSAppStatusByAppId error,user:{}", user, e);
            return false;
        }
    }
}
