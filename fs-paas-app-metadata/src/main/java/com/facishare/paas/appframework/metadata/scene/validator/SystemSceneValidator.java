package com.facishare.paas.appframework.metadata.scene.validator;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.SceneLogicService;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.appframework.metadata.dto.scene.ISystemScene;
import com.facishare.paas.appframework.metadata.dto.scene.SceneExt;
import com.facishare.paas.appframework.metadata.scene.ValidatorUtil;
import com.facishare.paas.appframework.metadata.scene.validate.SystemSceneVD;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/05/27
 */
@Component
public class SystemSceneValidator extends AbstractValidator<SystemSceneVD> {
    @Autowired
    private SceneLogicService sceneLogicService;

    @Override
    public void validate(SystemSceneVD systemSceneVD) {
        super.validate(systemSceneVD.getTenantScene(), systemSceneVD.getObjectDescribe());
        ISystemScene tenantScene = systemSceneVD.getTenantScene();
        validateSceneType(tenantScene);
        // 场景通用校验逻辑
        ValidatorUtil.validate(tenantScene);

        Preconditions.checkNotEmpty(tenantScene.getId(), I18N.text(I18NKey.SCENE_ID_NOT_EMPTY));
        Preconditions.checkNotEmpty(tenantScene.getApiName(), I18N.text(I18NKey.SCENE_API_NAME_NOT_EMPTY));
        IScene sceneInDb = sceneLogicService.findSceneByApiName(tenantScene.getObjectDescribeApiName(),
                tenantScene.getApiName(), buildUser(tenantScene.getTenantId()), tenantScene.getExtendAttribute());
        Preconditions.checkArgument(Objects.nonNull(sceneInDb), I18N.text(I18NKey.SCENE_NOT_EXIST_OR_DELETE));
        validateSceneType(sceneInDb);
        Preconditions.checkArgument(sceneInDb.getObjectDescribeApiName().equals(tenantScene.getObjectDescribeApiName()), I18N.text(I18NKey.SCENE_BINDING_OBJECT_IS_INCONSISTENT));
    }

    private User buildUser(String tenantId) {
        return new User(tenantId, User.SUPPER_ADMIN_USER_ID);
    }

    @Override
    protected void validateSceneType(IScene scene) {
        Preconditions.checkArgument(SceneExt.of(scene).isDefaultScene(), I18N.text(I18NKey.INCONSISTENT_SCENE_TYPES));
    }

    @Override
    protected void validateDisplayName(IScene scene) {

    }
}
