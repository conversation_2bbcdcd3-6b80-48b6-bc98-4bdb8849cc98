package com.facishare.paas.appframework.metadata.dto;

import lombok.AllArgsConstructor;
import lombok.Data;

/**
 * 表头合并信息
 */
@Data
@AllArgsConstructor
public class HeaderMergeInfo {
    private String parentTitle;    // 父级标题（如"产品图片"）
    private int startColumn;       // 开始列索引
    private int endColumn;         // 结束列索引
    private boolean isRequired;    // 是否必填

    // 兼容性构造函数
    public HeaderMergeInfo(String parentTitle, int startColumn, int endColumn) {
        this(parentTitle, startColumn, endColumn, false);
    }
}
