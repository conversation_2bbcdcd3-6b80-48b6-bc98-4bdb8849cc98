package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import static com.facishare.crm.openapi.Utils.PARTNER_API_NAME;

/**
 * 合作伙伴
 * create by <PERSON><PERSON><PERSON> on 2019/03/31
 */
@Component
public class PartnerObjectProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return PARTNER_API_NAME;
    }

    @Override
    protected boolean getOpenWorkFlow(IObjectDescribe objectDescribe) {
        return true;
    }
}
