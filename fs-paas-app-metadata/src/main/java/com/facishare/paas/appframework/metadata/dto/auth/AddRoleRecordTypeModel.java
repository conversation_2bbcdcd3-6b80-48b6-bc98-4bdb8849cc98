package com.facishare.paas.appframework.metadata.dto.auth;

import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

public interface AddRoleRecordTypeModel {

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Arg extends BaseAuthArg {
        private List<RecordTypePojo> recordTypePojos;
        private String entityId;
        private String recordTypeId;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    class Result extends BaseAuthResult {
    }

}
