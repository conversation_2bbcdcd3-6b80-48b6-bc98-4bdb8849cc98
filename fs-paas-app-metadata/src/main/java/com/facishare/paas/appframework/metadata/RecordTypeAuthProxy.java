package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.metadata.dto.auth.*;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

/**
 * Created by zhouwr on 2017/10/17
 */
@RestResource(value = "PAAS-PRIVILEGE", desc = "业务类型角色授权服务", contentType = "application/json")
public interface RecordTypeAuthProxy {

    @POST(value = "/addRoleRecordType", desc = "添加角色与业务类型的关系")
    AddRoleRecordTypeModel.Result addRoleRecordType(@Body AddRoleRecordTypeModel.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/batch/add/entity/recordType", desc = "批量添加角色与业务类型的关系")
    AddRoleRecordTypeModel.Result batchAddRoleRecordType(@Body BatchAddRoleRecordTypeModel.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/checkRecordType", desc = "检查是否存在使用指定对象类型作为默认类型的角色")
    CheckRecordTypeModel.Result checkRecordType(@Body CheckRecordTypeModel.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/findRecordType", desc = "查询对象类型")
    FindRecordTypeModel.Result findRecordType(@Body FindRecordTypeModel.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/batch/findRecordType", desc = "查询对象类型")
    FindRecordTypeModel.Result batchFindRecordType(@Body BatchFindRecordType.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/roleInfo", desc = "角色查询")
    RoleInfoModel.Result roleInfo(@Body RoleInfoModel.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/queryRoleInfoWithCodes", desc = "角色查询")
    QueryRoleInfoWithCodes.Result queryRoleInfoWithCodes(@Body QueryRoleInfoWithCodes.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/updateRoleRecordType", desc = "更新角色与业务类型的关系")
    UpdateRoleRecordTypeModel.Result updateRoleRecordType(@Body UpdateRoleRecordTypeModel.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/defaultRole/queryRoleInfoListByUsers", desc = "查询指定条件的用户角色关联")
    QueryRoleInfoListByUsersModel.Result queryRoleInfoListByUsers(@Body QueryRoleInfoListByUsersModel.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/getMultiEmployeeRoleRelationEntitiesByEmployeeIDs", desc = "根据用户列表获取所有用户角色关系")
    GetMultiEmployeeRoleRelationEntitiesByEmployeeIDsModel.Result getMultiEmployeeRoleRelationEntitiesByEmployeeIDs(@Body GetMultiEmployeeRoleRelationEntitiesByEmployeeIDsModel.Arg arg, @HeaderMap Map<String, String> header);

    @POST(value = "/batch/del/record-type", desc = "批量删除对象绑定角色业务类型关系")
    BatchDeleteRoleEntityRelation.Result batchDeleteRoleEntityRelation(@Body BatchDeleteRoleEntityRelation.Arg arg, @HeaderMap Map<String, String> header);

}
