package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.util.GetI18nKeyUtil;
import org.springframework.stereotype.Component;

import static com.facishare.crm.openapi.Utils.GOODS_RECEIVED_NOTE_API_NAME;
import static com.facishare.crm.openapi.Utils.GOODS_RECEIVED_NOTE_PRODUCT_API_NAME;

/**
 * 入库单
 * create by z<PERSON><PERSON> on 2019/03/27
 */
@Component
public class GoodsReceivedNoteObjectImportProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return GOODS_RECEIVED_NOTE_API_NAME;
    }

    @Override
    protected String getObjectName(IObjectDescribe objectDescribe) {
        return String.format("%s-%s",
                I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(GOODS_RECEIVED_NOTE_API_NAME)),
                I18N.text(GetI18nKeyUtil.getDescribeDisplayNameKey(GOODS_RECEIVED_NOTE_PRODUCT_API_NAME)));
    }

    @Override
    protected void generateObjAllObj(IObjectDescribe objectDescribe) {
        ObjectDescribeExt.of(objectDescribe).getFieldDescribeSilently(IObjectData.NAME).ifPresent(x -> {
            x.setDescribeApiName(null);
            x.setUnique(true);
        });
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        return ImportType.UNSUPPORT_UPDATE_IMPORT;
    }

    @Override
    protected boolean getNoBatch() {
        return true;
    }

    @Override
    protected boolean getIsNotSupportSaleEvent(IObjectDescribe objectDescribe) {
        return true;
    }

    @Override
    protected boolean supportImportId(IObjectDescribe objectDescribe) {
        return false;
    }
}
