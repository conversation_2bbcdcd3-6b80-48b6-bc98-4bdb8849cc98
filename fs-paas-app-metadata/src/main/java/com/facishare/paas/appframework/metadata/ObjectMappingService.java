package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectMappingParams;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.impl.ActionEnum;
import com.facishare.paas.metadata.impl.ObjectMappingParams;
import lombok.Builder;
import lombok.Data;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.BiConsumer;
import java.util.function.UnaryOperator;

public interface ObjectMappingService {

    List<IObjectMappingRuleInfo> createRule(User user, List<IObjectMappingRuleInfo> ruleList);

    List<IObjectMappingRuleInfo> updateRule(User user, List<IObjectMappingRuleInfo> ruleList);

    void enableRule(User user, String ruleApiName, String describeApiName);

    void disableRule(User user, String ruleApiName, String describeApiName);

    void disableRuleByTargetDescribe(User user, String describeApiName);

    void disableRuleBySourceDescribe(User user, String describeApiName);

    void deleteRule(User user, String ruleApiName, String describeApiName);

    List<IObjectMappingRuleInfo> findByApiName(User user, String ruleId);

    List<IObjectMappingRuleInfo> findRuleList(User user, int status, String ruleName);

    List<IObjectMappingRuleInfo> findRuleList(User user, int status, String ruleName, String bizType);

    List<IObjectMappingRuleInfo> findRuleListWithDetailRule(User user, int status, String ruleName);

    List<IObjectMappingRuleInfo> findRuleListBySourceApiNameAndTargetApiName(User user, int status, String sourceApiName, String targetApiName);

    MappingDataResult mappingData(User user, MappingDataArg arg);

    MappingDataResult mappingDataByConvertRule(User user, MappingDataArg arg, BiConsumer<IObjectMappingParams, List<IObjectMappingRuleInfo>> consumer);

    MappingDataResult mappingDataByRuleConfig(User user, MappingDataArg arg, List<IObjectMappingRuleInfo> rules, UnaryOperator<IObjectMappingParams> function);

    List<IObjectMappingRuleInfo> findConvertRuleByApiName(User user, String ruleApiName);

    IObjectData mappingData(User user, IObjectMappingRuleInfo rule, IObjectData objectData);

    IUdefButton findButtonByRuleApiName(String ruleApiName, String describeApiName, User user);

    void deleteDetailDescribeRule(User user, String detailApiName);

    void addFieldMappingRule(User user, Map map);

    void deleteRuleByTargetDescribe(User user, String describeApiName);

    void deleteRuleBySourceDescribe(User user, String describeApiName);

    List<IObjectMappingRuleInfo> getObjectMappingRule(String tenantId, String ruleName, int status, String bizType);

    static boolean isObjectMappingRule(IObjectMappingRuleInfo rule) {
        if (Objects.isNull(rule)) {
            return false;
        }
        return Objects.isNull(rule.getAction()) || rule.getAction() == ActionEnum.OBJECT_MAPPING;
    }

    @Data
    class MappingDataArg {
        String ruleApiName;
        IObjectData objectData;
        Map<String, List<IObjectData>> details;

        public IObjectMappingParams toMappingParams() {
            IObjectMappingParams param = new ObjectMappingParams();
            param.setObjectData(objectData);
            param.setRuleApiName(ruleApiName);
            param.setDetailObjectData(details);
            return param;
        }

        public static MappingDataArg from(IObjectMappingParams param) {
            MappingDataArg arg = new MappingDataArg();
            arg.setDetails(param.getDetailObjectData());
            arg.setObjectData(param.getObjectData());
            arg.setRuleApiName(param.getRuleApiName());
            return arg;
        }
    }

    @Builder
    @Data
    class MappingDataResult {
        IObjectData objectData;
        Map<String, List<IObjectData>> details;

        public static MappingDataResult parse(IObjectMappingParams param) {
            if (Objects.isNull(param)) {
                return MappingDataResult.builder().build();
            }
            return MappingDataResult.builder()
                    .details(param.getDetailObjectData())
                    .objectData(param.getObjectData())
                    .build();
        }
    }
}
