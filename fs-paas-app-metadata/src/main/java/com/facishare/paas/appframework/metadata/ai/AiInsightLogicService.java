package com.facishare.paas.appframework.metadata.ai;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.repository.model.AiInsightEntity;

public interface AiInsightLogicService {

    AiInsightEntity findByUniqKey(User user, String relateObjectApiName, String relateDataId, String componentApiName);

    void save(User user, AiInsightEntity aiInsightEntity);

}
