package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

import java.util.List;

public interface GetObjectByNames {
    String NEED_REPEATED_DATA = "needRepeatedData";
    @Data
    class Arg {

        @SerializedName("ObjectNames")
        @JSONField(name = "ObjectNames")
        private List<String> objectNames;

        @SerializedName("ObjectType")
        @JSONField(name = "ObjectType")
        private String objectType;

        @SerializedName("NeedRepeatedData")
        @JSONField(name = "NeedRepeatedData")
        private boolean needRepeatedData;
    }

    @Getter
    @Setter
    class Result extends BaseResult {
        private List<NameIDPojo> value;
    }

    @Data
    class NameIDPojo {
        @SerializedName("ObjectName")
        @JSONField(name = "ObjectName")
        private String objectName;

        @SerializedName("ObjectID")
        @JSONField(name = "ObjectID")
        private String objectID;

        @SerializedName("EmployeeID")
        @JSONField(name = "EmployeeID")
        private String employeeID;

        @SerializedName("OwnerID")
        @JSONField(name = "OwnerID")
        private String ownerID;

        @SerializedName("HasPermission")
        @JSONField(name = "HasPermission")
        private Boolean hasPermission;
    }
}
