package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.crm.common.exception.CrmDefObjCheckedException;
import com.facishare.crm.valueobject.SessionContext;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.google.common.base.Strings;

import java.math.BigDecimal;

/**
 * Created by linqiuying on 17/6/9.
 */
public class PercentileFieldDataConverter extends AbstractFieldDataConverter {
    @Override
    public String convertFieldData(SessionContext sessionContext) throws CrmDefObjCheckedException {
        Object value = getObjectData().get(getFieldDescribe().getApiName());
        if (value == null) {
            return "";
        }
        if (sessionContext.isFromQuoteField()
                && value instanceof BigDecimal
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.STRIP_TRAILING_ZEROS_FOR_NUMERIC_QUOTE_FIELD, String.valueOf(sessionContext.getEId()))) {
            value = ((BigDecimal) value).stripTrailingZeros();
        }
        String valueStr = value instanceof BigDecimal ? ((BigDecimal) value).toPlainString() : String.valueOf(value);
        if (!Strings.isNullOrEmpty(valueStr) && valueStr.contains("%")) {
            return valueStr.replace("%", "");
        }
        return valueStr;
    }
}
