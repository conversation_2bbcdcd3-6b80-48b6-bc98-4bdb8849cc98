package com.facishare.paas.appframework.metadata.domain;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.RequestType;
import com.facishare.paas.appframework.metadata.domain.DomainPluginDefinition.Action;
import com.facishare.paas.appframework.metadata.domain.DomainPluginDefinition.Resource;
import com.facishare.paas.appframework.metadata.repository.model.DomainPluginInstance;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2021/11/10.
 */
@Slf4j
public abstract class DomainPluginDefinitionHolder {

    private static Map<String, DomainPluginDefinition> pluginDefinitionMap;

    private static List<DomainPluginInstance> pluginInstanceList;

    static {
        ConfigFactory.getConfig("fs-paas-domain-plugin-definition", config -> {
            try {
                log.info("reload fs-paas-domain-plugin-definition:{}", config.getString());
                reload(config);
            } catch (Exception e) {
                log.error("reload fs-paas-domain-plugin-definition failed", e);
            }
        });
    }

    private static void reload(IConfig config) {
        String content = config.getString();
        if (Strings.isNullOrEmpty(content)) {
            pluginDefinitionMap = Maps.newLinkedHashMap();
            pluginInstanceList = Lists.newArrayList();
            return;
        }
        PluginConfig pluginConfig = JSON.parseObject(content, PluginConfig.class);
        Map<String, DomainPluginDefinition> newDefinitionMap = Maps.newLinkedHashMap();
        if (CollectionUtils.notEmpty(pluginConfig.getPluginDefinitions())) {
            pluginConfig.getPluginDefinitions().forEach(plugin -> newDefinitionMap.put(plugin.getApiName(), plugin));
        }
        pluginDefinitionMap = newDefinitionMap;
        pluginInstanceList = pluginConfig.getPluginInstances();
        if (CollectionUtils.notEmpty(pluginInstanceList)) {
            pluginInstanceList.forEach(x -> {
                x.reloadGrayRuleMap();
                x.reloadManagementGrayRule();
                x.setPredefined(Boolean.TRUE);
            });
            pluginInstanceList.removeIf(x -> x.getRefObjectApiName() == null);    //配置中心存在脏数据
        }
    }

    @Data
    private static class PluginConfig {
        private List<DomainPluginDefinition> pluginDefinitions;
        private List<DomainPluginInstance> pluginInstances;
    }

    public static boolean contains(String pluginApiName) {
        return pluginDefinitionMap.containsKey(pluginApiName);
    }

    public static DomainPluginDefinition getPluginDefinition(String pluginApiName) {
        DomainPluginDefinition definition = pluginDefinitionMap.get(pluginApiName);
        return Objects.isNull(definition) ? null : definition.copy();
    }

    public static List<DomainPluginDefinition> getDefinitionForManagement() {
        return pluginDefinitionMap.values().stream()
                .filter(DomainPluginDefinition::isSupportManagement)
                .map(DomainPluginDefinition::copy)
                .map(DomainPluginDefinition::processI18NProps)
                .sorted(Comparator.comparingInt(DomainPluginDefinition::getOrder).reversed())
                .collect(Collectors.toList());
    }

    public static DomainPluginType getPluginType(String pluginApiName) {
        DomainPluginDefinition definition = pluginDefinitionMap.get(pluginApiName);
        if (Objects.isNull(definition)) {
            return null;
        }
        return DomainPluginType.of(definition.type());
    }

    public static String getPluginLabel(String pluginApiName) {
        DomainPluginDefinition definition = pluginDefinitionMap.get(pluginApiName);
        if (Objects.isNull(definition)) {
            return pluginApiName;
        }
        return definition.i18nLabel();
    }

    public static int getPluginOrder(String pluginApiName) {
        DomainPluginDefinition definition = pluginDefinitionMap.get(pluginApiName);
        if (Objects.isNull(definition)) {
            return 0;
        }
        return definition.getOrder();
    }

    public static List<Resource> getResources(String pluginApiName, String actionCode, String agentType) {
        DomainPluginDefinition definition = pluginDefinitionMap.get(pluginApiName);
        if (Objects.isNull(definition)) {
            return null;
        }
        List<Resource> resources = definition.getResources(actionCode, agentType);
        return Objects.isNull(resources) ? null :
                resources.stream()
                        .map(Resource::copy)
                        .collect(Collectors.toList());
    }

    public static Map<String, Action> getActions(String pluginApiName, String actionCode, RequestType requestType) {
        DomainPluginDefinition definition = pluginDefinitionMap.get(pluginApiName);
        if (Objects.isNull(definition)) {
            return null;
        }
        Map<String, Action> actionMap = definition.getActionsByCode(actionCode, requestType);
        if (Objects.isNull(actionMap)) {
            return null;
        }
        Map<String, Action> result = Maps.newLinkedHashMap();
        actionMap.forEach((k, v) -> result.put(k, v.copy()));
        return result;
    }

    public static DomainPluginInstance getPluginInstanceByApiName(String pluginApiName, String objectApiName) {
        return getPluginInstanceByApiName(pluginApiName, objectApiName, null);
    }

    public static DomainPluginInstance getPluginInstanceByApiName(String pluginApiName, String objectApiName, String fieldApiName) {
        DomainPluginInstance result = null;
        if (CollectionUtils.notEmpty(pluginInstanceList)) {
            result = pluginInstanceList.stream()
                    .filter(x -> pluginApiName.equals(x.getPluginApiName()) && objectApiName.equals(x.getRefObjectApiName()))
                    .filter(x -> DomainPluginInstance.isFieldEquals(x.getFieldApiName(), fieldApiName))
                    .map(x -> x.copy())
                    .findFirst()
                    .orElse(null);
        }
        return result;
    }

    public static List<DomainPluginInstance> getPluginInstancesByAgentType(String tenantId, List<String> objectApiNames, String agentType) {
        return getPluginInstancesByRecordType(tenantId, objectApiNames, agentType, null);
    }

    public static List<DomainPluginInstance> getPluginInstancesByRecordType(String tenantId, List<String> objectApiNames, String agentType, List<String> recordTypeList) {
        List<DomainPluginInstance> result = Lists.newArrayList();
        if (CollectionUtils.notEmpty(pluginInstanceList)) {
            result = pluginInstanceList.stream()
                    .filter(x -> objectApiNames.contains(x.getRefObjectApiName()))
                    .filter(x -> x.inGrayList(tenantId, agentType))
                    .filter(x -> x.containsRecordType(recordTypeList))
                    .map(x -> x.copy())
                    .collect(Collectors.toList());
        }
        return result;
    }

    public static List<DomainPluginInstance> getPluginInstancesByPluginApiName(String tenantId, String pluginApiName) {
        List<DomainPluginInstance> result = Lists.newArrayList();
        if (CollectionUtils.notEmpty(pluginInstanceList)) {
            result = pluginInstanceList.stream()
                    .filter(x -> pluginApiName.equals(x.getPluginApiName()))
                    .filter(x -> x.inGrayList(tenantId, null))
                    .map(x -> x.copy())
                    .collect(Collectors.toList());
        }
        return result;
    }

    // 查询租户全部实例, 用于和数据库实例匹配
    public static List<DomainPluginInstance> getPluginInstancesManagementByTenantId(String tenantId) {
        List<DomainPluginInstance> result = Lists.newArrayList();
        if (CollectionUtils.notEmpty(pluginInstanceList)) {
            result = pluginInstanceList.stream()
                    .filter(x -> x.inManagementGrayList(tenantId))
                    .filter(x -> DomainPluginDefinitionHolder.supportManagement(x.getPluginApiName()))
                    .map(DomainPluginInstance::copy)
                    .collect(Collectors.toList());
        }
        return result;
    }

    // 唯一确定域插件实例需要[租户,插件api,关联对象api]
    public static boolean checkIfPredefined(String tenantId, DomainPluginInstance instance) {

        if (CollectionUtils.notEmpty(pluginInstanceList)) {
            return pluginInstanceList.stream()
                    .anyMatch(x ->
                            x.keyEquals(instance)
                    );
        }
        return false;
    }

    public static boolean checkIfPredefined(String tenantId, String pluginApiName, String refObjectApiName, String fieldApiName) {
        DomainPluginInstance instance = DomainPluginInstance.builder()
                .pluginApiName(pluginApiName)
                .refObjectApiName(refObjectApiName)
                .fieldApiName(fieldApiName)
                .build();
        return DomainPluginDefinitionHolder.checkIfPredefined(tenantId, instance);
    }

    public static DomainPluginInstance getPluginInstance(String tenantId, String pluginApiName, String refObjectApiName) {
        if (CollectionUtils.notEmpty(pluginInstanceList)) {
            return pluginInstanceList.stream()
                    .filter(x -> x.inGrayList(tenantId, null)
                            && pluginApiName.equals(x.getPluginApiName())
                            && refObjectApiName.equals(x.getRefObjectApiName())
                    )
                    .map(DomainPluginInstance::copy)
                    .findFirst()
                    .orElse(null);
        }
        return null;
    }

    // 查询依赖于当前插件的所有插件api
    public static List<String> findSonPluginApiNames(String pluginApiName) {
        List<String> result = Lists.newArrayList();
        if (CollectionUtils.notEmpty(pluginDefinitionMap)) {
            result = pluginDefinitionMap.keySet().stream()
                    .filter(x -> pluginDefinitionMap.get(x)
                            .dependencies()
                            .contains(pluginApiName))
                    .collect(Collectors.toList());
        }
        return result;
    }

    public static boolean supportManagement(String pluginApiName) {
        DomainPluginDefinition definition = pluginDefinitionMap.get(pluginApiName);
        return Objects.nonNull(definition) && definition.isSupportManagement();
    }

}
