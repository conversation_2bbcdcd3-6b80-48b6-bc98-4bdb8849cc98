package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.ConfigValueType;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.StageViewInfo;
import com.facishare.paas.appframework.metadata.layout.TableComponentRender;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.component.TableComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.ui.layout.ITableComponent;
import com.google.common.base.Strings;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * create by zhaoju on 2020/02/05
 */
@Component
public class StageViewLogicServiceImpl implements StageViewLogicService {
    public static final String STAGE_VIEW_POST = "stage_view";
    public static final String NEW_OPPORTUNTIY_USER_FILTER = "new_opportuntiy_user_filter";
    @Autowired
    private ConfigService configService;
    @Autowired
    private DescribeLogicService describeLogicService;
    @Autowired
    private LayoutLogicService layoutLogicService;
    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;

    @Override
    public boolean saveStageView(String describeApiName, List<StageViewInfo> viewInfo, User user) {
        if (StringUtils.isBlank(describeApiName) || CollectionUtils.empty(viewInfo)) {
            throw new ValidateException(I18NExt.text(I18NKey.PARAM_ERROR));
        }
        String key = getKeyByApiName(describeApiName);
        String userConfig = configService.findUserConfig(user, key);
        if (Strings.isNullOrEmpty(userConfig)) {
            configService.createUserConfig(user, key, JSON.toJSONString(viewInfo), ConfigValueType.JSON);
        } else {
            configService.updateUserConfig(user, key, JSON.toJSONString(viewInfo), ConfigValueType.JSON);
        }
        return true;
    }

    @Override
    public List<StageViewInfo> findStageView(String describeApiName, User user) {
        String config = configService.findUserConfig(user, getKeyByApiName(describeApiName));
        // 查询接口需要兼容 商机2.0 原有配置信息
        if (StringUtils.isBlank(config) && Utils.NEW_OPPORTUNITY_API_NAME.equals(describeApiName)) {
            config = configService.findUserConfig(user, NEW_OPPORTUNTIY_USER_FILTER);
        }
        IObjectDescribe describe = describeLogicService.findObjectWithoutCopyIfGray(user.getTenantId(), describeApiName);
        return getStageViewInfos(user, config, describe);
    }

    @Override
    public boolean deleteStageView(String describeApiName, User user) {
        configService.deleteUserConfig(user, getKeyByApiName(describeApiName));
        if (Utils.NEW_OPPORTUNITY_API_NAME.equals(describeApiName)) {
            configService.deleteUserConfig(user, NEW_OPPORTUNTIY_USER_FILTER);
        }
        return true;
    }

    private List<StageViewInfo> getStageViewInfos(User user, String config, IObjectDescribe describe) {
        if (StringUtils.isNotBlank(config)) {
            TableComponent tableComponent = StageViewInfo.toTableComponent(StageViewInfo.fromJsonArray(config));
            render(user, describe, tableComponent);
            return StageViewInfo.fromComponent(tableComponent);
        }
        List<ILayout> listLayout = layoutLogicService.findMobileListLayout(user, describe, false);
        if (CollectionUtils.empty(listLayout)) {
            return Collections.emptyList();
        }
        return listLayout.stream().findFirst()
                .flatMap(layout -> LayoutExt.of(layout).getTableComponent())
                .map(tableComponent -> {
                    render(user, describe, tableComponent);
                    return tableComponent;
                })
                .map(StageViewInfo::fromComponent)
                .orElse(Collections.emptyList());
    }

    private void render(User user, IObjectDescribe objectDescribe, ITableComponent tableComponent) {
        TableComponentRender.builder()
                .functionPrivilegeService(functionPrivilegeService)
                .user(user)
                .describeExt(ObjectDescribeExt.of(objectDescribe))
                .tableComponentExt(TableComponentExt.of(tableComponent))
                .build()
                .render();
    }

    private String getKeyByApiName(String describeApiName) {
        return describeApiName + "_" + STAGE_VIEW_POST;
    }
}
