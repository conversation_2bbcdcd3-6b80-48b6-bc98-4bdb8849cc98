package com.facishare.paas.appframework.metadata.multicurrency;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import lombok.Data;

import java.util.List;


public interface RefreshInternational {

    @Data
    class Arg {
        private List<String> objectApiNames;
        private String functionalCurrency;
        private Boolean replaceOldField;

        public Boolean getOnlySpecialObjects() {
            return CollectionUtils.notEmpty(objectApiNames);
        }
    }

    @Data
    class Result {

    }

}
