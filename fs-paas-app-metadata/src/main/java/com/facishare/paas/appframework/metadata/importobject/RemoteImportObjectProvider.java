package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RestUtils;
import com.facishare.paas.appframework.metadata.dto.ImportObjectModule;
import com.facishare.rest.core.exception.RestProxyRuntimeException;
import lombok.Builder;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * create by zhao<PERSON> on 2021/03/16
 */
@Slf4j
public class RemoteImportObjectProvider implements ImportObjectProvider {

    private String objectModule;
    private ImportObjectProviderProxy objectImportProviderProxy;

    @Builder
    private RemoteImportObjectProvider(@NonNull String objectModule,
                                       @NonNull ImportObjectProviderProxy objectImportProviderProxy) {
        this.objectModule = objectModule;
        this.objectImportProviderProxy = objectImportProviderProxy;
    }

    @Override
    public String getObjectModule() {
        return objectModule;
    }

    @Override
    public List<ImportObjectModule.ImportModule> getImportObjectModule(User user, ImportModuleContext context) {
        ImportObjectModule.Arg proxyArg = ImportObjectModule.Arg.builder()
                .objectModule(objectModule)
                .context(context)
                .build();
        Map<String, String> headers = RestUtils.buildHeadersByObjectModule(objectModule);
        try {
            ImportObjectModule.RestResult result = objectImportProviderProxy.getImportObjectModule(proxyArg, headers);
            return result.getData().getImportObjectModules();
        } catch (RestProxyRuntimeException e) {
            log.error("getImportObjectModule RestProxyRuntimeException, arg:{}", proxyArg, e);
        }
        return Collections.emptyList();
    }
}
