package com.facishare.paas.appframework.metadata.dto.ai;

import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.SelectOne;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;
import java.util.Objects;
import java.util.function.Function;

import static org.apache.commons.lang3.ObjectUtils.isNotEmpty;

/**
 * <AUTHOR> ZhenHui
 * @Data : 2025/2/28
 * @Description :
 */
public class AIDto {

	@Data
	@SuperBuilder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class Ret<T> {
		private Integer errCode;
		private String errMessage;
		private T result;

        public boolean isSuccess() {
            return errCode == 0;
        }
	}

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class Message {
		private String content;
		private String role;
	}

	@Data
	@Builder
	@NoArgsConstructor
	@AllArgsConstructor
	public static class Usage {
		private Integer input_tokens;
		private Integer output_tokens;
		private Double price;
	}

	public static final String USER = "user";

	private static StringBuilder removeSpaces(StringBuilder sb) {
		if (Objects.isNull(sb)) {
			return new StringBuilder();
		}
		int i = 0;
		while (i < sb.length()) {
			if (Character.isWhitespace(sb.charAt(i))) {
				sb.delete(i, i + 1);
			}
			else {
				i++;
			}
		}
		return sb;
	}


	@Builder
	@Data
	public static class Obj {
		private String objApi;
		private String objName;
		private List<Field> fields;

		@Override
		public String toString() {
			StringBuilder sb = new StringBuilder();
			sb.append("{\n");
			sb.append("    \"对象名称\": \"").append(objName).append("\",\n"); // ignoreI18n
			sb.append("    \"对象apiName\": \"").append(objApi).append("\",\n"); // ignoreI18n
			sb.append("    \"对象下字段\": [\n"); // ignoreI18n
			if (isNotEmpty(fields)) {
				for (int i = 0; i < fields.size(); i++) {
					sb.append(fields.get(i).toString());
					if (i < fields.size() - 1) {
						sb.append(",");
					}
					sb.append("\n");
				}
			}
			sb.append("    ]\n");
			sb.append("}");
			return removeSpaces(sb).toString();
		}
	}

	@Builder
	public static class Field {
		private String fieldApi;
		private String fieldType;
		private String returnType;
		private String fieldLabel;
		private boolean isRef;
		private String refObjApi;
		private boolean isOption;
		private List<Opt> opts;

		public AIDto.Field addOption(IFieldDescribe field, Function<SelectOne, List<Opt>> function) {
			if (field instanceof SelectOne) {
				this.opts = function.apply((SelectOne) field);
			}
			return this;
		}

		@Override
		public String toString() {
			StringBuilder sb = new StringBuilder();
			sb.append("      {\n");
			sb.append("        \"字段名称\": \"").append(fieldLabel).append("\",\n"); // ignoreI18n
			sb.append("        \"字段apiName\": \"").append(fieldApi).append("\",\n"); // ignoreI18n
			sb.append("        \"字段类型\": \"").append(fieldType).append("\",\n"); // ignoreI18n
			sb.append("        \"返回类型\": \"").append(returnType).append("\""); // ignoreI18n
			if (isRef) {
				sb.append(",\n");
				sb.append("        \"关联对象\": \"").append(refObjApi).append("\""); // ignoreI18n
			}
			if (isOption && isNotEmpty(opts)) {
				sb.append(",\n");
				sb.append("        \"字段下选项\": [\n"); // ignoreI18n
				for (int i = 0; i < opts.size(); i++) {
					sb.append(opts.get(i).toString());
					if (i < opts.size() - 1) {
						sb.append(",");
					}
					sb.append("\n");
				}
				sb.append("        ]\n");
			}
			sb.append("      }");
			return sb.toString();
		}
	}

	@Builder
	public static class Opt {
		private String value;
		private String label;

		@Override
		public String toString() {
			StringBuilder sb = new StringBuilder();
			sb.append("          {\n");
			sb.append("            \"选项label\": \"").append(label).append("\",\n"); // ignoreI18n
			sb.append("            \"选项value\": \"").append(value).append("\"\n"); // ignoreI18n
			sb.append("          }");
			return sb.toString();
		}
	}

	@Builder
	public static class GlobalVar {
		private String varApi;
		private String varLabel;
		private String returnType;

		@Override
		public String toString() {
			StringBuilder sb = new StringBuilder();
			sb.append("{\n");
			sb.append("    \"变量名称\": \"").append(varLabel).append("\",\n"); // ignoreI18n
			sb.append("    \"变量apiName\": \"").append(varApi).append("\",\n"); // ignoreI18n
			sb.append("    \"返回类型\": \"").append(returnType).append("\""); // ignoreI18n
			sb.append("}");
			return removeSpaces(sb).toString();
		}
	}
}
