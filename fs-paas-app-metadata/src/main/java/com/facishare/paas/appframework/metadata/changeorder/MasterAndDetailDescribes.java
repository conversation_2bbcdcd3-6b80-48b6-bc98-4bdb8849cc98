package com.facishare.paas.appframework.metadata.changeorder;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.changeorder.convert.ChangeOrderObjectDescribeConvert;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Maps;
import lombok.Getter;

import java.util.Collection;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/3/23
 */
@Getter
public class MasterAndDetailDescribes {
    private final IObjectDescribe objectDescribe;
    private final Map<String, IObjectDescribe> detailDescribes;
    private final boolean isSkipSetRelatedListName;

    private MasterAndDetailDescribes(IObjectDescribe objectDescribe, Map<String, IObjectDescribe> detailDescribes, boolean isSkipSetRelatedListName) {
        this.objectDescribe = objectDescribe;
        this.detailDescribes = detailDescribes;
        this.isSkipSetRelatedListName = isSkipSetRelatedListName;
    }

    public static MasterAndDetailDescribes of(IObjectDescribe describe, Collection<IObjectDescribe> details) {
        return of(describe, details, false);
    }

    public static MasterAndDetailDescribes of(IObjectDescribe describe, Collection<IObjectDescribe> details, boolean isCopyRelatedListName) {
        Map<String, IObjectDescribe> describeMap = details.stream()
                .collect(Collectors.toMap(IObjectDescribe::getApiName, Function.identity()));
        return new MasterAndDetailDescribes(describe, describeMap, isCopyRelatedListName);
    }

    public OpenChangeOrderResult verifyChangeOrder(User user) {
        String describeApiName = objectDescribe.getApiName();
        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            describeApiName = ObjectDescribeExt.of(objectDescribe).getMasterAPIName().orElseThrow(() -> new ValidateException(I18NExt.text(I18NKey.MASTER_DETAIL_FIELD_NOT_EXIST)));
        }
        if (!ChangeOrderConfig.changeOrderDescribeGray(user.getTenantId(), describeApiName)) {
            return OpenChangeOrderResult.buildFail(I18NExt.text(I18NKey.NOT_SUPPORT_OPEN_CHANGE_ORDER, objectDescribe.getDisplayName()));
        }
        ObjectDescribeExt objectDescribeExt = ObjectDescribeExt.of(objectDescribe);
        if (!Strings.isNullOrEmpty(objectDescribeExt.getOriginalDescribeApiName())) {
            return OpenChangeOrderResult.buildFail(I18NExt.text(I18NKey.NOT_SUPPORT_OPEN_CHANGE_ORDER, objectDescribeExt.getDisplayName()));
        }
        // 公共对象不能开启变更单
        if (objectDescribeExt.isPublicObject()) {
            return OpenChangeOrderResult.buildFail(I18NExt.text(I18NKey.PUBLIC_OBJECT_CANNOT_OPEN_CHANGE_ORDER, objectDescribeExt.getDisplayName()));
        }
        for (IObjectDescribe detailDescribe : detailDescribes.values()) {
            ObjectDescribeExt detailDescribeExt = ObjectDescribeExt.of(detailDescribe);
            if (!detailDescribeExt.isSlaveObjectCreateWithMaster() || detailDescribeExt.isShowDetailButton()) {
                return OpenChangeOrderResult.buildFail(I18NExt.text(I18NKey.DETAIL_OBJ_SINGLE_CREATE_CANNOT_OPEN_CHANGE_ORDER, detailDescribeExt.getDisplayName()));
            }
            // 公共对象不能开启变更单
            if (detailDescribeExt.isPublicObject()) {
                return OpenChangeOrderResult.buildFail(I18NExt.text(I18NKey.PUBLIC_OBJECT_CANNOT_OPEN_CHANGE_ORDER, detailDescribeExt.getDisplayName()));
            }
        }
        return OpenChangeOrderResult.buildSuccess();
    }

    public MasterAndDetailDescribes convert2ChangeOrderDescribes() {
        return ChangeOrderObjectDescribeConvert.getInstance().convert(this, isSkipSetRelatedListName);
    }

    public Map<String, IObjectDescribe> getObjectDescribes() {
        Map<String, IObjectDescribe> result = Maps.newHashMap(detailDescribes);
        result.put(objectDescribe.getApiName(), objectDescribe);
        return result;
    }

    public MasterAndDetailDescribes detailConvert2ChangeOrderDescribes(String masterChangeOrderApiName) {
        return ChangeOrderObjectDescribeConvert.getInstance().convert(this, masterChangeOrderApiName);

    }
}
