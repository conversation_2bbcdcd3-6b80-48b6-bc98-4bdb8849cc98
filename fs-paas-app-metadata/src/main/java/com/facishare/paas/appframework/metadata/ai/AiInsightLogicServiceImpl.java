package com.facishare.paas.appframework.metadata.ai;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.FilterExt;
import com.facishare.paas.appframework.metadata.repository.api.IRepository;
import com.facishare.paas.appframework.metadata.repository.model.AiInsightEntity;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.metadata.impl.search.Operator;

import java.util.List;
import java.util.Objects;

import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service("aiInsightLogicService")
public class AiInsightLogicServiceImpl implements AiInsightLogicService {

    @Autowired
    private IRepository<AiInsightEntity> aiInsightRepository;

    @Override
    public AiInsightEntity findByUniqKey(User user, String relateObjectApiName, String relateDataId, String componentApiName) {
        Query query = Query.builder().limit(1).needReturnCountNum(false).needReturnQuote(false).build();
        query.and(FilterExt.of(Operator.EQ, AiInsightEntity.RELATE_OBJECT_API_NAME, relateObjectApiName).getFilter());
        query.and(FilterExt.of(Operator.EQ, AiInsightEntity.RELATE_DATA_ID, relateDataId).getFilter());
        query.and(FilterExt.of(Operator.EQ, AiInsightEntity.COMPONENT_API_NAME, componentApiName).getFilter());
        List<AiInsightEntity> aiInsightEntities = aiInsightRepository.findBy(user, query, AiInsightEntity.class);
        return CollectionUtils.notEmpty(aiInsightEntities) ? aiInsightEntities.get(0) : null;
    }

    @Override
    public void save(User user, AiInsightEntity aiInsightEntity) {
        AiInsightEntity dbEntity = findByUniqKey(user, aiInsightEntity.getRelateObjectApiName(),
                aiInsightEntity.getRelateDataId(), aiInsightEntity.getComponentApiName());
        if (dbEntity != null) {
            List<String> updateFields = Lists.newArrayList(AiInsightEntity.INSIGHT_RESULT);
            if (Objects.nonNull(aiInsightEntity.getGenerateTime())) {
                updateFields.add(AiInsightEntity.GENERATE_TIME);
            }
            aiInsightEntity.setId(dbEntity.getId());
            aiInsightRepository.bulkUpdateByFields(user, Lists.newArrayList(aiInsightEntity), updateFields);
        } else {
            aiInsightRepository.create(user, aiInsightEntity);
        }
    }
}