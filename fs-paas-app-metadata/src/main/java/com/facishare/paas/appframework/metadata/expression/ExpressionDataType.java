package com.facishare.paas.appframework.metadata.expression;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.DateTimeUtils;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.expression.Type;
import com.facishare.paas.expression.type.PDate;
import com.facishare.paas.expression.type.PDateTime;
import com.facishare.paas.expression.type.PTime;
import com.facishare.paas.metadata.api.IRecordTypeOption;
import com.facishare.paas.metadata.api.describe.Count;
import com.facishare.paas.metadata.api.describe.Formula;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.timezone.TimeZoneContext;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.math.NumberUtils;

import java.math.BigDecimal;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * Created by liyiguang on 2018/4/23.
 */
@Slf4j
public enum ExpressionDataType implements ExpressionDataTypeConverter {
    Decimal(Type.DECIMAL) {
        @Override
        public Object toExpressionCalculateObject(Object data) {
            if (data instanceof BigDecimal) {
                return data;
            }
            if (data instanceof Number) {
                return new BigDecimal(data.toString());
            }
            if (data instanceof String && ObjectDataExt.isDecimal((String) data)) {
                return new BigDecimal((String) data);
            }
            throw new ExpressionCalculateException("UnSupport covert type:" + data.getClass() + " to BigDecimal");
        }

        @Override
        public Object toPaaSObject(Object object) {
            if (object instanceof BigDecimal) {
                return object;
            }
            if (object instanceof Number) {
                return new BigDecimal(object.toString());
            }
            throw new ExpressionCalculateException("UnSupport covert object:" + object.getClass() + " to BigDecimal");
        }
    },
    //百分有 mul/div 100的处理
    Percentile(Type.DECIMAL) {
        @Override
        public Object toExpressionCalculateObject(Object data) {
            if (data instanceof BigDecimal) {
                return ((BigDecimal) data).divide(BigDecimal.valueOf(100));
            }
            if (data instanceof Number) {
                return new BigDecimal(data.toString()).divide(BigDecimal.valueOf(100));
            }
            if (data instanceof String && ObjectDataExt.isDecimal((String) data)) {
                return new BigDecimal((String) data).divide(BigDecimal.valueOf(100));
            }
            throw new ExpressionCalculateException("UnSupport covert type:" + data.getClass() + " to BigDecimal");
        }

        @Override
        public Object toPaaSObject(Object object) {
            if (object instanceof Number) {
                BigDecimal ret = new BigDecimal(object.toString()).multiply(BigDecimal.valueOf(100));
                return ret;
            }
            throw new ExpressionCalculateException("UnSupport covert object:" + object.getClass() + " to BigDecimal");
        }
    },

    String(Type.STRING) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            return object.toString();
        }

        @Override
        public Object toPaaSObject(Object object) {
            return object.toString();
        }
    },
    DateTime(Type.DATETIME) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            return toExpressionCalculateObject(object, false);
        }

        private Object toExpressionCalculateObject(Object object, boolean notUseMultiTimeZone) {
            if (object instanceof java.util.Date) {
                return PDateTime.of(((java.util.Date) object).getTime());
            }
            if (object instanceof Long || object instanceof String || NumberUtils.isCreatable(object.toString())) {
                ZoneId zoneId = null;
                long time;
                if (DateTimeUtils.isGrayTimeZone()) {
                    if (notUseMultiTimeZone) {
                        zoneId = TimeZoneContext.DEFAULT_TIME_ZONE;
                    } else {
                        zoneId = TimeZoneContextHolder.getTenantTimeZone();
                    }
                } else {
                    zoneId = ZoneId.systemDefault();
                }
                if (object instanceof Long) {
                    time = (Long) object;
                } else {
                    time = DateTimeUtils.parseISOLocateDateTime(object.toString(), IFieldType.DATE_TIME, zoneId);
                }
                return PDateTime.of(time, zoneId);
            }
            throw new ExpressionCalculateException("UnSupport covert type:" + object.getClass() + " to DateTime");
        }

        @Override
        public Object toExpressionCalculateObject(Object object, IFieldDescribe fieldDescribe, boolean useValue, String tenantId) {
            if (!AppFrameworkConfig.isDateTimeSupportNotUseMultitimeZoneTenant(tenantId) || Objects.isNull(fieldDescribe) || !(fieldDescribe instanceof DateTimeFieldDescribe)) {
                return toExpressionCalculateObject(object, false);
            }
            DateTimeFieldDescribe dateTimeFieldDescribe = (DateTimeFieldDescribe) fieldDescribe;
            return toExpressionCalculateObject(object, dateTimeFieldDescribe.getNotUseMultiTimeZone());
        }

        @Override
        public Object toPaaSObject(Object object) {
            if (object instanceof PDateTime) {
                return ((PDateTime) object).toTimeStamp();
            }
            throw new ExpressionCalculateException("UnSupport covert type:" + object.getClass() + " to DateTime");
        }
    },
    Date(Type.DATE) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            if (object instanceof java.util.Date) {
                return PDate.of(((java.util.Date) object).getTime());
            }
            if (object instanceof Long || object instanceof String || NumberUtils.isCreatable(object.toString())) {
                Long timestamp = getTimestamp(IFieldType.DATE, object.toString());
                return PDate.of(timestamp);
            }
            throw new ExpressionCalculateException("UnSupport covert type:" + object.getClass() + " to Date");
        }

        @Override
        public Object toPaaSObject(Object object) {
            if (object instanceof PDate) {
                return ((PDate) object).toTimeStamp();
            }
            throw new ExpressionCalculateException("UnSupport covert type:" + object.getClass() + " to Date");
        }
    },
    Time(Type.TIME) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            if (object instanceof java.util.Date) {
                return PTime.of(((java.util.Date) object).getTime());
            }
            if (object instanceof Long || object instanceof String || NumberUtils.isCreatable(object.toString())) {
                Long timestamp = getTimestamp(IFieldType.TIME, object.toString());
                return PTime.of(timestamp);
            }
            throw new ExpressionCalculateException("UnSupport covert type:" + object.getClass() + " to Time");
        }

        @Override
        public Object toPaaSObject(Object object) {
            if (object instanceof PTime) {
                return ((PTime) object).toTimeStamp();
            }
            throw new ExpressionCalculateException("UnSupport covert type:" + object.getClass() + " to Time");
        }
    },
    Boolean(Type.BOOLEAN) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            if (object instanceof Boolean) {
                return object;
            }
            if (object instanceof String) {
                return java.lang.Boolean.valueOf(object.toString());
            }
            throw new ExpressionCalculateException("UnSupport covert type:" + object.getClass() + " to Boolean");
        }

        @Override
        public Object toPaaSObject(Object object) {
            if (object instanceof Boolean) {
                return object;
            }
            if (object instanceof Number) {
                return ((Number) object).longValue() > 0;
            }

            throw new ExpressionCalculateException("UnSupport covert type:" + object.getClass() + " to Boolean");
        }
    },
    SelectOne(Type.STRING) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            return object.toString();
        }

        @Override
        public Object toExpressionCalculateObject(Object object, IFieldDescribe fieldDescribe, boolean useValue, String tenantId) {
            if (useValue
                    || FieldDescribeExt.CURRENCY_FIELD.equals(fieldDescribe.getApiName())
                    || FieldDescribeExt.FUNCTIONAL_CURRENCY_FIELD.equals(fieldDescribe.getApiName())) {
                return object;
            }
            SelectOneFieldDescribe selectOneFieldDescribe = (SelectOneFieldDescribe) fieldDescribe;
            return SelectOneExt.of(selectOneFieldDescribe).getLabelByValue(object.toString());
        }

        @Override
        public Object toPaaSObject(Object object) {
            return object.toString();
        }
    },
    SelectMany(Type.LIST) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            if (object instanceof List) {
                return object;
            }
            throw new ExpressionCalculateException("UnSupport covert type:" + object.getClass() + " to List");
        }

        @Override
        public Object toExpressionCalculateObject(Object object, IFieldDescribe fieldDescribe, boolean useValue, String tenantId) {
            if (!(object instanceof List)) {
                throw new ExpressionCalculateException("UnSupport covert type:" + object.getClass() + " to List");
            }
            if (useValue) {
                return object;
            }
            SelectManyFieldDescribe selectManyFieldDescribe = (SelectManyFieldDescribe) fieldDescribe;
            return SelectManyExt.of(selectManyFieldDescribe).getLabelsByValues((List) object);
        }

        @Override
        public Object toPaaSObject(Object object) {
            if (!(object instanceof List)) {
                throw new ExpressionCalculateException("UnSupport covert type:" + object.getClass() + " to List");
            }
            return object;
        }
    },
    RecordType(Type.STRING) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            return object.toString();
        }

        @Override
        public Object toExpressionCalculateObject(Object object, IFieldDescribe fieldDescribe, boolean useValue, String tenantId) {
            if (useValue) {
                return object;
            }
            RecordTypeFieldDescribe recordTypeFieldDescribe = (RecordTypeFieldDescribe) fieldDescribe;
            IRecordTypeOption option = recordTypeFieldDescribe.getRecordTypeOption(object.toString());
            return option == null ? "" : option.getLabel();
        }

        @Override
        public Object toPaaSObject(Object object) {
            return object.toString();
        }
    },
    CountryArea(Type.STRING) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            return object.toString();
        }

        @Override
        public Object toExpressionCalculateObject(Object object, IFieldDescribe fieldDescribe, boolean useValue, String tenantId) {
            return CountryAreaManager.getLabelByCode(tenantId, object.toString(), fieldDescribe.getType());
        }

        @Override
        public Object toPaaSObject(Object object) {
            return object.toString();
        }
    },
    Location(Type.STRING) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            String[] strArray = object.toString().split("\\#\\%\\$");
            if (strArray.length > 1) {
                return strArray[strArray.length - 1];
            }
            return object.toString();
        }

        @Override
        public Object toPaaSObject(Object object) {
            return object.toString();
        }
    },
    RichText(Type.STRING) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            if (!(object instanceof Map)) {
                return object.toString();
            }
            Map map = (Map) object;
            return map.get("text");
        }

        @Override
        public Object toPaaSObject(Object object) {
            return object.toString();
        }
    },
    HtmlRichText(Type.STRING) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            if (!(object instanceof Map)) {
                return object.toString();
            }
            Map map = (Map) object;
            return map.get("text");
        }

        @Override
        public Object toPaaSObject(Object object) {
            return object.toString();
        }
    },
    ObjectReferenceMany(Type.LIST) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            return object;
        }

        @Override
        public Object toPaaSObject(Object object) {
            if (ObjectUtils.isEmpty(object)) {
                return Lists.newArrayList();
            }
            return object;
        }
    },
    EmployeeAndDepartment(Type.LIST) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            return object;
        }

        @Override
        public Object toPaaSObject(Object object) {
            if (ObjectUtils.isEmpty(object)) {
                return Lists.newArrayList();
            }
            return object;
        }
    },
    ARRAY(Type.LIST) {
        @Override
        public Object toExpressionCalculateObject(Object object) {
            return object;
        }

        @Override
        public Object toPaaSObject(Object object) {
            if (ObjectUtils.isEmpty(object)) {
                return Lists.newArrayList();
            }
            return object;
        }
    };


    private static Long getTimestamp(String timeType, String timeText) {
        return getTimestamp(timeType, timeText, TimeZoneContextHolder.getTenantTimeZone());
    }

    private static Long getTimestamp(String timeType, String timeText, ZoneId zoneId) {
        return DateTimeUtils.parseISOLocateDateTime(timeText, timeType, zoneId);


    }

    private Type type;

    ExpressionDataType(Type type) {
        this.type = type;
    }

    public Type getExpressionType() {
        return type;
    }

    public static ExpressionDataType of(IFieldDescribe field) {
        if (Objects.equals(field.getType(), IFieldType.ARRAY)) {
            log.warn("Array type in formula, object:{}, field:{}", field.getDescribeApiName(), field.getApiName());
        }
        switch (field.getType()) {
            case IFieldType.FORMULA:
                return of(((Formula) field).getReturnType());
            case IFieldType.QUOTE:
                return of(((QuoteFieldDescribe) field).getQuoteFieldType());
            case IFieldType.COUNT:
                Count countField = (Count) field;
                if (Count.TYPE_COUNT.equals(countField.getCountType())) {
                    return Decimal;
                }
                String countReturnType = countField.getReturnType();
                return Strings.isNullOrEmpty(countReturnType) ? Decimal : of(countReturnType);
            default:
                return of(field.getType());
        }
    }

    public static ExpressionDataType of(String type) {
        if (Strings.isNullOrEmpty(type)) {
            return Decimal;
        }
        switch (type) {
            case IFieldType.NUMBER:
            case IFieldType.CURRENCY:
                return Decimal;
            case IFieldType.PERCENTILE:
                return Percentile;
            case IFieldType.DATE:
                return Date;
            case IFieldType.DATE_TIME:
                return DateTime;
            case IFieldType.TIME:
                return Time;
            case IFieldType.TRUE_OR_FALSE:
                return Boolean;
            case IFieldType.SELECT_ONE:
                return SelectOne;
            case IFieldType.SELECT_MANY:
                return SelectMany;
            case IFieldType.COUNTRY:
            case IFieldType.PROVINCE:
            case IFieldType.CITY:
            case IFieldType.DISTRICT:
                return CountryArea;
            case IFieldType.LOCATION:
                return Location;
            case IFieldType.RICH_TEXT:
                return RichText;
            case IFieldType.HTML_RICH_TEXT:
                return HtmlRichText;
            case IFieldType.OBJECT_REFERENCE_MANY:
                return ObjectReferenceMany;
            case IFieldType.RECORD_TYPE:
                return RecordType;
            case IFieldType.DEPARTMENT:
            case IFieldType.DEPARTMENT_MANY:
            case IFieldType.EMPLOYEE:
            case IFieldType.EMPLOYEE_MANY:
            case IFieldType.DIMENSION:
                return EmployeeAndDepartment;
            case IFieldType.ARRAY:
                if (UdobjGrayConfig.isAllow("array_in_formula_gray", ARRAY.parseEI())) {
                    return ARRAY;
                } else {
                    return String;
                }
            default:
                return String;
        }
    }

    private String parseEI() {
        RequestContext context = RequestContextManager.getContext();
        if (Objects.nonNull(context)) {
            return context.getTenantId();
        }
        return null;
    }

    @Override
    public Object toExpressionCalculateObject(Object object, IFieldDescribe fieldDescribe, boolean useValue, String tenantId) {
        return toExpressionCalculateObject(object);
    }
}


