package com.facishare.paas.appframework.metadata.internal.model;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.List;
import java.util.Map;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CommonInternationalTranslate {

    public static final String PARAM_PREFIX = "#I18N#";

    //国际化整个消息的多语词条
    //词条:paas.udobj.mywork
    //0:今天{0}
    //词条样式：我{0}上班{1}小时
    private String internationalKey;
    //如果多语获取失败的默认值,我周一上班10小时
    private String defaultInternationalValue;
    //国际化词条的中对应参数的国际化词条
    //词条:[pass.udobj.zhouyi,pass.udobj.10]
    @Builder.Default
    private List<String> internationalParameters = Lists.newArrayList();
    //国际化词条单个词条的默认值,key是国际化词条,value是默认值
    //{"pass.udobj.zhouyi":"周一","pass.udobj.10":"10"}
    @Builder.Default
    private Map<String, String> defaultParameterValues = Maps.newHashMap();

    public String getTranslationValue() {
        if (StringUtils.isBlank(internationalKey)) {
            return defaultInternationalValue;
        }
        if (CollectionUtils.isEmpty(internationalParameters)) {
            return I18NExt.getOrDefault(internationalKey, defaultInternationalValue);
        }
        String internationalValue = I18NExt.getOrDefault(internationalKey, internationalKey);
        if (StringUtils.isBlank(internationalValue)) {
            return defaultInternationalValue;
        }
        MessageFormat messageFormat = new MessageFormat(internationalValue);
        int length = messageFormat.getFormatsByArgumentIndex().length;
        if (length != internationalParameters.size()) {
            return defaultInternationalValue;
        }
        Object[] parameters = new Object[internationalParameters.size()];
        int index = 0;
        for (String parameter : internationalParameters) {
            parameters[index++] = getParameterValue(parameter, defaultParameterValues);
        }
        return messageFormat.format(parameters);
    }

    private String getParameterValue(String parameter, Map<String, String> defaultParameters) {
        if (parameter.startsWith(PARAM_PREFIX)) {
            String defaultVal = null;
            String key = parameter.substring(PARAM_PREFIX.length());
            if (MapUtils.isNotEmpty(defaultParameters)) {
                defaultVal = defaultParameters.get(key);
            }
            return I18NExt.getOrDefault(key, defaultVal);
        } else {
            return parameter;
        }
    }
}
