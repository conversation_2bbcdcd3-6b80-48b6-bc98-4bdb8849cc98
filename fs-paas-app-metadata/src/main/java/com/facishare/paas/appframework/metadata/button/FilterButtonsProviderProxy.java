package com.facishare.paas.appframework.metadata.button;

import com.facishare.paas.appframework.core.rest.BaseAPIResult;
import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;
import lombok.*;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON>ju on 2022/12/26
 */
@RestResource(
        value = "NCRM",
        desc = "NCRM服务", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface FilterButtonsProviderProxy {

    @POST(value = "/API/v1/rest/object/FilterButtonsProvider/service/findButtons", desc = "获取预设对象的特殊按钮")
    RestResult findButtons(@Body Arg arg, @HeaderMap Map<String, String> header);


    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Arg {
        private List<Map> buttonList;
        private String describeApiName;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    class Result {
        private List<Map> result;
    }

    @EqualsAndHashCode(callSuper = true)
    @Data
    @ToString(callSuper = true)
    class RestResult extends BaseAPIResult {
        private GetLayoutDesignerButton.Result data;
    }
}
