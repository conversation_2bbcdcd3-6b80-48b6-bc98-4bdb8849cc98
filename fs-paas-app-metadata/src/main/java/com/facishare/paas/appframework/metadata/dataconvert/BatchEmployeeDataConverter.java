package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.common.service.OrgService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Created by zhouwr on 2019/4/1
 */
@Component
public class BatchEmployeeDataConverter extends BaseBatchFieldDataConverter {

    @Autowired
    private OrgService orgService;

    @Override
    protected Map<String, String> getIdNameMap(List<String> idList, IFieldDescribe fieldDescribe, User user) {
        return orgService.getUserNameMapByIds(user.getTenantId(), user.getUserId(), idList);
    }

    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.EMPLOYEE);
    }
}
