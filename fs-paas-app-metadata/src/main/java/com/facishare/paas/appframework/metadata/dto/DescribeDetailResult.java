package com.facishare.paas.appframework.metadata.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * Created by zhaopx on 2017/10/27.
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DescribeDetailResult {

    private Map layout;

    private Map objectDescribe;

    private List<RefObjectDescribeListResult> refObjectDescribeList;

    private ArrayList templates;

    private List<DetailObjectListResult> detailObjectList;

    private Map objectData;

    private List fieldList;

    private List<DetailObjectListResult> allObjectDetailObjectList;
}
