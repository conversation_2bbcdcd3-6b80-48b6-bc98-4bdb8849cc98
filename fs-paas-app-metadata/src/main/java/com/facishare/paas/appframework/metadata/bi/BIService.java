package com.facishare.paas.appframework.metadata.bi;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.dto.CrossFilterSupportedObjects;
import com.facishare.paas.appframework.metadata.dto.CrossObjectFilter;

import java.util.List;

/**
 * BI服务接口
 * 
 * <AUTHOR>
 */
public interface BIService {

    /**
     * 获取对象关系结果
     *
     * @param user 用户信息
     * @param arg 请求参数
     * @return 对象关系结果
     */
    CrossObjectFilter.ObjRelationResult getObjRelationResult(User user, CrossObjectFilter.ObjRelationArg arg);

    /**
     * 查询报表数据
     *
     * @param user 用户信息
     * @param arg 请求参数
     * @return 报表数据结果
     */
    CrossObjectFilter.QueryReportResult queryReportData(User user, CrossObjectFilter.QueryReportArg arg);

    /**
     * 获取支持跨对象筛选的对象列表
     *
     * @param user 用户信息
     * @param crmObjNames CRM对象名称列表
     * @return 验证结果
     */
    CrossFilterSupportedObjects.Result getCrossFilterSupportedObjects(User user, List<String> crmObjNames);

    /**
     * 检查对象是否支持跨对象筛选
     *
     * @param user 用户信息
     * @param objectApiName 对象API名称
     * @return 是否支持跨对象筛选
     */
    boolean supportCrossObjectFilter(User user, String objectApiName);
} 