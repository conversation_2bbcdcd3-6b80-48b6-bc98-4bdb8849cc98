package com.facishare.paas.appframework.metadata.data.validator;

import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.base.Strings;
import com.google.common.collect.Sets;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.*;

import static com.facishare.paas.appframework.core.i18n.I18NKey.FIELD_DATA_TYPE_WRONG;

@Component
public class EmployeeValidator implements IDataTypeValidator {
    @Override
    public Set<String> supportFieldTypes() {
        return Sets.newHashSet(IFieldType.EMPLOYEE, IFieldType.EMPLOYEE_MANY, IFieldType.OUT_EMPLOYEE);
    }

    @Override
    public void validateDataType(String fieldApiName, IObjectData data, IObjectDescribe describe) {
        if (checkParamError(fieldApiName, data, describe)) {
            return;
        }

        ObjectDataExt.of(data).validateEmployeeFieldDataType(getField(fieldApiName, describe));
    }

}
