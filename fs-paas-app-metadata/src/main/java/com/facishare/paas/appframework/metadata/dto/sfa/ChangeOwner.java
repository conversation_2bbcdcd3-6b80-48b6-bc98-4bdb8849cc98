package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.Builder;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

/**
 * Created by yusb on 2017/12/27.
 */
public interface ChangeOwner {

    @Data
    @Builder
    class Arg {
        @SerializedName("ObjectType")
        @JSONField(name = "ObjectType")
        private String objectType;

        @SerializedName("ObjectID")
        @JSONField(name = "ObjectID")
        private String objectId;

        @SerializedName("OwnerID")
        @JSONField(name = "OwnerID")
        private String ownerId;
    }

    @Getter
    @Setter
    class Result extends BaseResult {

    }
}
