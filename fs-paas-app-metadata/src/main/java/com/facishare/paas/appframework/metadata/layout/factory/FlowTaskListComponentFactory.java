package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.paas.appframework.common.util.DocumentBaseEntity;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.component.FlowTaskListComponentExt;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.FormField;
import com.facishare.paas.metadata.impl.ui.layout.component.CommonComponent;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2024/3/8
 */
@Component
public class FlowTaskListComponentFactory implements IComponentFactory {

    private final LayoutLogicService layoutLogicService;

    public FlowTaskListComponentFactory(LayoutLogicService layoutLogicService) {
        this.layoutLogicService = layoutLogicService;
    }

    @Override
    public String supportComponentType() {
        return ComponentExt.TYPE_TASK_LIST_COMPONENT;
    }

    @Override
    public IComponent createDefaultComponent(ILayoutFactory.Context context, IObjectDescribe describe) {
        IComponent component = new CommonComponent();
        component.setType(FlowTaskListComponentExt.COMPONENT_TYPE_TASK_LIST);
        FlowTaskListComponentExt flowTaskListComponentExt = FlowTaskListComponentExt.of(component);

        flowTaskListComponentExt.setName(FlowTaskListComponentExt.TASK_LIST_NAME);
        flowTaskListComponentExt.setHeader(I18NExt.getOrDefault(I18NKey.FLOW_TASK_LIST_PAGE, "流程待办列表"));// ignoreI18n
        flowTaskListComponentExt.setNameI18nKey(I18NKey.FLOW_TASK_LIST_PAGE);

        return flowTaskListComponentExt.getComponent();
    }

}
