package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.metadata.FieldDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.multiRegion.MultiRegionContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * Created by zhouwr on 2019/4/1
 */
@Slf4j
@Component
public class BatchFieldDataConverterAdapter extends AbstractBatchFieldDataConverter {

    @Autowired
    private FieldDataConverterManager fieldDataConverterManager;

    @Override
    protected void convert(List<IObjectData> dataList, IFieldDescribe fieldDescribe, DataConvertContext context) {
        FieldDataConverter fieldDataConverter = fieldDataConverterManager.getFieldDataConverter(fieldDescribe.getType());
        if (AppFrameworkConfig.isUseMultiRegionByFieldType(FieldDescribeExt.of(fieldDescribe).getTypeOrReturnType())) {
            context.setRegion(MultiRegionContextHolder.getUserRegion());
        }
        dataList.forEach(data -> {
            try {
                String value = fieldDataConverter.convertFieldData(data, fieldDescribe, context);
                data.set(fieldDescribe.getApiName(), value);
            } catch (Exception e) {
                log.warn("convert field data error,data:{},field:{},context:{}", data.toJsonString(),
                        fieldDescribe.toJsonString(), context, e);
            }
        });
    }

    @Override
    public List<String> getSupportedFieldTypes() {
        return Collections.emptyList();
    }
}
