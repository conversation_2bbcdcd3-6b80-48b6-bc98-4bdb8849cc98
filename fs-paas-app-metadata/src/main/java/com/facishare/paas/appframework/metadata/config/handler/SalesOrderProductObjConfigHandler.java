package com.facishare.paas.appframework.metadata.config.handler;


import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.metadata.config.util.TenantConfigUtil;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class SalesOrderProductObjConfigHandler implements ConfigHandler {
    @Autowired
    ConfigService configService;

    @Override
    public String getObjectAPIName() {
        return "SalesOrderProductObj";
    }

    @Override
    public void handle(String tenantId, Map<String, Object> objectConfig, Map<String, Map<String, Object>> fieldConfig) {
        boolean isPriceBookEnabled = TenantConfigUtil.isPriceBookEnabled(configService, tenantId);
        if (isPriceBookEnabled) {
            if (fieldConfig.containsKey("price_book_product_id")) {
                Map<String, Object> attrMap = Maps.newHashMap();
                attrMap.put("default_value", 0);
                attrMap.put("wheres", 1);
                attrMap.put("is_required", 0);
                attrMap.put("help_text", 1);
                attrMap.put("label", 1);
                attrMap.put("target_related_list_label", 1);
                fieldConfig.get("price_book_product_id").put("display", 1);
                fieldConfig.get("price_book_product_id").put("enable", 0);
                fieldConfig.get("price_book_product_id").put("edit", 1);
                fieldConfig.get("price_book_product_id").put("attrs", attrMap);
            }
        }

        boolean isPromotionEnabled = TenantConfigUtil.isPromotionEnabled(configService, tenantId);
        if (isPromotionEnabled) {
            if (fieldConfig.containsKey("promotion_id")) {
                Map<String, Object> attrsConfig = Maps.newHashMap();
                attrsConfig.put("label", 1);
                attrsConfig.put("target_related_list_label", 1);
                attrsConfig.put("is_required", 0);
                attrsConfig.put("default_value", 0);
                attrsConfig.put("is_unique", 0);
                attrsConfig.put("wheres", 1);
                fieldConfig.get("promotion_id").put("edit", 1);
                fieldConfig.get("promotion_id").put("display", 1);
                fieldConfig.get("promotion_id").put("enable", 0);
                fieldConfig.get("promotion_id").put("attrs", attrsConfig);
            }
        }

        boolean isPricePolicyEnabled = TenantConfigUtil.isPricePolicyEnabled(configService, tenantId);
        boolean isOpenCoupon = TenantConfigUtil.isOpenCoupon(configService, tenantId);
        boolean isOpenRebate = TenantConfigUtil.isOpenRebate(configService, tenantId);
        if (isPromotionEnabled || isPricePolicyEnabled || isOpenCoupon || isOpenRebate) {
            if (fieldConfig.containsKey("is_giveaway")) {
                Map<String, Object> attrsConfig = Maps.newHashMap();
                attrsConfig.put("label", 1);
                attrsConfig.put("target_related_list_label", 1);
                attrsConfig.put("is_required", 0);
                attrsConfig.put("default_value", 0);
                attrsConfig.put("is_unique", 0);
                attrsConfig.put("help_text", 1);
                attrsConfig.put("wheres", 1);
                fieldConfig.get("is_giveaway").put("edit", 1);
                fieldConfig.get("is_giveaway").put("display", 1);
                fieldConfig.get("is_giveaway").put("enable", 0);
                fieldConfig.get("is_giveaway").put("attrs", attrsConfig);
            }
        }

        boolean isDeliveryNoteEnabled = TenantConfigUtil.isDeliveryNoteEnabled(configService, tenantId);
        if (isDeliveryNoteEnabled) {
            if (fieldConfig.containsKey("delivered_count")) {
                Map<String, Object> attrsConfig = Maps.newHashMap();
                attrsConfig.put("is_required", 0);
                attrsConfig.put("default_value", 1);
                attrsConfig.put("label", 1);
                attrsConfig.put("help_text", 1);
                attrsConfig.put("length", 1);
                attrsConfig.put("max_length", 1);
                attrsConfig.put("decimal_places", 1);
                attrsConfig.put("wheres", 1);
                fieldConfig.get("delivered_count").put("display", 1);
                fieldConfig.get("delivered_count").put("enable", 0);
                fieldConfig.get("delivered_count").put("edit", 1);
                fieldConfig.get("delivered_count").put("attrs", attrsConfig);
            }
            if (fieldConfig.containsKey("delivery_amount")) {
                Map<String, Object> attrsConfig = Maps.newHashMap();
                attrsConfig.put("is_required", 0);
                attrsConfig.put("default_value", 1);
                attrsConfig.put("label", 1);
                attrsConfig.put("help_text", 1);
                attrsConfig.put("wheres", 1);
                attrsConfig.put("max_length", 1);
                attrsConfig.put("decimal_places", 1);
                attrsConfig.put("length", 1);
                fieldConfig.get("delivery_amount").put("display", 1);
                fieldConfig.get("delivery_amount").put("enable", 0);
                fieldConfig.get("delivery_amount").put("edit", 1);
                fieldConfig.get("delivery_amount").put("attrs", attrsConfig);
            }
        }

        //开启价格政策后不允许修改默认值
        if (isPricePolicyEnabled || isOpenRebate || isOpenCoupon) {
            if (fieldConfig.containsKey("sales_price")) {
                Map<String, Object> attrsConfig = Maps.newHashMap();
                attrsConfig.put("is_required", 1);
                attrsConfig.put("default_value", 0);
                attrsConfig.put("label", 1);
                attrsConfig.put("help_text", 1);
                attrsConfig.put("max_length", 1);
                attrsConfig.put("decimal_places", 1);
                attrsConfig.put("length", 1);
                fieldConfig.get("sales_price").put("display", 1);
                fieldConfig.get("sales_price").put("enable", 0);
                fieldConfig.get("sales_price").put("edit", 1);
                fieldConfig.get("sales_price").put("attrs", attrsConfig);
            }

            if (fieldConfig.containsKey("subtotal")) {
                Map<String, Object> attrsConfig = Maps.newHashMap();
                attrsConfig.put("is_required", 1);
                attrsConfig.put("default_value", 0);
                attrsConfig.put("label", 1);
                attrsConfig.put("help_text", 1);
                attrsConfig.put("max_length", 1);
                attrsConfig.put("decimal_places", 1);
                attrsConfig.put("length", 1);
                fieldConfig.get("subtotal").put("display", 1);
                fieldConfig.get("subtotal").put("enable", 1);
                fieldConfig.get("subtotal").put("edit", 1);
                fieldConfig.get("subtotal").put("attrs", attrsConfig);
            }
        }
        boolean isStratifiedPriceEnabled = TenantConfigUtil.isStratifiedPriceEnabled(configService, tenantId);
        if (isStratifiedPriceEnabled) {
            if (fieldConfig.containsKey("sales_price")) {
                Map<String, Object> attrMap = (Map<String, Object>) fieldConfig.get("sales_price").get("attrs");
                if (attrMap != null) {
                    attrMap.put("default_value", 0);
                }
                fieldConfig.get("sales_price").put("attrs", attrMap);
            }

            if (fieldConfig.containsKey("subtotal")) {
                Map<String, Object> attrMap = (Map<String, Object>) fieldConfig.get("subtotal").get("attrs");
                if (attrMap != null) {
                    attrMap.put("default_value", 0);
                }
                fieldConfig.get("subtotal").put("attrs", attrMap);
            }
        }
    }
}
