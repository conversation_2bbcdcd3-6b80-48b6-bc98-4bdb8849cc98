package com.facishare.paas.appframework.metadata.bizfield;

import com.alibaba.fastjson.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * Created by zhouwr on 2023/9/15.
 */
@Data
public class BizFieldDocument {
    @JsonProperty("biz_field_api_name")
    @JSONField(name = "biz_field_api_name")
    private String bizFieldApiName;
    private String label;

    public static BizFieldDocument of(BizFieldDefinition definition) {
        BizFieldDocument document = new BizFieldDocument();
        document.setBizFieldApiName(definition.getApiName());
        document.setLabel(definition.getLabel());
        return document;
    }
}
