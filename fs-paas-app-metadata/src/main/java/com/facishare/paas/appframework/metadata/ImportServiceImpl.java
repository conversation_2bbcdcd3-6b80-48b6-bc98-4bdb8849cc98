package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.UdobjGrayUtil;
import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.gray.config.OptionalFeaturesSwitchDTO;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.dto.ImportReferenceMapping;
import com.facishare.paas.appframework.metadata.dto.ImportTenantSetting;
import com.facishare.paas.appframework.metadata.importobject.ImportMarkInfo;
import com.facishare.paas.appframework.metadata.importobject.dataconvert.ImportBatchFieldDataConverter;
import com.facishare.paas.appframework.metadata.importobject.dataconvert.ImportBatchFieldDataConverterManager;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.mongo.imoprt.ImportInfo;
import com.facishare.paas.appframework.metadata.mongo.imoprt.ImportMongoDao;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.common.util.UdobjConstants;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.TeamRoleInfo;
import com.facishare.paas.metadata.api.checker.CheckerResult;
import com.facishare.paas.metadata.api.describe.*;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.*;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.facishare.paas.metadata.validator.data.ObjectValidator;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.core.util.UdobjGrayConfigKey.IMPORT_TEMPLATE_ORDER_EI;
import static com.facishare.paas.appframework.metadata.FieldDescribeExt.RECORD_TYPE;
import static com.facishare.paas.metadata.api.DBRecord.*;
import static com.facishare.paas.metadata.api.IObjectData.*;
import static com.facishare.paas.metadata.api.Packagable.PACKAGE;
import static com.facishare.paas.metadata.api.Tenantable.TENANT_ID;

@Slf4j
@Service("importService")
public class ImportServiceImpl implements ImportService {

    public final static String REDIS_SET_SUCCESS = "OK";
    public static final long EXPIRE_TIME_THRESHOLD = 15;
    public static final int EXPIRE_TIME_WHEN_WRITE = 1800;
    public static final int EXPIRE_TIME_WHEN_ACCESS = 900;    // 导入从对象时，redis过期时间设置短一些即可
    public static final String TEAM_MEMBER = "TEAM_MEMBER";

    @Autowired
    private FunctionPrivilegeService functionPrivilegeService;
    @Autowired
    private ObjectValidator objectValidator;
    @Autowired
    private AutoNumberLogicService autoNumberLogicService;
    @Autowired
    private ImportBatchFieldDataConverterManager importBatchFieldDataConverterManager;
    @Autowired
    private LayoutLogicService layoutLogicService;
    @Autowired
    private ImportMongoDao importMongoDao;
    @Autowired
    private RedisDao redisDao;
    @Autowired
    private OptionalFeaturesService optionalFeaturesService;
    @Autowired
    private TeamMemberRoleService teamMemberRoleService;
    @Autowired
    private ConfigService configService;
    @Autowired
    private DescribeLogicService describeLogicService;

    private static List<String> SYS_FIELDS =
            Lists.newArrayList(ID, VERSION, CREATED_BY, CREATE_TIME, LAST_MODIFIED_BY,
                    LAST_MODIFIED_TIME, IS_DELETED, PACKAGE, TENANT_ID, DESCRIBE_ID, DESCRIBE_API_NAME,
                    UdobjConstants.OWNER_DEPARTMENT_API_NAME, UdobjConstants.RELEVANT_TEAM_API_NAME,
                    UdobjConstants.LIFE_STATUS_API_NAME, UdobjConstants.LOCK_STATUS_API_NAME,
                    UdobjConstants.LOCK_RULE_API_NAME, UdobjConstants.LOCK_USER_API_NAME,
                    UdobjConstants.LIFE_STATUS_BEFORE_INVALID_API_NAME, OUT_TENANT_ID, OUT_OWNER, ORDER_BY,
                    ObjectDataExt.OUT_RESOURCES, "partner_id");

    private static List<String> SLAVE_EXCLUDE_FIELDS = Lists.newArrayList(OWNER, DATA_OWN_DEPARTMENT, DATA_OWN_ORGANIZATION);

    @Override
    public List<IFieldDescribe> getTemplateField(User user, IObjectDescribe objectDescribe) {
        return getTemplateFields(user, objectDescribe, null);
    }

    @Override
    public List<IFieldDescribe> getTemplateField(User user, IObjectDescribe objectDescribe, String recordTypeApiName) {
        return getTemplateFields(user, objectDescribe, recordTypeApiName);
    }

    @Override
    public List<IFieldDescribe> getUpdateImportTemplateFieldIncludeFieldNames(User user,
                                                                              IObjectDescribe objectDescribe,
                                                                              List<String> fieldNames) {
        return getUpdateImportTemplateFieldIncludeFieldNames(user, objectDescribe, fieldNames, null);
    }

    @Override
    public List<IFieldDescribe> getUpdateImportTemplateField(User user, IObjectDescribe objectDescribe) {
        return getUpdateImportTemplateFieldIncludeFieldNames(user, objectDescribe, Collections.emptyList());
    }

    @Override
    public List<IFieldDescribe> getUpdateImportTemplateField(User user, IObjectDescribe objectDescribe, String recordTypeApiName) {
        return getUpdateImportTemplateFieldIncludeFieldNames(user, objectDescribe, Collections.emptyList(), recordTypeApiName);
    }

    private List<IFieldDescribe> getTemplateFields(User user, IObjectDescribe describe, String recordType) {
        IObjectDescribe objectDescribe = ObjectDescribeExt.of(describe).copyOnWrite();
        processDateTimeRangeFields(objectDescribe);
        Set<String> canReadAndWriteFieldList = getReadAndWriteFields(user, objectDescribe);
        List<IFieldDescribe> fieldDescribes = getFields(user, objectDescribe, recordType);
        List<IFieldDescribe> validFieldList = Lists.newArrayList();
        //判断是否为从对象,从对象的导入模板不下发负责人、归属部门字段
        boolean isDetail = objectDescribe.getFieldDescribes().stream()
                .anyMatch(f -> IFieldType.MASTER_DETAIL.equals(f.getType()));
        for (IFieldDescribe field : fieldDescribes) {
            //从对象
            if (isDetail && SLAVE_EXCLUDE_FIELDS.contains(field.getApiName())) {
                continue;
            }
            // 主从联合导入，从对象屏蔽外部负责人、预置合作伙伴字段
            if (isDetail) {
                if (OUT_OWNER.equals(field.getApiName())
                        || ObjectDescribeExt.PARTNER_ID_API_NAME.equals(field.getApiName())) {
                    continue;
                }
            }

            // 下游人员导入，不支持部门、人员、负责人字段、合作伙伴，图片，附件,外部负责人
            if (isExcludedFieldForOuterUserImport(user, field)) {
                continue;
            }

            if (canReadAndWriteFieldList.contains(field.getApiName())
                    // 根据配置文件屏蔽不支持的字段
                    && checkIsTitle(field, objectDescribe)
                    && field.isActive()
                    && !"extend_obj_data_id".equals(field.getApiName())
                    && !Objects.equals(field.isAbstract(), Boolean.TRUE)) {
                validFieldList.add(field);
            }
        }
        //非从对象增加相关团队普通成员虚拟字段
        if (!isDetail && !user.isOutUser()) {
            OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), objectDescribe);
            if (optionalFeaturesSwitch.getIsRelatedTeamEnabled()) {
                //增加 普通成员
                addRelevantTeam(user, validFieldList, objectDescribe);
            }
        }
        // 新建导入的模板需要补充业务类型字段
        if (validFieldList.stream().noneMatch(it -> RECORD_TYPE.equals(it.getApiName()))) {
            ObjectDescribeExt.of(objectDescribe)
                    .getFieldDescribeSilently(RECORD_TYPE)
                    .ifPresent(validFieldList::add);
        }
        return validFieldList;
    }

    private boolean isExcludedFieldForOuterUserImport(User user, IFieldDescribe field) {
        if (!user.isOutUser()) {
            return false;
        }
        // 下游导入支持外部负责人
        if (IObjectData.OUT_OWNER.equals(field.getApiName())
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUTER_IMPORT_SUPPORT_OUT_OWNER_GRAY, user.getTenantId())) {
            return false;
        }
        return IFieldType.DEPARTMENT.equals(field.getType())
                || IFieldType.DEPARTMENT_MANY.equals(field.getType())
                || IFieldType.EMPLOYEE.equals(field.getType())
                || IFieldType.EMPLOYEE_MANY.equals(field.getType())
                || IFieldType.OUT_EMPLOYEE.equals(field.getType())
                || IFieldType.OUT_DEPARTMENT.equals(field.getType())
                || "partner_id".equals(field.getApiName())
                || IFieldType.IMAGE.equals(field.getType())
                || IFieldType.FILE_ATTACHMENT.equals(field.getType())
                || IFieldType.SIGNATURE.equals(field.getType())
                || OWNER.equals(field.getApiName());
    }

    private boolean isExcludedFieldForOuterUserUpdateImport(User user, IFieldDescribe field) {
        if (!user.isOutUser()) {
            return false;
        }
        // 下游导入支持外部负责人
        if (IObjectData.OUT_OWNER.equals(field.getApiName())
                && UdobjGrayConfig.isAllow(UdobjGrayConfigKey.OUTER_IMPORT_SUPPORT_OUT_OWNER_GRAY, user.getTenantId())) {
            return false;
        }
        return IFieldType.DEPARTMENT.equals(field.getType())
                || IFieldType.DEPARTMENT_MANY.equals(field.getType())
                || IFieldType.EMPLOYEE.equals(field.getType())
                || IFieldType.EMPLOYEE_MANY.equals(field.getType())
                || IFieldType.OUT_EMPLOYEE.equals(field.getType())
                || IFieldType.OUT_DEPARTMENT.equals(field.getType())
                || "partner_id".equals(field.getApiName())
                || OUT_OWNER.equals(field.getApiName());
    }


    private void processDateTimeRangeFields(IObjectDescribe objectDescribe) {
        List<DateTimeRangeFieldDescribe> dateTimeRangeFields = ObjectDescribeExt.of(objectDescribe).getDateTimeRangeFields();
        for (DateTimeRangeFieldDescribe dateTimeRangeField : dateTimeRangeFields) {
            DateTimeFieldDescribe startTimeField = dateTimeRangeField.getStartTimeField(objectDescribe);
            DateTimeFieldDescribe endTimeField = dateTimeRangeField.getEndTimeField(objectDescribe);
            if (Objects.nonNull(startTimeField) && Objects.nonNull(endTimeField) && startTimeField.isRequired()) {
                endTimeField.setRequired(true);
            }
        }
    }

    private void addRelevantTeam(User user, List<IFieldDescribe> validFieldList, IObjectDescribe objectDescribe) {
        validFieldList.addAll(generateTeamMemberField(user, objectDescribe.getApiName()));
    }

    public List<TeamRoleInfo> findEnableTeamRoleInfosWithoutOwner(User user, String describeApiName) {
        List<TeamRoleInfo> teamRoleInfos;
        boolean teamRoleGray = TeamMember.isTeamRoleGray(user.getTenantId());
        if (teamRoleGray) {
            teamRoleInfos = teamMemberRoleService.queryTeamRoleInfo(user.getTenantId(), describeApiName).stream()
                    .filter(teamRoleInfo -> !StringUtils.equals(teamRoleInfo.getRoleType(), TeamMember.Role.OWNER.getValue()))
                    .filter(role -> Objects.nonNull(role.getStatus()) && role.getStatus() == 1)
                    .collect(Collectors.toList());
        } else {
            teamRoleInfos = generateDefaultTeamRole();
        }
        return teamRoleInfos;
    }

    public List<TeamRoleInfo> generateDefaultTeamRole() {
        List<TeamRoleInfo> teamRoleInfos = Lists.newArrayList();
        TeamRoleInfo normal = new TeamRoleInfo();
        normal.setRoleType(TeamMember.Role.NORMAL_STAFF.getValue());
        normal.setRoleName(TeamMember.Role.NORMAL_STAFF.getLabel());
        normal.setStatus(1);
        teamRoleInfos.add(normal);
        return teamRoleInfos;
    }

    public List<IFieldDescribe> generateTeamMemberField(User user, String describeApiName) {
        List<TeamRoleInfo> teamRoleInfos = findEnableTeamRoleInfosWithoutOwner(user, describeApiName);
        List<TeamRoleInfo> teamRoleInfoList = teamRoleInfos.stream()
                .filter(role -> !StringUtils.equals(TeamMember.Role.OWNER.getValue(), role.getRoleType()) && role.getStatus() == 1)
                .collect(Collectors.toList());
        if (TeamMember.isTeamMemberTypeExportGray(user.getTenantId())) {
            return generateTeamMemberByType(teamRoleInfoList);
        } else {
            return generateTeamMemberByRole(teamRoleInfoList);
        }
    }

    private List<IFieldDescribe> generateTeamMemberByRole(List<TeamRoleInfo> teamRoleInfoList) {
        List<IFieldDescribe> validFieldList = Lists.newArrayList();
        teamRoleInfoList.forEach(role -> {
            Map<String, Object> map = Maps.newHashMap();
            map.put(IFieldDescribe.API_NAME, String.join("_", TEAM_MEMBER, role.getRoleType(), TeamMember.Permission.READONLY.getValue()));
            map.put(IFieldDescribe.LABEL, String.join("-", role.getRoleName(), I18NExt.text(I18NKey.constant_read_only)));
            validFieldList.add(createTeamField(map));
            Map<String, Object> map2 = Maps.newHashMap();
            map2.put(IFieldDescribe.API_NAME, String.join("_", TEAM_MEMBER, role.getRoleType(), TeamMember.Permission.READANDWRITE.getValue()));
            map2.put(IFieldDescribe.LABEL, String.join("-", role.getRoleName(), I18NExt.text(I18NKey.constant_read_write)));
            validFieldList.add(createTeamField(map2));
        });
        return validFieldList;
    }

    /**
     * 获取导入模板字段
     */
    private List<IFieldDescribe> getFields(User user, IObjectDescribe objectDescribe, String recordType) {
        if (StringUtils.isNotBlank(recordType)) {
            return getFieldsByRecordType(user, objectDescribe, recordType);
        }
        return ObjectDescribeExt.of(objectDescribe).getFieldDescribesSilently();
    }

    /**
     * 根据业务类型获取导入字段
     */
    private List<IFieldDescribe> getFieldsByRecordType(User user, IObjectDescribe objectDescribe, String recordType) {
        LayoutLogicService.LayoutContext layoutContext = buildLayoutContext(user);
        ILayout layout = layoutLogicService.findObjectLayoutByType(layoutContext, recordType, objectDescribe.getApiName(), LayoutTypes.EDIT);
        LayoutExt layoutExt = LayoutExt.of(layout);
        // 从布局中获取到字段api name， 然后根据api name取对象描述中的字段描述
        List<String> fieldApiNameList;
        if (UdobjGrayConfig.isAllow(IMPORT_TEMPLATE_ORDER_EI, user.getTenantId())) {
            fieldApiNameList = layoutExt.getOrderFieldList();
        } else {
            fieldApiNameList = layoutExt.getFieldList();
        }
        ObjectDescribeExt.of(objectDescribe).addDateTimeRangeEndTime(fieldApiNameList);

        Map<String, IFieldDescribe> fieldMap = objectDescribe.getFieldDescribeMap();
        List<IFieldDescribe> finalFieldList = Lists.newArrayList();
        for (String fieldApiName : fieldApiNameList) {
            if (Objects.nonNull(fieldMap.get(fieldApiName))) {
                finalFieldList.add(fieldMap.get(fieldApiName));
            }
        }
        return finalFieldList;
    }

    /**
     * 有限使用上下文中的appId
     *
     * @param user
     * @return
     */
    private LayoutLogicService.LayoutContext buildLayoutContext(User user) {
        return LayoutLogicService.LayoutContext.of(user, RequestUtil.getAppId());
    }

    public List<IFieldDescribe> generateTeamMemberByType(List<TeamRoleInfo> teamRoleInfos) {
        List<IFieldDescribe> validFieldList = Lists.newArrayList();
        for (TeamRoleInfo teamRoleInfo : teamRoleInfos) {
            Arrays.stream(TeamMember.MemberType.values()).filter(TeamMember.MemberType::isInnerMember).forEach(memberType -> {
                //成员类型-普通成员-只读
                Map<String, Object> readonlyMap = Maps.newHashMap();
                readonlyMap.put(IFieldDescribe.API_NAME, String.join("_", memberType.getValue(), teamRoleInfo.getRoleType(), TeamMember.Permission.READONLY.getValue()));
                readonlyMap.put(IFieldDescribe.LABEL, String.join("-", memberType.getLabel(), teamRoleInfo.getRoleName(), TeamMember.Permission.READONLY.getLabel()));
                validFieldList.add(createTeamField(readonlyMap));
                //成员类型-普通成员-读写
                Map<String, Object> readwriteMap = Maps.newHashMap();
                readwriteMap.put(IFieldDescribe.API_NAME, String.join("_", memberType.getValue(), teamRoleInfo.getRoleType(), TeamMember.Permission.READANDWRITE.getValue()));
                readwriteMap.put(IFieldDescribe.LABEL, String.join("-", memberType.getLabel(), teamRoleInfo.getRoleName(), TeamMember.Permission.READANDWRITE.getLabel()));
                validFieldList.add(createTeamField(readwriteMap));
            });
        }
        return validFieldList;
    }

    private IFieldDescribe createTeamField(Map<String, Object> map) {
        map.put(IFieldDescribe.TYPE, IFieldType.TEXT);
        map.put(IFieldDescribe.IS_REQUIRED, false);
        map.put("importFieldMark", TEAM_MEMBER);
        IFieldDescribe field = FieldDescribeFactory.newInstance(map);
        //extend info 中标记这个临时ID字段
        Map<String, String> extMap = Maps.newHashMap();
        extMap.put("importType", TEAM_MEMBER);
        field.setExtendInfo(extMap);
        return field;
    }

    private Set<String> getReadAndWriteFields(User user, IObjectDescribe objectDescribe) {
        List<IFieldDescribe> fieldDescribes = ObjectDescribeExt.of(objectDescribe).getFieldDescribesSilently();
        if (CollectionUtils.empty(fieldDescribes)) {
            log.warn("fields is empty,apiName:{}", objectDescribe.getApiName());
            return Collections.emptySet();
        }
        Set<String> unReadableFields = functionPrivilegeService.getUnauthorizedFields(user, objectDescribe.getApiName());
        Set<String> unWriteAbleFields = functionPrivilegeService.getReadonlyFields(user, objectDescribe.getApiName());
        Set<String> result = new HashSet<>();

        for (IFieldDescribe field : fieldDescribes) {
            if (isReadAndWriteField(unReadableFields, unWriteAbleFields, field, objectDescribe.getApiName())) {
                result.add(field.getApiName());
            }
        }
        return result;
    }

    private boolean isReadAndWriteField(Set<String> unReadableFields, Set<String> unWriteAbleFields, IFieldDescribe field, String apiName) {
        String fieldApiName = field.getApiName();
        //对价目表/价目表产品做特殊处理,过虑掉多余的字段
        if (Utils.PRICE_BOOK_API_NAME.equals(apiName)) {
            return !"partner_range".equals(fieldApiName)
                    && !"record_type".equals(fieldApiName)
                    && !"is_standard".equals(fieldApiName)
                    && !"extend_obj_data_id".equals(fieldApiName)
                    && !(unReadableFields.contains(fieldApiName))
                    && !(unWriteAbleFields.contains(fieldApiName));
        } else if (Utils.PRICE_BOOK_PRODUCT_API_NAME.equals(apiName)) {
            return !"extend_obj_data_id".equals(fieldApiName)
                    && !"record_type".equals(fieldApiName)
                    && !(unReadableFields.contains(fieldApiName))
                    && !(unWriteAbleFields.contains(fieldApiName));
        } else {
            return !(unReadableFields.contains(fieldApiName)) && !(unWriteAbleFields.contains(fieldApiName));
        }
    }

    @Override
    public boolean checkIsTitleForUpdateImport(IFieldDescribe fieldDescribe, IObjectDescribe describe) {
        //图片字段，只支持水印的字段不导出
        if (IFieldType.IMAGE.equals(fieldDescribe.getType()) && ((ImageFieldDescribe) fieldDescribe).getIsWaterMark()) {
            return false;
        }
        if (checkGroupField(fieldDescribe, describe)) {
            return false;
        }
        // 配置文件中不支持更新导入的字段
        return !AppFrameworkConfig.updateImportUnSupportField(fieldDescribe);
    }

    /**
     * 判断字段是否在fieldNames，
     *
     * @param fieldDescribe
     * @param describe
     * @param fieldNames
     * @return
     */
    private boolean checkIsTitleForUpdateImportIncludeFieldNames(IFieldDescribe fieldDescribe, IObjectDescribe describe,
                                                                 List<String> fieldNames) {

        if (checkIsTitleForUpdateImport(fieldDescribe, describe)) {
            return true;
        }

        if (fieldNames.contains(fieldDescribe.getApiName())) {
            fieldDescribe.setRequired(true);
            return true;
        }
        return false;
    }

    private boolean checkGroupField(IFieldDescribe fieldDescribe, IObjectDescribe describe) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        if (Objects.equals(fieldDescribe.getUsedIn(), IFieldDescribe.USED_IN_COMPONENT)) {
            //各种组件内的字段各自判断
            PaymentFieldDescribe paymentField = describeExt.getPaymentFieldDescribe().orElse(null);
            SignInFieldDescribe signInField = describeExt.getSignInFieldDescribe().orElse(null);
            List<WhatList> whatListFields = describeExt.getWhatListFields();

            if (!Objects.isNull(paymentField) && describeExt.isFieldInGroup(fieldDescribe, paymentField, describe)) {
                if (!Objects.equals(paymentField.getPayAmountFieldApiName(), fieldDescribe.getApiName())) {
                    return true;
                } else if (Objects.equals(Boolean.TRUE, paymentField.getAmountIsReadonly())) {
                    return true;
                }
            } else if (!Objects.isNull(signInField) && describeExt.isFieldInGroup(fieldDescribe, signInField, describe)) {
                return true;
            }

            if (CollectionUtils.notEmpty(whatListFields)) {
                WhatList whatList = whatListFields.get(0);
                if (Objects.nonNull(whatList) &&
                        (Objects.equals(whatList.getApiNameFieldApiName(), fieldDescribe.getApiName()) ||
                                Objects.equals(whatList.getIdFieldApiName(), fieldDescribe.getApiName()))) {
                    return true;
                }
            }
        }
        return false;
    }

    @Override
    public boolean checkIsTitle(IFieldDescribe fieldDescribe, IObjectDescribe describe) {
        //图片字段，只支持水印的字段不导出
        if (IFieldType.IMAGE.equals(fieldDescribe.getType()) && ((ImageFieldDescribe) fieldDescribe).getIsWaterMark()) {
            return false;
        }

        if (checkGroupField(fieldDescribe, describe)) {
            return false;
        }
        // 灰度企业，新建导入支持外部负责人
        if (OUT_OWNER.equals(fieldDescribe.getApiName())) {
            return true;
        }
        // 配置文件中不支持新建导入的字段
        return !AppFrameworkConfig.insertImportUnSupportField(fieldDescribe);
    }

    @Override
    public String checkUnique(IObjectData data, IObjectDescribe describe, boolean isUpdate) {
        try {
            CheckerResult result = objectValidator.checkUnique(data, describe, isUpdate);
            return result.getFailMessage();
        } catch (MetadataServiceException e) {
            log.warn("check unique error, data:{}", data, e);
        }
        return null;
    }

    @Override
    public boolean isUniqueCheck(IObjectData data, IObjectDescribe describe, boolean isUpdate) {
        try {
            return objectValidator.isUniqueCheck(data, describe, isUpdate);
        } catch (MetadataServiceException e) {
            log.warn("isUniqueCheck error,data:{}", data, e);
            return false;
        }
    }

    @Override
    public List<IObjectData> generateAutoNumber(IObjectDescribe describe, List<IObjectData> objectDataList) {
        try {
            return autoNumberLogicService.calculateAutoNumberValue(describe, objectDataList);
        } catch (MetadataServiceException e) {
            log.warn("generateAutoNumber error,describe:{}", describe, e);
            return Lists.newArrayList();
        }
    }


    @Override
    public Map<String, String> filterDataListByAutoNumberUniqueCheck(List<IObjectData> iObjectDataList, IObjectDescribe objectDescribe) {
        List<ObjectData> objectDataList = iObjectDataList.stream().map(iObjectData -> (ObjectData) iObjectData).collect(Collectors.toList());
        try {
            return objectValidator.filterDataListByAutoNumberUniqueCheck(objectDataList, objectDescribe);
        } catch (MetadataServiceException e) {
            log.warn("generateAutoNumber error,dataList:{}", objectDataList, e);
            return Maps.newHashMap();
        }
    }

    @Override
    public ImportBatchFieldDataConverter getBatchFieldDataConverter(String fieldType) {
        return importBatchFieldDataConverterManager.getBatchFieldDataConverter(fieldType);
    }

    @Override
    public boolean saveUnionInsertImportMark(User user, String jobId, String describeApiName, Map<String, String> markIdMap) {
        if (CollectionUtils.empty(markIdMap)) {
            return true;
        }

        List<ImportMarkInfo> importInfos = Lists.newArrayList();
        markIdMap.forEach((markLabel, value) -> {
            ImportMarkInfo importInfo = ImportMarkInfo.builder()
                    .markLabel(markLabel)
                    .dataId(value)
                    .triggerWorkFlowAfterImport(UdobjGrayConfig.isAllow(UdobjGrayConfigKey.UNION_INSERT_IMPORT_COMPLETE_TRIGGER_WORK_FLOW_GRAY, user.getTenantId()))
                    .triggerApprovalAfterImport(true)
                    .build();
            importInfos.add(importInfo);
        });
        return saveUnionInsertImportMark(user, jobId, describeApiName, importInfos);
    }

    @Override
    public boolean saveUnionInsertImportMark(User user, String jobId, String describeApiName, List<ImportMarkInfo> importMarkInfos) {
        if (CollectionUtils.empty(importMarkInfos)) {
            return true;
        }
        List<ImportInfo> importInfos = importMarkInfos.stream()
                .map(importMarkInfo -> ImportInfo.builder()
                        .jobId(jobId)
                        .markLabel(importMarkInfo.getMarkLabel())
                        .dataId(importMarkInfo.getDataId())
                        .triggerWorkFlowAfterImport(importMarkInfo.getTriggerWorkFlowAfterImport())
                        .triggerApprovalAfterImport(importMarkInfo.getTriggerApprovalAfterImport())
                        .build())
                .collect(Collectors.toList());
        importMongoDao.bulkCreate(user, importInfos);
        return true;
    }

    @Override
    public Map<String, String> findUnionInsertImportMark(User user, String jobId, String objectCode, Collection<String> markLabel) {
        if (CollectionUtils.empty(markLabel)) {
            return Maps.newHashMap();
        }
        List<ImportInfo> importInfos = importMongoDao.find(user, jobId, markLabel);
        if (CollectionUtils.notEmpty(importInfos)) {
            return importInfos.stream()
                    .collect(Collectors.toMap(ImportInfo::getMarkLabel, ImportInfo::getDataId, (x, y) -> x));
        }
        // 兼容逻辑，mongo没有查询到数据，走redis查询
        // 为了防止导入从对象过程中主对象redis过期，先检查一下主对象过期redis有效事件
        // 如果小于一定阈值则更新时间
        if (redisDao.getExpireTime(jobId) <= EXPIRE_TIME_THRESHOLD) {
            log.info("Union import tenantId:{} jobId:{} redis expire time updated.", user.getTenantId(), jobId);
            //如果该key的过期时间小于某个值，则重新设置过期时间
            redisDao.expire(jobId, EXPIRE_TIME_WHEN_ACCESS);
        }
        Map<String, String> result = Maps.newHashMap();
        String[] markArray = markLabel.toArray(new String[]{});
        int i = 0;
        for (String value : redisDao.get(jobId, markArray)) {
            result.put(markArray[i++], value);
        }
        return result;
    }

    private List<IFieldDescribe> getUpdateImportTemplateFieldIncludeFieldNames(User user,
                                                                               IObjectDescribe describe,
                                                                               List<String> fieldNames,
                                                                               String recordType) {
        IObjectDescribe objectDescribe = ObjectDescribeExt.of(describe).copyOnWrite();
        processDateTimeRangeFields(objectDescribe);
        Set<String> canReadAndWriteFieldList = getReadAndWriteFields(user, objectDescribe);
        List<IFieldDescribe> fieldDescribes = getFields(user, objectDescribe, recordType);
        List<IFieldDescribe> validFieldList = Lists.newArrayList();
        for (IFieldDescribe field : fieldDescribes) {
            // 下游不支持人员、部门、负责人字段
            // 下游人员导入，不支持部门、人员、负责人、合作伙伴字段
            if (isExcludedFieldForOuterUserUpdateImport(user, field)) {
                continue;
            }

            if (canReadAndWriteFieldList.contains(field.getApiName())
                    && checkIsTitleForUpdateImportIncludeFieldNames(field, objectDescribe, fieldNames)
                    && field.isActive()
                    && !"extend_obj_data_id".equals(field.getApiName())
                    && !Objects.equals(field.isAbstract(), Boolean.TRUE)) {
                validFieldList.add(field);
            }
            if (supportOwnerField(user, objectDescribe, field)) {
                validFieldList.add(field);
            }
        }
        //非从对象增加相关团队普通成员虚拟字段
        boolean isDetail = objectDescribe.getFieldDescribes().stream()
                .anyMatch(f -> IFieldType.MASTER_DETAIL.equals(f.getType()));
        // 下游不支持导入相关团队
        if (!isDetail && !user.isOutUser()) {
            OptionalFeaturesSwitchDTO optionalFeaturesSwitch = optionalFeaturesService.findOptionalFeaturesSwitch(user.getTenantId(), objectDescribe);
            if (optionalFeaturesSwitch.getIsRelatedTeamEnabled()) {
                //增加 普通成员
                addRelevantTeam(user, validFieldList, objectDescribe);
            }
        }
        return validFieldList;
    }

    private boolean supportOwnerField(User user, IObjectDescribe objectDescribe, IFieldDescribe field) {
        if (OWNER.equals(field.getApiName())) {
            return ObjectImportConfig.isSupportUpdateImportOwner(user, objectDescribe);
        }
        return false;
    }

    public ImportTenantSetting findImportTenantSetting(User user, String describeApiName) {
        String importSettingKey = buildImportSettingKey(describeApiName);
        String tenantConfig = configService.findTenantConfig(user, importSettingKey);
        ImportTenantSetting importTenantSetting = JSON.parseObject(tenantConfig, ImportTenantSetting.class);
        if (Objects.isNull(importTenantSetting)) {
            return null;
        }
        return importTenantSetting;
    }

    private String buildImportSettingKey(String describeApiName) {
        return "importSetting_" + describeApiName;
    }


    public ImportReferenceMapping findImportReferenceMapping(User user, String describeApiName) {
        return findImportReferenceMapping(user, describeApiName, false);
    }

    public ImportReferenceMapping findImportReferenceMapping(User user, String describeApiName, boolean needLabel) {

        boolean allow = UdobjGrayUtil.isObjectAndTenantGray("import_reference_mapping_new", user.getTenantId(), describeApiName);
        if (!allow) {
            return ImportReferenceMapping.builder().build();
        }

        String referenceConfig = configService.findTenantConfig(user, ImportReferenceMapping.buildImportReferenceMappingKey(describeApiName));

        if (StringUtils.isBlank(referenceConfig)) {
            return new ImportReferenceMapping();
        }

        ImportReferenceMapping importReferenceMapping;
        try {
            importReferenceMapping = JSON.parseObject(referenceConfig, ImportReferenceMapping.class);
        } catch (Exception e) {
            log.error("findImportReferenceMapping parse import reference mapping error,user:{},describeApiName:{}", user, describeApiName, e);
            return new ImportReferenceMapping();
        }

        if (importReferenceMapping == null) {
            return new ImportReferenceMapping();
        }

        if (BooleanUtils.isTrue(importReferenceMapping.getReferenceFieldMappingSwitch())) {
            populateReferenceFieldMappings(user, describeApiName, importReferenceMapping, needLabel);
        }

        return importReferenceMapping;
    }

    private void populateReferenceFieldMappings(User user, String describeApiName, ImportReferenceMapping importReferenceMapping, boolean needLabel) {
        IObjectDescribe objectDescribe = describeLogicService.findObjectWithoutCopy(user.getTenantId(), describeApiName);
        List<IFieldDescribe> refFields = Lists.newArrayList();

        if (ObjectDescribeExt.of(objectDescribe).isSlaveObject()) {
            refFields.addAll(ObjectDescribeExt.of(objectDescribe).getAllActiveRefFieldDescribesExcludeWhatField());
        } else {
            refFields.addAll(ObjectDescribeExt.of(objectDescribe).getActiveLookupFieldDescribes());
            List<IObjectDescribe> detailDescribes = describeLogicService.findDetailDescribesWithoutCopy(user.getTenantId(), describeApiName);
            if (CollectionUtils.notEmpty(detailDescribes)) {
                List<IFieldDescribe> detailRefFields = detailDescribes.stream()
                        .flatMap(detailDescribe -> ObjectDescribeExt.of(detailDescribe).getActiveLookupFieldDescribes().stream())
                        .collect(Collectors.toList());
                refFields.addAll(detailRefFields);
            }
        }


        updateReferenceFieldMappings(user, importReferenceMapping, refFields, needLabel);
    }

    private void updateReferenceFieldMappings(User user, ImportReferenceMapping importReferenceMapping, List<IFieldDescribe> refFields, boolean needLabel) {
        List<ImportReferenceMapping.ReferenceFieldMapping> referenceFieldMappingList = importReferenceMapping.getReferenceFieldMapping();

        Set<String> allObjectApiNames = new HashSet<>();

        for (ImportReferenceMapping.ReferenceFieldMapping mapping : referenceFieldMappingList) {
            if (mapping.getTargetObjectApiName() != null) {
                allObjectApiNames.add(mapping.getTargetObjectApiName());
            }
            if (mapping.getObjectApiName() != null) {
                allObjectApiNames.add(mapping.getObjectApiName());
            }
        }

        for (IFieldDescribe refField : refFields) {
            String currentObjectApiName = refField.getDescribeApiName();
            allObjectApiNames.add(currentObjectApiName);
            allObjectApiNames.addAll(getTargetApiNames(refField));
        }

        Map<String, IObjectDescribe> describeMap = describeLogicService.findObjectsWithoutCopy(user.getTenantId(), allObjectApiNames);

        for (IFieldDescribe refField : refFields) {
            if (!containRefField(referenceFieldMappingList, refField)) {
                addNewReferenceFieldMapping(refField, describeMap, referenceFieldMappingList);
            }
        }

        updateFieldMappingLabels(referenceFieldMappingList, describeMap, needLabel);
    }

    private void addNewReferenceFieldMapping(IFieldDescribe refField, Map<String, IObjectDescribe> describeMap, List<ImportReferenceMapping.ReferenceFieldMapping> referenceFieldMappingList) {
        IObjectDescribe targetDescribe = getTargetDescribe(refField, describeMap);
        if (targetDescribe != null) {
            ImportReferenceMapping.ReferenceFieldMapping fieldMapping = new ImportReferenceMapping.ReferenceFieldMapping();
            fieldMapping.setObjectApiName(refField.getDescribeApiName());
            fieldMapping.setObjectReferenceFieldApiName(refField.getApiName());
            fieldMapping.setTargetObjectApiName(targetDescribe.getApiName());

            Optional<IFieldDescribe> fieldDescribeSilently = ObjectDescribeExt.of(targetDescribe).getActiveFieldDescribeSilently(NAME);
            fieldMapping.setSpecifiedUniqueFieldApiName(fieldDescribeSilently.map(fieldDescribe -> fieldDescribe.isUnique() ? NAME : ID).orElse(ID));
            fieldMapping.setHasConfig(false);

            referenceFieldMappingList.add(fieldMapping);
        }
    }

    private IObjectDescribe getTargetDescribe(IFieldDescribe refField, Map<String, IObjectDescribe> describeMap) {
        IObjectDescribe targetDescribe = null;

        if (FieldDescribeExt.of(refField).isLookupField()) {
            targetDescribe = describeMap.get(ObjectReferenceWrapper.of(refField).getTargetApiName());
        } else if (FieldDescribeExt.of(refField).isMasterDetailField()) {
            targetDescribe = describeMap.get(((MasterDetail) refField).getTargetApiName());
        }

        // 检查描述是否被禁用
        if (targetDescribe != null && !targetDescribe.isActive()) {
            return null;
        }

        return targetDescribe;
    }

    private void updateFieldMappingLabels(List<ImportReferenceMapping.ReferenceFieldMapping> referenceFieldMappingList,
                                          Map<String, IObjectDescribe> describeMap,
                                          boolean needLabel) {
        Iterator<ImportReferenceMapping.ReferenceFieldMapping> iterator = referenceFieldMappingList.iterator();

        while (iterator.hasNext()) {
            ImportReferenceMapping.ReferenceFieldMapping fieldMapping = iterator.next();

            // 如果源对象为空，移除该映射
            if (fieldMapping.getObjectApiName() == null) {
                iterator.remove();
                continue;
            }

            // 获取源对象描述
            IObjectDescribe sourceDescribe = describeMap.get(fieldMapping.getObjectApiName());

            // 如果源对象不存在或已禁用，移除该映射
            if (sourceDescribe == null || !sourceDescribe.isActive()) {
                iterator.remove();
                continue;
            }

            // 验证源对象字段
            Optional<IFieldDescribe> sourceField = ObjectDescribeExt.of(sourceDescribe)
                    .getActiveFieldDescribeSilently(fieldMapping.getObjectReferenceFieldApiName());
            if (!sourceField.isPresent() &&
                    !(Objects.equals(fieldMapping.getObjectReferenceFieldApiName(), fieldMapping.getSpecifiedUniqueFieldApiName()) &&
                            Objects.equals(fieldMapping.getObjectApiName(), fieldMapping.getTargetObjectApiName()))) {
                // 如果源对象字段不存在且不是当前的对象字段，则移除该映射
                iterator.remove();
                continue;
            }

            // 获取目标对象描述（可能为空）
            IObjectDescribe targetDescribe = fieldMapping.getTargetObjectApiName() != null ?
                    describeMap.get(fieldMapping.getTargetObjectApiName()) : null;

            // 只有在需要更新标签时才执行标签更新逻辑
            if (needLabel) {
                // 更新源对象标签
                fieldMapping.setObjectLabel(sourceDescribe.getDisplayName());
                fieldMapping.setObjectReferenceFieldLabel(sourceField.map(IFieldDescribe::getLabel).orElse(""));

                // 如果目标对象存在且启用，更新目标对象相关标签
                if (targetDescribe != null && targetDescribe.isActive()) {
                    fieldMapping.setTargetObjectLabel(targetDescribe.getDisplayName());
                    Optional<IFieldDescribe> targetField = ObjectDescribeExt.of(targetDescribe)
                            .getActiveFieldDescribeSilently(fieldMapping.getSpecifiedUniqueFieldApiName());
                    fieldMapping.setSpecifiedUniqueFieldLabel(targetField.map(IFieldDescribe::getLabel).orElse(""));
                } else {
                    fieldMapping.setTargetObjectLabel("");
                    fieldMapping.setSpecifiedUniqueFieldLabel("");
                }
            }
        }
    }

    public boolean containRefField(List<ImportReferenceMapping.ReferenceFieldMapping> referenceFieldMappingList, IFieldDescribe fieldDescribe) {
        return referenceFieldMappingList.stream().anyMatch(x ->
                Objects.equals(x.getObjectApiName(), fieldDescribe.getDescribeApiName())
                        && Objects.equals(x.getObjectReferenceFieldApiName(), fieldDescribe.getApiName())
        );
    }

    private List<String> getTargetApiNames(IFieldDescribe refField) {
        if (FieldDescribeExt.of(refField).isLookupField()) {
            return Collections.singletonList(ObjectReferenceWrapper.of(refField).getTargetApiName());
        } else if (FieldDescribeExt.of(refField).isMasterDetailField()) {
            return Collections.singletonList(((MasterDetail) refField).getTargetApiName());
        }
        return Collections.emptyList();
    }
}
