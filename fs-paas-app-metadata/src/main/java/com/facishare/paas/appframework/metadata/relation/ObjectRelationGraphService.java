package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.appframework.metadata.relation.ObjectRelationGraphBuilder.GraphLayer;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Nonnull;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;


/**
 * create by zhaoju on 2019/02/21
 */
@Service
public class ObjectRelationGraphService {
    @Autowired
    private DescribeLogicService describeLogicService;

    /**
     * 构建以单个对象为中心的简单对象依赖网
     * <p>
     * 节点是每个对象，边为对应的查找关联/主从字段
     *
     * @param objectDescribe
     * @return
     */
    public ObjectRelationGraph buildSimpleObjectRelationNetwork(@Nonnull IObjectDescribe objectDescribe) {
        String tenantId = objectDescribe.getTenantId();
        String objectApiName = objectDescribe.getApiName();
        Set<String> objectApiNameSet = Sets.newHashSet(objectApiName);
        // 关联对象和主对象
        List<IObjectDescribe> associationDescribes = describeLogicService.findAssociationDescribesWithoutCopyIfGray(tenantId, objectDescribe);
        associationDescribes.removeIf(x -> !objectApiNameSet.add(x.getApiName()));
        // 相关对象和从对象
        List<IObjectDescribe> relatedDescribes = describeLogicService.findRelatedDescribesWithoutCopyIfGray(tenantId, objectApiName);
        relatedDescribes.removeIf(x -> !objectApiNameSet.add(x.getApiName()));

        List<GraphLayer> graphLayers = Lists.newArrayList();
        graphLayers.add(GraphLayer.of(Lists.newArrayList(objectDescribe)));
        graphLayers.add(GraphLayer.of(associationDescribes));
        graphLayers.add(GraphLayer.of(relatedDescribes));
        return ObjectRelationGraphBuilder.builder()
                .graphLayers(graphLayers)
                .allowsSelfLoops(true)
                .build()
                .getGraph();
    }

    /**
     * 构建五角关系个对象之间的依赖关系图
     * <p>
     * C <----md/lookup---- D --lookup---
     * ^                                |
     * |                                v
     * lookup                           E
     * |                                ^
     * |                                |
     * A <------ md ------ B --lookup---
     *
     * @param detailDescribe 出入B对象的描述
     * @return
     */
    @SuppressWarnings("unchecked")
    public ObjectRelationGraph buildObjectRelationNetwork(@Nonnull IObjectDescribe detailDescribe, IObjectDescribe targetDescribe) {
        ObjectDescribeExt detailDescribeExt = ObjectDescribeExt.of(detailDescribe);
        Optional<String> masterAPIName = detailDescribeExt.getMasterAPIName();
        // 传入的 B对象 需要为从对象
        if (!masterAPIName.isPresent()) {
            return ObjectRelationGraphBuilder.builder()
                    .graphLayers(Collections.EMPTY_LIST)
                    .build()
                    .getGraph(null);
        }

        // 查询主对象描述(A对象)
//        List<IObjectDescribe> describeList = findAssociationDescribes(detailDescribe);
        IObjectDescribe masterDescribe = findMasterDescribe(detailDescribe.getTenantId(), masterAPIName.get());
        // 查询主对象的lookup对象(C对象)
//        List<IObjectDescribe> associationDescribes = findAssociationDescribes(
//                findMasterDescribe(detailDescribe.getTenantId(), masterAPIName.get(), describeList));
        List<IObjectDescribe> associationDescribes = findAssociationDescribes(masterDescribe);

        return ObjectRelationGraphBuilder.builder()
                .graphLayers(buildGraphLayers(detailDescribe, masterDescribe, associationDescribes))
                .build()
                .getGraph(targetDescribe);
    }

    private List<GraphLayer> buildGraphLayers(@Nonnull IObjectDescribe detailDescribe,
                                              IObjectDescribe masterDescribe,
                                              List<IObjectDescribe> associationDescribes) {
        List<GraphLayer> graphLayers = Lists.newArrayList();
        graphLayers.add(GraphLayer.of(Lists.newArrayList(detailDescribe)));
        graphLayers.add(GraphLayer.of(Lists.newArrayList(masterDescribe)));
        graphLayers.add(GraphLayer.of(associationDescribes));
        graphLayers.add(GraphLayer.of(findRelatedDescribes(detailDescribe.getTenantId(), associationDescribes)));
        return graphLayers;
    }

    private List<IObjectDescribe> findAssociationDescribes(@Nonnull IObjectDescribe detailDescribe) {
        return describeLogicService.findAssociationDescribesWithoutCopyIfGray(detailDescribe.getTenantId(), detailDescribe);
    }

    private IObjectDescribe findMasterDescribe(String tenantId, String masterAPIName) {
        return describeLogicService.findObjectWithoutCopyIfGray(tenantId, masterAPIName);
    }

    private List<IObjectDescribe> findRelatedDescribes(String tenantId,
                                                       List<IObjectDescribe> associationDescribes) {
        return associationDescribes.stream()
                .flatMap(describe -> describeLogicService.findRelatedDescribesWithoutCopyIfGray(tenantId, describe.getApiName()).stream())
                .collect(Collectors.toList());
    }
}
