package com.facishare.paas.appframework.metadata.config.handler;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.metadata.config.util.TenantConfigUtil;
import com.google.common.collect.Maps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class QuoteLinesObjConfigHandler implements ConfigHandler {
    @Autowired
    ConfigService configService;

    @Override
    public String getObjectAPIName() {
        return "QuoteLinesObj";
    }

    @Override
    public void handle(String tenantId, Map<String, Object> objectConfig, Map<String, Map<String, Object>> fieldConfig) {
        boolean isPriceBookEnabled = TenantConfigUtil.isPriceBookEnabled(configService, tenantId);
        if (isPriceBookEnabled) {
            if (fieldConfig.containsKey("price_book_product_id")) {
                Map<String, Object> attrMap = Maps.newHashMap();
                attrMap.put("default_value", 0);
                attrMap.put("wheres", 1);
                attrMap.put("is_required", 0);
                attrMap.put("help_text", 1);
                attrMap.put("label", 1);
                attrMap.put("target_related_list_label", 1);
                fieldConfig.get("price_book_product_id").put("display", 1);
                fieldConfig.get("price_book_product_id").put("enable", 0);
                fieldConfig.get("price_book_product_id").put("edit", 1);
                fieldConfig.get("price_book_product_id").put("attrs", attrMap);
            }
        }
        boolean isStratifiedPriceEnabled = TenantConfigUtil.isStratifiedPriceEnabled(configService, tenantId);
        if (isStratifiedPriceEnabled) {
            if (fieldConfig.containsKey("price")) {
                Map<String, Object> attrMap = (Map<String, Object>) fieldConfig.get("price").get("attrs");
                if (attrMap != null) {
                    attrMap.put("default_value", 0);
                }
                fieldConfig.get("price").put("attrs", attrMap);
            }

            if (fieldConfig.containsKey("sales_price")) {
                Map<String, Object> attrMap = (Map<String, Object>) fieldConfig.get("sales_price").get("attrs");
                if (attrMap != null) {
                    attrMap.put("default_value", 0);
                }
                fieldConfig.get("sales_price").put("attrs", attrMap);
            }

            if (fieldConfig.containsKey("discount")) {
                Map<String, Object> attrMap = (Map<String, Object>) fieldConfig.get("discount").get("attrs");
                if (attrMap != null) {
                    attrMap.put("default_value", 0);
                }
                fieldConfig.get("discount").put("attrs", attrMap);
            }
        }
    }
}
