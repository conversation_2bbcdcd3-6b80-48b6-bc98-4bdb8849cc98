package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;

/**
 * create by z<PERSON><PERSON> on 2019/07/27
 */
@Component
public class DoNothingImportFieldDataConverter implements ImportFieldDataConverter {
    @Override
    public List<String> getSupportedFieldTypes() {
        return Collections.emptyList();
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, User user) {
        return null;
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, User user) {
        return null;
    }
}
