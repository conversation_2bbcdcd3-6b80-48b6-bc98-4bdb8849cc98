package com.facishare.paas.appframework.metadata.button;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.ui.layout.IButton;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * create by <PERSON><PERSON><PERSON> on 2020/03/07
 */
@Component
public class DefaultLayoutDesignerButtonProvider implements LayoutDesignerButtonProvider {
    @Override
    public String getApiName() {
        return null;
    }

    @Override
    public List<IButton> getButtons(List<IButton> buttons, User user) {
        return buttons;
    }
}
