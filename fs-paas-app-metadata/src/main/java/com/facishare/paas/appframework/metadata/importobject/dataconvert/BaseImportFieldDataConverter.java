package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ActionContextExt;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.IValidateResult;
import com.facishare.paas.metadata.validator.data.AbstractFieldValidator;
import com.facishare.paas.metadata.validator.data.FieldValidatorFactory;
import lombok.extern.slf4j.Slf4j;

import java.util.function.Function;

/**
 * create by z<PERSON><PERSON> on 2019/07/26
 */
@Slf4j
public abstract class BaseImportFieldDataConverter implements ImportFieldDataConverter {

    protected String getStringValue(IObjectData data, String apiName) {
        return ObjectDataExt.of(data).getStringValueInImport(apiName);
    }

    protected AbstractFieldValidator getFieldValidator(IFieldDescribe field) {
        return FieldValidatorFactory.getFieldValidator(field.getType());
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, User user) {
        return null;
    }

    @Override
    public ConvertResult convertFieldData(IObjectData objectData, IObjectDescribe objectDescribe, IFieldDescribe fieldDescribe, User user) {
        return convertFieldData(objectData, fieldDescribe, user);
    }

    protected ConvertResult doValidate(IObjectData objectData, IFieldDescribe fieldDescribe, User user, String valueStr, Function<IFieldDescribe, String> function) {
        try {
            objectData.set(fieldDescribe.getApiName(), valueStr);
            IActionContext context = ActionContextExt.of(user).getContext();
            context.setDataSource("import");
            IValidateResult validateResult = getFieldValidator(fieldDescribe).doValidate(fieldDescribe, objectData, context);
            if (validateResult.isSuccess()) {
                return ConvertResult.buildSuccess(valueStr);
            }
        } catch (MetadataServiceException e) {
            log.error("convertFieldData doValidate error, tenantId:{}, userId:{},fieldApiName:{}, data:{}",
                    user.getTenantId(), user.getUserId(), fieldDescribe.getApiName(), objectData, e);
            return ConvertResult.buildError(e.getMessage());
        }

        return ConvertResult.buildError(function.apply(fieldDescribe));
    }

}
