package com.facishare.paas.appframework.metadata.util;

import com.google.common.base.Strings;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;

/**
 * 高性能图片信息解析工具类
 * 通过直接解析图片文件头来获取图片尺寸信息，避免加载整个图片到内存
 * 
 * 移植自 com.facishare.stone.util.image.base.ImageInfoHelper
 * 优化了性能，支持 JPEG, PNG, GIF, BMP, WebP, TIFF, HEIF 等格式
 */
@UtilityClass
@Slf4j
public class FastImageInfoHelper {

 
    /**
     * 获取图片信息
     * 
     * @param imageData
     * @return
     */

    public ImageInfo getImageInfo(byte[] imageData) {
        if (imageData == null || imageData.length < 4) {
            return null;
        }
        try (InputStream inputStream = new ByteArrayInputStream(imageData)) {
            return processStream(inputStream);
        } catch (Exception e) {
            log.debug("Failed to get image info: {}", e.getMessage());
            return null;
        }
    }

    /**
     * 图片信息内部类
     */
    @Builder
    @Data
    public static class ImageInfo {
        int width;
        int height;
        String extensionName;
        String mimeType;
        boolean alpha ;
        boolean isAnimWebp;
        boolean image;
    }

    /**
     * 解析图片流获取图片信息
     * 
     * @param inputStream 图片输入流
     * @return 图片信息
     * @throws IOException IO异常
     */
    public ImageInfo processStream(InputStream inputStream) throws IOException {
        int c1 = inputStream.read();
        int c2 = inputStream.read();
        int c3 = inputStream.read();
        String mimeType = "";
        int width = -1;
        int height = -1;
        boolean alpha = false;
        boolean isAnimWebp = false;
        String ext = "";
        if (c1 == 'G' && c2 == 'I' && c3 == 'F') { // GIF
            inputStream.skip(3);
            width = readInt(inputStream, 2, false);
            height = readInt(inputStream, 2, false);
            mimeType = "image/gif";
            ext = "gif";
        } else if (c1 == 0xFF && c2 == 0xD8) { // JPG
            while (c3 == 255) {
                int marker = inputStream.read();
                int len = readInt(inputStream, 2, true);
                if (marker == 192 || marker == 193 || marker == 194) {
                    inputStream.skip(1);
                    height = readInt(inputStream, 2, true);
                    width = readInt(inputStream, 2, true);
                    mimeType = "image/jpeg";
                    ext = "jpg";
                    break;
                }
                inputStream.skip(len - 2);
                c3 = inputStream.read();
            }
        } else if (c1 == 137 && c2 == 80 && c3 == 78) { // PNG
            inputStream.skip(13);
            width = readInt(inputStream, 4, true);
            height = readInt(inputStream, 4, true);
            inputStream.skip(1); // bit depth
            int colorType = inputStream.read();
            alpha = colorType > 4;
            mimeType = "image/png";
            ext = "jpg";
        } else if (c1 == 66 && c2 == 77) { // BMP
            inputStream.skip(15);
            width = readInt(inputStream, 2, false);
            inputStream.skip(2);
            height = readInt(inputStream, 2, false);
            mimeType = "image/bmp";
            ext = "bmp";
        } else if (c1 == 'R' && c2 == 'I' && c3 == 'F') {
            int c4 = inputStream.read();
            inputStream.skip(4);
            int c9 = inputStream.read();
            int c10 = inputStream.read();
            int c11 = inputStream.read();
            int c12 = inputStream.read();
            int c13 = inputStream.read();
            int c14 = inputStream.read();
            int c15 = inputStream.read();
            int c16 = inputStream.read();
            inputStream.skip(5);
            int c21 = inputStream.read();
            int c22 = inputStream.read();
            int c23 = inputStream.read();
            int c24 = inputStream.read();
            int c25 = inputStream.read();
            int c26 = inputStream.read();
            int c27 = inputStream.read();
            int c28 = inputStream.read();
            int c29 = inputStream.read();
            int c30 = inputStream.read();
            int c31 = inputStream.read();
            int c32 = inputStream.read();
            int c33 = inputStream.read();
            int c34 = inputStream.read();
            if (c4 == 'F' && c9 == 'W' && c10 == 'E' && c11 == 'B' && c12 == 'P') { //WEBP
                mimeType = "image/webp";
                ext = "webp";
                if (c31 == 'A' && c32 == 'N' && c33 == 'I' && c34 == 'M') {
                    isAnimWebp = true;
                }
                if (c13 == 'V' && c14 == 'P' && c15 == '8' && c16 == 'X') {
                    //vp8x:width=24,25 height:27,28
                    byte[] byteWidth = new byte[2];
                    byteWidth[0] = (byte) c24;
                    byteWidth[1] = (byte) c25;
                    width = readInt(byteWidth) + 1;
                    byte[] byteHeight = new byte[2];
                    byteHeight[0] = (byte) c27;
                    byteHeight[1] = (byte) c28;
                    height = readInt(byteHeight) + 1;
                } else if (c13 == 'V' && c14 == 'P' && c15 == '8' && c16 == 'L') {
                    width = ((c22 & 0x3F) << 8 | c21) + 1;
                    height = ((c24 & 0x0F) << 10 | c23 << 2 | (c22 & 0xC0) >> 6) + 1;
                } else {
                    //vp8 h
                    byte[] byteWidth = new byte[2];
                    byteWidth[0] = (byte) c26;
                    byteWidth[1] = (byte) c27;
                    width = readInt(byteWidth);
                    byte[] byteHeight = new byte[2];
                    byteHeight[0] = (byte) c28;
                    byteHeight[1] = (byte) c29;
                    height = readInt(byteHeight);
                }
            }
        } else {
            int c4 = inputStream.read();
            if ((c1 == 'M' && c2 == 'M' && c3 == 0 && c4 == 42) || (c1 == 'I' && c2 == 'I' && c3 == 42 && c4 == 0)) { //TIFF
                boolean bigEndian = c1 == 'M';
                int ifd = 0;
                int entries;
                ifd = readInt(inputStream, 4, bigEndian);
                inputStream.skip(ifd - 8);
                entries = readInt(inputStream, 2, bigEndian);
                for (int i = 1; i <= entries; i++) {
                    int tag = readInt(inputStream, 2, bigEndian);
                    int fieldType = readInt(inputStream, 2, bigEndian);
                    int valOffset;
                    if ((fieldType == 3 || fieldType == 8)) {
                        valOffset = readInt(inputStream, 2, bigEndian);
                        inputStream.skip(2);
                    } else {
                        valOffset = readInt(inputStream, 4, bigEndian);
                    }
                    if (tag == 256) {
                        width = valOffset;
                    } else if (tag == 257) {
                        height = valOffset;
                    }
                    if (width != -1 && height != -1) {
                        mimeType = "image/tiff";
                        ext = "tiff";
                        break;
                    }
                }
            } else {
                inputStream.skip(4);
                int c8 = inputStream.read();
                int c9 = inputStream.read();
                int c10 = inputStream.read();
                int c11 = inputStream.read();
                log.info("{}:{}", c8, c9 + c10 + c11);
                if (c8 == 'h' && c9 == 'e' && c10 == 'i' && c11 == 'c') {
                    mimeType = "image/heif";
                    ext = "heif";
                }
            }
        }
        ImageInfo imageInfo = ImageInfo.builder().extensionName(ext).mimeType(mimeType).image(!Strings.isNullOrEmpty(mimeType)).build();
        imageInfo.setHeight(height);
        imageInfo.setWidth(width);
        imageInfo.setAlpha(alpha);
        imageInfo.setExtensionName(ext);
        imageInfo.setMimeType(mimeType);
        imageInfo.setAnimWebp(isAnimWebp);
        return imageInfo;
    }

    /**
     * 从输入流中读取整数
     * 
     * @param is 输入流
     * @param noOfBytes 字节数
     * @param bigEndian 是否大端序
     * @return 读取的整数值
     * @throws IOException IO异常
     */
    private int readInt(InputStream is, int noOfBytes, boolean bigEndian) throws IOException {
        int ret = 0;
        int sv = bigEndian ? ((noOfBytes - 1) * 8) : 0;
        int cnt = bigEndian ? -8 : 8;
        for (int i = 0; i < noOfBytes; i++) {
            ret |= is.read() << sv;
            sv += cnt;
        }
        return ret;
    }

    /**
     * 从字节数组中读取整数（小端序）
     * 
     * @param bytes 字节数组
     * @return 读取的整数值
     */
    private int readInt(byte[] bytes) {
        int result = 0;
        for (int i = 0; i < bytes.length && i < 4; i++) {
            result |= (bytes[i] & 0xFF) << (i * 8);
        }
        return result;
    }
} 