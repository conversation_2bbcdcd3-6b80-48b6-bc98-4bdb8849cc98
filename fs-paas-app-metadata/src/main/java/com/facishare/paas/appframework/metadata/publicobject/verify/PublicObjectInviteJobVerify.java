package com.facishare.paas.appframework.metadata.publicobject.verify;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.publicobject.module.InternationalVerifyMessage;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobParamVerifyInfo;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobType;
import com.google.common.collect.ImmutableList;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/26
 */
@Slf4j
@Component
public class PublicObjectInviteJobVerify extends AbstractPublicObjectJobVerify {

    @Override
    protected VerifyResult verifyByJobParam(User user, PublicObjectJobParamVerifyInfo jobParam) {
        VerifyResult verifyResult = VerifyResult.buildEmpty();
        // 企业参数超过一个企业
        PublicObjectJobParamVerifyInfo.EnterpriseHelper enterprises = jobParam.getEnterprises();
        if (CollectionUtils.notEmpty(enterprises.getOuterTenantGroupIds())) {
            InternationalVerifyMessage verifyMessage = InternationalVerifyMessage.of(
                    I18NKey.PARAM_ERROR,
                    I18NExt.text(I18NKey.PARAM_ERROR));
            verifyResult.append(verifyMessage);
            return verifyResult;
        }
        List<String> downstreamTenantIds = enterprises.getDownstreamTenantIds();
        if (CollectionUtils.empty(downstreamTenantIds) || downstreamTenantIds.size() > 1) {
            InternationalVerifyMessage verifyMessage = InternationalVerifyMessage.of(
                    I18NKey.PARAM_ERROR,
                    I18NExt.text(I18NKey.PARAM_ERROR));
            verifyResult.append(verifyMessage);
            return verifyResult;
        }
        return verifyResult;
    }

    @Override
    public List<PublicObjectJobType> getSupportedJobTypes() {
        return ImmutableList.of(PublicObjectJobType.INVITATION_JOB);
    }
}
