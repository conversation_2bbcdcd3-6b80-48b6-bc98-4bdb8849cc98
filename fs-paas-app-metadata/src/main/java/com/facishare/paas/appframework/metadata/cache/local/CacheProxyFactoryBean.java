package com.facishare.paas.appframework.metadata.cache.local;

import lombok.Setter;
import org.springframework.beans.factory.FactoryBean;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/5/19
 */
@Setter
public class CacheProxyFactoryBean<T> implements FactoryBean<T> {
    private CacheProxyFactory cacheProxyFactory;
    private T proxyData;
    private String cacheName;
    private Class<T> type;

    @Override
    public T getObject() throws Exception {
        return cacheProxyFactory.createCacheProxy(cacheName, type, proxyData);
    }

    @Override
    public Class<?> getObjectType() {
        return type;
    }

    @Override
    public boolean isSingleton() {
        return true;
    }
}
