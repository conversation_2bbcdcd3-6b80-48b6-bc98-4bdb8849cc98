package com.facishare.paas.appframework.metadata;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.DateTimeUtils;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.RequestContext;
import com.facishare.paas.appframework.core.model.RequestContextManager;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.dto.GlobalVariableResult;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.appframework.metadata.i18nKeyPlatform.Save;
import com.facishare.paas.appframework.metadata.relation.FieldRelationCalculateService;
import com.facishare.paas.appframework.privilege.EnterpriseRelationServiceProxy;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.facishare.paas.metadata.api.describe.IGlobalVariableDescribe;
import com.facishare.paas.metadata.api.service.IGlobalVariableDescribeService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import com.facishare.paas.metadata.impl.DocumentBasedBean;
import com.facishare.paas.metadata.impl.describe.GlobalVariableDescribe;
import com.facishare.paas.timezone.TimeZoneContextHolder;
import com.facishare.uc.api.model.enterprise.arg.GetSimpleEnterpriseDataArg;
import com.facishare.uc.api.model.enterprise.result.GetSimpleEnterpriseDataResult;
import com.facishare.uc.api.service.EnterpriseEditionService;
import com.fxiaoke.i18n.client.api.Localization;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.*;

import static com.facishare.paas.metadata.api.describe.IGlobalVariableDescribe.DEFINE_TYPE_CUSTOM;
import static com.facishare.paas.metadata.api.describe.IGlobalVariableDescribe.DEFINE_TYPE_SYSTEM;
import static com.facishare.paas.timezone.DateUtils.*;

/**
 * Created by linqiuying on 17/10/25.
 */
@Slf4j
@Service
public class GlobalVarServiceImpl implements GlobalVarService {
    public static final String CURRENT_INTERCON_ENTERPRISE__G = "current_intercon_enterprise__g";
    public static final String CURRENT_INTERCON_ENTERPRISE_VALUE = "${current_intercon_enterprise}";
    @Autowired
    private IGlobalVariableDescribeService globalVariableDescribeService;

    @Autowired
    private FieldRelationCalculateService fieldRelationCalculateService;

    @Autowired
    private EnterpriseEditionService enterpriseEditionService;

    @Autowired
    private EnterpriseRelationServiceProxy enterpriseRelationService;

    @Autowired
    private I18nSettingService i18nSettingService;

    @Autowired
    private LicenseService licenseService;

    @Override
    public Map<String, IGlobalVariableDescribe> findGlobalVariables(String tenantId, Collection<String> apiNames) {
        if (CollectionUtils.empty(apiNames)) {
            return Maps.newHashMap();
        }
        try {
            List<IGlobalVariableDescribe> globalVariables = globalVariableDescribeService.findByApiNames(Lists.newArrayList(apiNames), tenantId);
            Map<String, IGlobalVariableDescribe> result = Maps.newHashMap();
            CollectionUtils.nullToEmpty(globalVariables).forEach(x -> result.put(x.getApiName(), x));
            // 追加 current_intercon_enterprise__g 变量
            if (apiNames.contains(CURRENT_INTERCON_ENTERPRISE__G)) {
                result.computeIfAbsent(CURRENT_INTERCON_ENTERPRISE__G,
                        (key) -> buildGlobalVariable(tenantId, CURRENT_INTERCON_ENTERPRISE_VALUE, CURRENT_INTERCON_ENTERPRISE__G,
                                IFieldType.TEXT, I18N.text(I18NKey.CURRENT_DATE_TIME)));
            }
            return result;
        } catch (MetadataServiceException e) {
            log.warn("findGlobalVariables error,tenantId:{},apiNames:{}", tenantId, apiNames, e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public GlobalVariableResult create(IGlobalVariableDescribe globalVariableDescribe) {
        GlobalVariableResult result = new GlobalVariableResult();

        //校验value和type是否相符合
        checkValueIsMatchType(globalVariableDescribe);

        IGlobalVariableDescribe globalVariable = null;
        try {
            globalVariable = globalVariableDescribeService.create(globalVariableDescribe);
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
        if (globalVariable != null) {
            result.setSuccess(true);
        }
        return result;
    }

    @Override
    public GlobalVariableResult update(IGlobalVariableDescribe globalVariableDescribe) {
        GlobalVariableResult result = new GlobalVariableResult();

        //校验是否是系统全局变量

        if (globalVariableDescribe.getDefineType().equals("system")) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.GLOBAL_VARIABLE_UNMODIFY));
        }

        //校验value和type是否相符合
        checkValueIsMatchType(globalVariableDescribe);

        IGlobalVariableDescribe globalVariable = null;
        try {
            IGlobalVariableDescribe dbGlobalVariable = globalVariableDescribeService.findByApiName(globalVariableDescribe.getApiName(), globalVariableDescribe.getTenantId());
            globalVariable = globalVariableDescribeService.update(globalVariableDescribe);
            syncGlobalVariableMultiLanguage(globalVariableDescribe.getTenantId(), globalVariable);
            //检查全局变量是否被计算字段引用
            fieldRelationCalculateService.findRelationFormulaOfGlobalVariable(globalVariable, dbGlobalVariable);
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
        if (globalVariable != null) {
            result.setSuccess(true);
        }
        return result;
    }

    private void syncGlobalVariableMultiLanguage(String tenantId, IGlobalVariableDescribe globalVariable) {
        if (!DEFINE_TYPE_CUSTOM.equals(globalVariable.getDefineType())
                || !StringUtils.equalsAny(globalVariable.getType(), "text", "long_text")
                || Objects.isNull(globalVariable.getValue())) {
            return;
        }
        Map<String, String> keyToNewName = Maps.newHashMap();
        keyToNewName.put(getTextMultiLanguageKey(globalVariable.getApiName()), globalVariable.getValue().toString());
        i18nSettingService.syncTransValue(keyToNewName, I18N.getContext().getLanguage(), tenantId);
    }

    @Override
    public GlobalVariableResult delete(String apiName, String tenantId) {
        GlobalVariableResult result = new GlobalVariableResult();

        IGlobalVariableDescribe globalVariableDescribe = null;
        IGlobalVariableDescribe globalVariable = null;
        try {
            globalVariableDescribe = findGlobalVariableInfo(apiName, false, tenantId);
            if (globalVariableDescribe == null) {
                throw new MetaDataBusinessException(I18N.text(I18NKey.GLOBAL_VARIABLE_NOT_EXIST_OR_DELETED));
            }

            globalVariable = globalVariableDescribeService.delete(globalVariableDescribe);
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }
        if (globalVariable != null) {
            result.setSuccess(true);
        }
        return result;
    }

    @Override
    public IGlobalVariableDescribe findGlobalVariableInfo(String apiName, String tenantId) {
        return findGlobalVariableInfo(apiName, false, tenantId);
    }

    @Override
    public IGlobalVariableDescribe findGlobalVariableInfo(String apiName, Boolean realTimeTrans, String tenantId) {
        return findGlobalVariableInfo(apiName, realTimeTrans, null, tenantId);
    }

    @Override
    public IGlobalVariableDescribe findGlobalVariableInfo(String apiName, Boolean realTimeTrans, String lang, String tenantId) {
        log.debug("Entering findGlobalVariableInfo(tenantId = {}，apiName = {})", tenantId, apiName);
        try {
            IGlobalVariableDescribe variableDescribe = globalVariableDescribeService.findByApiName(apiName, tenantId);
            if (Objects.nonNull(variableDescribe) && "tenantName__g".equals(variableDescribe.getApiName())) {
                variableDescribe.setValue(getTenantName(tenantId));
            }
            fillGlobalVariableTransValue(variableDescribe, realTimeTrans, tenantId, lang);
            return variableDescribe;
        } catch (MetadataServiceException e) {
            log.warn("Error in findGlobalVariableInfo", e);
            throw new MetaDataBusinessException(e);
        }
    }

    @Override
    public GlobalVariableResult findGlobalVariableList(String label, String tenantId) {
        return findGlobalVariableList(label, false, tenantId);
    }

    @Override
    public GlobalVariableResult findGlobalVariableList(String label, Boolean realTimeTrans, String tenantId) {
        log.debug("Entering findGlobalVariableList(tenantId = {})", tenantId);

        GlobalVariableResult result = new GlobalVariableResult();
        List<IGlobalVariableDescribe> list;
        try {
            list = globalVariableDescribeService.findByTenantId(tenantId);
        } catch (MetadataServiceException e) {
            throw new MetaDataBusinessException(e.getMessage());
        }

        handleSysValue(list, tenantId);
        List resultList = Lists.newArrayList();
        for (IGlobalVariableDescribe globalVariable : list) {
            if (Strings.isNullOrEmpty(label) || globalVariable.getLabel().contains(label)) {
                fillGlobalVariableTransValue(globalVariable, realTimeTrans, tenantId, null);
                resultList.add(((DocumentBasedBean) globalVariable).getContainerDocument());
            }
        }
        result.setGlobalVariableList(resultList);
        result.setSuccess(true);
        return result;
    }

    @Override
    public List<IGlobalVariableDescribe> getGlobalVariableList(String tenantId) {
        try {
            if (StringUtils.isBlank(tenantId)) {
                return Lists.newArrayList();
            }
            return globalVariableDescribeService.findByTenantId(tenantId);
        } catch (MetadataServiceException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @param globalVariable
     * @param realTimeTrans  是否实时获取value的翻译值
     * @param tenantId
     * @param lang
     */
    private void fillGlobalVariableTransValue(IGlobalVariableDescribe globalVariable, Boolean realTimeTrans, String tenantId, String lang) {
        if (Objects.isNull(globalVariable)
                || !licenseService.isSupportMultiLanguage(tenantId)) {
            return;
        }
        if (StringUtils.isBlank(lang)) {
            lang = I18N.getContext().getLanguage();
        }
        if (DEFINE_TYPE_SYSTEM.equals(globalVariable.getDefineType())) {
            globalVariable.setLabel(I18NExt.getOnlyTextOrDefault(getGlobalNameTransKey(globalVariable.getApiName()), globalVariable.getLabel()));
            return;
        }
        String key = getTextMultiLanguageKey(globalVariable.getApiName());
        if (BooleanUtils.isTrue(realTimeTrans)) {
            Map<String, Localization> localizationMap = i18nSettingService.getLocalization(Lists.newArrayList(key), tenantId, true,true);
            if (CollectionUtils.notEmpty(localizationMap)) {
                Localization localization = localizationMap.get(key);
                String value = localization.get(lang, false);
                if (StringUtils.isNotEmpty(value)) {
                    globalVariable.setValue(value);
                }
            }
        } else {
            String text = I18NExt.getDesignatedLanguage(key, tenantId, lang);
            if (StringUtils.isNotBlank(text)) {
                globalVariable.setValue(text);
            }
        }
    }

    @Override
    public Object parseValue(IGlobalVariableDescribe globalVariable) {
        return parseValue(globalVariable, false);
    }

    @Override
    public Object parseValue(IGlobalVariableDescribe globalVariable, boolean isUseTransValueByGlobalVariable) {
        if (globalVariable == null) {
            return "";
        }
        Object value = globalVariable.getValue();

        if (value == null) {
            return "";
        }

        if (DEFINE_TYPE_CUSTOM.equals(globalVariable.getDefineType())) {
            String type = globalVariable.getType();

            if (FieldDescribeExt.isDateTypeField(type)) {
                return DateTimeUtils.parseISOLocateDateTime(String.valueOf(value), type,
                        TimeZoneContextHolder.getTenantTimeZone());
            }
            if ((IFieldType.LONG_TEXT.equals(globalVariable.getType()) || IFieldType.TEXT.equals(globalVariable.getType())) && isUseTransValueByGlobalVariable) {
                value = I18NExt.getOnlyTextOrDefault(getTextMultiLanguageKey(globalVariable.getApiName()), value.toString());
            }
            return value;
        }
        return handleSys(value, globalVariable.getTenantId(), globalVariable.getApiName());
    }

    private Object handleSys(Object value, String tenantId, String apiName) {
        Object result = value;
        try {
            if ("${date}".equals(value)) {
                result = getDateTimestamp();
            } else if ("${time}".equals(value)) {
                result = getTimeTimestamp();
            } else if ("${datetime}".equals(value)) {
                result = getDateTimeTimestamp();
            } else if ("${tenantName}".equals(value)) {
                result = getTenantName(tenantId);
            } else if ("${current_intercon_enterprise}".equals(value)) {
                result = getCurrentInterconEnterprise();
            }
        } catch (Exception e) {
            log.error("ParseGlobalVarValue fail tenantId={}, apiName={}", tenantId, apiName, e);
        }
        return result;
    }

    private long getDateTimeTimestamp() {
        if (DateTimeUtils.isGrayTimeZone()) {
            return nowDateTime();
        }
        return (new Date()).getTime();
    }

    private long getTimeTimestamp() throws ParseException {
        if (DateTimeUtils.isGrayTimeZone()) {
            return nowTime(TimeZoneContextHolder.getTenantTimeZone());
        }
        Date date = DateUtils.parseDate(DateFormatUtils.format(new Date(), "yyyy-MM-dd HH:mm"), "yyyy-MM-dd HH:mm");
        return date.getTime();
    }

    private long getDateTimestamp() throws ParseException {
        if (DateTimeUtils.isGrayTimeZone()) {
            return nowDate(TimeZoneContextHolder.getTenantTimeZone());
        }
        Date date = DateUtils.parseDate(DateFormatUtils.format(new Date(), "yyyy-MM-dd"), "yyyy-MM-dd");
        return date.getTime();
    }

    private String getTenantName(String tenantId) {
        GetSimpleEnterpriseDataArg arg = new GetSimpleEnterpriseDataArg();
        arg.setEnterpriseId(Integer.parseInt(tenantId));
        GetSimpleEnterpriseDataResult result = enterpriseEditionService.getSimpleEnterpriseData(arg);
        if (Objects.nonNull(result) && Objects.nonNull(result.getEnterpriseData())) {
            return result.getEnterpriseData().getEnterpriseName();
        }
        return null;
    }

    private String getCurrentInterconEnterprise() {
        return Optional.ofNullable(RequestContextManager.getContext())
                .map(RequestContext::getUser)
                .filter(User::isOutUser)
                .map(user -> enterpriseRelationService.getUpstreamMapperObjectId(user, Utils.ACCOUNT_API_NAME))
                .orElse(null);
    }

    private void handleSysValue(List<IGlobalVariableDescribe> globalVariableDescribeList, String tenantId) {
        if (CollectionUtils.empty(globalVariableDescribeList)) {
            return;
        }
        for (IGlobalVariableDescribe globalVariableDescribe : globalVariableDescribeList) {
            if (IGlobalVariableDescribe.DEFINE_TYPE_SYSTEM.equals(globalVariableDescribe.getDefineType())) {
                if ("tenantName__g".equals(globalVariableDescribe.getApiName())) {
                    globalVariableDescribe.setValue(getTenantName(tenantId));
                } else {
                    globalVariableDescribe.setValue(globalVariableDescribe.getLabel());
                }
            }
        }
    }

    /**
     * 初始化预设的全局变量
     *
     * @param tenantId
     * @throws MetadataServiceException
     */
    private List<IGlobalVariableDescribe> addSysGlobalVariables(String tenantId) throws MetadataServiceException {
        //当前时间
        IGlobalVariableDescribe currentTime = generateSys(tenantId, "${time}", "currentTime__g", IFieldType.TIME, I18N.text(I18NKey.CURRENT_TIME));
        //当前日期
        IGlobalVariableDescribe currentDate = generateSys(tenantId, "${date}", "currentDate__g", IFieldType.DATE, I18N.text(I18NKey.CURRENT_DATE));
        //当前日期时间
        IGlobalVariableDescribe currentDateTime = generateSys(tenantId, "${datetime}", "currentDateTime__g", IFieldType.DATE_TIME, I18N.text(I18NKey.CURRENT_DATE_TIME));

        List<IGlobalVariableDescribe> result = Lists.newArrayList();
        result.add(currentTime);
        result.add(currentDateTime);
        result.add(currentDate);
        return result;
    }

    /**
     * 创建系统全局变量
     *
     * @param tenantId
     * @param value
     * @param apiName
     * @param type
     * @param label
     * @return
     * @throws
     */
    private IGlobalVariableDescribe generateSys(String tenantId, String value, String apiName, String type, String label) throws MetadataServiceException {
        IGlobalVariableDescribe globalVariable = buildGlobalVariable(tenantId, value, apiName, type, label);
        create(globalVariable);
        return globalVariable;
    }

    private IGlobalVariableDescribe buildGlobalVariable(String tenantId, String value, String apiName, String type,
                                                        String label) {
        IGlobalVariableDescribe globalVariable = new GlobalVariableDescribe();
        globalVariable.setTenantId(tenantId);
        globalVariable.setApiName(apiName);
        globalVariable.setValue(value);
        globalVariable.setType(type);
        globalVariable.setDefineType(IGlobalVariableDescribe.DEFINE_TYPE_SYSTEM);
        globalVariable.setDeleted(false);
        globalVariable.setCreatedBy(I18N.text(I18NKey.SYSTEM_CREATION));
        globalVariable.setLastModifiedBy(I18N.text(I18NKey.SYSTEM));
        globalVariable.setLabel(label);
        globalVariable.setPackage("CRM");
        return globalVariable;
    }

    // TODO: 17/7/13
    private void checkValueIsMatchType(IGlobalVariableDescribe globalVariableDescribe) {
        Object value = globalVariableDescribe.getValue();
        String type = globalVariableDescribe.getType();
    }

    private String getTextMultiLanguageKey(String apiName) {
        return "global.variable.field." + apiName;
    }

    private String getGlobalNameTransKey(String apiName) {
        return "global.variable.field.name." + apiName;
    }
}
