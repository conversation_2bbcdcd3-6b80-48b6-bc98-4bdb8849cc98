package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.metadata.CountryAreaManager;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * Created by zhouwr on 2017/12/26
 */
@Component
public class CountryAreaDataConverter extends BaseFieldConverter {

    @Override
    public List<String> getSupportedFieldTypes() {
        return Lists.newArrayList(IFieldType.COUNTRY, IFieldType.PROVINCE, IFieldType.CITY, IFieldType.DISTRICT);
    }

    @Override
    protected String convert(IObjectData objectData, IFieldDescribe fieldDescribe, DataConvertContext context) {
        String value = objectData.get(fieldDescribe.getApiName()).toString();
        return CountryAreaManager.getLabelByCode(context.getUser().getTenantId(), value, fieldDescribe.getType());
    }
}
