package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;


@Data
@Slf4j
public class BatchEditCalculateParam {

    private List<IObjectData> objectDataList;
    private List<IObjectData> copyObjectDataList;
    private List<IObjectData> dbObjectDataList;
    //数据id：改的数据 字段：value
    private Map<String, Map<String, Object>> masterModifyData;
    private IObjectDescribe objectDescribe;
    private boolean excludeDefaultValue;
    private boolean includeQuoteField;

    @Builder
    public BatchEditCalculateParam(List<IObjectData> objectDataList,
                                   List<IObjectData> dbObjectDataList,
                                   Map<String, Map<String, Object>> masterModifyData,
                                   IObjectDescribe objectDescribe,
                                   boolean excludeDefaultValue, boolean includeQuoteField) {
        this.objectDataList = objectDataList;
        this.dbObjectDataList = dbObjectDataList;
        this.masterModifyData = masterModifyData;
        this.objectDescribe = objectDescribe;
        this.excludeDefaultValue = excludeDefaultValue;
        this.includeQuoteField = includeQuoteField;
    }

    public BatchEditCalculateParam init() {
        this.dbObjectDataList = CollectionUtils.nullToEmpty(this.dbObjectDataList);
        this.masterModifyData = CollectionUtils.nullToEmpty(this.masterModifyData);
        this.copyObjectDataList = CollectionUtils.nullToEmpty(this.copyObjectDataList);
        copyObjectDataList = ObjectDataExt.copyList(this.objectDataList);

        if (CollectionUtils.notEmpty(this.dbObjectDataList)) {
            ObjectDataExt.mergeWithDbData(this.copyObjectDataList, this.dbObjectDataList);
        }

        Map<String, IObjectData> dbObjectDataMap = dbObjectDataList.stream().collect(Collectors.toMap(DBRecord::getId, it -> it, (x1, x2) -> x1));
        for (IObjectData objectData : this.copyObjectDataList) {
            IObjectData dbObjectData = dbObjectDataMap.get(objectData.getId());
            if (Objects.isNull(dbObjectData)) {
                log.warn("batchEditCalculate init,there may be a problem with the edited data! tenantId:{},describeApiName:{},objectDataId:{}",
                        objectDescribe.getTenantId(), objectDescribe.getApiName(), objectData.getId());
                continue;
            }
            Map<String, Object> updateData = ObjectDataExt.of(dbObjectData).diff(objectData, objectDescribe);
            if (CollectionUtils.notEmpty(updateData)) {
                this.masterModifyData.put(objectData.getId(), updateData);
            }
        }
        return this;
    }

}
