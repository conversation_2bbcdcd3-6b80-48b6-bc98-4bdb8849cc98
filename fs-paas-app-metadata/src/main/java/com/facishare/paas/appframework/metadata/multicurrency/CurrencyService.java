package com.facishare.paas.appframework.metadata.multicurrency;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.MetaDataService;
import com.facishare.paas.appframework.metadata.MtCurrency;
import com.facishare.paas.appframework.metadata.SearchTemplateQueryExt;
import com.facishare.paas.appframework.metadata.exception.MetaDataBusinessException;
import com.facishare.paas.metadata.api.DELETE_STATUS;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.QueryResult;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@Component
@Slf4j
public class CurrencyService {

    @Autowired
    private MetaDataService metaDataService;

    public List<MtCurrency> queryCurrencyData(User user) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setOffset(0);
        SearchTemplateQueryExt.of(searchTemplateQuery).addFilter(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId());
        return findCurrencyBySearchQuery(user, searchTemplateQuery);
    }

    public List<MtCurrency> findCurrencyBySearchQuery(User user, SearchTemplateQuery searchTemplateQuery) {
        List<MtCurrency> mtCurrencyList = Lists.newArrayList();
        QueryResult<IObjectData> queryResult = metaDataService.findBySearchQuery(user, MtCurrency.MT_CURRENCY_OBJ, searchTemplateQuery);
        queryResult.getData().forEach(x -> mtCurrencyList.add(MtCurrency.buildCurrency(x)));
        return mtCurrencyList;
    }

    public MtCurrency queryCurrencyByCode(User user, String code) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setOffset(0);
        SearchTemplateQueryExt.of(searchTemplateQuery)
                .addFilter(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId())
                .addFilter(Operator.EQ, MtCurrency.CURRENCY_CODE, code);
        List<MtCurrency> data = findCurrencyBySearchQuery(user, searchTemplateQuery);
        if (data.size() != 1) {
            throw new MetaDataBusinessException(I18N.text(I18NKey.MULTI_CURRENCY_DATA_ERROR));
        }
        return data.get(0);
    }

    public List<MtCurrency> findCurrencyByCodes(User user, List<String> codes) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1000);
        searchTemplateQuery.setOffset(0);
        SearchTemplateQueryExt.of(searchTemplateQuery)
                .addFilter(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId())
                .addFilter(Operator.IN, MtCurrency.CURRENCY_CODE, codes);
        return findCurrencyBySearchQuery(user, searchTemplateQuery);
    }


    public Integer findCurrencyCountByCode(User user, String code) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        SearchTemplateQueryExt.of(searchTemplateQuery)
                .addFilter(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId())
                .addFilter(Operator.EQ, MtCurrency.CURRENCY_CODE, code);
        return findCurrencyBySearchQuery(user, searchTemplateQuery).size();
    }

    public MtCurrency queryFunctionalCode(User user) {
        SearchTemplateQuery searchTemplateQuery = new SearchTemplateQuery();
        searchTemplateQuery.setLimit(1);
        searchTemplateQuery.setOffset(0);
        searchTemplateQuery.setNeedReturnCountNum(false);
        SearchTemplateQueryExt.of(searchTemplateQuery)
                .addFilter(Operator.EQ, IObjectData.TENANT_ID, user.getTenantId())
                .addFilter(Operator.EQ, MtCurrency.STATUS, String.valueOf(DELETE_STATUS.NORMAL.getValue()))
                .addFilter(Operator.EQ, MtCurrency.IS_FUNCTIONAL, Boolean.toString(true));
        List<MtCurrency> functionalCode = findCurrencyBySearchQuery(user, searchTemplateQuery);
        if (CollectionUtils.empty(functionalCode)) {
            return MtCurrency.builder().build();
        }
        return functionalCode.get(0);
    }

    public MtCurrency findCurrencyById(User user, String id) {
        IObjectData objectData = metaDataService.findObjectData(user, id, MtCurrency.MT_CURRENCY_OBJ);
        return MtCurrency.buildCurrency(objectData);
    }


    public void batchUpsertCurrency(User user, List<MtCurrency> currencyList) {
        if (CollectionUtils.empty(currencyList)) {
            return;
        }
        List<IObjectData> objectDataList = Lists.newArrayList();
        currencyList.forEach(x -> objectDataList.add(MtCurrency.buildCurrency(x)));
        metaDataService.bulkUpsertObjectData(objectDataList, user);
    }


    public void deleteCurrency(User user, List<MtCurrency> currencyList) {
        if (CollectionUtils.empty(currencyList)) {
            return;
        }
        List<IObjectData> objectDataList = Lists.newArrayList();
        currencyList.forEach(x -> objectDataList.add(MtCurrency.buildCurrency(x)));
        metaDataService.bulkDeleteWithInternalDescribe(objectDataList, user);
    }
}
