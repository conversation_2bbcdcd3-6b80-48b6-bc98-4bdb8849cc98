package com.facishare.paas.appframework.metadata.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.facishare.paas.appframework.core.exception.SystemErrorCode;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.rest.core.codec.IRestCodeC;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

@Slf4j
public class CRMRestServiceCodec implements IRestCodeC {
    public <T> byte[] encodeArg(T obj) {
        return JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue).getBytes(StandardCharsets.UTF_8);
    }

    public <T> T decodeResult(int statusCode, Map<String, List<String>> headers, byte[] bytes, Class<T> clazz) {
        try {
            if (String.class.equals(clazz)) {
                return (T) new String(bytes, "UTF-8");
            }

            String json = new String(bytes, "UTF-8");
            return JSON.parseObject(json, clazz);
        } catch (Exception e) {
            log.error("decodeResult error,statusCode:{},clazz:{},ret:{}", statusCode, clazz.getName(), new String(bytes), e);
            throw new MetaDataException(SystemErrorCode.METADATA_ERROR, e);
        }
    }
}
