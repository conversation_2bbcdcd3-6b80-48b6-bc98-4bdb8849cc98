package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.button.ButtonURL;
import com.facishare.paas.appframework.metadata.config.IUdefButtonConfig;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IButton;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * Created by linqy on 2018/01/11
 */
public interface CustomButtonService {
    /**
     * 创建自定义按钮
     *
     * @param user
     * @param button
     * @return
     */
    IUdefButton createCustomButton(User user, IUdefButton button);

    /**
     * 更新自定义按钮
     *
     * @param user
     * @param button
     * @return
     */
    IUdefButton updateCustomButton(User user, IUdefButton button);

    /**
     * 删除自定义按钮
     *
     * @param user
     * @param buttonApiName
     * @param describeApiName
     * @return
     */
    boolean deleteCustomButton(User user, String buttonApiName, String describeApiName);

    /**
     * 根据apiname查询自定义按钮
     *
     * @param user
     * @param buttonApiName
     * @param describeApiName
     * @return
     */
    IUdefButton findButtonByApiName(User user, String buttonApiName, String describeApiName);


    /**
     * 根据apiName查询自定义按钮，不处理禁用和删除的字段
     *
     * @param user
     * @param buttonApiName
     * @param describeApiName
     * @return
     */
    IUdefButton findButtonByApiNameForDesigner(User user, String buttonApiName, String describeApiName);

    IUdefButton findButtonByApiName(User user, String buttonApiName, IObjectDescribe objectDescribe);

    /**
     * 查询一个对象下的按钮列表(包括系统和租户的), 过滤并排序, 系统按钮在前, 然后按照default.button.order
     *
     * @param user
     * @param describeApiName
     * @return
     */
    List<IUdefButton> findButtonList(User user, String describeApiName);

    /**
     * 功能权限过滤
     *
     * @param user
     * @param describe
     * @param buttonList
     * @return
     */
    List<IButton> filterFunPrivilege(User user, IObjectDescribe describe, List<IUdefButton> buttonList);

    List<IUdefButton> findButtonsByLastModifiedTime(User user, long lastUpdateTime);

    /**
     * 启用/禁用自定义按钮
     * isActive为true启用
     * * @return
     */
    boolean updateStatus(User user, String buttonApiName, String describeApiName, boolean isActive);

    /**
     * 根据usePageType查询一个对象下的自定义按钮列表
     *
     * @param user
     * @param objectData     usePageType 为 {@link ButtonUsePageType#Detail} 时，objectData 不能为空
     * @param describe
     * @param usePageType    {@link ButtonUsePageType}
     * @param buttonList
     * @param readonlyFields
     * @return 返回符合 usePageType 的按钮
     */
    List<IUdefButton> filterButtonsForUsePageType(User user, IObjectData objectData, IObjectDescribe describe, String usePageType, List<IUdefButton> buttonList, Set<String> readonlyFields);

    List<IUdefButton> filterButtonsForCreate(User user, String describeApiName);

    List<IUdefButton> filterButtonsForEdit(User user, String describeApiName);

    Map<String, List<IUdefButton>> findButtonByApiNameListAndType(User user, List<String> apiNameList, String buttonType);

    IUdefButtonConfig findButtonConfigByApiName(String describeApiName, String buttonApiName, User user);

    List<IUdefButtonConfig> findButtonConfigListByApiName(User user, String describeApiName, List<IUdefButton> buttonList);

    List<IUdefButton> findCustomButtonByUsePage(String describeApiName, String usePage, User user);

    List<IUdefButton> findCustomButtonByUsePageIncludeDisable(String describeApiName, String usePage, User user);

    List<IButton> findListBatchButton(IObjectDescribe describe, boolean enableMobileLayout, User user);

    List<IButton> findButtonsForCreate(IObjectDescribe describe, User user);

    List<IButton> findButtonsForEdit(IObjectDescribe describe, User user);

    /**
     * 查看回收站的按钮
     */
    List<IButton> findButtonsForRecycleBin(IObjectDescribe describe, User user);

    List<IButton> findTeamMemberButton(IObjectDescribe describe, IObjectData data, User user);

    Map<String, List<IUdefButton>> findListButtonByButtonFilter(IObjectDescribe describe, List<IObjectData> dataList,
                                                                User user, String usePageType);

    Map<String, List<IUdefButton>> filterListButton(List<IUdefButton> udefButtons, IObjectDescribe describe, List<IObjectData> dataList, User user, String usePageType);

    String submitBulkActionJob(User user, CallBackActionParams callBackActionParams, String objectApiName);

    void updateButtonUrl(User user, String objectDescribeApiName, ButtonURL buttonURL, Set<String> buttonApiNameSet);

    IUdefButton findButtonByApiNameInDesigner(User user, String buttonApiName, String describeApiName);

    List<IUdefButton> findButtonsByDescribeApiName(User user, String objectDescribeApiName);

    List<IUdefButton> findButtonsByDescribeApiName(User user, IObjectDescribe describe);

    Map<String, List<IUdefButton>> findButtonsByDescribeApiNames(User user, List<String> describeApiNames, boolean checkPartnerStatus);

    List<IButton> findButtonsByUsePageType(User user, IObjectDescribe describe, IObjectData objectData,
                                           ButtonUsePageType usePageType);

    List<IButton> findButtonsByUsePageType(User user, IObjectDescribe describe, IObjectData objectData,
                                           ButtonUsePageType usePageType, boolean isOnlyActivate);

    List<IButton> findButtonsByUsePageType(User user, IObjectDescribe describe, IObjectData objectData,
                                           ButtonUsePageType usePageType, boolean isOnlyActivate, boolean filterFunPrivilege);

    void checkCustomButtonCountLimit(User user, String describeApiName, int count);

//    List<IButton> processGdprButton(IObjectDescribe describe, User user, List<IButton> buttonList, ButtonUsePageType usePageType);
}
