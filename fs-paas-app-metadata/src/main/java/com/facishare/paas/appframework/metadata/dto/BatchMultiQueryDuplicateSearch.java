package com.facishare.paas.appframework.metadata.dto;

import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

public interface BatchMultiQueryDuplicateSearch {

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Arg {
        String tenantId;
        String describeApiName;
        IDuplicatedSearch.Type type;
        List<DuplicateData> dataList;
        boolean isIdValue;
        boolean removeSelf = false;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class Result {
        private String code;
        private String message;
        List<DuplicateResult> duplicateDataList;
        public boolean success() {
            return "200".equals(code);
        }
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class DuplicateResult {
        String ruleApiName;
        String describeApiName;
        List<String> dataIds;
        String sourceDataId;
    }

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    class DuplicateData {
        Map<String, Object> data;
        List<String> ruleApiNameList;
    }
}
