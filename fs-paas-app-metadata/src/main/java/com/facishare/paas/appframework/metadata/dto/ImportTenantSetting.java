package com.facishare.paas.appframework.metadata.dto;

import lombok.*;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.Objects;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ImportTenantSetting {
    private ImportSetting insertImport;
    private ImportSetting updateImport;


    @Getter
    public enum ImportMethod {
        NORMAL("normal"),
        ADD_ACTION("addAction");

        private final String method;

        ImportMethod(String method) {
            this.method = method;
        }

        public static ImportMethod of(String importMethod) {
            return Arrays.stream(ImportMethod.values()).filter(x -> Objects.equals(x.getMethod(), importMethod)).findFirst().orElse(NORMAL);
        }
    }

    @Getter
    public enum FuzzyDuplicateImportStrategy {
        NON_BLOCK("nonBlock"),
        BLOCK("block");

        private final String strategy;

        FuzzyDuplicateImportStrategy(String strategy) {
            this.strategy = strategy;
        }

        public static FuzzyDuplicateImportStrategy of(String strategy) {
            return Arrays.stream(FuzzyDuplicateImportStrategy.values())
                    .filter(x -> StringUtils.equalsIgnoreCase(x.getStrategy(), strategy))
                    .findFirst()
                    .orElse(NON_BLOCK);
        }
    }
}
