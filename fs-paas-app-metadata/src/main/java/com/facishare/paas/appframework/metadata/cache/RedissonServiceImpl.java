package com.facishare.paas.appframework.metadata.cache;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.core.exception.AppFrameworkErrorCode;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import lombok.extern.slf4j.Slf4j;
import com.google.common.base.Strings;
import org.redisson.RedissonMultiLock;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.redisson.codec.JsonJacksonCodec;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.UnaryOperator;
import java.util.stream.Stream;

/**
 * create by z<PERSON><PERSON> on 2020/12/09
 */
@Slf4j
@Primary
@Service("redissonService")
public class RedissonServiceImpl implements RedissonService {
    public static final String REDISSON_PRE = "REDISSON_KEY";

    // 默认最大重试次数
    private static final int RETRY_TIMES_LIMIT = 3;

    @Autowired
    private RedissonClient redissonClient;

    @Override
    public RLock tryLock(long waitTime, long leaseTime, TimeUnit unit, String key) {
        try {
            RLock lock = getLock(key);
            if (lock.tryLock(waitTime, leaseTime, unit)) {
                return lock;
            }
        } catch (InterruptedException e) {
            log.warn("tryLock fail, key:{}", key, e);
            Thread.currentThread().interrupt();
        } catch (Exception e) {
            log.warn("tryLock fail, key:{}", key, e);
        }
        return null;
    }

    @Override
    public RLock tryLockWithErrorMsg(long waitTime, long leaseTime, TimeUnit unit, String key, String message) {
        return tryLockWithErrorMsg(waitTime, leaseTime, unit, key, message, AppFrameworkErrorCode.VALIDATION_ERROR.getCode());
    }

    @Override
    public RLock tryLockWithErrorMsg(long waitTime, long leaseTime, TimeUnit unit, String key, String message, int errorCode) {
        RLock lock = tryLock(waitTime, leaseTime, unit, key);
        if (Objects.isNull(lock)) {
            throw new ValidateException(message, errorCode);
        }
        return lock;
    }

    @Override
    public RLock tryLock(User user, String describeApiName, String key) {
        String newKey = String.format("%s_%s_%s_%s", REDISSON_PRE, user.getTenantId(), describeApiName, key);
        return tryLock(AppFrameworkConfig.getDuplicateSearchLockWaitTimeSeconds(), AppFrameworkConfig.getDuplicateSearchLockWaitLeaseTimeSeconds(), TimeUnit.SECONDS, newKey);
    }

    @Override
    public RLock tryMultiLock(long waitTime, long leaseTime, TimeUnit unit, String... keys) {
        try {
            RLock multiLock = getMultiLock(keys);
            if (multiLock == null || multiLock.tryLock(waitTime, leaseTime, unit)) {
                return multiLock;
            }
        } catch (InterruptedException e) {
            log.warn("tryMultiLock fail, key:{}", keys, e);
            Thread.currentThread().interrupt();
        }
        return null;
    }

    @Override
    public RLock tryFairLock(long waitTime, long leaseTime, TimeUnit unit, String key) {
        try {
            RLock lock = getFairLock(key);
            if (lock.tryLock(waitTime, leaseTime, unit)) {
                return lock;
            }
        } catch (InterruptedException e) {
            log.warn("tryFairLock fail, key:{}", key, e);
            Thread.currentThread().interrupt();
        }
        return null;
    }

    @Override
    public boolean isLocked(String key) {
        RLock lock = getLock(key);
        return lock.isLocked();
    }

    @Override
    public void unlock(RLock lock) {
        if (lock == null) {
            return;
        }
        try {
            lock.unlock();
        } catch (Exception e) {
            log.warn("unlock fail", e);
        }
    }

    @Override
    public boolean limiter(String key, TimeUnit unit, int max) {
        RBucket<LimiterInfo> rBucket = redissonClient.getBucket(key, JsonJacksonCodec.INSTANCE);
        LimiterInfo oldLimiterInfo = rBucket.get();
        if (Objects.isNull(oldLimiterInfo)) {
            LimiterInfo newLimiterInfo = new LimiterInfo();
            return rBucket.trySet(newLimiterInfo, 1, unit);
        }
        LimiterInfo newLimiterInfo = oldLimiterInfo.incrementAndGet();
        if (newLimiterInfo.getCurrentNumber() > max) {
            return false;
        }
        return rBucket.compareAndSet(oldLimiterInfo, newLimiterInfo);
    }

    @Override
    public <T> T incrementAndGet(String key, long timeToLive, TimeUnit timeUnit,
                                 UnaryOperator<T> incrementAndGet, T defaultValue) {
        RBucket<T> rBucket = redissonClient.getBucket(key, JsonJacksonCodec.INSTANCE);
        int retryTimes = 0;
        T oldValue;
        T newValue;
        do {
            oldValue = rBucket.get();
            // redis中不存在，则将默认值存入redis
            if (Objects.isNull(oldValue)) {
                if (rBucket.trySet(newValue = defaultValue, timeToLive, timeUnit)) {
                    break;
                }
            }
            // 调用自增逻辑
            newValue = incrementAndGet.apply(oldValue);
            // 重试三次
        } while (!rBucket.compareAndSet(oldValue, newValue) && ++retryTimes < RETRY_TIMES_LIMIT);
        return retryTimes >= RETRY_TIMES_LIMIT ? null : newValue;
    }

    public RLock getLock(String key) {
        return redissonClient.getLock(key);
    }

    private RLock getFairLock(String key) {
        return redissonClient.getFairLock(key);
    }

    private RLock getMultiLock(String... keys) {
        RLock[] rLocks = Stream.of(keys)
                .filter(it -> !Strings.isNullOrEmpty(it))
                .map(this::getLock)
                .toArray(RLock[]::new);
        if (rLocks.length == 0) {
            return null;
        }
        return new RedissonMultiLock(rLocks);
    }

}
