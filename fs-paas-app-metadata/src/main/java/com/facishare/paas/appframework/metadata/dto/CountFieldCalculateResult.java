package com.facishare.paas.appframework.metadata.dto;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.IObjectData;
import lombok.Builder;
import lombok.Data;

import java.util.List;

/**
 * Created by zhouwr on 2018/1/27
 */
@Data
@Builder
public class CountFieldCalculateResult {
    private IObjectData masterObjectData;
    private List<String> countFieldNames;

    public boolean isNeedUpdate() {
        return masterObjectData != null && CollectionUtils.notEmpty(countFieldNames);
    }
}
