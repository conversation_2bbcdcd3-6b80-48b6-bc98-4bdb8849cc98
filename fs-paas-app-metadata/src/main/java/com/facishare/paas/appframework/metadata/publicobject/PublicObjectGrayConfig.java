package com.facishare.paas.appframework.metadata.publicobject;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicFieldType;
import com.facishare.paas.metadata.api.describe.IFieldType;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fxiaoke.release.GrayRule;
import com.github.autoconf.ConfigFactory;
import com.github.autoconf.api.IConfig;
import de.lab4inf.math.util.Strings;
import lombok.Data;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Created by zhaooju on 2023/12/19
 */
@Slf4j
@UtilityClass
public final class PublicObjectGrayConfig {
    private Map<String, GrayRule> publicObjectGrayRules;
    private Map<String, GrayRule> publicObjectVerifyWhiteFieldGrayRules;
    private Map<String, PublicObjectResourceConfigData> publicObjectResourceRules;

    static {
        ConfigFactory.getConfig("fs-paas-public-object-config", config -> {
            try {
                log.info("reload {},content:{}", config.getName(), config.getString());
                reload(config);
            } catch (Exception e) {
                log.error("reload {} failed", config.getName(), e);
            }
        });
    }

    private void reload(IConfig config) {
        ConfigData configData = JSON.parseObject(config.getString(), ConfigData.class);
        publicObjectGrayRules = UdobjGrayConfig.parseGrayRulesWithMap(configData.getPublicObjectGrayRules());
        publicObjectVerifyWhiteFieldGrayRules = UdobjGrayConfig.parseGrayRulesWithMap(configData.getPublicObjectVerifyWhiteFieldGrayRules());
        publicObjectResourceRules = configData.getPublicObjectResourceRules();
    }

    public boolean isGrayPublicObject(String tenantId, String describeApiName) {
        return UdobjGrayConfig.isGrayWithDescribeApiName(publicObjectGrayRules, tenantId, describeApiName, false);
    }

    /**
     * 不需要校验的字段
     *
     * @param fieldApiName
     * @param describeApiName
     * @return
     */
    public boolean isPublicObjectVerifyWhiteField(String describeApiName, String fieldApiName) {
        return UdobjGrayConfig.isGrayWithDescribeApiName(publicObjectVerifyWhiteFieldGrayRules, fieldApiName, describeApiName, false);
    }

    /**
     * 向后兼容版本，不传租户ID时默认为空
     *
     * @param describeApiName 对象API名
     * @param fieldApiName    字段API名
     * @param fieldType       字段类型
     * @return 字段公开类型
     */
    public static Optional<PublicFieldType> getPublicFieldType(String describeApiName, String fieldApiName, String fieldType) {
        return getPublicFieldType(describeApiName, fieldApiName, fieldType, null);
    }

    public static Optional<PublicFieldType> getPublicFieldType(String describeApiName, String fieldApiName, String fieldType, String tenantId) {
        PublicObjectResourceConfigData configData = UdobjGrayConfig.getWithDescribeApiName(publicObjectResourceRules, describeApiName, true);
        if (Objects.isNull(configData)) {
            return Optional.empty();
        }
        return Optional.ofNullable(configData.getPublicFieldType(fieldApiName, fieldType, tenantId));
    }

    @Data
    private static class ConfigData {
        private Map<String, String> publicObjectGrayRules;
        private Map<String, String> publicObjectVerifyWhiteFieldGrayRules;
        private Map<String, PublicObjectResourceConfigData> publicObjectResourceRules;
    }

    @Data
    public static class PublicObjectResourceConfigData {
        private List<PublicObjectResourceConfigDataItem> fields;
        private List<PublicObjectResourceConfigDataItem> fieldTypes;

        /**
         * 优先通过字段apiName匹配，没有命中的时候再通过字段类型匹配
         * 如果配置了租户灰度规则，则需要同时满足字段匹配和租户灰度规则
         *
         * @param fieldApiName 字段API名
         * @param fieldType    字段类型
         * @param tenantId     租户ID
         * @return 字段公开类型
         */
        public PublicFieldType getPublicFieldType(String fieldApiName, String fieldType, String tenantId) {
            PublicFieldType publicFieldType = getPublicFieldType(fieldApiName, tenantId, fields);
            if (Objects.isNull(publicFieldType)) {
                publicFieldType = getPublicFieldType(fieldType, tenantId, fieldTypes);
            }
            return publicFieldType;
        }

        private PublicFieldType getPublicFieldType(String fieldName, String tenantId, List<PublicObjectResourceConfigDataItem> items) {
            if (Strings.isNullOrEmpty(fieldName) || CollectionUtils.empty(items)) {
                return null;
            }
            for (PublicObjectResourceConfigDataItem item : items) {
                if (item.isAllow(fieldName)) {
                    PublicFieldType fieldType4Tenant = item.getPublicFieldTypeForTenant(tenantId, fieldName);
                    if (fieldType4Tenant != null) {
                        return fieldType4Tenant;
                    }
                }
            }
            return null;
        }
    }

    @Data
    public static class PublicObjectResourceConfigDataItem {
        private final GrayRule grayRule;
        private final PublicFieldType publicFieldType;
        private final GrayRule publicFieldPublicDataRule;
        private final GrayRule publicFieldPrivateDataRule;
        private final GrayRule privateFieldRule;

        public PublicObjectResourceConfigDataItem(@JsonProperty("grayRule") String grayRule,
                                                  @JsonProperty("publicFieldType") String publicFieldType,
                                                  @JsonProperty("publicFieldPublicDataRule") String publicFieldPublicDataRule,
                                                  @JsonProperty("publicFieldPrivateDataRule") String publicFieldPrivateDataRule,
                                                  @JsonProperty("privateFieldRule") String privateFieldRule) {
            this.grayRule = new GrayRule(grayRule);
            this.publicFieldType = PublicFieldType.fromType(publicFieldType);
            this.publicFieldPublicDataRule = Strings.isNullOrEmpty(publicFieldPublicDataRule) ?
                    null : new GrayRule(publicFieldPublicDataRule);
            this.publicFieldPrivateDataRule = Strings.isNullOrEmpty(publicFieldPrivateDataRule) ?
                    null : new GrayRule(publicFieldPrivateDataRule);
            this.privateFieldRule = Strings.isNullOrEmpty(privateFieldRule) ?
                    null : new GrayRule(privateFieldRule);
        }

        public boolean isAllow(String key) {
            return grayRule.isAllow(key);
        }

        /**
         * 根据租户ID和字段名获取适用的PublicFieldType
         * 处理逻辑按照以下优先级：
         * 1. 基础规则检查（privateFieldRule、publicFieldPrivateDataRule）
         * 2. 字段类型特定规则检查
         * 3. 默认值fallback
         *
         * @param tenantId  租户ID
         * @param fieldName 字段名称或字段类型
         * @return 该租户适用的PublicFieldType
         */
        public PublicFieldType getPublicFieldTypeForTenant(String tenantId, String fieldName) {
            if (tenantId == null) {
                return publicFieldType;
            }

            // 1. 检查基础规则
            PublicFieldType fieldType = checkBasicRules(tenantId);
            if (fieldType != null) {
                return fieldType;
            }

            // 2. 检查publicFieldPublicDataRule规则
            boolean matchesPublicDataRule = publicFieldPublicDataRule != null && publicFieldPublicDataRule.isAllow(tenantId);

            // 3. 根据字段类型应用不同的规则
            if (isImageOrFileAttachmentField(fieldName)) {
                // 特殊字段（图片和附件）需要额外检查
                PublicFieldType specialFieldType = checkMediaFieldTypeRules(tenantId, fieldName, matchesPublicDataRule);
                if (specialFieldType != null) {
                    // 返回为空，端上可以选择三个，返回PUBLIC_FIELD_PUBLIC_DATA，就只能选一个
                    return PublicFieldType.PUBLIC_FIELD_PUBLIC_DATA == specialFieldType ? null : specialFieldType;
                }
            } else if (matchesPublicDataRule) {
                // 普通字段只需检查publicFieldPublicDataRule
//                return PublicFieldType.PUBLIC_FIELD_PUBLIC_DATA;
                return null;
            }

            // 4. 如果没有匹配的规则，返回默认配置
            return publicFieldType;
        }

        /**
         * 检查基础灰度规则（privateFieldRule 和 publicFieldPrivateDataRule）
         *
         * @param tenantId 租户ID
         * @return 如果匹配则返回对应的PublicFieldType，否则返回null
         */
        private PublicFieldType checkBasicRules(String tenantId) {
            // 优先检查privateFieldRule
            if (privateFieldRule != null && privateFieldRule.isAllow(tenantId)) {
                return PublicFieldType.PRIVATE_FIELD;
            }

            // 其次检查publicFieldPrivateDataRule
            if (publicFieldPrivateDataRule != null && publicFieldPrivateDataRule.isAllow(tenantId)) {
                return PublicFieldType.PUBLIC_FIELD_PRIVATE_DATA;
            }

            // 未匹配任何基础规则
            return null;
        }

        /**
         * 检查媒体字段（图片和附件）的特殊规则
         * 只有同时满足以下条件才返回PUBLIC_FIELD_PUBLIC_DATA：
         * 1. publicFieldPublicDataRule规则匹配
         * 2. 如果是附件字段，FILE_ATTACHMENT_SIGNED_URL_GRAY灰度开关已开启
         *
         * @param tenantId              租户ID
         * @param fieldName             字段名称或字段类型
         * @param matchesPublicDataRule 是否匹配publicFieldPublicDataRule规则
         * @return 如果条件满足返回PUBLIC_FIELD_PUBLIC_DATA，否则返回null
         */
        private PublicFieldType checkMediaFieldTypeRules(String tenantId, String fieldName, boolean matchesPublicDataRule) {
            // 首先检查publicFieldPublicDataRule是否匹配
            if (!matchesPublicDataRule) {
                return null;
            }

            // 然后检查特定字段类型的灰度开关
            // 图片字段不需要额外检查灰度开关
            boolean grayEnabled = true;

            // 附件字段需要检查FILE_ATTACHMENT_SIGNED_URL_GRAY灰度开关
            if (isFileAttachmentField(fieldName)) {
                grayEnabled = UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FILE_ATTACHMENT_SIGNED_URL_GRAY, tenantId);
            }

            // 只有当所有条件都满足时，才返回PUBLIC_FIELD_PUBLIC_DATA
            return grayEnabled ? PublicFieldType.PUBLIC_FIELD_PUBLIC_DATA : null;
        }

        /**
         * 检查是否为图片或附件字段
         */
        private boolean isImageOrFileAttachmentField(String fieldName) {
            return isImageField(fieldName) || isFileAttachmentField(fieldName);
        }

        /**
         * 检查是否为图片字段
         */
        private boolean isImageField(String fieldName) {
            return IFieldType.IMAGE.equals(fieldName);
        }

        /**
         * 检查是否为附件字段
         */
        private boolean isFileAttachmentField(String fieldName) {
            return IFieldType.FILE_ATTACHMENT.equals(fieldName);
        }
    }
}
