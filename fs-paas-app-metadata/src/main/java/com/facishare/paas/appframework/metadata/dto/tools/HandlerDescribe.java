package com.facishare.paas.appframework.metadata.dto.tools;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.appframework.metadata.repository.model.HandlerDefinition;
import com.facishare.paas.appframework.metadata.repository.model.HandlerRuntimeConfig;
import lombok.*;

import java.util.Objects;
import java.util.Optional;

@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
@EqualsAndHashCode(exclude = {"id"})
public class HandlerDescribe {
    private String id;
    private String apiName;
    private String label;
    private String supportObjectApiName;
    private String description;
    private String handlerType;
    private String definitionInterfaceCode;
    private String interfaceCode;
    private String providerType;
    private String industryCode;
    private String restApiUrl;
    private String privilegeConfig;
    private String managementConfig;
    private boolean enableDistributedTransaction;
    private int defaultOrder;
    private boolean supportAsync;
    private boolean continueAfterApprovalSuccess;
    private boolean enableGrayConfig;
    private boolean isActive;

    public static HandlerDescribe of(HandlerDefinition handlerDefinition, HandlerRuntimeConfig config) {
        HandlerDescribe handlerDescribe = new HandlerDescribe();
        handlerDescribe.setId(handlerDefinition.getId());
        handlerDescribe.setApiName(handlerDefinition.getApiName());
        handlerDescribe.setLabel(handlerDefinition.getLabel());
        handlerDescribe.setSupportObjectApiName(handlerDefinition.getSupportObjectApiName());
        handlerDescribe.setDescription(handlerDefinition.getDescription());
        handlerDescribe.setHandlerType(handlerDefinition.getHandlerType());
        handlerDescribe.setDefinitionInterfaceCode(handlerDefinition.getInterfaceCode());
        handlerDescribe.setInterfaceCode(config.getInterfaceCode());
        handlerDescribe.setProviderType(handlerDefinition.getProviderType());
        handlerDescribe.setIndustryCode(handlerDefinition.getIndustryCode());
        handlerDescribe.setRestApiUrl(handlerDefinition.getRestApiUrl());
        if (Objects.nonNull(handlerDefinition.getPrivilegeConfig())) {
            handlerDescribe.setPrivilegeConfig(JSON.toJSONString(handlerDefinition.getPrivilegeConfig()));
        }
        if (Objects.nonNull(handlerDefinition.getManagementConfig())) {
            handlerDescribe.setManagementConfig(JSON.toJSONString(handlerDefinition.getManagementConfig()));
        }
        handlerDescribe.setEnableDistributedTransaction(handlerDefinition.isEnableDistributedTransaction());
        handlerDescribe.setDefaultOrder(Optional.ofNullable(config.getExecuteOrder()).orElse(Integer.MAX_VALUE));
        handlerDescribe.setSupportAsync(handlerDefinition.isSupportAsync());
        handlerDescribe.setEnableGrayConfig(handlerDefinition.isEnableGrayConfig());
        handlerDescribe.setContinueAfterApprovalSuccess(handlerDefinition.continueAfterApprovalSuccess());
        handlerDescribe.setActive(handlerDefinition.isActive());
        return handlerDescribe;
    }

    public HandlerDefinition toDefinition(String objectApiName, String interfaceCode) {
        return HandlerDefinition.builder()
                .interfaceCode(Optional.ofNullable(definitionInterfaceCode).orElse(interfaceCode))
                .supportObjectApiName(Optional.ofNullable(supportObjectApiName).orElse(objectApiName))
                .apiName(apiName)
                .label(label)
                .description(description)
                .handlerType(handlerType)
                .providerType(providerType)
                .defaultOrder(defaultOrder)
                .supportAsync(supportAsync)
                .enableDistributedTransaction(enableDistributedTransaction)
                .continueAfterApprovalSuccess(continueAfterApprovalSuccess)
                .enableGrayConfig(enableGrayConfig)
                .build();
    }

    public HandlerRuntimeConfig toConfig(String objectApiName, String interfaceCode) {
        return HandlerRuntimeConfig.builder()
                .interfaceCode(Optional.ofNullable(this.interfaceCode).orElse(interfaceCode))
                .bindingObjectApiName(Optional.ofNullable(supportObjectApiName).orElse(objectApiName))
                .handlerApiName(apiName)
                .active(!Boolean.FALSE.equals(isActive))
                .executeOrder(defaultOrder)
                .build();
    }
}
