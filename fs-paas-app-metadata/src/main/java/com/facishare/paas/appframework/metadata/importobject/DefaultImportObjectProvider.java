package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.DescribeLogicService;
import com.facishare.paas.appframework.metadata.dto.ImportObjectModule;
import com.facishare.paas.appframework.metadata.objects.ObjectListConfig;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.UDOBJ;

/**
 * create by zhao<PERSON> on 2021/03/16
 */
@Primary
@Component("defaultProvider")
public class DefaultImportObjectProvider implements ImportObjectProvider {
    @Autowired
    private DescribeLogicService describeLogicService;

    @Override
    public String getObjectModule() {
        return UDOBJ;
    }

    @Override
    public List<ImportObjectModule.ImportModule> getImportObjectModule(User user, ImportModuleContext arg) {
        List<IObjectDescribe> describeList = getDescribeList(user);
        return describeList.stream()
                .map(ImportObjectModule.ImportModule::of)
                .collect(Collectors.toList());
    }

    private List<IObjectDescribe> getDescribeList(User user) {
        List<IObjectDescribe> describeList = describeLogicService.findObjectsByTenantId(user.getTenantId(),
                true, true, false, true, ObjectListConfig.IMPORT);
        // 过滤功能权限
        return describeLogicService.filterDescribesWithActionCode(user,
                describeList, ObjectAction.BATCH_IMPORT.getActionCode());
    }
}
