package com.facishare.paas.appframework.metadata.dataconvert;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;

import java.util.List;

/**
 * Created by zhouwr on 2017/12/26
 */
public interface FieldDataConverter {
    /**
     * 转换器支持的字段类型
     *
     * @return
     */
    List<String> getSupportedFieldTypes();

    /**
     * 将指定字段的数据转成前端展现的格式
     *
     * @param objectData    数据集合
     * @param fieldDescribe 字段描述
     * @param user          用户信息
     * @deprecated This feature will be removed,
     * use {@link #convertFieldData(IObjectData, IFieldDescribe, DataConvertContext)}
     */
    @Deprecated
    default String convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, User user) {
        return convertFieldData(objectData, fieldDescribe, DataConvertContext.of(user));
    }

    String convertFieldData(IObjectData objectData, IFieldDescribe fieldDescribe, DataConvertContext context);
}
