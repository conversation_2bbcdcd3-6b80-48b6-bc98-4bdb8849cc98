package com.facishare.paas.appframework.metadata.publicobject.module;

import com.facishare.paas.appframework.metadata.publicobject.dto.PublicObjectJobParamDTO;
import com.facishare.paas.appframework.metadata.repository.model.MtPublicObjectJob;
import com.google.common.base.Converter;
import com.google.common.base.Strings;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2023/12/15
 */
public class PublicObjectJobConverter extends Converter<PublicObjectJobInfo, MtPublicObjectJob> {

    public static PublicObjectJobConverter getInstance() {
        return SingletonHolder.INSTANCE;
    }

    @Override
    protected MtPublicObjectJob doForward(PublicObjectJobInfo jobInfo) {
        MtPublicObjectJob resultJob = new MtPublicObjectJob();
        resultJob.setId(jobInfo.getJobId());
        resultJob.setObjectApiName(jobInfo.getObjectApiName());
        resultJob.setType(jobInfo.getType());
        resultJob.setStatus(jobInfo.getStatus());
        resultJob.setParam(jobInfo.getJobParamJsonString());
        resultJob.setResult(jobInfo.getJobResultJsonString());
        String createUserId = jobInfo.getCreateUserId();
        if (!Strings.isNullOrEmpty(createUserId)) {
            resultJob.setCreateBy(createUserId);
        }
        String tenantId = jobInfo.getUpstreamTenantId();
        if (!Strings.isNullOrEmpty(tenantId)) {
            resultJob.setTenantId(tenantId);
        }
        return resultJob;
    }

    @Override
    protected PublicObjectJobInfo doBackward(MtPublicObjectJob job) {
        PublicObjectJobInfo result = new PublicObjectJobInfo();
        result.setJobId(job.getId());
        result.setObjectApiName(job.getObjectApiName());
        result.setType(job.getType());
        result.setStatus(job.getStatus());
        result.setJobParam(PublicObjectJobParamDTO.fromJsonString(job.getParam()));
        result.setJobResult(PublicObjectJobResultInfo.fromJson(job.getResult()));
        String userId = job.getCreateBy();
        if (!Strings.isNullOrEmpty(userId)) {
            result.setCreateUserId(userId);
        }
        String tenantId = job.getTenantId();
        if (!Strings.isNullOrEmpty(tenantId)) {
            result.setUpstreamTenantId(tenantId);
        }
        return result;
    }

    private static class SingletonHolder {
        private static final PublicObjectJobConverter INSTANCE = new PublicObjectJobConverter();
    }
}
