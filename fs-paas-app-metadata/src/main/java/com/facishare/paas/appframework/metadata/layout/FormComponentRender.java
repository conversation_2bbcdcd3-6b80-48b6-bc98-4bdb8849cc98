package com.facishare.paas.appframework.metadata.layout;

import com.facishare.crm.userdefobj.DefObjConstants;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.RequestUtil;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.component.SummaryKeyComponentInfo;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.describe.Payment;
import com.facishare.paas.metadata.impl.ObjectData;
import com.facishare.paas.metadata.impl.describe.PaymentFieldDescribe;
import com.facishare.paas.metadata.ui.layout.IFieldSection;
import com.facishare.paas.metadata.ui.layout.IFormField;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.facishare.crm.userdefobj.DefObjConstants.*;
import static com.facishare.paas.appframework.metadata.ObjectDataExt.*;
import static com.facishare.paas.common.util.UdobjConstants.*;
import static com.facishare.paas.metadata.api.DBRecord.OUT_OWNER;
import static com.facishare.paas.metadata.api.describe.IFieldType.*;
import static com.facishare.paas.metadata.ui.layout.ILayout.DETAIL_LAYOUT_TYPE;
import static com.facishare.paas.metadata.ui.layout.ILayout.LIST_LAYOUT_TYPE;

/**
 * Created by zhouwr on 2018/1/30
 */
@Slf4j
public class FormComponentRender {

    private static final String[] UN_SUPPORT_FIELD_TYPES_IF_OUT_USER = new String[]{DEPARTMENT, DEPARTMENT_MANY, EMPLOYEE, EMPLOYEE_MANY, BIG_FILE_ATTACHMENT};
    private User user;

    private ObjectDescribeExt describeExt;

    private List<FormComponentExt> formComponentExtList;

    private List<SummaryKeyComponentInfo> summaryKeyComponentInfos;

    private List<FormTable> formTableList;

    private String layoutType;

    private String recordType;

    private IObjectData data;

    private FunctionPrivilegeService functionPrivilegeService;

    private DescribeLogicService objectDescribeService;

    private Collection<String> unauthorizedFields;

    private String dbLayoutType;

    private List<IFormField> orderFormFieldList;

    private final Set<String> needRemoveFieldNames = Sets.newHashSet();

    @Builder
    private FormComponentRender(User user, ObjectDescribeExt describeExt, List<FormComponentExt> formComponentExtList,
                                List<SummaryKeyComponentInfo> summaryKeyComponentInfos, List<FormTable> formTableList,
                                String layoutType, String recordType, IObjectData data, FunctionPrivilegeService functionPrivilegeService,
                                DescribeLogicService objectDescribeService, Collection<String> unauthorizedFields,
                                String dbLayoutType, List<IFormField> orderFormFieldList) {
        this.user = user;
        this.describeExt = describeExt;
        this.formComponentExtList = formComponentExtList;
        this.summaryKeyComponentInfos = summaryKeyComponentInfos;
        this.formTableList = formTableList;
        this.layoutType = layoutType;
        this.recordType = recordType;
        this.data = data;
        this.functionPrivilegeService = functionPrivilegeService;
        this.objectDescribeService = objectDescribeService;
        this.unauthorizedFields = unauthorizedFields;
        this.dbLayoutType = dbLayoutType;
        this.orderFormFieldList = orderFormFieldList;
    }

    public void render() {
        StopWatch stopWatch = StopWatch.create("renderForm");
        formComponentExtList = CollectionUtils.nullToEmpty(formComponentExtList);
        formTableList = CollectionUtils.nullToEmpty(formTableList);
        summaryKeyComponentInfos = CollectionUtils.nullToEmpty(summaryKeyComponentInfos);

        fillAllFields();
        stopWatch.lap("fillAllFields");
        removeInvisibleField();
        stopWatch.lap("removeInvisibleField");
        removeUnsupportedFields(user);
        stopWatch.lap("removeUnsupportedFields");
        adjustFieldRenderType();
        stopWatch.lap("adjustFieldRenderType");

        switch (layoutType) {
            case LAYOUT_TYPE_EDIT:
                resetRequiredField();
                setWritableForRequiredField();
                resetReadOnlyField();
                removeSystemField();
                removeFieldsByLayoutType();
                removeFieldByTypes();
                //TODO:set AccountObj name readOnly
                removeMasterDetailFields();
                removeSpecialFieldsForEditPage();
                removePaymentField();
                removeSignInField();
                dealWithPayAmountReadonly();
                removeFieldsByDetailObj();
                removeUnSupportFieldIfOutUser(user);
                break;
            case LAYOUT_TYPE_ADD:
                resetRequiredField();
                setWritableForRequiredField();
                resetReadOnlyField();
                removeSystemField();
                removeFieldsByLayoutType();
                removeFieldByTypes();
                removeSpecialFieldsForAddPage();
                removePaymentField();
                removeSignInField();
                dealWithPayAmountReadonly();
                removeOwnerAndDepartmentFieldIfDetailObj();
                removeFieldsByDetailObj();
                removeUnSupportFieldIfOutUser(user);
                break;
            case DETAIL_LAYOUT_TYPE:
                removeSignOutField();
                break;
            case LIST_LAYOUT_TYPE:
                removeFieldsByLayoutType();
                stopWatch.lap("removeFieldsByLayoutType");
                break;
        }

        addDisplayNameForRelatedField();
        stopWatch.lap("addDisplayNameForRelatedField");
        removeSpecialFieldsForAllPage();
        stopWatch.lap("removeSpecialFieldsForAllPage");
        removeFields();
        stopWatch.lap("removeEmptyFieldSections");
        removeEmptyFieldSections();
        stopWatch.lap("removeEmptyFieldSections");
        stopWatch.logSlow(100);
    }

    private void removeFields() {
        if (CollectionUtils.notEmpty(needRemoveFieldNames)) {
            formComponentExtList.forEach(formComponentExt -> formComponentExt.removeFields(needRemoveFieldNames));
            formTableList.forEach(formTable -> formTable.removeFields(needRemoveFieldNames));
        }
    }

    private void appendNeedRemoveFields(Collection<String> fieldNames) {
        if (CollectionUtils.notEmpty(fieldNames)) {
            needRemoveFieldNames.addAll(fieldNames);
        }
    }

    private void appendNeedRemoveFieldsByFieldType(Set<String> fieldTypes) {
        describeExt.getFieldByTypes(fieldTypes).stream()
                .map(IFieldDescribe::getApiName)
                .forEach(needRemoveFieldNames::add);
    }

    private void fillAllFields() {
        if (Strings.isNullOrEmpty(recordType) && LIST_LAYOUT_TYPE.equals(layoutType)) {
            //如果容器中没有表格组件，则不进行排序操作，按照原逻辑处理
            if (CollectionUtils.empty(formTableList)) {
                formComponentExtList.get(0).fillAllFields(describeExt);
            } else {
                formComponentExtList.get(0).orderFormFields(orderFormFieldList, describeExt);
            }
        }
    }

    private void removeEmptyFieldSections() {
        List<String> groupApiNameList = Lists.newArrayList();
        if (CollectionUtils.notEmpty(formTableList)) {
            //获取表单组件(没有apiName为form_component的组件则那布局中第一个表单组件)
            FormComponentExt formComponentExt = formComponentExtList.stream()
                    .filter(x -> ComponentExt.DETAIL_INFO_COMPONENT_NAME.equals(x.getName()))
                    .findFirst().orElse(formComponentExtList.stream().findFirst().orElse(null));
            //表单组件不为空并且其下的分组不为空
            if (Objects.nonNull(formComponentExt) && CollectionUtils.notEmpty(formComponentExt.getFieldSections())) {
                List<IFieldSection> notEmptyFieldSection = formComponentExt.getFieldSections().stream()
                        .filter(x -> CollectionUtils.notEmpty(x.getFields())).collect(Collectors.toList());
                //如果所有分组中的字段都为空，则保留第一个分组，移除其他分组，并在最后的删除空分组操作中排除该组件
                if (CollectionUtils.empty(notEmptyFieldSection)) {
                    groupApiNameList.add(formComponentExt.getName());
                    formComponentExt.removeSectionExcludeParamList(Lists.newArrayList(formComponentExt.getFieldSections().get(0).getName()));
                }
            }
        }
        formComponentExtList.stream().filter(x -> !groupApiNameList.contains(x.getName())).forEach(FormComponentExt::removeEmptySection);
    }

    private void removeSpecialFieldsForAllPage() {
        String[] toRemove = {EMBEDDED_OBJECT_LIST, GROUP};
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
            appendNeedRemoveFieldsByFieldType(Sets.newHashSet(toRemove));
            return;
        }
        formComponentExtList.forEach(formComponentExt -> formComponentExt.removeFieldByTypes(describeExt, toRemove));
        formTableList.forEach(formTable -> formTable.removeFieldByTypes(describeExt, toRemove));
    }

    private void removeSpecialFieldsForAddPage() {
        HashSet<String> toRemove = Sets.newHashSet(OWNER_DEPARTMENT);
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
            appendNeedRemoveFields(toRemove);
            return;
        }
        formComponentExtList.forEach(formComponentExt -> formComponentExt.removeFields(toRemove));
        formTableList.forEach(formTable -> formTable.removeFields(toRemove));
    }

    private void removeSpecialFieldsForEditPage() {
        HashSet<String> toRemove = Sets.newHashSet(OWNER, OWNER_DEPARTMENT, DATA_OWN_DEPARTMENT, DATA_OWN_ORGANIZATION);
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
            appendNeedRemoveFields(toRemove);
            return;
        }
        formComponentExtList.forEach(formComponentExt -> formComponentExt.removeFields(toRemove));
        formTableList.forEach(formComponentExt -> formComponentExt.removeFields(toRemove));
    }

    private void removeMasterDetailFields() {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
            appendNeedRemoveFieldsByFieldType(Sets.newHashSet(MASTER_DETAIL));
            return;
        }
        formComponentExtList.forEach(formComponentExt -> formComponentExt.removeFieldByTypes(describeExt, MASTER_DETAIL));
        formTableList.forEach(formTable -> formTable.removeFieldByTypes(describeExt, MASTER_DETAIL));
    }

    private void resetReadOnlyField() {
        Set<String> readOnlyFields = getReadOnly();
        formComponentExtList.forEach(formComponentExt -> formComponentExt.setReadOnly(readOnlyFields, true));
        formTableList.forEach(formTable -> formTable.setReadOnly(readOnlyFields, true));
    }

    private void resetRequiredField() {
        formComponentExtList.forEach(formComponentExt -> formComponentExt.setRequired(describeExt));
        formTableList.forEach(formTable -> formTable.setRequired(describeExt));
    }

    private void adjustFieldRenderType() {
        formComponentExtList.forEach(formComponentExt -> formComponentExt.adjustFieldRenderType(describeExt.getApiName()));
        formTableList.forEach(formTable -> formTable.adjustFieldRenderType(describeExt.getApiName()));
    }

    private void removeOwnerAndDepartmentFieldIfDetailObj() {
        if (describeExt.isSlaveObject()) {
            HashSet<String> removeFieldNames = Sets.newHashSet(OWNER, OWNER_DEPARTMENT, DATA_OWN_DEPARTMENT, DATA_OWN_ORGANIZATION);
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
                appendNeedRemoveFields(removeFieldNames);
                return;
            }
            formComponentExtList.forEach(formComponentExt -> formComponentExt.removeFields(removeFieldNames));
            formTableList.forEach(formTable -> formTable.removeFields(removeFieldNames));
        }
    }

    private void removeFieldsByDetailObj() {
        if (!describeExt.isSlaveObject()) {
            return;
        }
        Set<String> toRemove = Sets.newHashSet(OUT_OWNER, PARTNER_ID);
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
            appendNeedRemoveFields(toRemove);
            return;
        }
        formComponentExtList.forEach(formComponentExt -> formComponentExt.removeFields(toRemove));
        formTableList.forEach(formTable -> formTable.removeFields(toRemove));
    }

    private void removeFieldByTypes() {
        Set<String> toRemoveTypes = Sets.newHashSet();
        toRemoveTypes.addAll(invisibleFieldTypeList4AddOrUpdateLayoutFormObjectMap.getOrDefault(UDOBJ, Collections.emptySet()));
        String[] fieldTypes = toRemoveTypes.toArray(new String[toRemoveTypes.size()]);

        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
            appendNeedRemoveFieldsByFieldType(Sets.newHashSet(fieldTypes));
            return;
        }
        formComponentExtList.forEach(formComponentExt -> formComponentExt.removeFieldByTypes(describeExt, fieldTypes));
        formTableList.forEach(formTable -> formTable.removeFieldByTypes(describeExt, fieldTypes));
    }

    private void removeFieldsByLayoutType() {
        Set<String> toRemoveFields = Sets.newHashSet();
        if (layoutType.equals(LAYOUT_TYPE_EDIT)) {
            toRemoveFields.addAll(invisibleFieldNameListForEditLayout.getOrDefault(UDOBJ, Collections.emptySet()));
            toRemoveFields.addAll(invisibleFieldNameListForEditLayout.getOrDefault(describeExt.getApiName(), Collections.emptySet()));
        } else if (layoutType.equals(LAYOUT_TYPE_ADD)) {
            toRemoveFields.addAll(DefObjConstants.getInvisibleFieldNameListForAddLayout(user.getTenantId(), describeExt.getApiName()));
        } else if (layoutType.equals(LAYOUT_TYPE_LIST)) {
            toRemoveFields.addAll(invisibleFieldNameListForListLayout.getOrDefault(UDOBJ, Collections.emptySet()));
            toRemoveFields.addAll(invisibleFieldNameListForListLayout.getOrDefault(describeExt.getApiName(), Collections.emptySet()));
        }
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
            appendNeedRemoveFields(toRemoveFields);
            return;
        }

        formComponentExtList.forEach(formComponentExt -> formComponentExt.removeFields(toRemoveFields));
        formTableList.forEach(formTable -> formTable.removeFields(toRemoveFields));
    }

    private void setWritableForRequiredField() {
        if (describeExt.isSFAObject()) {
            Set<String> requiredFields = describeExt.stream()
                    .filter(x -> x.isRequired())
                    .map(x -> x.getApiName()).collect(Collectors.toSet());

            //SalesOrder的 account_id 只读
            if (describeExt.getApiName().equals(ObjectAPINameMapping.SalesOrder.getApiName())) {
                requiredFields.remove("account_id");
            }

            formComponentExtList.forEach(formComponentExt -> formComponentExt.setReadOnly(requiredFields, false));
            formTableList.forEach(formTable -> formTable.setReadOnly(requiredFields, false));
        }
    }

    private void removePaymentField() {
        describeExt.getPaymentFieldDescribe().ifPresent(x -> {
                    Set<String> toRemove = Sets.newHashSet(
                            x.getPayStatusFieldApiName(), x.getPayTimeFieldApiName(), x.getPayTypeFieldApiName());
                    if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
                        appendNeedRemoveFields(toRemove);
                        return;
                    }
                    formComponentExtList.forEach(formComponentExt -> formComponentExt.removeFields(toRemove));
                    formTableList.forEach(formTable -> formTable.removeFields(toRemove));
                }
        );
    }

    private void removeSignInField() {
        describeExt.getSignInFieldDescribe().ifPresent(x -> {
                    Set<String> toRemove = Sets.newHashSet(
                            x.getSignInInfoListFieldApiName(), x.getSignInStatusFieldApiName(),
                            x.getSignInTimeFieldApiName(), x.getSignOutStatusFieldApiName(),
                            x.getVisitStatusFieldApiName(), x.getIntervalFieldApiName(),
                            x.getSignInLocationFieldApiName(), x.getSignOutLocationFieldApiName(),
                            x.getSignOutTimeFieldApiName());
                    if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
                        appendNeedRemoveFields(toRemove);
                        return;
                    }
                    formComponentExtList.forEach(formComponentExt -> formComponentExt.removeFields(toRemove));
                    formTableList.forEach(formTable -> formTable.removeFields(toRemove));
                }
        );
    }

    private void removeSignOutField() {
        describeExt.getSignInFieldDescribe().ifPresent(x -> {
            HashSet<String> removeFieldNames = Sets.newHashSet(x.getSignInInfoListFieldApiName());
            HashSet<String> removeFieldNames1 = Sets.newHashSet(
                    x.getSignOutStatusFieldApiName(),
                    x.getSignOutLocationFieldApiName(),
                    x.getSignOutTimeFieldApiName(),
                    x.getIntervalFieldApiName());
            formComponentExtList.forEach(formComponentExt -> {
                formComponentExt.removeFields(removeFieldNames);
                if (Boolean.FALSE.equals(x.getIsEnableSignOut())) {
                    formComponentExt.removeFields(removeFieldNames1);
                }
            });
            formTableList.forEach(formTable -> {
                formTable.removeFields(removeFieldNames);
                if (Boolean.FALSE.equals(x.getIsEnableSignOut())) {
                    formTable.removeFields(removeFieldNames1);
                }
            });
        });
    }

    private void removeSystemField() {
        Set<String> toRemoveFields = Sets.newHashSet();
        toRemoveFields.addAll(systemFieldListFormObjectMap.getOrDefault(UDOBJ, Collections.emptySet()));

        formComponentExtList.forEach(formComponentExt -> {
            Set<String> fields = formComponentExt.stream().filter(x -> {
                Optional<IFieldDescribe> optional = describeExt.getFieldDescribeSilently(x.getFieldName());
                if (optional.isPresent()) {
                    IFieldDescribe fieldDescribe = optional.get();
                    if (fieldDescribe.getType().equals(TEXT) && ObjectDescribeExt.isAutoNumber(fieldDescribe)) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return true; //字段不存在需要删除
                }
            }).map(x -> x.getFieldName()).collect(Collectors.toSet());
            fields.addAll(toRemoveFields);
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
                appendNeedRemoveFields(fields);
                return;
            }
            formComponentExt.removeFields(fields);
        });

        formTableList.forEach(formTable -> {
            Set<String> fields = formTable.getFields().stream().filter(x -> {
                Optional<IFieldDescribe> optional = describeExt.getFieldDescribeSilently(x.getFieldName());
                if (optional.isPresent()) {
                    IFieldDescribe fieldDescribe = optional.get();
                    if (fieldDescribe.getType().equals(TEXT) && ObjectDescribeExt.isAutoNumber(fieldDescribe)) {
                        return true;
                    } else {
                        return false;
                    }
                } else {
                    return true; //字段不存在需要删除
                }
            }).map(x -> x.getFieldName()).collect(Collectors.toSet());
            fields.addAll(toRemoveFields);
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
                appendNeedRemoveFields(fields);
                return;
            }
            formTable.removeFields(fields);
        });
    }

    private Set<String> getReadOnly() {
        Set<String> readOnly = Sets.newHashSet();
        if (functionPrivilegeService != null) {
            readOnly.addAll(functionPrivilegeService.getReadonlyFields(user, describeExt.getApiName()));
        }
        describeExt.stream().filter(x -> FieldDescribeExt.of(x).isCountField() || FieldDescribeExt.of(x).isFormula())
                .forEach(x -> readOnly.add(x.getApiName()));
        //汇率、本位币、汇率版本只读
        readOnly.add(FieldDescribeExt.EXCHANGE_RATE_FIELD);
        readOnly.add(FieldDescribeExt.FUNCTIONAL_CURRENCY_FIELD);
        readOnly.add(FieldDescribeExt.EXCHANGE_RATE_VERSION_FIELD);
        //编辑情况下，合作伙伴只读，币种只读
        if (LAYOUT_TYPE_EDIT.equals(layoutType)) {
            readOnly.add(PARTNER_ID);
            //主从一起编辑的从的币种设为只读，没有灰度支持编辑币种的企业设为只读
            if (describeExt.isSlaveObjectCreateWithMaster()
                    || !AppFrameworkConfig.supportEditCurrency(user.getTenantId(), describeExt.getApiName())) {
                readOnly.add(FieldDescribeExt.CURRENCY_FIELD);
            }
        } else if (LAYOUT_TYPE_ADD.equals(layoutType)) {
            //主从一起新建的从的币种设为只读
            if (describeExt.isSlaveObjectCreateWithMaster()) {
                readOnly.add(FieldDescribeExt.CURRENCY_FIELD);
            }
        }
        return readOnly;
    }


    private void removeInvisibleField() {
        Set<String> invisibleFields = Sets.newHashSet();

        //只有详细信息需要过滤这些字段，顶部信息不过滤
        if (CollectionUtils.empty(formComponentExtList) || ComponentExt.of(formComponentExtList.get(0)).isFormType()) {
            invisibleFields.addAll(invisibleFieldListFormObjectMap.getOrDefault(UDOBJ, Collections.emptySet()));
            invisibleFields.addAll(invisibleFieldListFormObjectMap.getOrDefault(describeExt.getApiName(), Collections.emptySet()));
        }

        invisibleFields.addAll(getUnauthorizedFields());

        Set<String> unActiveFields = describeExt.stream()
                .filter(x -> !x.isActive())
                .map(IFieldDescribe::getApiName)
                .collect(Collectors.toSet());
        invisibleFields.addAll(unActiveFields);

        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
            appendNeedRemoveFields(invisibleFields);
            return;
        }

        formComponentExtList.forEach(formComponentExt -> {
            Set<String> deletedFields = formComponentExt.stream()
                    .filter(x -> !describeExt.containsField(x.getFieldName()))
                    .map(IFormField::getFieldName)
                    .collect(Collectors.toSet());
            deletedFields.addAll(invisibleFields);

            formComponentExt.removeFields(deletedFields);
        });

        formTableList.forEach(formTable -> {
            Set<String> deletedFields = formTable.getFields().stream()
                    .filter(x -> !describeExt.containsField(x.getFieldName()))
                    .map(IFormField::getFieldName)
                    .collect(Collectors.toSet());
            deletedFields.addAll(invisibleFields);
            formTable.removeFields(deletedFields);
        });

        summaryKeyComponentInfos.forEach(summaryKeyComponentInfo -> {
            Set<String> deletedFields = summaryKeyComponentInfo.getFieldSections()
                    .stream()
                    .filter(x -> !describeExt.containsField(x.getFieldName())
                            && StringUtils.isNotEmpty(x.getFieldName())
                            && !x.getFieldName().contains("__r."))
                    .map(IFormField::getFieldName)
                    .collect(Collectors.toSet());
            deletedFields.addAll(invisibleFields);
            summaryKeyComponentInfo.removeFields(deletedFields);
        });
    }

    private Collection<String> getUnauthorizedFields() {
        if (unauthorizedFields != null) {
            return unauthorizedFields;
        }
        if (functionPrivilegeService != null) {
            return functionPrivilegeService.getUnauthorizedFields(user, describeExt.getApiName());
        }
        return Collections.emptySet();
    }

    private void dealWithPayAmountReadonly() {
        Optional<PaymentFieldDescribe> fieldDescribe = describeExt.getPaymentFieldDescribe();
        if (!fieldDescribe.isPresent()) {
            return;
        }
        PaymentFieldDescribe paymentField = fieldDescribe.get();

        String payAmountFieldApiName = paymentField.getPayAmountFieldApiName();
        Boolean amountIsReadonly = paymentField.getAmountIsReadonly();
        if (Strings.isNullOrEmpty(payAmountFieldApiName)) {
            return;
        }

        HashSet<String> readOnlyField = Sets.newHashSet(payAmountFieldApiName);
        if (Boolean.TRUE.equals(amountIsReadonly)) {
            formComponentExtList.forEach(formComponentExt -> formComponentExt.setReadOnly(readOnlyField, true));
            formTableList.forEach(formTable -> formTable.setReadOnly(readOnlyField, true));
            return;
        }

        if (data == null) {
            return;
        }
        Object o = data.get(paymentField.getPayStatusFieldApiName());
        if (null == o) {
            return;
        }

        if (!Payment.PAY_STATUS_COMPLETE.equals(String.valueOf(o))) {
            return;
        }

        formComponentExtList.forEach(formComponentExt -> formComponentExt.setReadOnly(readOnlyField, true));
        formTableList.forEach(formTable -> formTable.setReadOnly(readOnlyField, true));
    }

    //给ObjectReference类型的FormField补充一个TargetDisplayName属性
    private void addDisplayNameForRelatedField() {
        if (objectDescribeService == null) {
            return;
        }

        StopWatch stopWatch = StopWatch.create("addDisplayNameForRelatedField");
        try {
            List<Tuple<String, IFormField>> refDetailFields = Lists.newArrayList();
            Set<String> apiNames = Sets.newHashSet();

            describeExt.getActiveReferenceFieldDescribes().forEach(x -> {
                formComponentExtList.forEach(formComponentExt -> formComponentExt.getField(x.getApiName()).ifPresent(y -> {
                    String targetApiName = x.getTargetApiName();
                    apiNames.add(targetApiName);
                    refDetailFields.add(Tuple.of(targetApiName, y));
                }));
                formTableList.forEach(formTable -> formTable.getField(x.getApiName()).ifPresent(y -> {
                    String targetApiName = x.getTargetApiName();
                    apiNames.add(targetApiName);
                    refDetailFields.add(Tuple.of(targetApiName, y));
                }));
            });
            stopWatch.lap("getRefFields");

            describeExt.getMasterDetailFieldDescribe().ifPresent(x -> {
                formComponentExtList.forEach(formComponentExt -> formComponentExt.getField(x.getApiName()).ifPresent(y -> {
                    String targetApiName = x.getTargetApiName();
                    apiNames.add(targetApiName);
                    refDetailFields.add(Tuple.of(targetApiName, y));
                }));
                formTableList.forEach(formTable -> formTable.getField(x.getApiName()).ifPresent(y -> {
                    String targetApiName = x.getTargetApiName();
                    apiNames.add(targetApiName);
                    refDetailFields.add(Tuple.of(targetApiName, y));
                }));
            });
            stopWatch.lap("getMasterFields");

            if (CollectionUtils.empty(apiNames)) {
                return;
            }

            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FORM_COMPONENT_RELATED_FIELD_DISPLAY_NAME_GRAY, user.getTenantId())) {
                Map<String, String> displayNames = objectDescribeService.queryDisplayNameByApiNames(user.getTenantId(), Lists.newArrayList(apiNames));
                stopWatch.lap("queryDisplayNameByApiNames");
                if (CollectionUtils.empty(displayNames)) {
                    return;
                }
                refDetailFields.forEach(x ->
                        x.getValue().set(TARGET_DISPLAY_NAME, displayNames.getOrDefault(x.getKey(), "")));
                stopWatch.lap("fillRefDisplayName");
                return;
            }

            List<IObjectDescribe> describes = objectDescribeService.findDescribeListWithoutFields(user.getTenantId(), apiNames);
            stopWatch.lap("findRefDescribes");
            if (CollectionUtils.empty(describes)) {
                return;
            }
            Map<String, IObjectDescribe> describeMap = describes.stream().collect(Collectors.toMap(x -> x.getApiName(), x -> x));
            refDetailFields.forEach(x -> {
                IObjectDescribe targetDescribe = describeMap.get(x.getKey());
                x.getValue().set(TARGET_DISPLAY_NAME, targetDescribe == null ? "" : targetDescribe.getDisplayName());
            });
            stopWatch.lap("fillRefDisplayName");
        } finally {
            stopWatch.logSlow(100);
        }
    }

    public void removeUnsupportedFields(User user) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
            if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_735) || user.isOutUser()) {
                appendNeedRemoveFields(Lists.newArrayList(IObjectData.PUBLIC_DATA_TYPE));
                appendNeedRemoveFieldsByFieldType(Sets.newHashSet(DIMENSION, DATA_VISIBILITY_RANGE));
            }
            if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_740)) {
                appendNeedRemoveFields(Lists.newArrayList(OUT_EMPLOYEE));
            }
            return;
        }
        formComponentExtList.forEach(formComponentExt -> {
            if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_735) || user.isOutUser()) {
                formComponentExt.removeFieldByTypes(DIMENSION, DATA_VISIBILITY_RANGE);
                formComponentExt.removeField(IObjectData.PUBLIC_DATA_TYPE);
            }
            if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_740)) {
                formComponentExt.removeFieldByTypes(OUT_EMPLOYEE);
            }
        });

        formTableList.forEach(formTable -> {
            if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_735) || user.isOutUser()) {
                formTable.removeFieldByTypes(DIMENSION, DATA_VISIBILITY_RANGE);
                formTable.removeFields(Sets.newHashSet(IObjectData.PUBLIC_DATA_TYPE));
            }
            if (RequestUtil.isMobileRequestBeforeVersion(RequestUtil.VERSION_740)) {
                formTable.removeFieldByTypes(OUT_EMPLOYEE);
            }
        });
    }

    private void removeUnSupportFieldIfOutUser(User user) {
        // 下游新建编辑页面屏蔽部门、部门多选、人员多选、大文件
        if (!user.isOutUser()) {
            return;
        }
        Set<String> fieldTypes = getUnSupportFieldTypesIfOutUser(user, describeExt.getApiName());
        String[] toRemove = CollectionUtils.empty(fieldTypes) ? UN_SUPPORT_FIELD_TYPES_IF_OUT_USER : fieldTypes.toArray(new String[0]);
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.FROM_COMPONENT_RENDER_REMOVE_FIELDS_GRAY, user.getTenantId())) {
            describeExt.getFieldByTypes(fieldTypes).stream()
                    .map(IFieldDescribe::getApiName)
                    .filter(it -> !Objects.equals(it, ObjectData.OWNER))
                    .forEach(needRemoveFieldNames::add);
            return;
        }
        formComponentExtList.forEach(formComponentExt -> formComponentExt.removeFieldByTypesExceptApiNameList(Lists.newArrayList(ObjectData.OWNER), toRemove));
        formTableList.forEach(formTable -> formTable.removeFieldByTypesExceptApiNameList(Lists.newArrayList(ObjectData.OWNER), toRemove));
    }

}
