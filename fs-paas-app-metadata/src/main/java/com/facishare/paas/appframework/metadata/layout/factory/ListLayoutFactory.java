package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.layout.LayoutStructure;
import com.facishare.paas.appframework.metadata.layout.LayoutTypes;
import com.facishare.paas.appframework.metadata.layout.component.ListComponentExt;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ui.layout.Layout;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

import static com.facishare.paas.appframework.metadata.ListLayoutExt.DEFAULT_LIST_LAYOUT;
import static com.facishare.paas.appframework.metadata.ListLayoutExt.LIST_LAYOUT;

/**
 * Created by zhaooju on 2022/6/27
 */
@Component
public class ListLayoutFactory extends BaseLayoutFactory {

    protected ListLayoutFactory(IComponentFactoryManager componentFactoryManager) {
        super(componentFactoryManager);
    }

    public ILayout generateDefaultLayout(User user, IObjectDescribe describe) {
        ILayout layout = new Layout();
        layout.setId(IdGenerator.get());

        IComponentFactory listComponentFactory = getComponentFactory(ListComponentExt.COMPONENT_TYPE_LIST);
        Context context = Context.of(user, null);
        IComponent component = listComponentFactory.createDefaultComponent(context, describe);
        layout.addComponent(component);

        layout.setRefObjectApiName(describe.getApiName());
        layout.setLayoutType(LIST_LAYOUT);
        layout.setName(DEFAULT_LIST_LAYOUT);
        layout.setDisplayName(I18N.text(I18NKey.DEFAULT_LIST_LAYOUT));
        layout.setPackage("CRM");
        layout.setLayoutDescription("");
        layout.setIsDefault(true);
        layout.setTenantId(describe.getTenantId());
        layout.setLayoutStructure(generateDefaultListLayoutStructure());

        layout.setCreatedBy(describe.getCreatedBy());
        layout.setCreateTime(describe.getCreateTime());
        layout.setLastModifiedBy(describe.getCreatedBy());
        layout.setLastModifiedTime(System.currentTimeMillis());
        layout.setVersion(0);
        addDefaultI18n(layout, describe.getTenantId(), I18NKey.DEFAULT_LIST_LAYOUT);

        return layout;
    }

    private Map<String, Object> generateDefaultListLayoutStructure() {
        Map<String, Object> layoutStructure = Maps.newLinkedHashMap();

        Map<String, Object> row1 = Maps.newLinkedHashMap();
        Map<String, String> columnItem = Maps.newHashMap();
        columnItem.put("width", "100%");
        List<Map<String, String>> columns = Lists.newArrayList(columnItem);
        row1.put(LayoutStructure.COLUMNS, columns);
        List<List<String>> components = Lists.newArrayList();
        components.add(Lists.newArrayList());
        row1.put(LayoutStructure.COMPONENTS, components);

        Map<String, Object> row2 = Maps.newLinkedHashMap();
        row2.put(LayoutStructure.COLUMNS, columns);
        List<List<String>> componentsList = Lists.newArrayList();
        componentsList.add(Lists.newArrayList(ListComponentExt.LIST_COMPONENT));
        row2.put(LayoutStructure.COMPONENTS, componentsList);

        layoutStructure.put(LayoutStructure.LAYOUT, Lists.newArrayList(row1, row2));
        return layoutStructure;
    }

    @Override
    public String supportLayoutType() {
        return LayoutTypes.LIST_LAYOUT;
    }

    @Override
    public ILayout generateDefaultLayout(Context context, IObjectDescribe describe) {
        return generateDefaultLayout(context.getUser(), describe);
    }
}
