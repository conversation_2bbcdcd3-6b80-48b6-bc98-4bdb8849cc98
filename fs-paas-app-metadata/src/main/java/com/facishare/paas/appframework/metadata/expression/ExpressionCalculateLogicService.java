package com.facishare.paas.appframework.metadata.expression;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.IRule;

import java.util.List;
import java.util.Map;

/**
 * 计算业务逻辑封装接口
 * <p>
 * Created by liyiguang on 2018/3/25.
 */
public interface ExpressionCalculateLogicService {

    /**
     * 表达式编译检查
     *
     * @param expressionDTO
     */
    void compileCheck(String tenantId, ExpressionDTO expressionDTO);

    void compileCheck(String tenantId, ExpressionDTO expressionDTO, IObjectDescribe objectDescribe);

    /**
     * 表达式试算
     *
     * @param tenantId      企业id
     * @param expressionDTO 表达式信息
     * @param dataId        数据id
     * @return 计算结果
     */
    String expressionDebug(String tenantId, ExpressionDTO expressionDTO, String dataId);

    /**
     * 批量计算验证规则
     *
     * @param dataList
     * @param rules
     */
    RuleCalculateResult validateRules(IObjectDescribe describe, List<IObjectData> dataList, List<IRule> rules);

    /**
     * 批量计算验证规则
     *
     * @param describe
     * @param dataList
     * @param rules
     * @param masterData
     * @return
     */
    RuleCalculateResult validateRules(IObjectDescribe describe, List<IObjectData> dataList, List<IRule> rules, IObjectData masterData);

    /**
     * 批量计算默认值，计算性字段
     *
     * @param describe
     * @param dataList
     * @param fields
     */
    void bulkCalculate(IObjectDescribe describe, List<IObjectData> dataList, List<IFieldDescribe> fields);

    /**
     * 批量计算计算字段
     *
     * @param describe
     * @param dataList
     */
    void bulkCalculate(IObjectDescribe describe, List<IObjectData> dataList);

    /**
     * 批量计算计算字段
     *
     * @param describe
     * @param dataList
     * @param masterData
     */
    void bulkCalculateWithMasterData(IObjectDescribe describe, List<IObjectData> dataList, IObjectData masterData);

    /**
     * 批量计算计算字段
     *
     * @param describe
     * @param dataList
     * @param fields
     * @param masterData
     */
    void bulkCalculateWithMasterData(IObjectDescribe describe, List<IObjectData> dataList, List<IFieldDescribe> fields, IObjectData masterData);

    void bulkCalculateWithDependentData(IObjectDescribe describe, List<IObjectData> dataList,
                                        List<IFieldDescribe> fields, Map<String, List<IObjectData>> dependentDataMap, boolean ignoreInvalidVariable);

    void bulkCalculateWithDependentDataAndDescribe(IObjectDescribe describe,
                                                   List<IObjectData> dataList,
                                                   List<IFieldDescribe> fields,
                                                   Map<String, List<IObjectData>> dependentDataMap,
                                                   boolean ignoreInvalidVariable,
                                                   Map<String, IObjectDescribe> describeMap);

    /**
     * 字段更新批量计算
     *
     * @param describe
     * @param fieldUpdateDTOList
     * @param data
     */
    void bulkCalculate(IObjectDescribe describe, List<FieldUpdateDTO> fieldUpdateDTOList, IObjectData data, Map<String, ExpressionDTO.FormVariableDTO> formData);

    /**
     * 批量计算表达式
     *
     * @param describe
     * @param dataList
     * @param expressionList
     */
    void bulkCalculateWithExpression(IObjectDescribe describe, List<IObjectData> dataList, List<SimpleExpression> expressionList);

    void bulkCalculateWithExpression(IObjectDescribe describe, List<IObjectData> dataList, List<SimpleExpression> expressionList, boolean calculateFormulaWhenFindData);

    void bulkCalculateWithExpressionWithMasterData(IObjectDescribe describe,
                                                   List<IObjectData> dataList,
                                                   List<SimpleExpression> expressionList,
                                                   Map<String, Object> extData,
                                                   IObjectData masterData);

    void bulkCalculateWithExpressionWithMasterData(IObjectDescribe describe,
                                                   List<IObjectData> dataList,
                                                   List<SimpleExpression> expressionList,
                                                   Map<String, Object> extData,
                                                   IObjectData masterData,
                                                   boolean calculateFormulaWhenFindData);

    /**
     * 计算表达式
     *
     * @param describe       对象描述
     * @param objectData     对象数据
     * @param extData        自定义变量数据
     * @param expressionList 表达式列表
     * @return 表达式的计算结果（key：表达式id，value：计算结果）
     */
    Map<String, Object> calculateWithExpression(IObjectDescribe describe, IObjectData objectData,
                                                Map<String, Object> extData, List<SimpleExpression> expressionList);

    void bulkCalculateWithCalculateDataInfo(List<CalculateDataInfo> calculateDataInfoList);

    /**
     * 批量数据 - 计算表达式
     *
     * @param describe       对象描述
     * @param objectDataContextList     对象数据上下文
     * @param expressionList 表达式列表
     * @return 表达式的计算结果（key：表达式id，value：计算结果）
     */
    Map<String, Map<String, Object>> bulkDataCalculateWithExpression(IObjectDescribe describe, List<CalculateDataContext> objectDataContextList,
                                                        List<SimpleExpression> expressionList);
}
