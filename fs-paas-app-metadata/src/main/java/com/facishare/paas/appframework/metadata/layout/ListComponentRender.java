package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.ObjectAction;
import com.facishare.paas.appframework.config.OptionalFeaturesService;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.license.LicenseService;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.dto.scene.IScene;
import com.facishare.paas.appframework.metadata.layout.component.*;
import com.facishare.paas.appframework.metadata.layout.factory.ViewComponentFactory;
import com.facishare.paas.appframework.metadata.layout.resource.LayoutResourceService;
import com.facishare.paas.appframework.metadata.layout.resource.module.LayoutView;
import com.facishare.paas.appframework.privilege.FunctionPrivilegeService;
import com.facishare.paas.metadata.api.ISelectOption;
import com.facishare.paas.metadata.api.describe.SelectOne;
import com.facishare.paas.metadata.ui.layout.IButton;
import com.google.common.base.Strings;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Table;
import lombok.Builder;

import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Created by zhaooju on 2022/11/16
 */
public class ListComponentRender {
    private final ListComponentExt listComponentExt;

    private final User user;
    private final ObjectDescribeExt describeExt;

    private final SceneLogicService sceneLogicService;
    private final CustomButtonService customButtonService;
    private final ButtonLogicService buttonLogicService;
    private final LicenseService licenseService;
    private final OptionalFeaturesService optionalFeaturesService;
    private final FunctionPrivilegeService functionPrivilegeService;

    private LayoutResourceService layoutResourceService;
    private ViewComponentFactory viewComponentFactory;

    private final boolean ignoreButton;
    private final boolean isMobileLayout;
    private final boolean isDesigner;
    private final String renderPageType;

    @Builder
    private ListComponentRender(ListComponentExt listComponentExt, User user, ObjectDescribeExt describeExt, SceneLogicService sceneLogicService,
                                CustomButtonService customButtonService, ButtonLogicService buttonLogicService,
                                LicenseService licenseService, OptionalFeaturesService optionalFeaturesService, FunctionPrivilegeService functionPrivilegeService,
                                LayoutResourceService layoutResourceService, ViewComponentFactory viewComponentFactory,
                                boolean ignoreButton, boolean isMobileLayout, boolean isDesigner, String renderPageType) {
        this.functionPrivilegeService = functionPrivilegeService;
        this.ignoreButton = ignoreButton;
        this.listComponentExt = listComponentExt;
        this.user = user;
        this.describeExt = describeExt;
        this.sceneLogicService = sceneLogicService;
        this.customButtonService = customButtonService;
        this.buttonLogicService = buttonLogicService;
        this.licenseService = licenseService;
        this.optionalFeaturesService = optionalFeaturesService;
        this.layoutResourceService = layoutResourceService;
        this.viewComponentFactory = viewComponentFactory;
        this.isMobileLayout = isMobileLayout;
        this.isDesigner = isDesigner;
        this.renderPageType = renderPageType;
    }

    public void render() {
        renderViewInfo();
        renderScene();
        if (!ignoreButton) {
            renderButton();
        }
        renderFiltersInfo();

        filterListComponentInfoByRenderPageType();
    }

    /**
     * renderPageType 处理列表页布局组件
     */
    private void filterListComponentInfoByRenderPageType() {
        // 设计器，且 renderPageType 为空，保持原样不动
        if (isDesigner && Strings.isNullOrEmpty(renderPageType)) {
            return;
        }
        // renderPageType 为空 或 list，不需要处理
        if (Strings.isNullOrEmpty(renderPageType) || IComponentInfo.PAGE_TYPE_LIST.equals(renderPageType)) {
            return;
        }
        // 场景
        List<IScenesComponentInfo> sceneInfo = listComponentExt.getSceneInfoOrDefault();
        // 选数据列表的场景，只支持 drop_down（下拉）样式
        listComponentExt.resetSceneInfos(appendComponentInfoIfAbsent(listComponentExt, sceneInfo,
                it -> it.setRenderType(IListComponentInfo.RENDER_TYPE_DROP_DOWN)));
        // 快速筛选
        List<IFiltersComponentInfo> filtersInfo = listComponentExt.getFiltersComponentInfo();
        listComponentExt.resetFiltersInfos(appendComponentInfoIfAbsent(listComponentExt, filtersInfo, it -> {
        }));
    }

    private <T extends IComponentInfo> List<T> appendComponentInfoIfAbsent(ListComponentExt listComponentExt,
                                                                           List<T> componentInfo, Consumer<T> consumer) {
        // 启用了选数据列表，直接返回
        if (listComponentExt.isEnableSelectedLayout()) {
            return Lists.newArrayList(componentInfo);
        }
        // 如果是设计器的转化接口，优先使用历史配置
        if (isDesigner() && componentInfo.stream().anyMatch(it -> Objects.equals(renderPageType, it.getPageType()))) {
            return Lists.newArrayList(componentInfo);
        }
        // 防止出现两组 renderPageType 相同的 componentInfo
        List<T> result = componentInfo.stream()
                .filter(it -> !Objects.equals(renderPageType, it.getPageType()))
                .collect(Collectors.toList());
        componentInfo.stream()
                .filter(it -> IComponentInfo.PAGE_TYPE_LIST.equals(it.getPageType()))
                .findFirst()
                .ifPresent(it -> {
                    T copyComponentInfo = (T) it.copy();
                    copyComponentInfo.setPageType(renderPageType);
                    consumer.accept(copyComponentInfo);
                    result.add(copyComponentInfo);
                });
        return result;
    }

    private void renderFiltersInfo() {
        List<IFiltersComponentInfo> filtersComponentInfo = listComponentExt.getFiltersComponentInfo();
        listComponentExt.resetFiltersInfos(filtersComponentInfo);
    }

    private void renderScene() {
        List<IScene> scenes = sceneLogicService.findScenes(describeExt.getApiName(), user, null).stream()
                .filter(IScene::isActive)
                .collect(Collectors.toList());

        // 按 layout 中的配置，对场景进行排序和隐藏
        List<IScenesComponentInfo> sceneInfos = listComponentExt.getSceneInfoOrDefault().stream()
                .peek(sceneInfo -> {
                    List<String> order = CollectionUtils.sortByGivenOrder(scenes, sceneInfo.getOrder(), IScene::getApiName).stream()
                            .map(IScene::getApiName)
                            .filter(it -> !CollectionUtils.nullToEmpty(sceneInfo.getHidden()).contains(it))
                            .collect(Collectors.toList());
                    sceneInfo.setOrder(order);
                }).collect(Collectors.toList());
        listComponentExt.resetSceneInfos(sceneInfos);
    }

    private void renderViewInfo() {
        List<IViewComponentInfo> viewInfos = listComponentExt.getViewInfos();

        List<LayoutView> layoutViewResource = layoutResourceService.findLayoutViewResource(user, describeExt, LayoutTypes.LIST_LAYOUT);
        Map<String, IViewComponentInfo> componentInfoMap = viewInfos.stream()
                .collect(Collectors.toMap(this::getViewName, Function.identity(), (x, y) -> y));
        List<IViewComponentInfo> resultViewInfos = Lists.newArrayList();
        for (LayoutView layoutView : layoutViewResource) {
            IViewComponentInfo viewComponentInfo = componentInfoMap.get(layoutView.getApiName());
            if (Objects.nonNull(viewComponentInfo)) {
                resultViewInfos.add(viewComponentInfo);
                continue;
            }
            // 定义过地图视图,不需要再补充地图视图
            if (listComponentExt.isDefineViewInfo(layoutView.getApiName())) {
                continue;
            }
            IViewComponentInfo componentInfo = viewComponentFactory.create(user, describeExt, layoutView);
            if (Objects.nonNull(componentInfo)) {
                resultViewInfos.add(componentInfo);
            }
        }
        listComponentExt.resetViewInfos(resultViewInfos);
        // 处理树形视图的初始化
        appendTreeViewIfAbsent(listComponentExt);

        syncMapViewFromDescribe(listComponentExt);

        restCardViewFromDescribe(listComponentExt);

        // 处理 is_show 标记,兼容历史数据
        List<IViewComponentInfo> infos = listComponentExt.getViewInfos().stream()
                // 记录一下组件中定义过哪些视图
                .peek(it -> listComponentExt.defineViewInfo(it.getName()))
                .peek(it -> {
                    // 此处为兼容代码,要求类表页视图必须展示
                    // 原因:815 灰度期间第一次编辑默认列表页布局,会导致列表视图被清空
                    if (IViewComponentInfo.LIST_VIEW.equals(it.getName())) {
                        it.setIsShow(true);
                    }
                })
                .filter(IViewComponentInfo::isShow)
                .peek(it -> it.setIsShow(true))
                .collect(Collectors.toList());
        listComponentExt.resetViewInfos(infos);
    }

    private void restCardViewFromDescribe(ListComponentExt listComponentExt) {
        listComponentExt.getViewInfoByName(IViewComponentInfo.CARD_VIEW).ifPresent(cardView -> {
            Map cardLayout = cardView.get("card_layout", Map.class);
            if (Objects.isNull(cardLayout)) {
                return;
            }
            NewTableComponentExt newTableComponentExt = new NewTableComponentExt(cardLayout);
            Set<String> unauthorizedFields = functionPrivilegeService.getUnauthorizedFields(user, describeExt.getApiName());
            newTableComponentExt.formatNewLayoutByDescribe(describeExt, unauthorizedFields, user, true);
        });
    }

    private String getViewName(IViewComponentInfo viewComponentInfo) {
        String viewName = viewComponentInfo.getName();
        Map<String, String> viewNameMap = ListLayoutExt.VIEW_NAME_MAP;
        return viewNameMap.getOrDefault(viewName, viewName);
    }

    private void appendTreeViewIfAbsent(ListComponentExt listComponentExt) {
        if (listComponentExt.isDefineViewInfo(IViewComponentInfo.TREE_VIEW)) {
            return;
        }
        List<IViewComponentInfo> viewInfos = listComponentExt.getViewInfos();
        boolean includeTreeView = viewInfos.stream().anyMatch(it -> IViewComponentInfo.TREE_VIEW.equals(it.getName()));
        if (includeTreeView) {
            return;
        }
        // 直接通过配置判断是否支持树形视图
        if (describeExt.isSupportTreeViewObject()) {
            viewInfos.add(ViewComponentInfo.treeView());
            listComponentExt.resetViewInfos(viewInfos);
        }
    }

    private void syncMapViewFromDescribe(ListComponentExt listComponentExt) {
        listComponentExt.getViewInfoByName(IViewComponentInfo.MAP_VIEW)
                .map(IViewComponentInfo::getBubbleInfo)
                .ifPresent(bubbleInfo -> {
                    describeExt.getActiveFieldDescribeSilently(bubbleInfo.getField())
                            .filter(fieldDescribe -> FieldDescribeExt.of(fieldDescribe).isSelectOne())
                            .map(fieldDescribe -> ((SelectOne) fieldDescribe))
                            .ifPresent(selectOne -> syncOptionBubbleInfoFromSelectOne(bubbleInfo, selectOne));
                });

    }

    private void syncOptionBubbleInfoFromSelectOne(IMapViewBubbleInfo oldBubbleInfo, SelectOne selectOne) {
        Map<String, IMapViewOptionBubbleInfo> oldOptionBubbleInfoMap = oldBubbleInfo.getOptions().stream()
                .collect(Collectors.toMap(IMapViewOptionBubbleInfo::getValue, it -> it));
        List<IMapViewOptionBubbleInfo> optionBubbleInfos = selectOne.getSelectOptions().stream()
                .map(it -> buildOptionBubbleInfo(oldOptionBubbleInfoMap, it))
                .collect(Collectors.toList());
        oldBubbleInfo.setOptions(optionBubbleInfos);
    }

    private IMapViewOptionBubbleInfo buildOptionBubbleInfo(Map<String, IMapViewOptionBubbleInfo> oldOptionBubbleInfoMap,
                                                           ISelectOption selectOption) {
        IMapViewOptionBubbleInfo optionBubbleInfo = MapViewOptionBubbleInfo.buildBySelectOption(selectOption);
        Optional.ofNullable(oldOptionBubbleInfoMap.get(selectOption.getValue()))
                .map(IMapViewOptionBubbleInfo::getColor)
                .ifPresent(optionBubbleInfo::setColor);
        return optionBubbleInfo;
    }

    public void renderButton() {
        // 按 layout 中的配置，对按钮进行排序和隐藏
        List<IListComponentInfo> buttonInfos = getButtonInfos(listComponentExt);
        listComponentExt.resetButtonInfos(buttonInfos); // 写回
    }

    private List<IListComponentInfo> getButtonInfos(ListComponentExt listComponentExt) {
        ButtonRender buttonRender = ButtonRender.builder()
                .user(user)
                .customButtonService(customButtonService)
                .layoutType(LayoutTypes.LIST_LAYOUT)
                .describe(describeExt)
                .licenseService(licenseService)
                .buttonLogicService(buttonLogicService)
                .optionalFeaturesService(optionalFeaturesService)
                .build()
                .render();
        Table<String, String, IListComponentInfo> componentInfoTable = HashBasedTable.create(); // buttonInfo对应的IListComponentInfo表
        for (IListComponentInfo listComponentInfo : listComponentExt.getButtonInfoIfAbsentInit()) {
            componentInfoTable.put(listComponentInfo.getRenderType(), listComponentInfo.getPageType(), listComponentInfo);
        }

        List<IListComponentInfo> buttonInfos = Lists.newArrayList();

        handleButtonInfoIfAbsentInit(componentInfoTable, ListComponentInfo.ButtonRenderType.LIST_NORMAL, IComponentInfo.PAGE_TYPE_LIST,
                buttonRender, ListComponentInfo::listNormal).ifPresent(buttonInfos::add);

        handleButtonInfoIfAbsentInit(componentInfoTable, ListComponentInfo.ButtonRenderType.LIST_BATCH, IComponentInfo.PAGE_TYPE_LIST,
                buttonRender, ListComponentInfo::listBatch).ifPresent(buttonInfos::add);

        if (listComponentExt.isEnableSelectedLayout()) {
            handleButtonInfoIfAbsentInit(componentInfoTable, ListComponentInfo.ButtonRenderType.LIST_NORMAL, IComponentInfo.PAGE_TYPE_SELECTED,
                    buttonRender, () -> null).ifPresent(buttonInfos::add);
        }

        if (!(isMobileLayout() && isDesigner())) {
            handleButtonInfoIfAbsentInit(componentInfoTable, ListComponentInfo.ButtonRenderType.LIST_SINGLE, IComponentInfo.PAGE_TYPE_LIST,
                    buttonRender, ListComponentInfo::listSingle).ifPresent(buttonInfos::add);
        }
        return buttonInfos;
    }

    private Optional<IListComponentInfo> handleButtonInfoIfAbsentInit(Table<String, String, IListComponentInfo> componentInfoTable,
                                                                      ListComponentInfo.ButtonRenderType buttonRenderType,
                                                                      String pageType,
                                                                      ButtonRender buttonRender,
                                                                      Supplier<IListComponentInfo> supplier) {
        IListComponentInfo listComponent = componentInfoTable.get(buttonRenderType.getType(), pageType);
        if (Objects.isNull(listComponent)) {
            listComponent = supplier.get();
        }
        if (Objects.isNull(listComponent)) {
            return Optional.empty();
        }
        ButtonUsePageType usePageType = ListComponentInfo.ButtonRenderType.getUsePageByType(listComponent.getRenderType());
        List<IButton> buttonList = getButtonList(buttonRender, usePageType, pageType);
        List<String> hidden = listComponent.getHidden();
        List<String> order = CollectionUtils.sortByGivenOrder(buttonList, listComponent.getOrder(), IButton::getName).stream()
                .map(IButton::getName)
                .filter(it -> !CollectionUtils.nullToEmpty(hidden).contains(it))
                .collect(Collectors.toList());
        listComponent.setOrder(order);
        // 资源列表中不存在的按钮，需要从 hidden 中移除
        if (CollectionUtils.notEmpty(hidden)) {
            Set<String> buttonNames = buttonList.stream().map(IButton::getName).collect(Collectors.toSet());
            List<String> newHiddens = hidden.stream().filter(buttonNames::contains).collect(Collectors.toList());
            listComponent.setHidden(newHiddens);
        }
        // 处理按钮默认外漏个数
        handleExposedButton(listComponent, usePageType);
        return Optional.of(listComponent);
    }


    private List<IButton> getButtonList(ButtonRender buttonRender, ButtonUsePageType usePageType, String pageType) {
        List<IButton> buttons = buttonRender.getButtonsByUsePage(usePageType);
        if (isMobileLayout()
                && IComponentInfo.PAGE_TYPE_SELECTED.equals(pageType)
                && ListComponentInfo.ButtonRenderType.LIST_NORMAL.getType().equals(usePageType.getId())) {
            buttons.removeIf(button -> !ObjectAction.CREATE.getButtonApiName().equals(button.getName()));
        }
        return filterButtonsIfMobile(buttons);
    }

    private List<IButton> filterButtonsIfMobile(List<IButton> buttons) {
        if (isMobileLayout()) {
            return buttons.stream()
                    .filter(button -> !ButtonExt.IMPORT_EXPORT_BUTTON_NAME.contains(button.getName()))
                    .collect(Collectors.toList());
        }
        return buttons;
    }

    private void handleExposedButton(IListComponentInfo buttonInfo, ButtonUsePageType usePageType) {
        if (isMobileLayout() && ButtonUsePageType.DataList != usePageType) {
            buttonInfo.setExposedButton(null);
            return;
        }
        if (buttonInfo.getExposedButton() == null) {
            int size = ListComponentInfo.ButtonRenderType.fromUsePageType(usePageType).getExposedButtonDefaultSize(buttonInfo);
            buttonInfo.setExposedButton(size < 0 ? null : size);
        }
    }

    private boolean isMobileLayout() {
        return isMobileLayout;
    }

    private boolean isDesigner() {
        return isDesigner;
    }
}
