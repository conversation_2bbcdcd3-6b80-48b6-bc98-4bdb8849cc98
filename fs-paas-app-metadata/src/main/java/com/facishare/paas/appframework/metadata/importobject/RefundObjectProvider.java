package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import org.springframework.stereotype.Component;

import static com.facishare.crm.openapi.Utils.REFUND_API_NAME;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/03/31
 */
@Component
public class RefundObjectProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return REFUND_API_NAME;
    }

    @Override
    protected boolean getOpenWorkFlow(IObjectDescribe objectDescribe) {
        return true;
    }
}
