package com.facishare.paas.appframework.metadata.scene;

import com.facishare.paas.appframework.metadata.scene.validator.Validator;
import com.facishare.paas.appframework.metadata.scene.validator.ValidatorRegistry;

/**
 * create by <PERSON><PERSON><PERSON> on 2019/05/22
 */
public class ValidatorUtil {

    public static <T> void validate(T object) {
        Validator<T> validator = ValidatorRegistry.getValidator(object.getClass());
        if (validator != null) {
            validator.validate(object);
        }
    }
}
