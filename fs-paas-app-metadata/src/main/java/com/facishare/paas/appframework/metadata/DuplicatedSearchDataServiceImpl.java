package com.facishare.paas.appframework.metadata;

import com.alibaba.fastjson.JSON;
import com.facishare.paas.I18N;
import com.facishare.paas.appframework.common.service.DepartmentService;
import com.facishare.paas.appframework.common.util.*;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.ServiceContext;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.appframework.metadata.cache.RedisDao;
import com.facishare.paas.appframework.metadata.cache.RedissonServiceImpl;
import com.facishare.paas.appframework.metadata.dto.*;
import com.facishare.paas.appframework.metadata.duplicated.DuplicatedSearchDataContainer;
import com.facishare.paas.appframework.metadata.duplicated.DuplicatedSearchDataStoreService;
import com.facishare.paas.appframework.metadata.duplicated.DuplicatedSearchKeyContainer;
import com.facishare.paas.appframework.metadata.exception.MetaDataException;
import com.facishare.paas.metadata.api.DBRecord;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.data.IDuplicatedSearch;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.api.IdGenerator;
import com.google.common.collect.HashBasedTable;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.google.common.collect.Table;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.facishare.paas.appframework.common.util.AppFrameworkConfig.isDuplicatedSearchRemoveSelf;

/**
 * create by zhaoju on 2020/09/29
 */
@Slf4j
@Service
@Primary
public class DuplicatedSearchDataServiceImpl implements DuplicatedSearchDataService {
    @Autowired
    private DuplicatedSearchService duplicatedSearchService;
    @Autowired
    private DuplicateSearchProxy duplicateSearchProxy;
    @Autowired
    private RedisDao redisDao;
    @Autowired
    private RedissonServiceImpl redissonService;
    @Autowired
    private DuplicatedSearchDataStoreService searchDataStoreService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private DepartmentService departmentService;

    public static final String DUPLICATE_GLOBAL_LOCK_PRE = "DUPLICATE_GLOBAL_LOCK";


    private static final String LUA_SCRIPT = "local arg = ARGV[1]" +
            "    local temp_keys = {}" +
            "    for i, value in ipairs(KEYS) do" +
            "       local body = cjson.decode(value)" +
            "       local temp_key = arg..\"_\"..tostring(i)" +
            "       redis.call(\"sinterstore\", temp_key, unpack(body))" +
            "       table.insert(temp_keys, temp_key)" +
            "    end" +
            "    local result = redis.call(\"sunion\", unpack(temp_keys))" +
            "    redis.call(\"del\", unpack(temp_keys))" +
            "    return result";

    private static final String LUA_SCRIPT_LOG = "local arg = ARGV[1]" +
            "    redis.log(redis.LOG_DEBUG, \"Processing with arg: \" .. arg)" +
            "    local temp_keys = {}" +
            "    for i, value in ipairs(KEYS) do" +
            "       local body = cjson.decode(value)" +
            "       redis.log(redis.LOG_DEBUG, \"Processing key \" .. i .. \": \" .. value)" +

            "       local temp_key = arg..\"_\"..tostring(i)" +
            "       redis.log(redis.LOG_DEBUG, \"Created temp_key: \" .. temp_key)" +

            "       redis.call(\"sinterstore\", temp_key, unpack(body))" +
            "       local inter_result = redis.call(\"smembers\", temp_key)" +
            "       redis.log(redis.LOG_DEBUG, \"Intermediate result for key \" .. i .. \": \" .. #inter_result)" +

            "       table.insert(temp_keys, temp_key)" +
            "    end" +
            "    local result = redis.call(\"sunion\", unpack(temp_keys))" +
            "    redis.log(redis.LOG_DEBUG, \"Final result size: \" .. #result)" +

            "    redis.call(\"del\", unpack(temp_keys))" +
            "    return result";

    @Override
    public List<DuplicateSearchResult.DuplicateData> searchDuplicateDataByType(User user, IObjectData objectData,
                                                                               IDuplicatedSearch.Type type,
                                                                               IObjectDescribe objectDescribe) {
        return findDuplicateDataByType(user, objectData, type, objectDescribe, false);
    }


    public List<DuplicateSearchResult.DuplicateData> findDuplicateDataByType(User user, IObjectData objectData,
                                                                             IDuplicatedSearch.Type type,
                                                                             IObjectDescribe objectDescribe, boolean skipFuzzyRuleDuplicateSearch) {
        IDuplicatedSearch duplicatedSearch = duplicatedSearchService.findDuplicatedSearchByApiNameAndType(user.getTenantId(), objectDescribe.getApiName(), type, false);
        if (!DuplicatedSearchExt.isEnableDuplicate(duplicatedSearch)) {
            return Collections.emptyList();
        }

        if (skipFuzzyRuleDuplicateSearch && DuplicatedSearchExt.of(duplicatedSearch).machFuzzyRule()) {
            return Collections.emptyList();
        }

        // 处理特殊字段的值
        IObjectData data = duplicatedSearchService.processFieldValueForDuplicatedSearch(duplicatedSearch, objectDescribe, objectData, user);

        // 为缺少id 的数据补充临时id
        Tuple<List<IObjectData>, List<IObjectData>> noIdDataTuple = ObjectDataExt.fillDataId(Lists.newArrayList(data), null, objectDescribe);
        try {
            return searchDuplicateData(user, data, type, objectDescribe, duplicatedSearch);
        } finally {
            // 删除临时id
            ObjectDataExt.removeDataId(noIdDataTuple, objectDescribe);
        }
    }


    /**
     * 多规则查重调用
     *
     * @param user
     * @param objectData
     * @param type
     * @param objectDescribe
     * @param duplicatedSearchRuleList
     * @return
     */
    @Override
    public List<DuplicateSearchResult.DuplicateData> searchDuplicateDataByType(User user, IObjectData objectData,
                                                                               IDuplicatedSearch.Type type,
                                                                               IObjectDescribe objectDescribe,
                                                                               List<IDuplicatedSearch> duplicatedSearchRuleList) {
        if (CollectionUtils.empty(duplicatedSearchRuleList)) {
            return Collections.emptyList();
        }
        duplicatedSearchRuleList.removeIf(x -> !DuplicatedSearchExt.of(x).isEnable());
        if (CollectionUtils.empty(duplicatedSearchRuleList)) {
            return Collections.emptyList();
        }
        //每个规则的主对象都是一样的，所以取共同的主对象描述
        // 处理特殊字段的值
        for (IDuplicatedSearch duplicatedSearch : duplicatedSearchRuleList) {
            objectData = duplicatedSearchService.processFieldValueForDuplicatedSearch(duplicatedSearch, objectDescribe, objectData, user);
        }

        // 为缺少id 的数据补充临时id
        Tuple<List<IObjectData>, List<IObjectData>> noIdDataTuple = ObjectDataExt.fillDataId(Lists.newArrayList(objectData), null, objectDescribe);
        try {
            return searchDuplicateData(user, objectData, type, objectDescribe, duplicatedSearchRuleList);
        } finally {
            ObjectDataExt.removeParentDeptSymbol(Lists.newArrayList(objectData));
            // 删除临时id
            ObjectDataExt.removeDataId(noIdDataTuple, objectDescribe);
        }
    }

    private List<DuplicateSearchResult.DuplicateData> searchDuplicateData(User user,
                                                                          IObjectData objectData,
                                                                          IDuplicatedSearch.Type type,
                                                                          IObjectDescribe objectDescribe,
                                                                          List<IDuplicatedSearch> duplicatedSearchRuleList) {
        List<DuplicateSearchDataInfo> esResult = Lists.newArrayList();
        if (AppFrameworkConfig.isEsCheckDuplicate()) {
            // 从 es 查询重复数据
            esResult = getBatchDuplicateSearchDataInEs(user, type, objectDescribe, duplicatedSearchRuleList, objectData);
        }
        // 从 redis或pg 查询重复数据
        List<DuplicateSearchDataInfo> momentResult = Lists.newArrayList();
        if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_NOT_USE_REDIS, user.getTenantId())) {
            if (DuplicatedSearchExt.isHasDepartCascadeFilterByDataScope(duplicatedSearchRuleList, objectDescribe)) {
                momentResult = getBatchDuplicateSearchDataInPg(user, type, objectDescribe, duplicatedSearchRuleList, objectData);
            } else {
                momentResult = getBatchDuplicateSearchDataInRedis(user.getTenantId(), objectData, objectDescribe, duplicatedSearchRuleList);
            }
        }

        Map<String, List<DuplicateSearchDataInfo>> eSResultMap = esResult.stream().collect(Collectors.groupingBy(DuplicateSearchDataInfo::getRuleApiName));
        Map<String, List<DuplicateSearchDataInfo>> redisResultMap = momentResult.stream().collect(Collectors.groupingBy(DuplicateSearchDataInfo::getRuleApiName));

        List<DuplicateSearchDataInfo> mergeResult = Lists.newArrayList();
        IDuplicatedSearch finalDuplicatedSearch = null;
        for (IDuplicatedSearch duplicatedSearch : duplicatedSearchRuleList) {
            List<DuplicateSearchDataInfo> es = eSResultMap.get(duplicatedSearch.getRuleApiName());
            List<DuplicateSearchDataInfo> redis = redisResultMap.get(duplicatedSearch.getRuleApiName());
            mergeResult = mergeResult(es, redis);
            if (CollectionUtils.notEmpty(mergeResult)) {
                finalDuplicatedSearch = duplicatedSearch;
                break;
            }
        }

        if (CollectionUtils.empty(mergeResult) || Objects.isNull(finalDuplicatedSearch)) {
            return Lists.newArrayList();
        }
        if (log.isDebugEnabled()) {
            log.debug("searchDuplicateData mergeResult,tenantId:{},userId:{},object:{}, result:{}", user.getTenantId(), user.getUserId(), objectDescribe.getApiName(), JSON.toJSONString(mergeResult));
        }
        return handleResultByIsEnable(finalDuplicatedSearch, mergeResult);
    }

    private List<DuplicateSearchDataInfo> getBatchDuplicateSearchDataInPg(User user, IDuplicatedSearch.Type type, IObjectDescribe objectDescribe, List<IDuplicatedSearch> duplicatedSearchRuleList, IObjectData objectData) {
        List<String> ruleApiNameList = duplicatedSearchRuleList.stream().map(IDuplicatedSearch::getRuleApiName).collect(Collectors.toList());
        MultiDuplicateSearchResult.Arg arg = MultiDuplicateSearchResult.Arg.builder()
                .ruleApiNameList(ruleApiNameList)
                .objectData(ObjectDataExt.of(objectData).toMap())
                .type(type)
                .tenantId(user.getTenantId())
                .describeApiName(objectDescribe.getApiName())
                .isIdValue(false)
                .removeSelf(isDuplicatedSearchRemoveSelf())
                .build();
        MultiDuplicateSearchResult.Result result;
        if (log.isDebugEnabled()) {
            log.debug("searchDuplicateDataByType tenantId:{},userId:{}, duplicatedSearch:{}, arg:{}",
                    user.getTenantId(), user.getUserId(), JSON.toJSONString(duplicatedSearchRuleList), JSON.toJSONString(arg));
        }
        try {
            result = duplicateSearchProxy.getMultiDuplicateSearchResultByPg(user.getTenantId(), arg);
        } catch (Exception e) {
            log.error("getBatchDuplicateSearchDataInPg error!:user:{},type:{},describeApiName:{},objectData:{}",
                    user, type, objectDescribe.getApiName(), objectData, e);
            throw new RuntimeException(e);
        }
        return dealDuplicatedSearchResult(result, user, arg, objectData);
    }

    private List<DuplicateSearchDataInfo> getBatchDuplicateSearchDataInRedis(String tenantId, IObjectData objectData, IObjectDescribe objectDescribe, List<IDuplicatedSearch> duplicatedSearchRuleList) {
        List<DuplicateSearchDataInfo> duplicateSearchDataInfos = Lists.newArrayList();

        List<IObjectData> objectDataList = Lists.newArrayList();
        //灰度查重数据范围
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATE_RULE_SUPPORT_DATA_SCOPE, tenantId)) {
            Set<String> redisKeys = Sets.newHashSet();
            //获取当前数据的所有查重key
            for (IDuplicatedSearch iDuplicatedSearch : duplicatedSearchRuleList) {
                DuplicatedSearchKeyContainer duplicatedSearchKeyContainer = DuplicatedSearchKeyContainer.of(iDuplicatedSearch, objectData);
                redisKeys.addAll(duplicatedSearchKeyContainer.getSaveKeys(null));
            }
            //获取与当前数据有查重字段重复的数据并集,只取前2000个数据
            objectDataList = getRedisObjectData(tenantId, objectDescribe.getApiName(), redisKeys);
        }

        //查重
        for (IDuplicatedSearch duplicatedSearch : duplicatedSearchRuleList) {
            //用当前规则查重
            duplicateSearchDataInfos = duplicatedSearchInRedis(objectData, objectDescribe, duplicatedSearch);
            if (CollectionUtils.notEmpty(duplicateSearchDataInfos)) {
                //给查重结果中添加查重规则ApiName信息
                duplicateSearchDataInfos.forEach(x -> x.setRuleApiName(duplicatedSearch.getRuleApiName()));
                if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATE_RULE_SUPPORT_DATA_SCOPE, tenantId)) {
                    break;
                }
                //灰度了查重数据范围,则根据其数据范围条件过滤重复的数据
                List<String> currenDuplicatedRuleDataId = Lists.newArrayList();
                //根据查重数据范围过滤查重结果
                for (DuplicateSearchDataInfo duplicateSearchDataInfo : duplicateSearchDataInfos) {
                    List<IObjectData> duplicatedObjectDataList = objectDataList.stream().filter(x -> duplicateSearchDataInfo.getDataIds().contains(x.getId())).collect(Collectors.toList());
                    List<String> filterObjectDataList = duplicatedObjectDataList.stream().filter(x -> DuplicatedSearchExt.of(duplicatedSearch)
                            .filterDuplicateSearchByDataScope(x, objectDescribe)).map(DBRecord::getId).collect(Collectors.toList());
                    currenDuplicatedRuleDataId.addAll(filterObjectDataList);
                    duplicateSearchDataInfo.setDataIds(Sets.newLinkedHashSet(filterObjectDataList));
                }
                //当前查重规则有重复数据则跳出循环,结束redis查重
                if (CollectionUtils.notEmpty(currenDuplicatedRuleDataId)) {
                    break;
                }
            }
        }
        if (CollectionUtils.notEmpty(duplicateSearchDataInfos)) {
            log.debug("getBatchDuplicateSearchDataInRedis: objectData:{},duplicatedSearchRuleList:{},result:{}", objectData, duplicatedSearchRuleList, duplicateSearchDataInfos);
        }

        return duplicateSearchDataInfos;
    }

//    @Override
//    public List<DuplicateSearchDataInfo> searchDuplicateDataListByType(User user, List<IObjectData> dataList, IDuplicatedSearch.Type type, IObjectDescribe objectDescribe) {
//        IDuplicatedSearch duplicatedSearch = duplicatedSearchService.findDuplicatedSearchByApiNameAndType(user.getTenantId(), objectDescribe.getApiName(), type, false);
//        if (!DuplicatedSearchExt.isEnableDuplicate(duplicatedSearch)) {
//            return Collections.emptyList();
//        }
//        List<IObjectData> objectDataList = duplicatedSearchService.processFieldValueForDuplicatedSearch(duplicatedSearch, objectDescribe, dataList, user);
//
//        return bulkSearchDuplicateData(user, objectDataList, type, objectDescribe, duplicatedSearch);
//    }

    @Override
    public List<DuplicateSearchDataInfo> searchDuplicateDataListByType(User user, List<IObjectData> dataList,
                                                                       IDuplicatedSearch.Type type, IObjectDescribe objectDescribe,
                                                                       List<IDuplicatedSearch> duplicatedSearchList, boolean useMultiRule) {
        duplicatedSearchList.removeIf(x -> !DuplicatedSearchExt.of(x).isEnable());
        if (CollectionUtils.empty(duplicatedSearchList)) {
            return Collections.emptyList();
        }

        for (IDuplicatedSearch duplicatedSearch : duplicatedSearchList) {
            dataList = duplicatedSearchService.processFieldValueForDuplicatedSearch(duplicatedSearch, objectDescribe, dataList, user);
        }

        return bulkSearchDuplicateData(user, dataList, type, objectDescribe, duplicatedSearchList, useMultiRule);
    }

    /**
     * 查询重复数据
     *
     * @param user
     * @param objectDescribe
     * @param objectData
     * @param duplicateRuleApiName 从哪个规则开始截取规则列表查重
     * @return
     */
    public List<DuplicateSearchResult.DuplicateData> getDuplicateData(User user, IObjectDescribe objectDescribe, IObjectData objectData, String duplicateRuleApiName,
                                                                      boolean useMultiRule, boolean skipFuzzyRuleDuplicateSearch) {
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(user.getTenantId(), objectDescribe.getApiName())) {
            //查询对象下所有的新建规则
            List<IDuplicatedSearch> duplicatedSearchList = duplicatedSearchService.getEnableDuplicateSearchRuleList(objectDescribe.getApiName(),
                    IDuplicatedSearch.Type.NEW, user.getTenantId(), false, DuplicateSearchOrderByType.ORDER_BY_SORT);

            duplicatedSearchList = DuplicatedSearchExt.filterDuplicatedSearch(duplicatedSearchList, objectData, objectDescribe, departmentService);
            if (skipFuzzyRuleDuplicateSearch) {
                duplicatedSearchList.removeIf(x -> DuplicatedSearchExt.of(x).machFuzzyRule());
            }
            //根据ruleApiName截取规则list
            if (useMultiRule) {
                duplicatedSearchList = DuplicatedSearchExt.subListDuplicateRuleInfoByRuleApiName(duplicateRuleApiName, duplicatedSearchList);
            } else {
                if (CollectionUtils.notEmpty(duplicatedSearchList)) {
                    duplicatedSearchList = duplicatedSearchList.subList(0, 1);
                }
            }
            return searchDuplicateDataByType(user, objectData, IDuplicatedSearch.Type.NEW, objectDescribe, duplicatedSearchList);
        }
        return findDuplicateDataByType(user, objectData, IDuplicatedSearch.Type.NEW, objectDescribe, skipFuzzyRuleDuplicateSearch);
    }

    /**
     * 批量接口，不处理联合查重的逻辑
     * 导入查重
     *
     * @param user
     * @param objectDataList
     * @param type
     * @param objectDescribe
     * @param useMultiRule
     */
    private List<DuplicateSearchDataInfo> bulkSearchDuplicateData(User user, List<IObjectData> objectDataList, IDuplicatedSearch.Type type,
                                                                  IObjectDescribe objectDescribe, List<IDuplicatedSearch> duplicatedSearchList, boolean useMultiRule) {
        //处理where条件
        List<BatchMultiQueryDuplicateSearch.DuplicateData> duplicateDataList = filterDuplicateRule(objectDataList, duplicatedSearchList, objectDescribe, useMultiRule);

        List<DuplicateSearchDataInfo> searchResultInEs;
        List<DuplicateSearchDataInfo> searchResultInRedis = Lists.newArrayList();
        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(user.getTenantId(), objectDescribe.getApiName())) {
            searchResultInEs = bulkMultiSearchDuplicateDataInEs(user, type, objectDescribe, duplicateDataList);
            if (!UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_NOT_USE_REDIS, user.getTenantId())) {
                searchResultInRedis = searchResultInRedis(user, duplicatedSearchList, duplicateDataList, objectDescribe, objectDataList);
            }
        } else {
            searchResultInEs = bulkSearchDuplicateDataInEs(user, objectDataList, type, objectDescribe);
            searchResultInRedis = bulkSearchDuplicateDataInRedis(objectDataList, duplicatedSearchList.get(0));
        }
        return mergeResult(searchResultInEs, searchResultInRedis);
    }

    private List<DuplicateSearchDataInfo> bulkSearchDuplicateDataInRedis(List<IObjectData> objectDataList, IDuplicatedSearch duplicatedSearch) {
        if (CollectionUtils.empty(objectDataList) || Objects.isNull(duplicatedSearch)) {
            return Lists.newArrayList();
        }
        DuplicatedSearchDataContainer searchDataContainer = DuplicatedSearchDataContainer.of(duplicatedSearch,
                objectDataList, searchInRedis(duplicatedSearch.getTenantId()));
        return searchDataContainer.searchDuplicatedData();
    }

    private List<DuplicateSearchDataInfo> searchResultInRedis(User user, List<IDuplicatedSearch> duplicatedSearchList,
                                                              List<BatchMultiQueryDuplicateSearch.DuplicateData> duplicateDataList,
                                                              IObjectDescribe objectDescribe, List<IObjectData> objectDataList) {
        Map<String, IDuplicatedSearch> duplicateSearchMap = duplicatedSearchList.stream()
                .collect(Collectors.toMap(IDuplicatedSearch::getRuleApiName, it -> it, (x, y) -> x));
        Set<String> redisKeys = Sets.newHashSet();
        for (BatchMultiQueryDuplicateSearch.DuplicateData duplicateData : duplicateDataList) {
            List<String> ruleApiNameList = duplicateData.getRuleApiNameList();
            for (String ruleApiName : ruleApiNameList) {
                IDuplicatedSearch iDuplicatedSearch = duplicateSearchMap.get(ruleApiName);
                DuplicatedSearchKeyContainer duplicatedSearchKeyContainer = DuplicatedSearchKeyContainer.of(iDuplicatedSearch, ObjectDataExt.of(duplicateData.getData()));
                redisKeys.addAll(duplicatedSearchKeyContainer.getSaveKeys(null));
            }
        }
        //拿到所有可能重复的数据，取所有查重key的并集
        List<IObjectData> redisObjectData = getRedisObjectData(user.getTenantId(), objectDescribe.getApiName(), redisKeys);
        return searchDataStoreService.searchDuplicatedData(objectDescribe, objectDataList, duplicateDataList, duplicateSearchMap, redisObjectData);
    }

    private List<IObjectData> getRedisObjectData(String tenantId, String objectApiName, Set<String> redisKeys) {
        Set<String> idSet = redisDao.sunion(redisKeys);
        if (idSet.size() > 2000) {
            log.warn("redis has more than 2000 duplicate data,size:{}", idSet.size());
        }
        List<String> idList = idSet.stream().limit(2000).collect(Collectors.toList());
        return metaDataFindService.findObjectDataByIds(tenantId, idList, objectApiName);
    }

    private List<BatchMultiQueryDuplicateSearch.DuplicateData> filterDuplicateRule(List<IObjectData> objectDataList, List<IDuplicatedSearch> duplicatedSearchList, IObjectDescribe objectDescribe, boolean useMultiRule) {
        List<BatchMultiQueryDuplicateSearch.DuplicateData> dateRuleList = Lists.newArrayList();
        Map<String, IDuplicatedSearch> duplicatedSearchMap = duplicatedSearchList.stream().collect(Collectors.toMap(IDuplicatedSearch::getRuleApiName, it -> it, (x1, x2) -> x1));

        List<DuplicatedSearchExt> duplicatedSearchExtList = duplicatedSearchList.stream().map(DuplicatedSearchExt::of).collect(Collectors.toList());
        //给数据中补充父级部门数据
        DuplicatedSearchExt.fillParentDeptToObjectData(objectDataList, objectDescribe, departmentService, duplicatedSearchExtList);

        for (IObjectData objectData : objectDataList) {
            List<String> suitDuplicateRuleApiName = duplicatedSearchExtList.stream()
                    .filter(x -> x.filterDuplicateSearchByWheres(objectData, objectDescribe))
                    .map(DuplicatedSearchExt::getRuleApiName)
                    .collect(Collectors.toList());
            if (!useMultiRule && CollectionUtils.notEmpty(suitDuplicateRuleApiName)) {
                suitDuplicateRuleApiName = suitDuplicateRuleApiName.subList(0, 1);
            }
            //开启单规则,并且数据匹配的第一个规则为模糊匹配,则规则不生效,不做查重
            suitDuplicateRuleApiName.removeIf(x -> DuplicatedSearchExt.of(duplicatedSearchMap.get(x)).machFuzzyRule());
            if (CollectionUtils.empty(suitDuplicateRuleApiName)) {
                continue;
            }
            BatchMultiQueryDuplicateSearch.DuplicateData data = BatchMultiQueryDuplicateSearch.DuplicateData.builder()
                    .ruleApiNameList(suitDuplicateRuleApiName)
                    .data(ObjectDataExt.of(objectData).toMap())
                    .build();
            dateRuleList.add(data);
        }
        ObjectDataExt.removeParentDeptSymbol(objectDataList);
        return dateRuleList;
    }

    private List<DuplicateSearchDataInfo> mergeResult(List<DuplicateSearchDataInfo> searchResultInEs, List<DuplicateSearchDataInfo> searchResultInRedis) {
        if (CollectionUtils.empty(searchResultInEs) && CollectionUtils.empty(searchResultInRedis)) {
            return Lists.newArrayList();
        }
        if (CollectionUtils.empty(searchResultInEs)) {
            return searchResultInRedis.stream()
                    .filter(it -> !it.sourceDataIdIsEmpty())
                    .filter(DuplicateSearchDataInfo::isDuplicated)
                    .collect(Collectors.toList());
        }
        if (CollectionUtils.empty(searchResultInRedis)) {
            return searchResultInEs.stream()
                    .filter(it -> !it.sourceDataIdIsEmpty())
                    .filter(DuplicateSearchDataInfo::isDuplicated)
                    .collect(Collectors.toList());
        }
        // 处理 es 和 redis 查询结果的合并
        Table<String, String, DuplicateSearchDataInfo> idApiNameDuplicateSearchData = HashBasedTable.create();
        List<DuplicateSearchDataInfo> duplicateSearchDataInfos = Lists.newArrayList(searchResultInEs);
        duplicateSearchDataInfos.addAll(searchResultInRedis);
        for (DuplicateSearchDataInfo it : duplicateSearchDataInfos) {
            // 忽略 sourceDataId 为空的数据
            if (it.sourceDataIdIsEmpty()) {
                continue;
            }
            //对象apiName+ruleApiName 拼起来使用防止ruleApiName重复，以及对象黑名单没有规则apiName
            String apiName = StringUtils.isEmpty(it.getRuleApiName()) ? it.getApiName() : it.getApiName() + "_" + it.getRuleApiName();
            DuplicateSearchDataInfo dataInfo = idApiNameDuplicateSearchData.get(it.getSourceDataId(), apiName);
            if (Objects.isNull(dataInfo)) {
                idApiNameDuplicateSearchData.put(it.getSourceDataId(), apiName, it);
            } else {
                idApiNameDuplicateSearchData.put(it.getSourceDataId(), apiName, dataInfo.merge(it));
            }
        }
        return idApiNameDuplicateSearchData.values().stream().filter(DuplicateSearchDataInfo::isDuplicated).collect(Collectors.toList());
    }

//    private List<DuplicateSearchDataInfo> bulkSearchDuplicateDataInRedis(List<IObjectData> objectDataList, IObjectDescribe describe, IDuplicatedSearch duplicatedSearch) {
//        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_EMPTY_POLICY, describe.getTenantId())) {
//            return searchDataStoreService.searchDuplicatedData(describe, duplicatedSearch, objectDataList);
//        }
//        DuplicatedSearchDataContainer searchDataContainer = DuplicatedSearchDataContainer.of(duplicatedSearch,
//                objectDataList, searchInRedis());
//        return searchDataContainer.searchDuplicatedData();
//    }

    /**
     * @return
     */
    private Function<DuplicatedSearchDataContainer.DuplicatedSearchContainer, Collection<String>> searchInRedis(String tenantId) {
        return (container) -> {
            Set<List<String>> ruleSearchKeyByPrecise = container.getRuleFieldNameByPrecise();
            Map<String, String> fieldNameSearchKeyMap = container.getFieldNameSearchKeyMap();

            List<String> keys = ruleSearchKeyByPrecise.stream()
                    .map(fieldNames -> fieldNames.stream().map(fieldNameSearchKeyMap::get)
                            .filter(Objects::nonNull)
                            .collect(Collectors.toSet()))
                    .filter(CollectionUtils::notEmpty)
                    .map(JacksonUtils::toJson)
                    .collect(Collectors.toList());

            if (CollectionUtils.empty(keys)) {
                return Collections.emptySet();
            }
            String luaScript = LUA_SCRIPT;
            String tempKey = container.getRedisTempKey();
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_REDIS_LUA_LOG_GRAY, tenantId)) {
                luaScript = LUA_SCRIPT_LOG;
            }
            if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_REDIS_TEMP_KEY_PRE_GRAY, tenantId)) {
                tempKey = IdGenerator.get() + "_" + tempKey;
            }
            Collection<String> result = redisDao.evalScript(luaScript, keys, tempKey);
            if (log.isInfoEnabled()) {
                int size = CollectionUtils.empty(result) ? 0 : result.size();
                log.info("redis search result! keys:{}, tempKey:{}, size:{}, result:{}", keys, tempKey, size, JacksonUtils.toJson(result));
            }
            return result;
        };
    }

    private List<DuplicateSearchDataInfo> bulkMultiSearchDuplicateDataInEs(User user,
                                                                           IDuplicatedSearch.Type type,
                                                                           IObjectDescribe objectDescribe,
                                                                           List<BatchMultiQueryDuplicateSearch.DuplicateData> duplicateDataList) {


        try {
            BatchMultiQueryDuplicateSearch.Arg arg = BatchMultiQueryDuplicateSearch.Arg.builder()
                    .describeApiName(objectDescribe.getApiName())
                    .tenantId(user.getTenantId())
                    .type(type)
                    .dataList(duplicateDataList)
                    .isIdValue(false)
                    .removeSelf(isDuplicatedSearchRemoveSelf())
                    .build();
            BatchMultiQueryDuplicateSearch.Result result = duplicateSearchProxy.batchMultiQueryDuplicateSearch(user.getTenantId(), arg);
            if (!result.success()) {
                log.warn("batchQueryDuplicateSearch fail, ei:{}, describeApiName:{}, message:{}", user.getTenantId(), objectDescribe.getApiName(), result.getMessage());
                throw new MetaDataException(I18NExt.text(I18NKey.DUPLICATE_CHECK_SERVICE_EXCEPTION) + ":" + result.getMessage());
            }
            return DuplicateSearchDataInfo.fromMultiList(result.getDuplicateDataList());
        } catch (Exception e) {
            log.error("batchQueryDuplicateSearch error, ei:{}, describeApiName:{}", user.getTenantId(), objectDescribe.getApiName(), e);
            throw new RuntimeException(e);
        }

    }

    private List<DuplicateSearchDataInfo> bulkSearchDuplicateDataInEs(User user, List<IObjectData> objectDataList, IDuplicatedSearch.Type type,
                                                                      IObjectDescribe objectDescribe) {
        List<Map<String, Object>> objectList = objectDataList.stream()
                .map(ObjectDataExt::toMap)
                .collect(Collectors.toList());

        List<List<Map<String, Object>>> objectDataLists = Lists.partition(objectList, 15);

        List<DuplicateSearchDataInfo> resultList = Lists.newCopyOnWriteArrayList();
        ParallelUtils.ParallelTask parallelTask = ParallelUtils.createParallelTask();
        objectDataLists.forEach(dataList -> {
            parallelTask.submit(() -> {
                BatchQueryDuplicateSearch.Arg arg = BatchQueryDuplicateSearch.Arg.builder()
                        .describeApiName(objectDescribe.getApiName())
                        .tenantId(user.getTenantId())
                        .type(type.toString())
                        .dataList(dataList)
                        .isIdValue(false)
                        .build();
                BatchQueryDuplicateSearch.Result result = duplicateSearchProxy.batchQueryDuplicateSearch(user.getTenantId(), arg);
                if (!result.success()) {
                    log.warn("batchQueryDuplicateSearch fail, ei:{}, describeApiName:{}, message:{}", user.getTenantId(), objectDescribe.getApiName(), result.getMessage());
                    throw new MetaDataException(I18NExt.text(I18NKey.DUPLICATE_CHECK_SERVICE_EXCEPTION) + ":" + result.getMessage());
                }
                resultList.addAll(DuplicateSearchDataInfo.fromList(result.getDuplicateDataList()));
            });
        });

        try {
            parallelTask.await(75, TimeUnit.SECONDS);
        } catch (TimeoutException e) {
            log.error("batchQueryDuplicateSearch time out! ei:{}, userId:{}, describeApiName:{}", user.getTenantId(),
                    user.getUserId(), objectDescribe.getApiName(), e);
            throw new RuntimeException(e);
        }
        return resultList;
    }

    @Override
    public void saveDuplicateDataResultInRedis(List<IObjectData> dataList, IObjectDescribe describe, IDuplicatedSearch duplicatedSearch, User user) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.DUPLICATED_SEARCH_NOT_USE_REDIS, user.getTenantId())) {
            return;
        }
        searchDataStoreService.saveDuplicateData(user, describe, duplicatedSearch, dataList);
    }

    @Nullable
    @Override
    public RLock duplicateSearchLock(User user, List<IDuplicatedSearch> duplicatedSearchList, IObjectDescribe describe, IObjectData objectData) {
        if (AppFrameworkConfig.disableDuplicateSearchLock(user.getTenantId())) {
            return null;
        }
        if (CollectionUtils.empty(duplicatedSearchList)) {
            return null;
        }
        return getLockByDuplicatedSearchKeyContainer(user, describe, duplicatedSearchList, objectData);
    }

    @Override
    public List<DuplicateSearchDataInfo> dataDuplicatedByRedis(ServiceContext context, IObjectDescribe describe, List<IObjectData> objectDataList) {
        List<IDuplicatedSearch> duplicateSearchRuleList = duplicatedSearchService.getEnableDuplicateSearchRuleList(describe.getApiName(), IDuplicatedSearch.Type.NEW, context.getTenantId(), false,
                DuplicateSearchOrderByType.ORDER_BY_SORT);
        for (IDuplicatedSearch duplicatedSearch : duplicateSearchRuleList) {
            objectDataList = duplicatedSearchService.processFieldValueForDuplicatedSearch(duplicatedSearch, describe, objectDataList, context.getUser());
        }
        return searchDataStoreService.multiSaveAndDuplicateData(context.getUser(), describe, duplicateSearchRuleList, objectDataList, true);
    }

    private RLock getLockByDuplicatedSearchKeyContainer(User user, IObjectDescribe objectDescribe, List<IDuplicatedSearch> duplicatedSearchList, IObjectData objectData) {
        Set<String> lockKeys = Sets.newHashSet();
        Supplier<RLock> getGlobalLock;

        if (AppFrameworkConfig.isGrayMultiDuplicateRuleAndSupportFilterGray(user.getTenantId(), objectDescribe.getApiName())) {
            getGlobalLock = () -> tryDuplicateSearchGlobalLock(user.getTenantId(), objectDescribe.getApiName());
            for (IDuplicatedSearch duplicatedSearch : duplicatedSearchList) {
                if (AppFrameworkConfig.isPreciseDuplicateSearchGray(user.getTenantId(), objectDescribe.getApiName())) {
                    DuplicatedSearchKeyContainer searchKeyContainer = DuplicatedSearchKeyContainer.of(duplicatedSearch, objectData);
                    lockKeys.addAll(searchKeyContainer.getPreciseLockKeys());
                } else {
                    lockKeys.addAll(DuplicatedSearchKeyContainer.getLockKeys(duplicatedSearch, objectData));
                }
            }
        } else {
            IDuplicatedSearch duplicatedSearch = duplicatedSearchList.get(0);
            if (Objects.isNull(duplicatedSearch)) {
                return null;
            }
            if (AppFrameworkConfig.isPreciseDuplicateSearchGray(user.getTenantId(), objectDescribe.getApiName())) {
                DuplicatedSearchKeyContainer searchKeyContainer = DuplicatedSearchKeyContainer.of(duplicatedSearch, objectData);
                lockKeys = searchKeyContainer.getPreciseLockKeys();
                getGlobalLock = () -> null;
            } else {
                getGlobalLock = () -> tryDuplicateSearchGlobalLock(user.getTenantId(), objectDescribe.getApiName());
                lockKeys = DuplicatedSearchKeyContainer.getLockKeys(duplicatedSearch, objectData);
            }
        }
        if (CollectionUtils.empty(lockKeys)) {
            return null;
        }
        RLock globalLock = null;
        try {
            globalLock = getGlobalLock.get();
            RLock duplicateSearchLock = redissonService.tryMultiLock(AppFrameworkConfig.getDuplicateSearchLockWaitTimeSeconds(),
                    AppFrameworkConfig.getDuplicateSearchLockWaitLeaseTimeSeconds(), TimeUnit.SECONDS,
                    lockKeys.toArray(new String[0]));
            if (Objects.isNull(duplicateSearchLock)) {
                log.warn("try duplicateSearchLock fail, ei:{}, apiName:{}, keys:{}", user.getTenantId(), objectDescribe.getApiName(), lockKeys);
                throw new ValidateException(I18N.text(I18NKey.DUPLICATED_SEARCH_SAVE_DATA_FAIL));
            }
            return duplicateSearchLock;
        } finally {
            redissonService.unlock(globalLock);
        }
    }


    private RLock tryDuplicateSearchGlobalLock(String tenantId, String objectAPIName) {
        // 添加全局锁，将创建、编辑接口变成顺序执行的
        String globalLockKey = getGlobalLockKey(tenantId, objectAPIName);
        RLock globalLock = redissonService.tryFairLock(AppFrameworkConfig.getDuplicateSearchGlobalLockWaitTimeSeconds(),
                AppFrameworkConfig.getDuplicateSearchGlobalLockWaitLeaseTimeSeconds(),
                TimeUnit.SECONDS, globalLockKey);

        return globalLock;
    }

    private List<DuplicateSearchResult.DuplicateData> searchDuplicateData(User user, IObjectData objectData, IDuplicatedSearch.Type type, IObjectDescribe objectDescribe, IDuplicatedSearch duplicatedSearch) {
        // 从 es 查询重复数据
        List<DuplicateSearchDataInfo> eSResult = getDuplicateSearchDataInEs(user, type, objectDescribe,
                duplicatedSearch, objectData);
        // 从 redis 查询重复数据
        List<DuplicateSearchDataInfo> duplicateData = duplicatedSearchInRedis(objectData, objectDescribe, duplicatedSearch);
        if (CollectionUtils.notEmpty(duplicateData)) {
            log.debug("searchDuplicateData redis duplicated: objectData:{},duplicatedSearch:{}, result:{}", objectData, duplicatedSearch, duplicateData);
        }
        // 合并查询结果
        List<DuplicateSearchDataInfo> result = mergeResult(eSResult, duplicateData);
        if (log.isDebugEnabled()) {
            log.debug("searchDuplicatedData mergeResult,tenantId:{},userId:{},objectApiName:{},result:{}", user.getTenantId(), user.getUserId(), objectData.getDescribeApiName(), JSON.toJSONString(result));
        }
        return handleResultByIsEnable(duplicatedSearch, result);
    }

    private List<DuplicateSearchResult.DuplicateData> handleResultByIsEnable(IDuplicatedSearch duplicatedSearch, List<DuplicateSearchDataInfo> result) {
        Map<String, Boolean> enableMap = DuplicatedSearchExt.of(duplicatedSearch).getEnableMap();
        return result.stream()
                .peek(x -> {
                    if (!enableMap.getOrDefault(x.getApiName(), false) || Objects.isNull(x.getDataIds())) {
                        x.setDataIds(Sets.newLinkedHashSet());
                    }
                })
                .map(DuplicateSearchDataInfo::convertTo)
                .peek(x -> x.setRuleLabel(duplicatedSearch.getName()))
                .collect(Collectors.toList());
    }

    // TODO: 2022/12/4 新建查重
    private List<DuplicateSearchDataInfo> duplicatedSearchInRedis(IObjectData objectData, IObjectDescribe objectDescribe, IDuplicatedSearch duplicatedSearch) {
        return searchDataStoreService.searchDuplicatedData(objectDescribe, duplicatedSearch, objectData)
                .filter(DuplicateSearchDataInfo::isDuplicated)
                .map(Lists::newArrayList)
                .orElseGet(Lists::newArrayList);
    }


    private List<DuplicateSearchDataInfo> getDuplicateSearchDataInEs(User user, IDuplicatedSearch.Type type, IObjectDescribe objectDescribe, IDuplicatedSearch duplicatedSearch, IObjectData data) {
        DuplicateSearch arg = DuplicateSearch.builder()
                .describeApiName(objectDescribe.getApiName())
                .objectData(ObjectDataExt.of(data).toMap())
                .type(type)
                .tenantId(user.getTenantId())
                .isIdValue(false)
                .build();
        DuplicateSearchResult result;
        try {
            if (log.isDebugEnabled()) {
                log.debug("searchDuplicateDataByType tenantId:{},userId:{}, duplicatedSearch:{}, arg:{}",
                        user.getTenantId(), user.getUserId(), JSON.toJSONString(duplicatedSearch), JSON.toJSONString(arg));
            }
            result = duplicateSearchProxy.getDuplicateSearchResult(user.getTenantId(), arg);
        } catch (Exception e) {
            log.error("searchDuplicateDataByType error, arg:{}, tenantId:{},userId:{}", JSON.toJSONString(arg), user.getTenantId(), user.getUserId(), e);
            throw new MetaDataException(I18NExt.text(I18NKey.DUPLICATE_CHECK_SERVICE_EXCEPTION) + ":" + e.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug("searchDuplicateDataByEs tenantId:{},userId:{},objectApiName:{},result:{}", user.getTenantId(), user.getUserId(), objectDescribe.getApiName(), JSON.toJSONString(result));
        }
        return result.toDuplicateSearchDataInfo(data);
    }

    private List<DuplicateSearchDataInfo> getBatchDuplicateSearchDataInEs(User user, IDuplicatedSearch.Type type,
                                                                          IObjectDescribe objectDescribe,
                                                                          List<IDuplicatedSearch> duplicatedSearchList,
                                                                          IObjectData objectData) {
        List<String> ruleApiNameList = duplicatedSearchList.stream().map(IDuplicatedSearch::getRuleApiName).collect(Collectors.toList());
        MultiDuplicateSearchResult.Arg arg = MultiDuplicateSearchResult.Arg.builder()
                .ruleApiNameList(ruleApiNameList)
                .objectData(ObjectDataExt.of(objectData).toMap())
                .type(type)
                .tenantId(user.getTenantId())
                .removeSelf(isDuplicatedSearchRemoveSelf())
                .describeApiName(objectDescribe.getApiName())
                .isIdValue(false)
                .build();
        MultiDuplicateSearchResult.Result result;
        if (log.isDebugEnabled()) {
            log.debug("searchDuplicateDataByType tenantId:{},userId:{}, duplicatedSearch:{}, arg:{}",
                    user.getTenantId(), user.getUserId(), JSON.toJSONString(duplicatedSearchList), JSON.toJSONString(arg));
        }
        try {
            result = duplicateSearchProxy.getMultiDuplicateSearchResult(user.getTenantId(), arg);
        } catch (Exception e) {
            log.error("getBatchDuplicateSearchDataInEs error! user:{},type:{},describeApiName:{},objectData:{}", user,
                    type, objectDescribe.getApiName(), objectData, e);
            throw new RuntimeException(e);
        }
        return dealDuplicatedSearchResult(result, user, arg, objectData);
    }

    private List<DuplicateSearchDataInfo> dealDuplicatedSearchResult(MultiDuplicateSearchResult.Result result, User user, MultiDuplicateSearchResult.Arg arg, IObjectData objectData) {
        if (!result.success()) {
            log.error("searchDuplicateDataByType error, arg:{}, tenantId:{},userId:{},message:{}", JSON.toJSONString(arg), user.getTenantId(), user.getUserId(), result.getMessage());
            throw new MetaDataException(I18NExt.text(I18NKey.DUPLICATE_CHECK_SERVICE_EXCEPTION) + ":" + result.getMessage());
        }
        if (log.isDebugEnabled()) {
            log.debug("searchDuplicateDataByEs info,tenantId:{},userId:{},result:{}", user.getTenantId(), user.getUserId(), JSON.toJSONString(result.getResult()));
        }
        return result.getResult().getDuplicateDataList().stream()
                .map(x -> DuplicateSearchDataInfo.of(x.getApiName(), result.getResult().getRuleApiName(), objectData.getId(), x.getDataIds()))
                .collect(Collectors.toList());
    }


    private String getGlobalLockKey(String tenantId, String objectAPIName) {
        return String.join("_", DUPLICATE_GLOBAL_LOCK_PRE, tenantId, objectAPIName);
    }


}
