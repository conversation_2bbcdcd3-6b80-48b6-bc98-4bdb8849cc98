package com.facishare.paas.appframework.metadata.importobject;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.metadata.util.OrganizationConfigUtil;
import com.facishare.paas.metadata.api.data.IUniqueRule;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 人员
 * create by z<PERSON><PERSON> on 2019/03/31
 */
@Component
public class DimensionObjectProvider extends DefaultObjectImportProvider {
    @Override
    public String getObjectCode() {
        return Utils.DIMENSION_OBJ_API_NAME;
    }

    @Override
    protected int getDuplicateJudgmentType(IObjectDescribe objectDescribe) {
        return UNSUPPORT_INSERT_IMPORT;
    }

    @Override
    protected ImportType getImportType(IObjectDescribe objectDescribe, IUniqueRule uniqueRule) {
        if (OrganizationConfigUtil.isImportDimensionGray(objectDescribe.getTenantId())) {
            return ImportType.DEFAULT;
        }
        return ImportType.UNSUPPORT_INSERT_IMPORT;
    }

    @Override
    protected List<MatchingType> getMatchingTypesByInsert(IObjectDescribe objectDescribe) {
        return Lists.newArrayList(MatchingType.NAME);
    }

    @Override
    protected List<MatchingType> getMatchingTypesByUpdate(IObjectDescribe objectDescribe) {
        return Lists.newArrayList(MatchingType.NAME);
    }
}
