package com.facishare.paas.appframework.metadata.cache.local.wrapper;

import com.facishare.paas.appframework.core.util.UdobjGrayConfig;
import com.facishare.paas.appframework.core.util.UdobjGrayConfigKey;
import com.facishare.paas.metadata.api.IUdefButton;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.service.IUdefButtonService;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import lombok.Setter;

import java.util.List;
import java.util.Map;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2022/5/19
 */
@Setter
public class UdefButtonServerWrapper implements IUdefButtonService {

    private IUdefButtonService buttonService;
    private IUdefButtonService buttonCacheService;

    private IUdefButtonService buttonService(String tenantId) {
        if (UdobjGrayConfig.isAllow(UdobjGrayConfigKey.THREAD_LOCAL_CACHE_GRAY, tenantId)) {    // 线上跟随 variables_gray_conf_810 灰度了一部分企业
            return buttonCacheService;
        }
        return buttonService;
    }

    @Override
    public IUdefButton create(IUdefButton udefButton) throws MetadataServiceException {
        return buttonService(udefButton.getTenantId()).create(udefButton);
    }

    @Override
    public IUdefButton update(IUdefButton udefButton) throws MetadataServiceException {
        return buttonService(udefButton.getTenantId()).update(udefButton);
    }

    @Override
    public boolean changeUdefButtonStatus(String apiName, String describeApiName, String tenantId, boolean isActive) {
        return buttonService(tenantId).changeUdefButtonStatus(apiName, describeApiName, tenantId, isActive);
    }

    @Override
    public boolean changeUdefButtonStatus(String apiName, String describeApiName, String tenantId, boolean isActive, IActionContext context) {
        return buttonService(tenantId).changeUdefButtonStatus(apiName, describeApiName, tenantId, isActive, context);
    }

    @Override
    public boolean deleteUdefButton(String apiName, String describeApiName, String tenantId) {
        return buttonService(tenantId).deleteUdefButton(apiName, describeApiName, tenantId);
    }

    @Override
    public List<IUdefButton> findButtonsByDescribeApiName(String describeApiName, String tenantId) {
        return buttonService(tenantId).findButtonsByDescribeApiName(describeApiName, tenantId);
    }

    @Override
    public List<IUdefButton> findButtonsByDescribeApiName(String describeApiName, String tenantId, boolean onlyTenantDB) {
        return buttonService(tenantId).findButtonsByDescribeApiName(describeApiName, tenantId, onlyTenantDB);
    }

    @Override
    public IUdefButton findByApiNameAndTenantId(String apiName, String describeApiName, String tenantId) {
        return buttonService(tenantId).findByApiNameAndTenantId(apiName, describeApiName, tenantId);
    }

    @Override
    public List<IUdefButton> findByLastModifiedTime(String tenantId, String defineType, long lastModifiedTime) {
        return buttonService(tenantId).findByLastModifiedTime(tenantId, defineType, lastModifiedTime);
    }

    @Override
    public Map<String, List<IUdefButton>> findButtonsByDescribeApiNamesAndType(String tenantId, List<String> describeApiNames, String buttonType) {
        return buttonService(tenantId).findButtonsByDescribeApiNamesAndType(tenantId, describeApiNames, buttonType);
    }

    @Override
    public Map<String, List<IUdefButton>> findButtonsByDescribeApiNames(List<String> describeApiNames, String tenantId,
            boolean onlyTenantDB) {
        return buttonService(tenantId).findButtonsByDescribeApiNames(describeApiNames, tenantId, onlyTenantDB);
    }
}
