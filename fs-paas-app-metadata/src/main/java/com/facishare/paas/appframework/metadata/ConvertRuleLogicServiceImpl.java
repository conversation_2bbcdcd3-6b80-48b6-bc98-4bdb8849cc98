package com.facishare.paas.appframework.metadata;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.StopWatch;
import com.facishare.paas.appframework.common.util.Tuple;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.function.FunctionLogicService;
import com.facishare.paas.appframework.function.dto.RunResult;
import com.facishare.paas.appframework.function.util.FunctionQueryTemplateUtils;
import com.facishare.paas.appframework.log.ActionType;
import com.facishare.paas.appframework.log.EventType;
import com.facishare.paas.appframework.log.LogService;
import com.facishare.paas.appframework.metadata.dto.ConvertRuleDataContainer;
import com.facishare.paas.appframework.metadata.dto.DetailResource;
import com.facishare.paas.appframework.metadata.expression.ExpressionCalculateLogicService;
import com.facishare.paas.appframework.metadata.expression.RuleCalculateResult;
import com.facishare.paas.appframework.metadata.filter.ObjectDataFilter;
import com.facishare.paas.appframework.metadata.repository.model.MtConvertRule;
import com.facishare.paas.appframework.metadata.repository.model.MtConvertTracker;
import com.facishare.paas.appframework.metadata.search.Query;
import com.facishare.paas.appframework.metadata.search.QueryConditionContainer;
import com.facishare.paas.appframework.metadata.search.SearchQueryImpl;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.IObjectMappingRuleEnumInfo;
import com.facishare.paas.metadata.api.IObjectMappingRuleInfo;
import com.facishare.paas.metadata.api.MultiRecordType;
import com.facishare.paas.metadata.api.action.IActionContext;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.api.search.IFilter;
import com.facishare.paas.metadata.api.search.Wheres;
import com.facishare.paas.metadata.impl.search.Operator;
import com.facishare.paas.metadata.impl.search.SearchTemplateQuery;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.ObjectUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.support.TransactionSynchronizationAdapter;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import java.util.*;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.function.UnaryOperator;
import java.util.stream.Collectors;

@Slf4j
@Service("convertRuleLogicService")
public class ConvertRuleLogicServiceImpl implements ConvertRuleLogicService {
    @Autowired
    private MetaDataActionService metaDataActionService;
    @Autowired
    private MetaDataFindService metaDataFindService;
    @Autowired
    private ExpressionCalculateLogicService expressionCalculateLogicService;
    @Autowired
    private ObjectConvertRuleService objectConvertRuleService;
    @Autowired
    private FunctionLogicService functionLogicService;
    @Autowired
    protected QuoteValueService quoteValueService;
    @Autowired
    private LogService logService;

    @Autowired
    private ObjectConvertTrackerService objectConvertTrackerService;

    @Override
    public void closeSourceOrder(User user, ConvertRuleDataContainer convertRuleDataContainer, IObjectData objectData, Map<String, List<IObjectData>> detailObjectData) {
        if (Objects.isNull(convertRuleDataContainer)) {
            return;
        }
        StopWatch stopWatch = StopWatch.create("closeSourceOrder");
        IObjectDescribe sourceObjectDescribe = convertRuleDataContainer.getObjectDescribe();
        List<IObjectData> sourceObjectDataList = metaDataFindService.findObjectDataByIds(buildActionContextNeedCalculateCount(user), convertRuleDataContainer.getFromMasterIds(), sourceObjectDescribe.getApiName());
        stopWatch.lap("findObjectDataByIds");
        convertRuleDataContainer.setSourceObjectDataList(sourceObjectDataList);
        trackerRecord(user, convertRuleDataContainer, objectData, detailObjectData);
        if (convertRuleDataContainer.notNeedCloseSourceOrder()) {
            return;
        }
        List<MtConvertRule> needCloseLogicConvertRules = convertRuleDataContainer.getConvertRuleList().stream()
                .filter(MtConvertRule::needCloseSourceOrder)
                .collect(Collectors.toList());
        if (CollectionUtils.empty(needCloseLogicConvertRules)) {
            return;
        }
        MtConvertRule masterConvertRule = convertRuleDataContainer.getMasterConvertRule();
        if (convertRuleDataContainer.closeSourceMasterDataOnly()) {
            Map<String, IObjectDescribe> objectDescribeMap = Maps.newHashMap();
            objectDescribeMap.put(sourceObjectDescribe.getApiName(), sourceObjectDescribe);
            doValidateRuleFieldConfig(Lists.newArrayList(masterConvertRule), objectDescribeMap);
            closeSourceObjectDataAndLog(user, sourceObjectDescribe, sourceObjectDataList, masterConvertRule);
            stopWatch.lap("closeSourceObjectData");
        } else if (masterConvertRule.closeAfterAllDetailConvert() && !onlyMasterConvertRule(needCloseLogicConvertRules, masterConvertRule)) {
            closeSourceDetailObjectData(user, convertRuleDataContainer, objectData, detailObjectData, needCloseLogicConvertRules, sourceObjectDescribe, sourceObjectDataList);
            stopWatch.lap("closeSourceDetailObjectData");
        }
        stopWatch.logSlow(300);
    }

    private void trackerRecord(User user, ConvertRuleDataContainer convertRuleDataContainer, IObjectData objectData, Map<String, List<IObjectData>> detailObjectData) {
        List<MtConvertRule> convertRuleList = convertRuleDataContainer.getConvertRuleList();
        MtConvertRule masterConvertRule = convertRuleDataContainer.getMasterConvertRule();
        List<String> fromMasterIds = convertRuleDataContainer.getFromMasterIds();
        IObjectDescribe sourceObjectDescribe = convertRuleDataContainer.getObjectDescribe();
        List<MtConvertTracker> mtConvertTrackers = Lists.newArrayList();
        fromMasterIds.forEach(fromMasterId -> {
            MtConvertTracker mtConvertTracker = new MtConvertTracker();
            mtConvertTracker.setSourceApiName(sourceObjectDescribe.getApiName());
            mtConvertTracker.setSourceId(fromMasterId);
            mtConvertTracker.setTargetApiName(objectData.getDescribeApiName());
            mtConvertTracker.setTargetId(objectData.getId());
            mtConvertTracker.setEventId(convertRuleDataContainer.getEventId());
            mtConvertTracker.setRuleApiName(masterConvertRule.getApiName());
            mtConvertTrackers.add(mtConvertTracker);
        });
        List<DetailResource> fromDetails = convertRuleDataContainer.getFromDetails();
        Map<String, String> targetSourceMap = Maps.newHashMap();
        detailObjectData.forEach((detailApiName, detailObjectDataList) -> {
            Optional<MtConvertRule> mtConvertRule = convertRuleList.stream()
                    .filter(convertRule -> StringUtils.equals(detailApiName, convertRule.getTargetObjectDescribeApiName()))
                    .findFirst();
            if (!mtConvertRule.isPresent()) {
                return;
            }
            MtConvertRule convertRule = mtConvertRule.get();
            detailObjectDataList.forEach(detail -> {
                String associatedFieldApiName = convertRule.getAssociatedFieldApiName();
                String sourceDetailId = detail.get(associatedFieldApiName, String.class);
                if (StringUtils.isEmpty(sourceDetailId)) {
                    return;
                }
                for (DetailResource detailResource : fromDetails) {
                    detailResource.getDetails().forEach(detailInfo -> {
                        if (detailInfo.getIds().contains(sourceDetailId)) {
                            MtConvertTracker mtConvertTracker = new MtConvertTracker();
                            mtConvertTracker.setSourceMasterApiName(sourceObjectDescribe.getApiName());
                            mtConvertTracker.setSourceApiName(convertRule.getSourceObjectDescribeApiName());
                            mtConvertTracker.setSourceId(sourceDetailId);
                            mtConvertTracker.setSourceMasterId(detailResource.getMasterId());
                            mtConvertTracker.setTargetApiName(detailApiName);
                            mtConvertTracker.setTargetMasterApiName(objectData.getDescribeApiName());
                            mtConvertTracker.setTargetId(detail.getId());
                            mtConvertTracker.setTargetMasterId(objectData.getId());
                            mtConvertTracker.setEventId(convertRuleDataContainer.getEventId());
                            mtConvertTracker.setRuleApiName(masterConvertRule.getApiName());
                            mtConvertTrackers.add(mtConvertTracker);
                            targetSourceMap.put(detail.getId(), sourceDetailId);
                        }
                    });
                }
            });
        });
        objectConvertTrackerService.bulkCreate(user, mtConvertTrackers);
        Map<String, List<String>> detailIds = mtConvertTrackers.stream()
                .filter(tracker -> !StringUtils.isEmpty(tracker.getSourceMasterApiName()))
                .collect(Collectors.groupingBy(MtConvertTracker::getSourceApiName, Collectors.mapping(MtConvertTracker::getSourceId, Collectors.toList())));
        Map<String, List<IObjectData>> detailObjectList = Maps.newHashMap();
        detailIds.forEach((detailApiName, ids) -> {
            List<IObjectData> details = metaDataFindService.findObjectDataByIds(user.getTenantId(), ids, detailApiName);
            detailObjectList.put(detailApiName, details);
        });
        convertRuleDataContainer.setSourceDetailList(detailObjectList);
        convertRuleDataContainer.setTargetSourceIdMap(targetSourceMap);
    }

    private static IActionContext buildActionContextNeedCalculateCount(User user) {
        return ActionContextExt.of(user)
                .allowUpdateInvalid(false)
                .setSkipRelevantTeam(false)
                .setCalculateCount(true)
                .setCalculateFormula(true)
                .setCalculateCountFromDB(true)
                .getContext();
    }

    private boolean onlyMasterConvertRule(List<MtConvertRule> needCloseLogicConvertRules, MtConvertRule masterConvertRule) {
        return CollectionUtils.size(needCloseLogicConvertRules) == 1 && StringUtils.equals(masterConvertRule.getApiName(), needCloseLogicConvertRules.get(0).getApiName());
    }

    private void closeSourceDetailObjectData(User user, ConvertRuleDataContainer convertRuleDataContainer, IObjectData objectData, Map<String, List<IObjectData>> detailObjectData,
                                             List<MtConvertRule> needCloseLogicConvertRules, IObjectDescribe sourceObjectDescribe, List<IObjectData> sourceObjectDataList) {
        StopWatch stopWatch = StopWatch.create("closeSourceDetailObjectData");
        List<IObjectDescribe> sourceDetailDescribe = convertRuleDataContainer.getDetailDescribe();
        Map<String, IObjectDescribe> objectDescribeMap = sourceDetailDescribe.stream()
                .collect(Collectors.toMap(IObjectDescribe::getApiName, x -> x, (x, y) -> x));
        objectDescribeMap.put(sourceObjectDescribe.getApiName(), sourceObjectDescribe);
        doValidateRuleFieldConfig(convertRuleDataContainer.getConvertRuleList(), objectDescribeMap);
        MtConvertRule masterConvertRule = convertRuleDataContainer.getMasterConvertRule();
        Map<String, List<IObjectData>> objectDataList = Maps.newHashMap();
        objectDataList.put(sourceObjectDescribe.getApiName(), sourceObjectDataList);
        objectDataList.put(objectData.getDescribeApiName(), Lists.newArrayList(objectData));
        objectDataList.putAll(detailObjectData);
        Map<String, List<IObjectData>> sourceDetailData = Maps.newHashMap();
        IActionContext context = buildActionContextNeedCalculateCount(user);
        sourceDetailDescribe.forEach(detailDescribe -> {
            List<IObjectData> detailObjectDataList = metaDataFindService.findDetailObjectDataList(context, detailDescribe, sourceObjectDataList, buildCloseFieldFilter(needCloseLogicConvertRules, detailDescribe));
            objectDataList.put(detailDescribe.getApiName(), detailObjectDataList);
            sourceDetailData.put(detailDescribe.getApiName(), detailObjectDataList);
        });
        stopWatch.lap("findDetailObjectDataList");
        if (allDetailDataIsEmpty(sourceDetailData)) {
            closeSourceObjectDataAndLog(user, sourceObjectDescribe, sourceObjectDataList, masterConvertRule);
            stopWatch.lap("closeSourceObjectData");
            stopWatch.logSlow(20);
            return;
        }
        Map<String, List<IObjectData>> closedSourceDetailList = closeDetailDataAndLog(user, needCloseLogicConvertRules, convertRuleDataContainer.getFromDetails(), objectDataList, objectDescribeMap, true);
        stopWatch.lap("closeDetailDataAndBuildParam");
        Set<String> noNeedClosedMasterIds = allDetailDataClosed(sourceDetailData, objectDescribeMap, closedSourceDetailList, needCloseLogicConvertRules);
        stopWatch.lap("allDetailDataClosed");
        sourceObjectDataList.removeIf(data -> noNeedClosedMasterIds.contains(data.getId()));
        closeSourceObjectDataAndLog(user, sourceObjectDescribe, sourceObjectDataList, masterConvertRule);
        stopWatch.lap("closeSourceObjectData");
        stopWatch.logSlow(20);
    }

    private UnaryOperator<SearchTemplateQueryExt> buildCloseFieldFilter(List<MtConvertRule> needCloseLogicConvertRules, IObjectDescribe detailDescribe) {
        return query -> {
            needCloseLogicConvertRules.stream()
                    .filter(rule -> StringUtils.equals(detailDescribe.getApiName(), rule.getSourceObjectDescribeApiName()))
                    .findFirst()
                    .ifPresent(rule -> query.setFilters(FilterExt.of(Operator.ISN, rule.getCloseFieldApiName(), rule.getCloseFieldValue()).getFilters()));
            return query;
        };
    }

    private static boolean allDetailDataIsEmpty(Map<String, List<IObjectData>> sourceDetailData) {
        return sourceDetailData.entrySet().stream().allMatch(x -> CollectionUtils.empty(x.getValue()));
    }

    private Map<String, List<IObjectData>> closeDetailDataAndLog(User user, List<MtConvertRule> needCloseLogicConvertRules, List<DetailResource> fromDetails,
                                                                 Map<String, List<IObjectData>> objectDataList, Map<String, IObjectDescribe> objectDescribeMap, boolean isCreate) {
        Map<String, List<IObjectData>> closedSourceDetailList = Maps.newHashMap();
        MtConvertRule masterConvertRule = MtConvertRule.getMasterConvertRule(needCloseLogicConvertRules);
        Map<String, List<IObjectData>> needClosedSourceDetailInDB = Maps.newHashMap();
        Map<String, Map<String, Object>> detailUpdateFieldMap = Maps.newHashMap();
        needCloseLogicConvertRules.forEach(rule -> {
            String sourceObjectDescribeApiName = rule.getSourceObjectDescribeApiName();
            if (StringUtils.equals(sourceObjectDescribeApiName, masterConvertRule.getSourceObjectDescribeApiName())) {
                return;
            }
            IObjectDescribe describe = objectDescribeMap.get(sourceObjectDescribeApiName);
            List<IObjectData> dataList = objectDataList.get(sourceObjectDescribeApiName);
            if (Objects.isNull(describe) || CollectionUtils.empty(dataList)) {
                return;
            }
            List<String> fromIdList = Lists.newArrayList();
            CollectionUtils.nullToEmpty(fromDetails).stream()
                    .flatMap(detailResource -> detailResource.getDetails().stream()
                            .filter(detail -> StringUtils.equals(detail.getApiName(), rule.getSourceObjectDescribeApiName())))
                    .forEach(detail -> fromIdList.addAll(detail.getIds()));
            if (CollectionUtils.empty(fromIdList)) {
                return;
            }
            List<Wheres> closeWheres = rule.toCloseWheres(describe);
            if (CollectionUtils.empty(closeWheres)) {
                return;
            }
            IFilter filter = FilterExt.of(Operator.IN, IObjectData.ID, fromIdList).getFilter();
            SearchTemplateQueryExt queryExt = SearchTemplateQueryExt.ofWheresAndFilter(closeWheres, filter);
            ObjectDataFilter dataFilter = ObjectDataFilter.builder()
                    .describeExt(ObjectDescribeExt.of(describe))
                    .queryExt(queryExt)
                    .filterLabel(masterConvertRule.getName())
                    .build();
            List<IObjectData> needUpdateDataList = dataFilter.doFilter(dataList);
            log.info("closeDetailDataAndLog needUpdateDataList size:{}", CollectionUtils.size(needUpdateDataList));
            if (CollectionUtils.notEmpty(needUpdateDataList)) {
                needClosedSourceDetailInDB.put(describe.getApiName(), ObjectDataExt.copyList(needUpdateDataList));
                String closeFieldApiName = rule.getCloseFieldApiName();
                needUpdateDataList.forEach(data -> {
                    Object closeFieldValue = rule.convertCloseFieldValueSilently(describe);
                    data.set(closeFieldApiName, closeFieldValue);
                    Map<String, Object> updateField = Maps.newHashMap();
                    updateField.put(closeFieldApiName, closeFieldValue);
                    detailUpdateFieldMap.put(describe.getApiName(), updateField);
                });
                List<IObjectData> dataListUpdated = metaDataActionService.batchUpdateByFields(user, needUpdateDataList, Lists.newArrayList(closeFieldApiName));
                closedSourceDetailList.put(describe.getApiName(), dataListUpdated);
            }
        });
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                logDetailModifyRecord(user, objectDescribeMap, closedSourceDetailList, needClosedSourceDetailInDB, detailUpdateFieldMap);
            }
        });
        return closedSourceDetailList;
    }

    private void logDetailModifyRecord(User user, Map<String, IObjectDescribe> objectDescribeMap, Map<String, List<IObjectData>> closedSourceDetailList, Map<String, List<IObjectData>> needClosedSourceDetailInDB, Map<String, Map<String, Object>> detailUpdateFieldMap) {
        CollectionUtils.nullToEmpty(needClosedSourceDetailInDB).forEach((apiName, dataList) -> {
            List<IObjectData> detailDataList = closedSourceDetailList.get(apiName);
            dataList.forEach(data -> detailDataList.stream()
                    .filter(x -> StringUtils.equals(x.getId(), data.getId()))
                    .findFirst()
                    .ifPresent(it -> logService.log(User.systemUser(user.getTenantId()), EventType.MODIFY, ActionType.Modify,
                            objectDescribeMap.get(apiName), it, detailUpdateFieldMap.get(apiName), data, "convert-rule", null, null)));

        });
    }

    private Set<String> allDetailDataClosed(Map<String, List<IObjectData>> sourceDetailData, Map<String, IObjectDescribe> objectDescribeMap, Map<String, List<IObjectData>> closedSourceDetailList, List<MtConvertRule> needCloseLogicConvertRules) {
        Set<String> noNeedCloseMasterIds = Sets.newHashSet();
        for (Map.Entry<String, List<IObjectData>> entry : sourceDetailData.entrySet()) {
            String apiName = entry.getKey();
            List<IObjectData> dataList = entry.getValue();
            List<String> sourceDetailUpdatedIds = CollectionUtils.nullToEmpty(closedSourceDetailList.get(apiName))
                    .stream().map(IObjectData::getId).collect(Collectors.toList());
            Optional<MtConvertRule> mtConvertRule = needCloseLogicConvertRules.stream()
                    .filter(rule -> StringUtils.equals(rule.getSourceObjectDescribeApiName(), apiName))
                    .findFirst();
            List<IObjectData> noNeedCloseMasterDataList = dataList.stream()
                    .filter(filterNoNeedCloseData(sourceDetailUpdatedIds, mtConvertRule))
                    .collect(Collectors.toList());
            if (CollectionUtils.notEmpty(noNeedCloseMasterDataList)) {
                ObjectDescribeExt.of(objectDescribeMap.get(apiName)).getMasterDetailFieldDescribe()
                        .ifPresent(masterDetailFieldDescribe -> {
                            String fieldApiName = masterDetailFieldDescribe.getApiName();
                            dataList.stream()
                                    .map(data -> data.get(fieldApiName, String.class))
                                    .filter(StringUtils::isNotEmpty)
                                    .forEach(noNeedCloseMasterIds::add);
                        });
            }
        }
        return noNeedCloseMasterIds;
    }

    private Predicate<IObjectData> filterNoNeedCloseData(List<String> sourceDetailUpdatedIds, Optional<MtConvertRule> mtConvertRule) {
        return data -> {
            if (sourceDetailUpdatedIds.contains(data.getId())) {
                return false;
            }
            if (!mtConvertRule.isPresent()) {
                return true;
            }
            String closeFieldApiName = mtConvertRule.get().getCloseFieldApiName();
            return !StringUtils.equals(data.get(closeFieldApiName, String.class), mtConvertRule.get().getCloseFieldValue());
        };
    }

    private void closeSourceObjectDataAndLog(User user, IObjectDescribe objectDescribe, List<IObjectData> sourceObjectDataList, MtConvertRule masterConvertRule) {
        Object fieldValue = masterConvertRule.convertCloseFieldValueSilently(objectDescribe);
        List<Tuple<IObjectData, IObjectData>> dataMapList = Lists.newArrayList();
        for (IObjectData sourceObjectData : sourceObjectDataList) {
            IObjectData dbData = ObjectDataExt.of(sourceObjectData).copy();
            sourceObjectData.set(masterConvertRule.getCloseFieldApiName(), fieldValue);
            dataMapList.add(Tuple.of(sourceObjectData, dbData));
        }
        metaDataActionService.batchUpdateByFields(user, sourceObjectDataList, Lists.newArrayList(masterConvertRule.getCloseFieldApiName()));
        Map<String, Object> updateField = Maps.newHashMap();
        updateField.put(masterConvertRule.getCloseFieldApiName(), fieldValue);
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronizationAdapter() {
            @Override
            public void afterCommit() {
                dataMapList.forEach(dataMap ->
                        logService.log(User.systemUser(user.getTenantId()), EventType.MODIFY, ActionType.Modify,
                                objectDescribe, dataMap.getKey(), updateField, dataMap.getValue(), "convert-rule", null, null));
            }
        });
    }

    @Override
    public void doConvertRulesExcessCheck(IObjectDescribe objectDescribe, IObjectData masterObjectData, Map<String, IObjectDescribe> detailDescribes, Map<String, List<IObjectData>> detailDataMap, List<MtConvertRule> convertRules) {
        if (CollectionUtils.empty(convertRules) || BooleanUtils.isNotTrue(MtConvertRule.getMasterConvertRule(convertRules).getBackWrite())
                || convertRules.stream().noneMatch(rule -> StringUtils.equals(MtConvertRule.STRICT_CONTROL, rule.getExcessCheckMode()))) {
            return;
        }
        doValidateConvertRulesExcessCheck(objectDescribe, Lists.newArrayList(masterObjectData), convertRules);
        if (CollectionUtils.empty(detailDescribes) || CollectionUtils.empty(detailDataMap)) {
            return;
        }
        List<IObjectDescribe> detailDescribeList = Lists.newArrayList(detailDescribes.values()).stream()
                .filter(x -> detailDataMap.containsKey(x.getApiName()))
                .collect(Collectors.toList());
        detailDescribeList.forEach(describe -> doValidateConvertRulesExcessCheck(describe, detailDataMap.get(describe.getApiName()), convertRules));
    }

    private void doValidateConvertRulesExcessCheck(IObjectDescribe describe, List<IObjectData> objectDataList, List<MtConvertRule> convertRules) {
        convertRules.stream()
                .filter(rule -> StringUtils.equals(rule.getTargetObjectDescribeApiName(), describe.getApiName())
                        && StringUtils.equals(MtConvertRule.STRICT_CONTROL, rule.getExcessCheckMode()))
                .findFirst()
                .ifPresent(rule -> {
                    RuleCalculateResult validateResult = expressionCalculateLogicService.validateRules(describe,
                            objectDataList, Lists.newArrayList(rule.toValidateRule()));
                    if (validateResult.isMatch()) {
                        throw new ValidateException(rule.getExcessPrompt());
                    }
                });
    }

    @Override
    public void doValidateRuleFieldConfig(List<MtConvertRule> convertRules, Map<String, IObjectDescribe> objectDescribeMap) {
        MtConvertRule masterConvertRule = MtConvertRule.getMasterConvertRule(convertRules);
        CollectionUtils.nullToEmpty(convertRules).forEach(rule -> {
            String targetObjectApiName = rule.getTargetObjectDescribeApiName();
            String associatedFieldApiName = rule.getAssociatedFieldApiName();
            Optional.ofNullable(objectDescribeMap.get(targetObjectApiName))
                    .ifPresent(describe -> ObjectDescribeExt.of(describe).getActiveFieldDescribeByValidate(associatedFieldApiName, I18NKey.CONVERT_RULE_ASSOCIATED_FIELD_DISABLE_OR_DELETE, masterConvertRule.getName()));
            if (!rule.needCloseSourceOrder()) {
                return;
            }
            String sourceObjectApiName = rule.getSourceObjectDescribeApiName();
            String closeFieldApiName = rule.getCloseFieldApiName();
            Optional.ofNullable(objectDescribeMap.get(sourceObjectApiName))
                    .ifPresent(describe -> ObjectDescribeExt.of(describe).getActiveFieldDescribeByValidate(closeFieldApiName, I18NKey.CONVERT_RULE_CLOSE_FIELD_DISABLE_OR_DELETE, masterConvertRule.getName()));
        });
    }

    @Override
    public void processFilterByConvertRule(User user, IObjectDescribe objectDescribe, SearchTemplateQuery query, Supplier<Tuple<IObjectData, Map<String, List<IObjectData>>>> supplier, boolean isDoublePull) {
        IFilter convertRuleFilter = getConvertRuleFilter(query.getFilters());
        if (Objects.isNull(convertRuleFilter)) {
            return;
        }
        String convertRuleApiName = convertRuleFilter.getFieldValues().get(0);
        if (StringUtils.isEmpty(convertRuleApiName)) {
            return;
        }
        QueryConditionContainer queryConditionContainer = buildQueryConditionContainer(user, objectDescribe, convertRuleApiName, supplier, isDoublePull);
        Optional.ofNullable(queryConditionContainer.getFilters()).ifPresent(query::setFilters);
        Optional.ofNullable(queryConditionContainer.getWheres()).ifPresent(query::setWheres);
    }

    @Override
    public void processFilterByConvertRule(User user, IObjectDescribe objectDescribe, Query query, Supplier<Tuple<IObjectData, Map<String, List<IObjectData>>>> supplier, boolean isDoublePull) {
        IFilter convertRuleFilter = getConvertRuleFilter(query.getFilters());
        if (Objects.isNull(convertRuleFilter)) {
            return;
        }
        String convertRuleApiName = convertRuleFilter.getFieldValues().get(0);
        if (StringUtils.isEmpty(convertRuleApiName)) {
            return;
        }
        query.removeIf(searchQuery -> convertRuleFilter.equals(searchQuery.getFilter()));
        QueryConditionContainer queryConditionContainer = buildQueryConditionContainer(user, objectDescribe, convertRuleApiName, supplier, isDoublePull);
        Optional.ofNullable(queryConditionContainer.getFilters())
                .ifPresent(filters -> query.mergeSearchQuery(SearchQueryImpl.filters(filters)));
        Optional.ofNullable(queryConditionContainer.getWheres())
                .ifPresent(wheres -> query.mergeSearchQuery(SearchQueryImpl.wheres(wheres)));
    }

    public QueryConditionContainer buildQueryConditionContainer(User user, IObjectDescribe objectDescribe, String convertRuleApiName, Supplier<Tuple<IObjectData, Map<String, List<IObjectData>>>> supplier, boolean isDoublePull) {
        QueryConditionContainer queryConditionContainer = new QueryConditionContainer();
        List<MtConvertRule> mtConvertRuleList = objectConvertRuleService.findConvertRuleInInternalObjByApiName(user, convertRuleApiName);
        Optional<MtConvertRule> convertRule = mtConvertRuleList.stream()
                .filter(rule -> StringUtils.equals(rule.getSourceObjectDescribeApiName(), objectDescribe.getApiName()))
                .findFirst();
        if (!convertRule.isPresent()) {
            return queryConditionContainer;
        }

        MtConvertRule masterConvertRule = MtConvertRule.getMasterConvertRule(mtConvertRuleList);
        if (isDoublePull && masterConvertRule.isSimplyScene()) {
            String associatedFieldApiName = masterConvertRule.getAssociatedFieldApiName();
            IObjectData objectData = supplier.get().getKey();
            if (Objects.nonNull(objectData) && StringUtils.isNotEmpty(associatedFieldApiName)
                    && StringUtils.isNotEmpty(objectData.get(associatedFieldApiName, String.class))) {
                queryConditionContainer.addFilter(FilterExt.of(Operator.EQ, ObjectDataExt.ID, objectData.get(associatedFieldApiName, String.class)).getFilter());
            }
        }
        MtConvertRule mtConvertRule = convertRule.get();
        if (masterConvertRule.needCloseSourceOrder() && StringUtils.isNoneEmpty(mtConvertRule.getCloseFieldApiName(), mtConvertRule.getCloseFieldApiName())) {
            queryConditionContainer.addFilter(FilterExt.of(Operator.N, mtConvertRule.getCloseFieldApiName(), mtConvertRule.getCloseFieldValue()).getFilter());
        }
        getConvertRuleRangeRule(user, supplier, mtConvertRule, queryConditionContainer);
        List<IObjectMappingRuleInfo> mappingRuleInfoList = objectConvertRuleService.findObjectMappingRuleByApiName(user, masterConvertRule.getRuleApiName());
        IObjectMappingRuleInfo masterMappingRule = ObjectMappingExt.of(mappingRuleInfoList).getObjectMappingRuleInfo();
        mappingRuleInfoList.stream()
                .filter(rule -> StringUtils.equals(rule.getSourceApiName(), objectDescribe.getApiName()))
                .findFirst()
                .ifPresent(rule -> rule.getFieldMapping().stream()
                        .filter(fieldMap -> StringUtils.equals(fieldMap.getSourceFieldName(), MultiRecordType.RECORD_TYPE))
                        .findFirst()
                        .ifPresent(recordTypeMap -> {
                            List<String> recordTypeList = CollectionUtils.nullToEmpty(recordTypeMap.getOptionMapping()).stream()
                                    .map(IObjectMappingRuleEnumInfo::getSourceEnumCode)
                                    .collect(Collectors.toList());
                            if (CollectionUtils.notEmpty(recordTypeList)) {
                                queryConditionContainer.addFilter(FilterExt.of(Operator.IN, recordTypeMap.getSourceFieldName(), recordTypeList).getFilter());
                            }
                            if (CollectionUtils.empty(recordTypeList) && !StringUtils.equals(rule.getSourceApiName(), masterMappingRule.getSourceApiName())) {
                                queryConditionContainer.addFilter(FilterExt.of(Operator.IN, recordTypeMap.getSourceFieldName(), Lists.newArrayList(StringUtils.EMPTY)).getFilter());
                            }
                        }));
        return queryConditionContainer;
    }

    private void getConvertRuleRangeRule(User user, Supplier<Tuple<IObjectData, Map<String, List<IObjectData>>>> supplier, MtConvertRule
            mtConvertRule, QueryConditionContainer queryConditionContainer) {
        String whereType = mtConvertRule.getWhereType();
        if (StringUtils.equals(whereType, MtConvertRule.WHERE_TYPE_FUNCTION)) {
            Tuple<Wheres, Integer> functionWhereFilter = FunctionQueryTemplateUtils.getFunctionFilter(WheresExt.castToWheresList(mtConvertRule.getWheres()));
            if (Objects.isNull(functionWhereFilter)) {
                return;
            }
            String functionAPIName = FunctionQueryTemplateUtils.getFilterValue(functionWhereFilter);
            if (StringUtils.isEmpty(functionAPIName)) {
                return;
            }
            RunResult runResult = functionLogicService.findAndExecuteFunction(user, supplier, mtConvertRule.getSourceObjectDescribeApiName(), functionAPIName);
            FunctionQueryTemplateUtils.handleFunctionResult(user.getTenantId(), functionWhereFilter.getKey(), functionWhereFilter.getValue(), runResult);
            queryConditionContainer.addWheres(Lists.newArrayList(functionWhereFilter.getKey()));
        } else {
            queryConditionContainer.addWheres(WheresExt.castToWheresList(mtConvertRule.getWheres()));
        }
    }

    private IFilter getConvertRuleFilter(List<IFilter> filters) {
        IFilter filter = null;
        for (int i = 0; i < filters.size(); i++) {
            IFilter currentFilter = filters.get(i);
            if (ObjectUtils.equals(currentFilter.getValueType(), FilterExt.FilterValueTypes.CONVERT_RULE_VARIABLE)) {
                filter = currentFilter;
            }
        }
        if (Objects.isNull(filter)) {
            return null;
        }
        return filter;
    }
}
