package com.facishare.paas.appframework.metadata.count;

import com.facishare.paas.I18N;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.metadata.api.describe.Count;
import com.google.common.collect.Maps;

import java.util.Map;
import java.util.Objects;

/**
 * Created by zhouwr on 2018/4/25
 */
public class CountFieldCalculatorFactory {

    private static final Map<String, CountFieldCalculator> calculatorMap = Maps.newHashMap();

    static {
        calculatorMap.put(Count.TYPE_SUM, new SumCalculator());
        calculatorMap.put(Count.TYPE_AVERAGE, new AverageCalculator());
        calculatorMap.put(Count.TYPE_MAX, new MaxCalculator());
        calculatorMap.put(Count.TYPE_MIN, new MinCalculator());
        calculatorMap.put(Count.TYPE_COUNT, new CountCalculator());
    }

    public static CountFieldCalculator getCountFieldCalculator(String countType) {
        CountFieldCalculator calculator = calculatorMap.get(countType);
        return Objects.requireNonNull(calculator, I18N.text(I18NKey.UNSUPPORTED_COUNT_TYPE,countType));
    }

}
