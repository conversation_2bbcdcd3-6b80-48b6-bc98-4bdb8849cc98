package com.facishare.paas.appframework.metadata.cache.local.proxy;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.SearchTemplateExt;
import com.facishare.paas.appframework.metadata.cache.local.ThreadLocalCacheManager;
import com.facishare.paas.metadata.api.search.ISearchTemplate;
import com.facishare.paas.metadata.api.service.ISearchTemplateService;
import com.facishare.paas.metadata.common.MetadataContext;
import com.facishare.paas.metadata.exception.MetadataServiceException;
import lombok.Setter;
import lombok.experimental.Delegate;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.cache.Cache;
import org.springframework.cache.CacheManager;

import java.util.List;
import java.util.StringJoiner;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/5/26
 */
public class SearchTemplateCacheService implements ISearchTemplateService {
    @Setter
    @Delegate(excludes = Proxy.class)
    private ISearchTemplateService searchTemplateService;

    @Setter
    private CacheManager cacheManager;

    @Override
    public List<ISearchTemplate> findForCustom(MetadataContext context, String describeApiName, String extendAttribute) throws MetadataServiceException {
        Cache cache = cacheManager.getCache(ThreadLocalCacheManager.CacheName.SEARCH_TEMPLATE.name());
        String cacheKey = buildCacheKey(context, describeApiName, extendAttribute);
        List<ISearchTemplate> searchTemplates = cache.get(cacheKey, () -> searchTemplateService.findForCustom(context, describeApiName, extendAttribute));
        if (CollectionUtils.empty(searchTemplates)) {
            return searchTemplates;
        }
        return searchTemplates.stream()
                .map(ISearchTemplate::copy)
                .collect(Collectors.toList());
    }

    private String buildCacheKey(MetadataContext context, String describeApiName, String extendAttribute) {
        StringJoiner joiner = new StringJoiner("_");
        joiner.add(context.getTenantId());
        joiner.add(context.getUserId());
        joiner.add(context.getOutTenantId());
        joiner.add(context.getOutUserId());
        if (BooleanUtils.isTrue(context.getAttribute(IS_OUT_TEMPLATE))) {
            joiner.add("outer");
        }
        joiner.add(describeApiName);
        joiner.add(extendAttribute);
        return joiner.toString();
    }

    private interface Proxy {
        List<ISearchTemplate> findForCustom(MetadataContext context, String describeApiName, String extendAttribute) throws MetadataServiceException;
    }
}
