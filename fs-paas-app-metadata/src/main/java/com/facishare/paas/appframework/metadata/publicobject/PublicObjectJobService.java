package com.facishare.paas.appframework.metadata.publicobject;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobDetailInfo;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobInfo;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobStatus;
import com.facishare.paas.appframework.metadata.publicobject.module.PublicObjectJobType;

import java.util.Collection;
import java.util.List;
import java.util.Optional;

/**
 * Created by z<PERSON><PERSON>ju on 2023/12/12
 */
public interface PublicObjectJobService {

    String createPublicObjectJob(User user, PublicObjectJobInfo publicObjectJobInfo);

    PublicObjectJobInfo queryPublicObjectJobById(User user, String objectApiName, String jobId);

    Optional<PublicObjectJobInfo> queryPublicObjectJobByType(User user, String objectApiName, Collection<PublicObjectJobType> jobTypes);

    String updatePublicObjectJobStatus(User user, String objectApiName, String jobId, PublicObjectJobStatus jobStatus);

    void updatePublicObjectJobDisplayStatus(User user, String objectApiName, String jobId, String displayStatus);

    List<PublicObjectJobDetailInfo> queryPublicObjectJobDetail(User user, String jobId, PublicObjectJobStatus jobStatus);

    Integer queryPublicObjectJobDetailCount(User user, String jobId, PublicObjectJobStatus jobStatus);

    void initPublicObjectJobDetail(User user, String jobId, String objectApiName, PublicObjectJobStatus jobStatus, String downstreamTenantId);

    /**
     * 最后修改人需要记录为同意/拒绝邀请的人员id
     *
     * @param user           上游租户用户
     * @param objectApiName
     * @param jobId
     * @param jobStatus
     * @param downstreamUser 下游租户用户
     */
    void updatePublicObjectDetailJobStatus(User user, String objectApiName, String jobId, PublicObjectJobStatus jobStatus, User downstreamUser);

    Optional<PublicObjectJobDetailInfo> queryPublicObjectJobDetailByDownstreamTenantId(User user, String objectApiName,
                                                                                       String jobId, String downstreamTenantId);
}
