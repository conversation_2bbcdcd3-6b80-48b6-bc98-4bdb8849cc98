package com.facishare.paas.appframework.metadata.layout;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.*;
import com.facishare.paas.appframework.metadata.layout.component.BaseFlowTaskListComponentExt;
import com.facishare.paas.appframework.metadata.layout.factory.IComponentFactory;
import com.facishare.paas.appframework.metadata.layout.factory.IComponentFactoryManager;
import com.facishare.paas.appframework.metadata.layout.factory.ILayoutFactory;
import com.facishare.paas.appframework.metadata.layout.strcuture.LayoutStructureExt;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IComponent;
import com.facishare.paas.metadata.ui.layout.ILayout;
import com.google.common.collect.Maps;
import lombok.Builder;

import java.util.Map;
import java.util.Objects;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/3/11
 */
public class MobileFlowTaskListLayoutBuilder {

    private final LayoutExt layoutExt;
    private final PageType pageType;
    private final IComponentFactoryManager componentFactoryManager;
    private final DescribeLogicService describeLogicService;

    private final LayoutLogicService layoutLogicService;
    private final User user;

    private final IObjectDescribe objectDescribe;
    private final IObjectDescribe whatObjectDescribe;

    @Builder
    private MobileFlowTaskListLayoutBuilder(ILayout layout, PageType pageType, IComponentFactoryManager componentFactoryManager,
                                            DescribeLogicService describeLogicService, LayoutLogicService layoutLogicService, User user,
                                            IObjectDescribe objectDescribe, IObjectDescribe whatObjectDescribe) {
        this.layoutExt = LayoutExt.of(layout);
        this.pageType = pageType;
        this.componentFactoryManager = componentFactoryManager;
        this.describeLogicService = describeLogicService;
        this.layoutLogicService = layoutLogicService;
        this.user = user;
        this.objectDescribe = objectDescribe;
        this.whatObjectDescribe = whatObjectDescribe;
    }

    public LayoutExt getMobileListLayout() {
        IObjectDescribe describe = getDescribe();
        IObjectDescribe whatDescribe = getWhatObject();
        FlowTaskLayoutExt mobileListLayout;
        boolean enableMobileLayout = layoutExt.isEnableMobileLayout();
        if (enableMobileLayout) {
            mobileListLayout = FlowTaskLayoutExt.of(layoutExt.getMobileLayout());
        } else {
            mobileListLayout = FlowTaskLayoutExt.of(Maps.newHashMap());
            mobileListLayout.setLayoutStructure(getLayoutStructure(describe, whatDescribe));
            mobileListLayout.setRefObjectApiName(layoutExt.getRefObjectApiName());
        }
        mobileListLayout.setWhatApiName(layoutExt.getWhatApiName());

        if (!PageType.Designer.equals(pageType)) {
            return LayoutExt.of(mobileListLayout.getLayout());
        }

        ILayout objectLayout = layoutLogicService.findObjectLayoutWithWhatDescribe(user, describe, whatDescribe);
        mobileListLayout.getFlowTaskListMobileComponent()
                .ifPresent(flowTaskListMobileComponentExt -> FlowTaskListMobileComponentRender.builder()
                        .describeExt(ObjectDescribeExt.of(describe))
                        .whatDescribeExt(ObjectDescribeExt.of(whatDescribe))
                        .user(user)
                        .flowTaskListMobileComponentExt(flowTaskListMobileComponentExt)
                        .pageType(pageType)
                        .objectLayout(objectLayout)
                        .build()
                        .render());

        return LayoutExt.of(mobileListLayout.getLayout());
    }

    private IObjectDescribe getWhatObject() {
        if (Objects.nonNull(whatObjectDescribe)) {
            return whatObjectDescribe;
        }
        return describeLogicService.findObject(user.getTenantId(), layoutExt.getWhatApiName());
    }

    private IObjectDescribe getDescribe() {
        if (Objects.nonNull(objectDescribe)) {
            return objectDescribe;
        }
        return describeLogicService.findObject(user.getTenantId(), layoutExt.getRefObjectApiName());
    }

    private Map<String, Object> getLayoutStructure(IObjectDescribe objectDescribe, IObjectDescribe whatObjectDescribe) {
        IComponentFactory managerFactory = componentFactoryManager.getFactory(BaseFlowTaskListComponentExt.COMPONENT_TYPE_TASK_LIST_MOBILE);
        ILayoutFactory.Context context = ILayoutFactory.Context.of(user, whatObjectDescribe);
        IComponent component = managerFactory.createDefaultComponent(context, objectDescribe);
        LayoutStructureExt layoutStructureExt = LayoutStructureExt.generateDefaultFlowTaskListLayoutStructure(component);
        return layoutStructureExt.getContainerDocument();
    }
}
