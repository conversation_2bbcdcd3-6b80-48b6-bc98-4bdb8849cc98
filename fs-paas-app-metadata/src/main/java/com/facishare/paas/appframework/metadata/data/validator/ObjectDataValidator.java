package com.facishare.paas.appframework.metadata.data.validator;

import com.facishare.paas.appframework.common.util.AppFrameworkConfig;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.common.util.RuntimeUtils;
import com.facishare.paas.appframework.common.util.TenantUtil;
import com.facishare.paas.appframework.core.exception.ValidateException;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.fxiaoke.log.AuditLog;
import com.fxiaoke.log.BizLogClient;
import com.fxiaoke.log.dto.AuditLogDTO;
import com.fxiaoke.pb.Pojo2Protobuf;
import com.github.trace.TraceContext;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;

@Slf4j
@Component
public class ObjectDataValidator implements ApplicationContextAware {
    private final Map<String, IFieldValidator> dataTypeValidatorMap = Maps.newHashMap();
    private final Map<String, IFieldValidator> maxLengthValidatorMap = Maps.newHashMap();
    private Map<Class<? extends IFieldValidator>, Map<String, IFieldValidator>> map = Maps.newHashMap();

    @PostConstruct
    private void init() {
        map.put(IDataTypeValidator.class, dataTypeValidatorMap);
        map.put(IMaxLengthValidator.class, maxLengthValidatorMap);
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        try {
            Map<String, IDataTypeValidator> springMap = applicationContext.getBeansOfType(IDataTypeValidator.class);
            if (CollectionUtils.notEmpty(springMap)) {
                springMap.forEach((x, y) -> y.supportFieldTypes().forEach(type -> dataTypeValidatorMap.put(type, y)));
            }

            Map<String, IMaxLengthValidator> beanMap = applicationContext.getBeansOfType(IMaxLengthValidator.class);
            if (CollectionUtils.notEmpty(beanMap)) {
                beanMap.forEach((x, y) -> y.supportFieldTypes().forEach(type -> maxLengthValidatorMap.put(type, y)));
            }
        } catch (BeansException e) {
            log.error("init FieldValidator error", e);
        }
    }


    public void validateDataType(User user, IObjectData data, IObjectDescribe describe) {
        doValidate(user, data, describe, IDataTypeValidator.class, (fieldApiName, validator) -> {
            if (Objects.nonNull(validator)) {
                ((IDataTypeValidator) validator).validateDataType(fieldApiName, data, describe);
            }
        });
    }

    public void validateMaxLength(User user, IObjectData data, IObjectDescribe describe) {
        doValidate(user, data, describe, IMaxLengthValidator.class, (fieldApiName, validator) -> {
            if (Objects.nonNull(validator)) {
                ((IMaxLengthValidator) validator).validateMaxLength(fieldApiName, data, describe);
            }
        });
    }

    private void doValidate(User user, IObjectData data, IObjectDescribe describe, Class<? extends IFieldValidator> validatorClass, BiConsumer<String, IFieldValidator> invoker) {
        ObjectDescribeExt describeExt = ObjectDescribeExt.of(describe);
        ObjectDataExt.of(data).toMap().keySet().forEach(key -> {
            Optional<IFieldDescribe> fieldDescribe = describeExt.getFieldDescribeSilently(key);
            if (!fieldDescribe.isPresent()) {
                return;
            }

            IFieldValidator validator = map.getOrDefault(validatorClass, Maps.newHashMap()).get(fieldDescribe.get().getType());
            if (Objects.nonNull(validator)) {
                try {
                    invoker.accept(key, validator);
                } catch (ValidateException e) {
                    if (!AppFrameworkConfig.isGrayDataTypeValidate(user.getTenantId()) ||
                            AppFrameworkConfig.isIgnoreDataTypeValidate(describe.getApiName(), key)) {
                        AuditLogDTO dto = AuditLogDTO.builder()
                                .appName(RuntimeUtils.getAppName())
                                .serverIp(RuntimeUtils.getServerIp())
                                .profile(RuntimeUtils.getProfile())
                                .action("dataValidate")
                                .tenantId(user.getTenantId())
                                .userId(user.getUserId())
                                .objectApiNames(describe.getApiName())
                                .num(0)
                                .message(e.getMessage())
                                .traceId(TraceContext.get().getTraceId())
                                .extra(validatorClass.getCanonicalName())
                                .build();
                        BizLogClient.send("biz-audit-log", Pojo2Protobuf.toMessage(dto, AuditLog.class).toByteArray());
                    } else {
                        throw e;
                    }
                }
            }
        });
    }
}
