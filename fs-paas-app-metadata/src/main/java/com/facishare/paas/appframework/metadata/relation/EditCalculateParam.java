package com.facishare.paas.appframework.metadata.relation;

import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.appframework.metadata.ObjectDescribeExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.impl.ObjectData;
import com.github.autoconf.helper.ConfigHelper;
import com.google.common.base.Strings;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.Builder;
import lombok.Data;

import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhouwr on 2023/8/3.
 */
@Data
public class EditCalculateParam {
    public static final String APP_NAME = ConfigHelper.getProcessInfo().getName();
    public static final String SERVER_IP = ConfigHelper.getProcessInfo().getIp();
    public static final String PROFILE = ConfigHelper.getProcessInfo().getProfile();

    private IObjectData masterData;
    private Map<String, List<IObjectData>> detailDataMap;
    private IObjectData oldMasterData;
    private Map<String, List<IObjectData>> oldDetailDataMap;
    private Map<String, Object> masterModifyData;
    private Map<String, List<IObjectData>> detailAddDataMap;
    private Map<String, List<IObjectData>> detailDeleteDataMap;
    private Map<String, List<IObjectData>> detailModifyDataMap;
    private IObjectDescribe masterDescribe;
    private Map<String, IObjectDescribe> detailDescribeMap;
    private boolean excludeDefaultValue;
    private boolean includeQuoteField;
    private boolean excludeLookupRelateField;
    private boolean fillNewDataBeforeCalculate;

    private boolean filterDefaultValueByCalculateFields;
    private Set<String> masterCalculateFields;
    private Map<String, Map<String, Set<String>>> detailCalculateFields;
    private Function<IObjectData, String> getIndexByObjectData;

    private final FieldRelationGraph graph;

    @Builder
    public EditCalculateParam(IObjectData masterData, Map<String, List<IObjectData>> detailDataMap,
                              IObjectData oldMasterData, Map<String, List<IObjectData>> oldDetailDataMap,
                              Map<String, Object> masterModifyData, Map<String, List<IObjectData>> detailAddDataMap,
                              Map<String, List<IObjectData>> detailDeleteDataMap, Map<String, List<IObjectData>> detailModifyDataMap,
                              IObjectDescribe masterDescribe, Map<String, IObjectDescribe> detailDescribeMap,
                              boolean excludeDefaultValue, boolean includeQuoteField, boolean excludeLookupRelateField,
                              Set<String> masterCalculateFields, Map<String, Map<String, Set<String>>> detailCalculateFields,
                              Function<IObjectData, String> getDataIndex, boolean fillNewDataBeforeCalculate,
                              boolean filterDefaultValueByCalculateFields, FieldRelationGraph graph) {
        this.masterData = masterData;
        this.detailDataMap = detailDataMap;
        this.oldMasterData = oldMasterData;
        this.oldDetailDataMap = oldDetailDataMap;

        this.masterModifyData = masterModifyData;
        this.detailAddDataMap = detailAddDataMap;
        this.detailDeleteDataMap = detailDeleteDataMap;
        this.detailModifyDataMap = detailModifyDataMap;

        this.masterDescribe = masterDescribe;
        this.detailDescribeMap = detailDescribeMap;

        this.excludeDefaultValue = excludeDefaultValue;
        this.includeQuoteField = includeQuoteField;
        this.excludeLookupRelateField = excludeLookupRelateField;

        this.masterCalculateFields = masterCalculateFields;
        this.detailCalculateFields = detailCalculateFields;
        this.getIndexByObjectData = getDataIndex;

        this.fillNewDataBeforeCalculate = fillNewDataBeforeCalculate;
        this.filterDefaultValueByCalculateFields = filterDefaultValueByCalculateFields;
        this.graph = graph;
    }

    public EditCalculateParam initWithUIEvent() {
        this.masterCalculateFields = initMasterCalculateFields(this.masterDescribe, this.masterModifyData);
        this.detailCalculateFields = initDetailCalculateFields(this.detailAddDataMap, this.detailModifyDataMap);
        this.getIndexByObjectData = objectData -> ObjectDataExt.of(objectData).getTemporaryId();
        this.fillNewDataBeforeCalculate = true;
        this.filterDefaultValueByCalculateFields = true;
        return this;
    }

    public EditCalculateParam initModifyData(FieldRelationCalculateService fieldRelationCalculateService, BiConsumer<ObjectDataExt, String> setIndex2ObjectData) {
        if (Objects.isNull(this.oldMasterData)) {
            this.oldMasterData = new ObjectData();
        }
        this.oldDetailDataMap = CollectionUtils.nullToEmpty(this.oldDetailDataMap);
        this.detailDescribeMap = CollectionUtils.nullToEmpty(this.detailDescribeMap);
        this.detailDataMap = CollectionUtils.nullToEmpty(this.detailDataMap);
        initCalculateFields(fieldRelationCalculateService);

        this.masterModifyData = ObjectDataExt.of(this.oldMasterData).diff(this.masterData, this.masterDescribe);
        Map<String, List<IObjectData>> detailAddDataMap = Maps.newHashMap();
        Map<String, List<IObjectData>> detailDeleteDataMap = Maps.newHashMap();
        Map<String, List<IObjectData>> detailModifyDataMap = Maps.newHashMap();

        this.detailDataMap.forEach((apiName, dataList) -> {
            IObjectDescribe detailDescribe = this.detailDescribeMap.get(apiName);
            if (Objects.isNull(detailDescribe)) {
                return;
            }
            List<IObjectData> oldDetailDataList = this.oldDetailDataMap.getOrDefault(apiName, Collections.emptyList());
            Map<String, IObjectData> oldDataMap = Maps.newHashMap();
            for (IObjectData objectData : oldDetailDataList) {
                String index = getIndexByObjectData(objectData);
                if (!Strings.isNullOrEmpty(index)) {
                    oldDataMap.put(index, objectData);
                }
            }

            //新建的从数据
            List<IObjectData> addDataList = Lists.newArrayList();
            //更新的从数据
            List<IObjectData> modifyDataList = Lists.newArrayList();
            //删除的从数据
            List<IObjectData> deleteDataList = oldDetailDataList.stream()
                    .filter(it -> Strings.isNullOrEmpty(getIndexByObjectData(it)))
                    .collect(Collectors.toList());

            for (IObjectData data : dataList) {
                String index = getIndexByObjectData(data);
                IObjectData oldData = oldDataMap.get(index);
                // old 没有就是新增的
                if (Objects.isNull(oldData)) {
                    addDataList.add(data);
                    continue;
                }
                // 更新的数据需要diff
                Map<String, Object> objectMap = ObjectDataExt.of(oldData).diff(data, detailDescribe);
                if (CollectionUtils.notEmpty(objectMap)) {
                    ObjectDataExt objectDataExt = ObjectDataExt.of(objectMap);
                    setIndex2ObjectData.accept(objectDataExt, index);
                    modifyDataList.add(objectDataExt);
                }
            }

            if (CollectionUtils.notEmpty(addDataList)) {
                detailAddDataMap.put(apiName, addDataList);
            }
            if (CollectionUtils.notEmpty(deleteDataList)) {
                detailDeleteDataMap.put(apiName, deleteDataList);
            }
            if (CollectionUtils.notEmpty(modifyDataList)) {
                detailModifyDataMap.put(apiName, modifyDataList);
            }
        });
        this.detailAddDataMap = detailAddDataMap;
        this.detailDeleteDataMap = detailDeleteDataMap;
        this.detailModifyDataMap = detailModifyDataMap;
        return this;
    }

    private void initCalculateFields(FieldRelationCalculateService fieldRelationCalculateService) {
        this.masterCalculateFields = CollectionUtils.nullToEmpty(this.masterCalculateFields);
        this.detailCalculateFields = CollectionUtils.nullToEmpty(this.detailCalculateFields);
        if (CollectionUtils.empty(this.masterCalculateFields) && CollectionUtils.empty(this.detailCalculateFields)) {
            return;
        }
        Map<String, Set<String>> unReCalculateFields = findUnReCalculateFields(fieldRelationCalculateService);
        if (CollectionUtils.empty(unReCalculateFields)) {
            return;
        }
        Set<String> masterFields = unReCalculateFields.get(masterDescribe.getApiName());
        if (CollectionUtils.notEmpty(masterFields) && CollectionUtils.notEmpty(this.masterCalculateFields)) {
            this.masterCalculateFields.removeAll(masterFields);
        }

        this.detailCalculateFields.forEach((describeApiName, indexFieldMap) -> {
            Set<String> detailFields = unReCalculateFields.get(describeApiName);
            if (CollectionUtils.empty(detailFields)) {
                return;
            }
            indexFieldMap.forEach((index, fields) -> fields.removeAll(detailFields));
        });
    }

    private Map<String, Set<String>> findUnReCalculateFields(FieldRelationCalculateService fieldRelationCalculateService) {
        if (Objects.isNull(this.graph) || Objects.isNull(fieldRelationCalculateService)) {
            return Collections.emptyMap();
        }
        return fieldRelationCalculateService.findUnReCalculateFields(this.masterDescribe, this.detailDescribeMap, this.graph);
    }

    private Set<String> initMasterCalculateFields(IObjectDescribe masterDescribe, Map<String, Object> masterModifyData) {
        List<IFieldDescribe> defaultValueFields = ObjectDescribeExt.of(masterDescribe).getDefaultValueFields();
        return defaultValueFields.stream()
                .map(IFieldDescribe::getApiName)
                .filter(it -> !masterModifyData.containsKey(it))
                .collect(Collectors.toSet());
    }

    private Map<String, Map<String, Set<String>>> initDetailCalculateFields(Map<String, List<IObjectData>> detailAddDataMap,
                                                                            Map<String, List<IObjectData>> detailModifyDataMap) {
        Map<String, Map<String, Set<String>>> result = Maps.newHashMap();
        for (IObjectDescribe detailDescribe : CollectionUtils.nullToEmpty(detailDescribeMap).values()) {
            List<IFieldDescribe> defaultValueFields = ObjectDescribeExt.of(detailDescribe).getDefaultValueFields();
            List<IObjectData> addDataList = CollectionUtils.nullToEmpty(detailAddDataMap).getOrDefault(detailDescribe.getApiName(), Collections.emptyList());
            List<IObjectData> modifyDataList = CollectionUtils.nullToEmpty(detailModifyDataMap).getOrDefault(detailDescribe.getApiName(), Collections.emptyList());

            Map<String, Set<String>> map = Maps.newHashMap();
            for (IFieldDescribe field : defaultValueFields) {
                for (IObjectData objectData : addDataList) {
                    if (!objectData.containsField(field.getApiName())) {
                        map.computeIfAbsent(ObjectDataExt.of(objectData).getTemporaryId(), key -> Sets.newHashSet())
                                .add(field.getApiName());
                    }
                }
                for (IObjectData objectData : modifyDataList) {
                    if (!objectData.containsField(field.getApiName())) {
                        map.computeIfAbsent(ObjectDataExt.of(objectData).getTemporaryId(), key -> Sets.newHashSet())
                                .add(field.getApiName());
                    }
                }
            }
            if (CollectionUtils.notEmpty(map)) {
                result.put(detailDescribe.getApiName(), ImmutableMap.copyOf(map));
            }
        }
        return ImmutableMap.copyOf(result);
    }

    public boolean unCalculateFields(String describeApiName, IObjectData objectData, String fieldName) {
        if (!filterDefaultValueByCalculateFields) {
            return false;
        }
        if (Objects.equals(masterDescribe.getApiName(), describeApiName)) {
            return !masterCalculateFields.contains(fieldName);
        }
        Map<String, Set<String>> calculateFieldMap = detailCalculateFields.getOrDefault(describeApiName, Collections.emptyMap());
        Set<String> calculateFields = calculateFieldMap.getOrDefault(getIndexByObjectData(objectData), Collections.emptySet());
        return !calculateFields.contains(fieldName);
    }

    public List<IObjectDescribe> getDetailDescribes() {
        return Lists.newArrayList(CollectionUtils.nullToEmpty(detailDescribeMap).values());
    }

    public Set<String> getAllDescribeApiNames() {
        Set<String> describeApiNames = Sets.newHashSet(masterDescribe.getApiName());
        if (CollectionUtils.notEmpty(detailDataMap)) {
            describeApiNames.addAll(detailDataMap.keySet());
        }
        return describeApiNames;
    }

    public String getIndexByObjectData(IObjectData objectData) {
        return getIndexByObjectData.apply(objectData);
    }


    public Map<String, Object> diffMasterWithDefaultValue(IObjectData beforeObject) {
        Set<String> defaultValueFields = ObjectDescribeExt.of(masterDescribe).getDefaultValueFields().stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet());
        Map<String, Object> diffData = diff(beforeObject, masterData, masterDescribe, defaultValueFields);
        if (CollectionUtils.empty(diffData)) {
            return Maps.newHashMap();
        }
        return getDetailDiff(beforeObject, diffData);
    }

    public Map<String, List<Map<String, Object>>> diffDetailsWithDefaultValue(Map<String, List<IObjectData>> beforeDetailDescribeMap) {
        Map<String, List<Map<String, Object>>> result = Maps.newHashMap();
        if (CollectionUtils.empty(detailCalculateFields)) {
            return result;
        }
        detailDataMap.forEach((objectApiName, dataList) -> {
            List<Map<String, Object>> diffDataList = Lists.newArrayList();
            IObjectDescribe detailsDescribe = detailDescribeMap.get(objectApiName);
            Set<String> defaultValueFields = ObjectDescribeExt.of(detailsDescribe).getDefaultValueFields().stream().map(IFieldDescribe::getApiName).collect(Collectors.toSet());
            List<IObjectData> beforeDetailDataList = beforeDetailDescribeMap.get(objectApiName);
            Map<String, IObjectData> beforeDataMap = beforeDetailDataList.stream()
                    .collect(Collectors.toMap(this::getIndexByObjectData, Function.identity()));

            for (IObjectData data : dataList) {
                String dataIndex = getIndexByObjectData(data);
                IObjectData beforeData = beforeDataMap.get(dataIndex);
                Map<String, Object> diffData = diff(beforeData, data, detailsDescribe, defaultValueFields);
                if (CollectionUtils.notEmpty(diffData)) {
                    Map<String, Object> detailDiff = getDetailDiff(beforeData, diffData);
                    diffDataList.add(detailDiff);
                }
            }
            if (CollectionUtils.notEmpty(diffDataList)) {
                result.put(objectApiName, diffDataList);
            }
        });
        return result;
    }

    private Map<String, Object> diff(IObjectData beforeObject, IObjectData objectData, IObjectDescribe objectDescribe, Set<String> defaultValueFields) {
        if (CollectionUtils.empty(defaultValueFields)) {
            return Maps.newHashMap();
        }
        Map<String, Object> dataDiff = ObjectDataExt.of(beforeObject).diff(objectData, objectDescribe);
        // 只保留默认值
        dataDiff.keySet().removeIf(it -> !defaultValueFields.contains(it));
        return dataDiff;
    }


    private Map<String, Object> getDetailDiff(IObjectData beforeData, Map<String, Object> diffData) {
        Map<String, Object> detailDiff = Maps.newHashMap();
        detailDiff.put(IObjectData.ID, beforeData.getId());
        detailDiff.put(ObjectDataExt.DATA_INDEX, getIndexByObjectData(beforeData));
        detailDiff.put("after_data", diffData);
        detailDiff.put("before_data", getBeforeDiffData(beforeData, diffData));
        return detailDiff;
    }

    private Map<String, Object> getBeforeDiffData(IObjectData beforeObject, Map<String, Object> dataDiff) {
        if (CollectionUtils.empty(dataDiff)) {
            return Maps.newHashMap();
        }
        Map<String, Object> beforeDiffData = Maps.newHashMap();
        dataDiff.forEach((key, afterValue) -> beforeDiffData.put(key, beforeObject.get(key)));
        return beforeDiffData;
    }

    public boolean canCalculate() {
        if (CollectionUtils.empty(detailDataMap)) {
            return true;
        }
        return detailDataMap.values().stream()
                .flatMap(Collection::stream)
                .noneMatch(it -> Strings.isNullOrEmpty(getIndexByObjectData(it)));
    }

    /**
     * @return 主对象变化的字段
     */
    public List<String> getMasterChangedFields() {
        if (CollectionUtils.empty(masterModifyData)) {
            return Lists.newArrayList();
        }
        return Lists.newArrayList(masterModifyData.keySet());
    }

    /**
     * @return 从对象变化的字段
     */
    public Map<String, List<String>> getDetailChangedFields() {
        Map<String, List<String>> result = Maps.newHashMap();
        if (CollectionUtils.notEmpty(detailModifyDataMap)) {
            detailModifyDataMap.forEach((apiName, dataList) -> {
                result.put(apiName, batchFindChangedFields(dataList));
            });
        }
        return result;
    }

    private List<String> batchFindChangedFields(List<IObjectData> dataList) {
        return CollectionUtils.nullToEmpty(dataList).stream()
                .map(data -> ObjectDataExt.of(data).toMap().keySet())
                .flatMap(Collection::stream)
                .distinct()
                .collect(Collectors.toList());
    }

    /**
     * @return 有删除或新增数据的从对象apiName
     */
    public List<String> getDescribeApiNameListDeleteOrAdd() {
        List<String> result = Lists.newArrayList();
        if (CollectionUtils.notEmpty(detailAddDataMap)) {
            result.addAll(detailAddDataMap.keySet());
        }
        if (CollectionUtils.notEmpty(detailDeleteDataMap)) {
            result.addAll(detailDeleteDataMap.keySet());
        }
        return result;
    }

}
