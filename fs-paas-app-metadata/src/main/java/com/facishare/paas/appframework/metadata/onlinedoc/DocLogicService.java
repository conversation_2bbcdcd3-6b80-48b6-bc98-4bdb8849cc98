package com.facishare.paas.appframework.metadata.onlinedoc;

import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.onlinedoc.model.GetPreviewUrl;
import com.facishare.paas.appframework.metadata.onlinedoc.model.QueryFileList;

import java.util.Map;

/**
 * <AUTHOR> create by liy on 2024/6/5
 */
public interface DocLogicService {
    /**
     * 查询文件列表
     */
    QueryFileList.Result queryFileList(User user,
                                       String appType,
                                       String pluginApiName,
                                       boolean needNextPage,
                                       QueryFileList.File folder,
                                       QueryFileList.Group group,
                                       Map<String, Object> extraInfo);

    /**
     * 获取预览url
     */
    GetPreviewUrl.Result getPreviewUrl(User user, String appType,
                                       String pluginApiName,
                                       QueryFileList.Group group,
                                       QueryFileList.File file);
}
