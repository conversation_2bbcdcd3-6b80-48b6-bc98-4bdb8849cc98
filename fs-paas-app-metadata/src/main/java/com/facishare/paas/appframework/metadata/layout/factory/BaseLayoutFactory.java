package com.facishare.paas.appframework.metadata.layout.factory;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2024/3/5
 */
public abstract class BaseLayoutFactory implements ILayoutFactory {

    private final IComponentFactoryManager componentFactoryManager;

    protected BaseLayoutFactory(IComponentFactoryManager componentFactoryManager) {
        this.componentFactoryManager = componentFactoryManager;
    }

    IComponentFactory getComponentFactory(String type) {
        return componentFactoryManager.getFactory(type);
    }
}
