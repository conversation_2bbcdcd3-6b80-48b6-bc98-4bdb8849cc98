package com.facishare.paas.appframework.metadata.button;

import com.facishare.rest.core.annotation.Body;
import com.facishare.rest.core.annotation.HeaderMap;
import com.facishare.rest.core.annotation.POST;
import com.facishare.rest.core.annotation.RestResource;

import java.util.Map;

@RestResource(
        value = "NCRM",
        desc = "NCRM服务", // ignoreI18n
        contentType = "application/json",
        codec = "com.facishare.paas.appframework.common.service.codec.RestResultCodeC"
)
public interface LayoutDesignerButtonProviderProxy {
    @POST(value = "/API/v1/rest/object/LayoutDesignerButtonProvider/service/getButtons", desc = "查设计器中的可用按钮")
    GetLayoutDesignerButton.RestResult getLayoutDesignerButton(@Body GetLayoutDesignerButton.Arg arg, @HeaderMap Map<String, String> header);
}
