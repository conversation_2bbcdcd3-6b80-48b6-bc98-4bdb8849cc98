package com.facishare.paas.appframework.metadata.cache.local;

import lombok.Lombok;
import lombok.NonNull;
import lombok.experimental.Delegate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.Cache;

import java.util.Objects;
import java.util.concurrent.Callable;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022/7/20
 */

@Slf4j
public class CacheWrapper implements Cache {

    @Delegate(excludes = Proxy.class)
    private final Cache cache;

    public CacheWrapper(@NonNull Cache cache) {
        if (CacheWrapper.class.isAssignableFrom(cache.getClass())) {
            this.cache = ((CacheWrapper) cache).cache;
        } else {
            this.cache = cache;
        }
    }

    @Override
    public <T> T get(Object key, Callable<T> valueLoader) {
        try {
            return cache.get(key, valueLoader);
        } catch (ValueRetrievalException e) {
            log.warn("get cache fail! key:{}, cache:{}", key, cache.getName(), e);
            Throwable cause = e.getCause();
            Throwable ex = Objects.isNull(cause) ? e : cause;
            throw Lombok.sneakyThrow(ex);
        }
    }

    interface Proxy {
        <T> T get(Object key, Callable<T> valueLoader);
    }

}
