package com.facishare.paas.appframework.metadata.domain;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by zhouwr on 2022/9/29.
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class DomainPluginInstanceEvent {
    private String tenantId;
    private String actionCode;
    private String id;
    private String pluginApiName;
    private String objectApiName;
    private String fieldApiName;
}
