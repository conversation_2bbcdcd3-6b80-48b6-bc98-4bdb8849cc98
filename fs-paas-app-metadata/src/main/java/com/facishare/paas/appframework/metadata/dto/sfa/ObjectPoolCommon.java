package com.facishare.paas.appframework.metadata.dto.sfa;

import com.alibaba.fastjson.annotation.JSONField;
import com.google.gson.annotations.SerializedName;
import lombok.*;

import java.util.List;

public interface ObjectPoolCommon {
    @Data
    @NoArgsConstructor
    class Arg {
        @SerializedName("ObjectIDs")
        @JSONField(name = "ObjectIDs")
        private List<String> objectIDs;
        @SerializedName("PoolID")
        @JSONField(name = "PoolID")
        private  String objectPoolId;
    }

    @Data
    class Result extends BaseResult {
        private PoolRestResult value;
    }

    @Data
    class PoolRestResult {
        @SerializedName("SuccessList")
        @JSONField(name = "SuccessList")
        private List<String> successList;

        @SerializedName("FailedList")
        @JSONField(name = "FailedList")
        private List<String> failedList;

        @SerializedName("ErrorListE")
        @JSONField(name = "ErrorList")
        private List<String> errorList;
    }
}
