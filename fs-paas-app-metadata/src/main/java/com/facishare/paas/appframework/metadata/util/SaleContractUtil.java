package com.facishare.paas.appframework.metadata.util;

import com.facishare.crm.openapi.Utils;
import com.facishare.paas.appframework.common.util.CollectionUtils;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.util.List;

/**
 * 根据开关下发合同对象
 */
@Slf4j
public class SaleContractUtil {

    public static void grayHandle(String tenantId, List<IObjectDescribe> result) {
        if (StringUtils.isBlank(tenantId) || CollectionUtils.empty(result)) {
            return;
        }
        try {
            if (!SfaGrayUtil.isSaleContractOpen(tenantId)) {
                result.removeIf(o -> StringUtils.equals(o.getApiName(), Utils.SALE_CONTRACT_API_NAME)||StringUtils.equals(o.getApiName(), Utils.SALE_CONTRACT_LINE_API_NAME));
            }
        } catch (Exception e) {
            log.error("remove SaleContractObj failed!. tenant:{}", tenantId);
        }
    }
}
