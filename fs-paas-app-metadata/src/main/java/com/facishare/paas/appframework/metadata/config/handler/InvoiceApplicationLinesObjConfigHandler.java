package com.facishare.paas.appframework.metadata.config.handler;

import com.facishare.paas.appframework.config.ConfigService;
import com.facishare.paas.appframework.metadata.config.util.TenantConfigUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class InvoiceApplicationLinesObjConfigHandler implements ConfigHandler {
    @Autowired
    ConfigService configService;
    @Override
    public String getObjectAPIName() {
        return "InvoiceApplicationLinesObj";
    }

    @Override
    public void handle(String tenantId, Map<String, Object> objectConfig, Map<String, Map<String, Object>> fieldConfig) {
        boolean isSalesOrderProductInvoiceModel = TenantConfigUtil.isSalesOrderProductInvoiceModel(configService,tenantId);
        if(isSalesOrderProductInvoiceModel){
            if(fieldConfig.containsKey("order_id")){
                Map<String,Object> attrMap =(Map<String,Object>) fieldConfig.get("order_id").get("attrs");
                if(attrMap != null){
                    attrMap.put("is_readonly",0);
                }
                fieldConfig.get("order_id").put("attrs",attrMap);
            }
            if(fieldConfig.containsKey("product_id")){
                Map<String,Object> attrMap =(Map<String,Object>) fieldConfig.get("product_id").get("attrs");
                if(attrMap != null){
                    attrMap.put("is_readonly",0);
                }
                fieldConfig.get("product_id").put("attrs",attrMap);
            }
        }
        boolean isInvoiceOrderDecoupling = TenantConfigUtil.isInvoiceOrderDecoupling(configService, tenantId);
        if (isInvoiceOrderDecoupling) {
            if (fieldConfig.containsKey("order_id")) {
                Map<String, Object> attrMap = (Map<String, Object>) fieldConfig.get("order_id").get("attrs");
                if (attrMap != null) {
                    attrMap.put("is_required", 1);
                    attrMap.put("is_readonly", 1);
                }
                fieldConfig.get("order_id").put("attrs", attrMap);
            }
            if (fieldConfig.containsKey("product_id")) {
                Map<String, Object> attrMap = (Map<String, Object>) fieldConfig.get("product_id").get("attrs");
                if (attrMap != null) {
                    attrMap.put("is_required", 1);
                    attrMap.put("is_readonly", 1);
                }
                fieldConfig.get("product_id").put("attrs", attrMap);
            }
            if (fieldConfig.containsKey("order_product_id")) {
                Map<String, Object> attrMap = (Map<String, Object>) fieldConfig.get("order_product_id").get("attrs");
                if (attrMap != null) {
                    attrMap.put("is_required", 1);
                    attrMap.put("is_readonly", 1);
                }
                fieldConfig.get("order_product_id").put("attrs", attrMap);
            }
            if (fieldConfig.containsKey("invoiced_amount")) {
                Map<String, Object> attrMap = (Map<String, Object>) fieldConfig.get("invoiced_amount").get("attrs");
                if (attrMap != null) {
                    attrMap.put("default_value", 1);
                }
                fieldConfig.get("invoiced_amount").put("attrs", attrMap);
            }
            if (fieldConfig.containsKey("invoiced_quantity")) {
                Map<String, Object> attrMap = (Map<String, Object>) fieldConfig.get("invoiced_quantity").get("attrs");
                if (attrMap != null) {
                    attrMap.put("is_required", 1);
                    attrMap.put("is_readonly", 1);
                }
                fieldConfig.get("invoiced_quantity").put("attrs", attrMap);
            }
        }
    }
}
