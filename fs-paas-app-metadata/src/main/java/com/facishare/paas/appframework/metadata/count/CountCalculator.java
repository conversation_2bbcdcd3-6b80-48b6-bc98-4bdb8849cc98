package com.facishare.paas.appframework.metadata.count;

import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.Count;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by zhouwr on 2018/4/25
 */
public class CountCalculator extends AbstractCountFieldCalculator {

    @Override
    protected BigDecimal doCalculate(BigDecimal result, List<IObjectData> detailDataList, Count countField,
                                     CountValues countValues) {
        if (result == null) {
            return BigDecimal.valueOf(detailDataList.size());
        }
        return result.add(BigDecimal.valueOf(detailDataList.size()));
    }

}
