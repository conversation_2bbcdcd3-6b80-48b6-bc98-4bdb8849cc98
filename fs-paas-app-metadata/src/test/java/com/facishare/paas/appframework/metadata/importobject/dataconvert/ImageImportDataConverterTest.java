package com.facishare.paas.appframework.metadata.importobject.dataconvert;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.model.User;
import com.facishare.paas.appframework.metadata.ObjectDataExt;
import com.facishare.paas.metadata.api.IObjectData;
import com.facishare.paas.metadata.api.describe.IFieldDescribe;
import com.facishare.paas.metadata.impl.describe.ImageFieldDescribe;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * ImageImportDataConverter 图片错误处理功能测试
 */
@ExtendWith(MockitoExtension.class)
class ImageImportDataConverterTest {

    @InjectMocks
    private ImageImportDataConverter converter;

    @Mock
    private IObjectData objectData;

    @Mock
    private ImageFieldDescribe fieldDescribe;

    @Mock
    private User user;

    @BeforeEach
    void setUp() {
        when(fieldDescribe.getApiName()).thenReturn("test_image_field");
        when(fieldDescribe.getLabel()).thenReturn("测试图片字段");
        when(fieldDescribe.getFileAmountLimit()).thenReturn(10);
        when(fieldDescribe.getIsWaterMark()).thenReturn(false);
    }

    @Test
    void testProcessImageErrors_SingleError() {
        // 模拟包含单个错误的图片数据
        String imageData = "[IMG_ERROR:paas.udobj.image_max_size]产品图片.jpg";
        
        try (MockedStatic<I18NExt> mockedI18NExt = mockStatic(I18NExt.class)) {
            mockedI18NExt.when(() -> I18NExt.text("paas.udobj.image_max_size"))
                    .thenReturn("文件大小超过限制");

            ObjectDataExt dataExt = mock(ObjectDataExt.class);
            when(dataExt.get(anyString(), eq(String.class))).thenReturn(imageData);
            
            try (MockedStatic<ObjectDataExt> mockedDataExt = mockStatic(ObjectDataExt.class)) {
                mockedDataExt.when(() -> ObjectDataExt.of(objectData)).thenReturn(dataExt);

                ConvertResult result = converter.convertFieldData(objectData, fieldDescribe, user);

                assertNotNull(result);
                assertFalse(result.isSuccess());
                assertEquals("[测试图片字段] 文件大小超过限制", result.getErrorMessage());
            }
        }
    }

    @Test
    void testProcessImageErrors_MultipleErrors() {
        // 模拟包含多个错误的图片数据
        String imageData = "[IMG_ERROR:paas.udobj.image_max_size]图片1.jpg|[IMG_ERROR:paas.udobj.image_format_unsupported]图片2.bmp";
        
        try (MockedStatic<I18NExt> mockedI18NExt = mockStatic(I18NExt.class)) {
            mockedI18NExt.when(() -> I18NExt.text("paas.udobj.image_max_size"))
                    .thenReturn("文件大小超过限制");
            mockedI18NExt.when(() -> I18NExt.text("paas.udobj.image_format_unsupported"))
                    .thenReturn("图片格式不支持");

            ObjectDataExt dataExt = mock(ObjectDataExt.class);
            when(dataExt.get(anyString(), eq(String.class))).thenReturn(imageData);
            
            try (MockedStatic<ObjectDataExt> mockedDataExt = mockStatic(ObjectDataExt.class)) {
                mockedDataExt.when(() -> ObjectDataExt.of(objectData)).thenReturn(dataExt);

                ConvertResult result = converter.convertFieldData(objectData, fieldDescribe, user);

                assertNotNull(result);
                assertFalse(result.isSuccess());
                assertEquals("[测试图片字段] 文件大小超过限制,图片格式不支持", result.getErrorMessage());
            }
        }
    }

    @Test
    void testProcessImageErrors_NoErrors() {
        // 模拟正常的图片数据（无错误标识）
        String imageData = "正常图片1.jpg|正常图片2.png";
        
        ObjectDataExt dataExt = mock(ObjectDataExt.class);
        when(dataExt.get(anyString(), eq(String.class))).thenReturn(imageData);
        
        try (MockedStatic<ObjectDataExt> mockedDataExt = mockStatic(ObjectDataExt.class)) {
            mockedDataExt.when(() -> ObjectDataExt.of(objectData)).thenReturn(dataExt);

            // 这里需要mock更多的依赖来完成正常流程，但主要是验证错误处理不会被触发
            // 实际测试中可能需要更复杂的setup
        }
    }

    @Test
    void testProcessImageErrors_MixedData() {
        // 模拟混合数据：既有错误也有正常图片
        String imageData = "正常图片.jpg|[IMG_ERROR:paas.udobj.image_max_size]错误图片.jpg|另一个正常图片.png";
        
        try (MockedStatic<I18NExt> mockedI18NExt = mockStatic(I18NExt.class)) {
            mockedI18NExt.when(() -> I18NExt.text("paas.udobj.image_max_size"))
                    .thenReturn("文件大小超过限制");

            ObjectDataExt dataExt = mock(ObjectDataExt.class);
            when(dataExt.get(anyString(), eq(String.class))).thenReturn(imageData);
            
            try (MockedStatic<ObjectDataExt> mockedDataExt = mockStatic(ObjectDataExt.class)) {
                mockedDataExt.when(() -> ObjectDataExt.of(objectData)).thenReturn(dataExt);

                ConvertResult result = converter.convertFieldData(objectData, fieldDescribe, user);

                assertNotNull(result);
                assertFalse(result.isSuccess());
                assertEquals("[测试图片字段] 文件大小超过限制", result.getErrorMessage());
            }
        }
    }
}
