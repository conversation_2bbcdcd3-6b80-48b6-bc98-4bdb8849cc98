package com.facishare.paas.appframework.metadata.layout.factory;

import com.facishare.paas.appframework.core.i18n.I18NExt;
import com.facishare.paas.appframework.core.i18n.I18NKey;
import com.facishare.paas.appframework.metadata.ComponentExt;
import com.facishare.paas.appframework.metadata.LayoutLogicService;
import com.facishare.paas.appframework.metadata.layout.component.FlowTaskListComponentExt;
import com.facishare.paas.metadata.api.describe.IObjectDescribe;
import com.facishare.paas.metadata.ui.layout.IComponent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * FlowTaskListComponentFactory 单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("FlowTaskListComponentFactory 测试")
class FlowTaskListComponentFactoryTest {

  @Mock
  private LayoutLogicService layoutLogicService;

  @Mock
  private ILayoutFactory.Context context;

  @Mock
  private IObjectDescribe describe;

  private FlowTaskListComponentFactory factory;

  @BeforeEach
  void setUp() {
    factory = new FlowTaskListComponentFactory(layoutLogicService);
  }

  @Test
  @DisplayName("测试支持的组件类型")
  void testSupportComponentType() {
    // 执行测试
    String result = factory.supportComponentType();

    // 验证结果
    assertEquals(ComponentExt.TYPE_TASK_LIST_COMPONENT, result);
  }

  @Test
  @DisplayName("测试创建默认组件 - 正常场景")
  void testCreateDefaultComponent_Success() {
    try (MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class);
         MockedStatic<FlowTaskListComponentExt> flowTaskListComponentExtMock = mockStatic(FlowTaskListComponentExt.class)) {

      // 准备测试数据
      i18nExtMock.when(() -> I18NExt.getOrDefault(I18NKey.FLOW_TASK_LIST_PAGE, "流程待办列表"))
          .thenReturn("流程待办列表");

      FlowTaskListComponentExt mockFlowTaskListComponentExt = mock(FlowTaskListComponentExt.class);
      IComponent mockComponent = mock(IComponent.class);

      flowTaskListComponentExtMock.when(() -> FlowTaskListComponentExt.of(any(IComponent.class)))
          .thenReturn(mockFlowTaskListComponentExt);
      when(mockFlowTaskListComponentExt.getComponent()).thenReturn(mockComponent);

      // 执行测试
      IComponent result = factory.createDefaultComponent(context, describe);

      // 验证结果
      assertNotNull(result);
      assertEquals(mockComponent, result);

      // 验证方法调用
      verify(mockFlowTaskListComponentExt).setName(FlowTaskListComponentExt.TASK_LIST_NAME);
      verify(mockFlowTaskListComponentExt).setHeader("流程待办列表");
      verify(mockFlowTaskListComponentExt).setNameI18nKey(I18NKey.FLOW_TASK_LIST_PAGE);
    }
  }

  @Test
  @DisplayName("测试创建默认组件 - 验证组件类型设置")
  void testCreateDefaultComponent_ComponentTypeSet() {
    try (MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class);
         MockedStatic<FlowTaskListComponentExt> flowTaskListComponentExtMock = mockStatic(FlowTaskListComponentExt.class)) {

      // 准备测试数据
      i18nExtMock.when(() -> I18NExt.getOrDefault(anyString(), anyString()))
          .thenReturn("流程待办列表");

      FlowTaskListComponentExt mockFlowTaskListComponentExt = mock(FlowTaskListComponentExt.class);
      IComponent mockComponent = mock(IComponent.class);

      flowTaskListComponentExtMock.when(() -> FlowTaskListComponentExt.of(any(IComponent.class)))
          .thenReturn(mockFlowTaskListComponentExt);
      when(mockFlowTaskListComponentExt.getComponent()).thenReturn(mockComponent);

      // 执行测试
      IComponent result = factory.createDefaultComponent(context, describe);

      // 验证结果
      assertNotNull(result);

      // 验证FlowTaskListComponentExt.of被调用，这意味着组件类型已经设置
      flowTaskListComponentExtMock.verify(() -> FlowTaskListComponentExt.of(any(IComponent.class)));
    }
  }

  @Test
  @DisplayName("测试创建默认组件 - 空上下文")
  void testCreateDefaultComponent_NullContext() {
    try (MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class);
         MockedStatic<FlowTaskListComponentExt> flowTaskListComponentExtMock = mockStatic(FlowTaskListComponentExt.class)) {

      // 准备测试数据
      i18nExtMock.when(() -> I18NExt.getOrDefault(anyString(), anyString()))
          .thenReturn("流程待办列表");

      FlowTaskListComponentExt mockFlowTaskListComponentExt = mock(FlowTaskListComponentExt.class);
      IComponent mockComponent = mock(IComponent.class);

      flowTaskListComponentExtMock.when(() -> FlowTaskListComponentExt.of(any(IComponent.class)))
          .thenReturn(mockFlowTaskListComponentExt);
      when(mockFlowTaskListComponentExt.getComponent()).thenReturn(mockComponent);

      // 执行测试
      IComponent result = factory.createDefaultComponent(null, describe);

      // 验证结果
      assertNotNull(result);
    }
  }

  @Test
  @DisplayName("测试创建默认组件 - 空描述")
  void testCreateDefaultComponent_NullDescribe() {
    try (MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class);
         MockedStatic<FlowTaskListComponentExt> flowTaskListComponentExtMock = mockStatic(FlowTaskListComponentExt.class)) {

      // 准备测试数据
      i18nExtMock.when(() -> I18NExt.getOrDefault(anyString(), anyString()))
          .thenReturn("流程待办列表");

      FlowTaskListComponentExt mockFlowTaskListComponentExt = mock(FlowTaskListComponentExt.class);
      IComponent mockComponent = mock(IComponent.class);

      flowTaskListComponentExtMock.when(() -> FlowTaskListComponentExt.of(any(IComponent.class)))
          .thenReturn(mockFlowTaskListComponentExt);
      when(mockFlowTaskListComponentExt.getComponent()).thenReturn(mockComponent);

      // 执行测试
      IComponent result = factory.createDefaultComponent(context, null);

      // 验证结果
      assertNotNull(result);
    }
  }

  @Test
  @DisplayName("测试创建默认组件 - I18N回退值")
  void testCreateDefaultComponent_I18nFallback() {
    try (MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class);
         MockedStatic<FlowTaskListComponentExt> flowTaskListComponentExtMock = mockStatic(FlowTaskListComponentExt.class)) {

      // 准备测试数据 - I18N返回默认值
      i18nExtMock.when(() -> I18NExt.getOrDefault(I18NKey.FLOW_TASK_LIST_PAGE, "流程待办列表"))
          .thenReturn("流程待办列表");

      FlowTaskListComponentExt mockFlowTaskListComponentExt = mock(FlowTaskListComponentExt.class);
      IComponent mockComponent = mock(IComponent.class);

      flowTaskListComponentExtMock.when(() -> FlowTaskListComponentExt.of(any(IComponent.class)))
          .thenReturn(mockFlowTaskListComponentExt);
      when(mockFlowTaskListComponentExt.getComponent()).thenReturn(mockComponent);

      // 执行测试
      IComponent result = factory.createDefaultComponent(context, describe);

      // 验证结果
      assertNotNull(result);
      verify(mockFlowTaskListComponentExt).setHeader("流程待办列表");
    }
  }

  @Test
  @DisplayName("测试工厂方法稳定性")
  void testFactoryMethodStability() {
    try (MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class);
         MockedStatic<FlowTaskListComponentExt> flowTaskListComponentExtMock = mockStatic(FlowTaskListComponentExt.class)) {

      // 准备测试数据
      i18nExtMock.when(() -> I18NExt.getOrDefault(anyString(), anyString()))
          .thenReturn("流程待办列表");

      FlowTaskListComponentExt mockFlowTaskListComponentExt = mock(FlowTaskListComponentExt.class);
      IComponent mockComponent = mock(IComponent.class);

      flowTaskListComponentExtMock.when(() -> FlowTaskListComponentExt.of(any(IComponent.class)))
          .thenReturn(mockFlowTaskListComponentExt);
      when(mockFlowTaskListComponentExt.getComponent()).thenReturn(mockComponent);

      // 多次执行测试
      IComponent result1 = factory.createDefaultComponent(context, describe);
      IComponent result2 = factory.createDefaultComponent(context, describe);

      // 验证结果
      assertNotNull(result1);
      assertNotNull(result2);
      // 每次调用都应该创建新的组件实例
      assertEquals(mockComponent, result1);
      assertEquals(mockComponent, result2);
    }
  }

  @Test
  @DisplayName("测试构造函数")
  void testConstructor() {
    // 测试构造函数不抛异常
    assertDoesNotThrow(() -> {
      new FlowTaskListComponentFactory(layoutLogicService);
    });

    // 测试空参数构造
    assertDoesNotThrow(() -> {
      new FlowTaskListComponentFactory(null);
    });
  }

  @Test
  @DisplayName("测试接口实现")
  void testInterfaceImplementation() {
    // 验证实现了正确的接口
    assertTrue(factory instanceof IComponentFactory);
  }

  @Test
  @DisplayName("测试组件名称和I18N键设置")
  void testComponentNameAndI18nKeySetup() {
    try (MockedStatic<I18NExt> i18nExtMock = mockStatic(I18NExt.class);
         MockedStatic<FlowTaskListComponentExt> flowTaskListComponentExtMock = mockStatic(FlowTaskListComponentExt.class)) {

      // 准备测试数据
      i18nExtMock.when(() -> I18NExt.getOrDefault(I18NKey.FLOW_TASK_LIST_PAGE, "流程待办列表"))
          .thenReturn("Task List");

      FlowTaskListComponentExt mockFlowTaskListComponentExt = mock(FlowTaskListComponentExt.class);
      IComponent mockComponent = mock(IComponent.class);

      flowTaskListComponentExtMock.when(() -> FlowTaskListComponentExt.of(any(IComponent.class)))
          .thenReturn(mockFlowTaskListComponentExt);
      when(mockFlowTaskListComponentExt.getComponent()).thenReturn(mockComponent);

      // 执行测试
      factory.createDefaultComponent(context, describe);

      // 验证名称和I18N键设置
      verify(mockFlowTaskListComponentExt).setName(FlowTaskListComponentExt.TASK_LIST_NAME);
      verify(mockFlowTaskListComponentExt).setHeader("Task List");
      verify(mockFlowTaskListComponentExt).setNameI18nKey(I18NKey.FLOW_TASK_LIST_PAGE);
    }
  }
}
