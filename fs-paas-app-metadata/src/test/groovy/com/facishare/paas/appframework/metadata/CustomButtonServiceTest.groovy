package com.facishare.paas.appframework.metadata

import com.facishare.crm.userdefobj.DefObjConstants
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.config.ButtonConfig
import com.facishare.paas.appframework.metadata.config.IUdefButtonConfig
import com.facishare.paas.appframework.metadata.config.UdefButtonConfig
import com.facishare.paas.metadata.api.service.IUdefButtonService
import com.facishare.paas.metadata.impl.UdefButton
import spock.lang.Shared
import spock.lang.Specification

/**
 * create by zhao<PERSON> on 2019/09/18
 */
class CustomButtonServiceTest extends Specification {


    @Shared
    String addButtonJson = '''{"wheres":[],"param_form":[],"actions":[],"_id":"standard_add_button_id","api_name":"Add_button_default","description":"","label":"新建保存","button_type":"common","use_pages":"create","is_active":true,"is_deleted":false,"version":1,"tenant_id":"71698","created_by":"1000","last_modified_by":"1000","create_time":*************,"last_modified_time":*************,"describe_api_name":"object_1xq2p__c","define_type":"system"}'''
    @Shared
    String convertButtonJson = '''{"wheres":[],"param_form":[],"actions":["5b39d165a5083db01f1352ad"],"_id":"5b39d165a5083db01f1352ae","api_name":"btn_2QMUm__c","tenant_id":"71698","describe_api_name":"AccountObj","description":"","label":"客户映射工单","created_by":"1000","create_time":*************,"last_modified_time":*************,"button_type":"convert","use_pages":["detail"],"jump_url":"","define_type":null,"last_modified_by":"1000","is_active":false,"is_deleted":false,"version":2,"is_batch":null,"url":null}'''
    @Shared
    String commonButtonJson = '''{"wheres":[],"param_form":[],"actions":["5c370230a5083d427d8daff9","5c370230a5083d427d8daffa"],"_id":"5c0f3188a5083d602c0c209a","api_name":"button_O27as__c","tenant_id":"71698","describe_api_name":"AccountObj","description":"","label":"按钮4","created_by":"1000","create_time":*************,"last_modified_time":*************,"button_type":"common","use_pages":["detail"],"jump_url":null,"define_type":null,"last_modified_by":"1000","is_active":true,"is_deleted":false,"version":3,"is_batch":null,"url":null}'''
    @Shared
    String redirectButtonJson = '''{"wheres":[],"param_form":[],"actions":["5cb82283a5083dab4b0d4958"],"_id":"5c18be71a5083db4882b52fa","api_name":"button_dMTc2__c","tenant_id":"71698","describe_api_name":"AccountObj","description":"","label":"网址跳转","created_by":"1000","create_time":*************,"last_modified_time":*************,"button_type":"redirect","use_pages":["detail"],"jump_url":null,"define_type":null,"last_modified_by":"1000","is_active":true,"is_deleted":false,"version":2,"is_batch":null,"url":null}'''


    IUdefButtonService buttonService = Mock(IUdefButtonService)
    CustomButtonService customButtonService

    def setup() {
        initButtonConfig()
        customButtonService = new CustomButtonServiceImpl(buttonService: buttonService)
    }

    def "查询按钮config 根据buttonType判断按钮是否支持编辑"() {
        given:
        when:
        IUdefButtonConfig config = customButtonService.findButtonConfigByApiName(describeApiName, buttonApiName, createUser(tenantId, userId))
        then:
        1 * buttonService.findByApiNameAndTenantId(_ as String, _ as String, _ as String) >> {
            return createButton(describeApiName, buttonApiName, buttonJson, createUser(tenantId, userId))
        }
        config.getEdit() == edit
        where:
        describeApiName   | buttonApiName        | tenantId | userId | buttonJson         | edit
        "object_1xq2p__c" | "Add_button_default" | "71698"  | "1000" | addButtonJson      | 0
        "AccountObj"      | "btn_2QMUm__c"       | "71698"  | "1000" | convertButtonJson  | 1
        "AccountObj"      | "button_O27as__c"    | "71698"  | "1000" | commonButtonJson   | 1
        "AccountObj"      | "button_dMTc2__c"    | "71698"  | "1000" | redirectButtonJson | 0
    }

    def createUser(String tenantId, String userId) {
        return new User(tenantId, userId)
    }

    def createButton(String describeApiName, String buttonApiName, String buttonJson, User user) {
        UdefButton button = new UdefButton()
        button.fromJsonString(buttonJson)
        button.setDescribeApiName(describeApiName)
        button.setApiName(buttonApiName)
        button.setTenantId(user.getTenantId())
        if (isDefaultButton(buttonApiName)) {
            button.setDefineType("system")
        } else {
            button.setDefineType("custom")
        }
        return button
    }

    def isDefaultButton(String buttonApiName) {
        return ButtonExt.DEFAULT_BUTTON_API_NAME_LIST.contains(buttonApiName)
    }

    private void initButtonConfig() {
        def udefButtenConfigJson = '''{"api_name":"udef_button","descrieb_api_name":"udef","tenant_id":"-100",
"type":"udef_button_config","config":{"type":"button_config","delete":0,"edit":1,"attr":{"api_name":{"edit":0},
"description":{"edit":1},"label":{"edit":1},"button_type":{"edit":0},"roles":{"edit":0},"wheres":{"edit":1},
"param_form":{"edit":1},"use_pages":{"edit":0,"position":0,"position_pages":0},"stage_actions":{"pre":{"edit":1},
"current":{"edit":0},"post":{"edit":1}}}},"edit":1}'''
        def addButtonConfigJson = '''{"api_name":"Add_button_default","descrieb_api_name":"udef",
"tenant_id":"-100","type":"udef_button_config","config":{"type":"button_config","delete":0,"edit":1,
"attr":{"api_name":{"edit":0},"description":{"edit":1},"label":{"edit":1},"button_type":{"edit":0},"roles":{"edit":0},
"wheres":{"edit":1},"param_form":{"edit":1},"use_pages":{"edit":0,"position":0,"position_pages":0},
"stage_actions":{"pre":{"edit":1},"current":{"edit":0},"post":{"edit":1}}}},"edit":0}'''
        def allocateButtonConfigJson = '''{"api_name":"AccountObj_Allocate_button_default","descrieb_api_name":"AccountObj",
"tenant_id":"-100","type":"udef_button_config","config":{"type":"button_config","delete":0,"edit":1,
"attr":{"api_name":{"edit":0},"description":{"edit":1},"label":{"edit":1},"button_type":{"edit":0},"roles":{"edit":0},
"wheres":{"edit":1},"param_form":{"edit":1},"use_pages":{"edit":0,"position":0,"position_pages":0},
"stage_actions":{"pre":{"edit":1},"current":{"edit":0},"post":{"edit":1}}}},"edit":0}'''

        def udefButtenConfig = UdefButtonConfig.fromJson(udefButtenConfigJson)
        ButtonConfig.BUTTON_CONFIG_TABLE.put(DefObjConstants.UDOBJ, ButtonConfig.UDEF_BUTTON, udefButtenConfig)
        def addButtonConfig = UdefButtonConfig.fromJson(addButtonConfigJson)
        ButtonConfig.BUTTON_CONFIG_TABLE.put(DefObjConstants.UDOBJ, "Add_button_default", addButtonConfig)
        def allocateButtonConfig = UdefButtonConfig.fromJson(allocateButtonConfigJson)
        ButtonConfig.BUTTON_CONFIG_TABLE.put("AccountObj", "AccountObj_Allocate_button_default", allocateButtonConfig)
    }

    def "findCustomButtonByUsePage"() {
        given:
        def user = createUser(tenantId, userId)
        when:
        def buttons = customButtonService.findCustomButtonByUsePage(describeApiName, usePage, user)
        then:
        1 * buttonService.findButtonsByDescribeApiName(describeApiName, tenantId) >> resultButton

        buttons.size() == buttonCount
        where:
        tenantId | userId | describeApiName   | usePage      | resultButton     || buttonCount
        "71698"  | "1000" | "object_e9J2F__c" | "detail"     | []               || 6
        "71698"  | "1000" | "object_e9J2F__c" | "list_batch" | []               || 6
        "71698"  | "1000" | "object_e9J2F__c" | "detail"     | [editButton()]   || 6
        "71698"  | "1000" | "object_e9J2F__c" | "detail"     | [lockButton()]   || 6
        "71698"  | "1000" | "object_e9J2F__c" | "list_batch" | [lockButton()]   || 5
        "71698"  | "1000" | "object_e9J2F__c" | "list_batch" | [customButton()] || 7
    }

    def customButton() {
        def buttonJson = '''{"_id":"5af42526a5083db98b3b019e","api_name":"button_5wly1__c","tenant_id":"71698",
"describe_api_name":"object_1xq2p__c","description":"","label":"自定义按钮1","created_by":"1000","create_time":1525949734135,"last_modified_time":1568974804527,"button_type":"redirect","use_pages":["detail","list_batch"],"jump_url":null,"define_type":null,"last_modified_by":"1000","is_active":true,"is_deleted":false,"version":90,"is_batch":null,"url":null}'''
        def button = new UdefButton()
        button.fromJsonString(buttonJson)
        return button
    }

    def lockButton() {
        def buttonJson = '''{"_id":"5af42526a5083db98b3b019e","api_name":"Lock_button_default","tenant_id":"71698","describe_api_name":"object_1xq2p__c","description":"","label":"自定义按钮1","created_by":"1000","create_time":1525949734135,"last_modified_time":1568974804527,"button_type":"redirect","use_pages":["detail"],"jump_url":null,"define_type":null,"last_modified_by":"1000","is_active":true,"is_deleted":false,"version":90,"is_batch":null,"url":null}'''
        def button = new UdefButton()
        button.fromJsonString(buttonJson)
        return button
    }

    def editButton() {
        def buttonJson = '''{"wheres":[],"param_form":[],"actions":[],"_id":"standard_edit_button_id","api_name":"Edit_button_default","description":"","label":"编辑保存","button_type":"common","use_pages":["edit"],"is_active":true,"is_deleted":false,"version":1,"tenant_id":"71698","created_by":"1000","last_modified_by":"1000","create_time":1554949897535,"last_modified_time":1569231167255,"describe_api_name":"object_e9J2F__c","define_type":"system"}'''
        def button = new UdefButton()
        button.fromJsonString(buttonJson)
        return button
    }
}
