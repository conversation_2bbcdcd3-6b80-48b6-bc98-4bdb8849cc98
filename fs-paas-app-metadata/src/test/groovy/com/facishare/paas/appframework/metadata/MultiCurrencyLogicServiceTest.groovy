package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.common.util.AppFrameworkConfig
import com.facishare.paas.appframework.config.ConfigService
import com.facishare.paas.appframework.core.model.RequestContext
import com.facishare.paas.appframework.core.model.RequestContextManager
import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.multicurrency.CurrencyService
import com.facishare.paas.appframework.metadata.multicurrency.ExchangeRateService
import com.facishare.paas.appframework.metadata.repository.api.IRepository
import com.facishare.paas.metadata.api.para.MultiCurrencyOpen
import com.facishare.paas.metadata.api.service.IMetadataMultiCurrencyService
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.facishare.social.personnel.PersonnelObjService
import com.fxiaoke.i18n.client.I18nClient
import com.fxiaoke.i18n.client.impl.I18nServiceImpl
import org.powermock.reflect.Whitebox
import spock.lang.Specification
import spock.lang.Unroll
import java.lang.reflect.Field

class MultiCurrencyLogicServiceTest extends Specification {

    MultiCurrencyLogicService multiCurrencyLogicService
    def configService = Mock(ConfigService)
    def currencyService = Mock(CurrencyService)
    def exchangeRateService = Mock(ExchangeRateService)
    def describeLogicService = Mock(DescribeLogicService)
    def metadataMultiCurrencyService = Mock(IMetadataMultiCurrencyService)
    def currencyExchangeRepository = Mock(IRepository)
    def personnelObjService = Mock(PersonnelObjService)

    def setupSpec() {
        // 创建 mock 实例
        def i18nClient = Mock(I18nClient)
        def i18nServiceImpl = Mock(I18nServiceImpl)

        // 给 mock 设置返回值
        i18nClient.getAllLanguage() >> []

        // 设置内部字段 impl（如果不是 final static，可保留）
        Whitebox.setInternalState(i18nClient, "impl", i18nServiceImpl)

        // 用 Groovy 的 metaClass 替代反射修改 SINGLETON
        Whitebox.setInternalState(I18nClient, "SINGLETON", i18nClient )
    }

    def setup() {
        multiCurrencyLogicService = new MultiCurrencyLogicServiceImpl(configService: configService,
                currencyService: currencyService, exchangeRateService: exchangeRateService,
                describeLogicService: describeLogicService, metadataMultiCurrencyService: metadataMultiCurrencyService,
                currencyExchangeRepository: currencyExchangeRepository, personnelObjService: personnelObjService)
    }

    @Unroll
    def "test openMultiCurrency with functionalCurrency: #functionalCurrency and user: #user"() {
        given:
        AppFrameworkConfig.setCurrencyList([["currencyCode": "USD", "currencySymbol": "\$"], ["currencyCode": "CNY", "currencySymbol": "¥"]])
        AppFrameworkConfig.currencyBlackObjectList = []

        def requestContext = RequestContext.builder()
                .tenantId("55732")
                .user(user)
                .build()
        RequestContextManager.setContext(requestContext)
        when:
        configService.findTenantConfig(user, MultiCurrencyLogicServiceImpl.MULTI_CURRENCY_KEY) >> status
        describeLogicService.findAllObjectsByTenantId(*_) >> objectApiNames

        metadataMultiCurrencyService.openMultiCurrency(_) >> MultiCurrencyOpen.Result.builder().success(true).build()
        multiCurrencyLogicService.openMultiCurrency(functionalCurrency, user)
        then:
        noExceptionThrown()

        where:
        functionalCurrency | user                     | status | initCurrencyList | objectApiNames
        "USD"              | User.systemUser('74255') | "1"    | ["USD", "EUR"]   | [new ObjectDescribe(['api_name': 'object1__c', 'tenant_id': '74255'])]
        "USD"              | User.systemUser('74255') | "2"    | ["USD", "EUR"]   | [new ObjectDescribe(['api_name': 'object1__c', 'tenant_id': '74255'])]
        "USD"              | User.systemUser('74255') | null   | ["USD", "EUR"]   | [new ObjectDescribe(['api_name': 'object1__c', 'tenant_id': '74255'])]
    }
}