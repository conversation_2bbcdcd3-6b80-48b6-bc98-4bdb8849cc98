package com.facishare.paas.appframework.metadata.relation

import com.facishare.crm.openapi.Utils
import com.facishare.paas.metadata.api.describe.*
import lombok.Getter
import org.elasticsearch.common.Strings
import spock.lang.Specification

/**
 * create by <PERSON><PERSON><PERSON> on 2020/08/13
 */
class GraphHelper extends Specification {

    def SalesOrderObj() {
        IObjectDescribe salesOrderProductObj = Mock(IObjectDescribe)
        salesOrderProductObj.getApiName() >> Utils.SALES_ORDER_API_NAME
        salesOrderProductObj.getDisplayName() >> "销售订单"
        // 销售订单金额（元）=产品合计*整单折扣
        IFieldDescribe order_amount = CurrencyField("order_amount", Utils.SALES_ORDER_API_NAME, '$product_amount$*$discount$')
        // 产品合计 =sum( 订单产品-小计）
        IFieldDescribe product_amount = Count("product_amount", Utils.SALES_ORDER_API_NAME, "subtotal", Utils.SALES_ORDER_PRODUCT_API_NAME)
        // 整单折扣(%)
        IFieldDescribe discount = Percentile("discount", Utils.SALES_ORDER_API_NAME)

        salesOrderProductObj.getFieldDescribes() >> [order_amount, product_amount, discount]

        salesOrderProductObj.getFieldDescribe("order_amount") >> order_amount
        salesOrderProductObj.getFieldDescribe("product_amount") >> product_amount
        salesOrderProductObj.getFieldDescribe("discount") >> discount
        salesOrderProductObj
    }

    /**
     * mock 订单产品
     */
    def SalesOrderProductObj() {
        IObjectDescribe salesOrderProductObj = Mock(IObjectDescribe)
        salesOrderProductObj.getApiName() >> Utils.SALES_ORDER_PRODUCT_API_NAME
        salesOrderProductObj.getDisplayName() >> "订单产品"

        //  订单产品.价格 = 产品名称.标准价格
        IFieldDescribe product_price = CurrencyField("product_price", Utils.SALES_ORDER_PRODUCT_API_NAME, '$product_id__r.price$')
        IFieldDescribe product_id = IObjectReferenceField("product_id", Utils.SALES_ORDER_PRODUCT_API_NAME, Utils.PRODUCT_API_NAME)

        IFieldDescribe order_id = MasterDetail("order_id", Utils.SALES_ORDER_PRODUCT_API_NAME, Utils.SALES_ORDER_API_NAME)
        // 小计=销售单价*数量
        IFieldDescribe subtotal = Number("subtotal", Utils.SALES_ORDER_PRODUCT_API_NAME, '$sales_price$*$quantity$')
        // 销售单价=价格*折扣
        IFieldDescribe sales_price = CurrencyField("sales_price", Utils.SALES_ORDER_PRODUCT_API_NAME, '$product_price$*$discount$')
        // 折扣
        IFieldDescribe discount = Percentile("discount", Utils.SALES_ORDER_PRODUCT_API_NAME)
        // 数量
        IFieldDescribe quantity = Number("quantity", Utils.SALES_ORDER_PRODUCT_API_NAME)
        salesOrderProductObj.getFieldDescribes() >> [product_price, product_id, order_id, subtotal, sales_price, discount, quantity]
        salesOrderProductObj.getFieldDescribe("product_id") >> product_id
        salesOrderProductObj.getFieldDescribe("product_price") >> product_price
        salesOrderProductObj.getFieldDescribe("order_id") >> order_id
        salesOrderProductObj.getFieldDescribe("subtotal") >> subtotal
        salesOrderProductObj.getFieldDescribe("sales_price") >> sales_price
        salesOrderProductObj.getFieldDescribe("discount") >> discount
        salesOrderProductObj.getFieldDescribe("quantity") >> quantity

        salesOrderProductObj
    }

    /**
     * mock 产品
     * @return
     */
    def ProductObj() {
        IObjectDescribe ProductObj = Mock(IObjectDescribe)
        ProductObj.getApiName() >> Utils.PRODUCT_API_NAME
        ProductObj.getDisplayName() >> "产品"

        IFieldDescribe price = CurrencyField("CurrencyField", Utils.PRODUCT_API_NAME)

        ProductObj.getFieldDescribes() >> [price]
        ProductObj.getFieldDescribe("price") >> price

        ProductObj
    }


    def CurrencyField(apiName, describeApiName, String defaultVale = '') {
        Currency product_price = FieldDescribe(apiName, describeApiName, Currency)

        product_price.getDefaultToZero() >> !Strings.isNullOrEmpty(defaultVale)
        product_price.getDefaultValue() >> defaultVale
        product_price.getDefaultIsExpression() >> !Strings.isNullOrEmpty(defaultVale)

        product_price.getDecimalPlaces() >> 2

        product_price
    }

    def Number(apiName, describeApiName, String defaultVale = '') {
        Number number = FieldDescribe(apiName, describeApiName, Number)

        number.getDecimalPlaces() >> 2
        number.getDefaultToZero() >> !Strings.isNullOrEmpty(defaultVale)
        number.getDefaultValue() >> defaultVale
        number.getDefaultIsExpression() >> !Strings.isNullOrEmpty(defaultVale)
        number
    }

    def IObjectReferenceField(apiName, describeApiName, targetApiName) {
        IObjectReferenceField lookup = FieldDescribe(apiName, describeApiName, IObjectReferenceField)

        lookup.getTargetApiName() >> targetApiName
        lookup
    }

    def MasterDetail(apiName, describeApiName, targetApiName) {
        MasterDetail masterDetail = FieldDescribe(apiName, describeApiName, MasterDetail)

        masterDetail.getTargetApiName() >> targetApiName

        masterDetail
    }

    def Formula(apiName, describeApiName, expression) {
        Formula formula = FieldDescribe(apiName, describeApiName, Formula)

        formula.getExpression() >> expression
        formula.getDefaultToZero() >> true

        formula
    }

    def Count(apiName, describeApiName, countFieldApiName, subObjectDescribeApiName, fieldApiName = null, countType = "sum") {
        Count count = FieldDescribe(apiName, describeApiName, Count)
        count.getCountFieldApiName() >> countFieldApiName
        count.getCountType() >> countType
        count.getSubObjectDescribeApiName() >> subObjectDescribeApiName
        count.getFieldApiName() >> fieldApiName

        count
    }

    def Percentile(apiName, describeApiName, expression = "") {
        Percentile percentile = FieldDescribe(apiName, describeApiName, Percentile)
        percentile.getExpression() >> expression
        percentile
    }

    def <T extends IFieldDescribe> T FieldDescribe(apiName, describeApiName, Class<T> clazz) {
        IFieldDescribe fieldDescribe = Mock(clazz)
        fieldDescribe.getApiName() >> apiName
        fieldDescribe.getType() >> FieldType.of(clazz)
        fieldDescribe.getDescribeApiName() >> describeApiName
        fieldDescribe.isActive() >> true
        fieldDescribe
    }


    enum FieldType {
        COUNT(IFieldType.COUNT, Count),
        FORMULA(IFieldType.FORMULA, Formula),
        OBJECT_REFERENCE(IFieldType.OBJECT_REFERENCE, IObjectReferenceField),
        MASTER_DETAIL(IFieldType.MASTER_DETAIL, MasterDetail),
        CURRENCY(IFieldType.CURRENCY, Currency),
        NUMBER(IFieldType.NUMBER, Number),
        PERCENTILE(IFieldType.PERCENTILE, Percentile),
        ;

        private String type
        @Getter
        private Class<? extends IFieldDescribe> clazz

        private FieldType(String type, Class<? extends IFieldDescribe> clazz) {
            this.type = type
            this.clazz = clazz
        }

        static {
            map = values().collectEntries { [it.clazz, it] }
        }
        static Map<Class<? extends IFieldDescribe>, String> map

        static of(Class<? extends IFieldDescribe> clazz) {
            map[clazz]
        }
    }
}
