package com.facishare.paas.appframework.metadata.ai


import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.appframework.metadata.FilterExt
import com.facishare.paas.appframework.metadata.repository.api.IRepository
import com.facishare.paas.appframework.metadata.repository.model.AiInsightEntity
import com.facishare.paas.appframework.metadata.search.Query
import com.facishare.paas.metadata.impl.search.Operator
import spock.lang.Specification
import spock.lang.Unroll

/**
 * Unit test for AiInsightLogicServiceImpl
 */
@Unroll
class AiInsightLogicServiceImplTest extends Specification {

    AiInsightLogicServiceImpl aiInsightLogicService
    IRepository<AiInsightEntity> aiInsightRepository

    def setup() {
        aiInsightRepository = Mock(IRepository)
        aiInsightLogicService = new AiInsightLogicServiceImpl()
        aiInsightLogicService.aiInsightRepository = aiInsightRepository
    }

    /**
     * Test description: Test finding AI insight by unique key, including successful query and no result scenarios
     */
    def "findByUniqKeyTest with successful and failed query"() {
        given:
        def user = new User("12345", "1001")
        def relateObjectApiName = "TestObject"
        def relateDataId = "123456789"
        def componentApiName = "TestComponent"
        
        def queryCapture
        def expectedQuery = Query.builder()
                .limit(1)
                .needReturnCountNum(false)
                .needReturnQuote(false)
                .build()
        expectedQuery.and(FilterExt.of(Operator.EQ, AiInsightEntity.RELATE_OBJECT_API_NAME, relateObjectApiName).getFilter())
        expectedQuery.and(FilterExt.of(Operator.EQ, AiInsightEntity.RELATE_DATA_ID, relateDataId).getFilter())
        expectedQuery.and(FilterExt.of(Operator.EQ, AiInsightEntity.COMPONENT_API_NAME, componentApiName).getFilter())
        
        // Create a mock entity that will be returned by the repository
        def mockEntity = returnValue ? createAiInsightEntity() : null
        def mockResult = returnValue ? [mockEntity] : []

        when:
        def result = aiInsightLogicService.findByUniqKey(user, relateObjectApiName, relateDataId, componentApiName)

        then:
        1 * aiInsightRepository.findBy(_, _, AiInsightEntity.class) >> { args ->
            queryCapture = args[1]
            return mockResult
        }
        
        // Verify query parameters are correctly built
        queryCapture.limit == 1
        queryCapture.needReturnCountNum == false
        queryCapture.needReturnQuote == false
        
        // Instead of comparing whole objects, check key fields
        if (returnValue) {
            result.relateObjectApiName == mockEntity.relateObjectApiName
            result.relateDataId == mockEntity.relateDataId
            result.componentApiName == mockEntity.componentApiName
            result.insightResult == mockEntity.insightResult
        } else {
            result == null
        }

        where:
        returnValue | expectedResult
        true        | _
        false       | null
    }

    /**
     * Test description: Test saving AI insight, including updating existing records and creating new records
     */
    def "saveTest#scenario"() {
        given:
        def user = new User("12345", "1001")
        def aiInsightEntity = createAiInsightEntity()
        // Set required properties
        aiInsightEntity.relateObjectApiName = "TestObject"
        aiInsightEntity.relateDataId = "123456789"
        aiInsightEntity.componentApiName = "TestComponent"
        
        // Mock findByUniqKey return value
        def existingEntity = existingId ? createAiInsightEntity() : null
        if (existingId) {
            existingEntity.id = "existing_id_123"
        }

        when:
        aiInsightLogicService.save(user, aiInsightEntity)

        then:
        1 * aiInsightRepository.findBy(_, _, AiInsightEntity.class) >> (existingId ? [existingEntity] : [])
        
        // If exists, should call update and set id
        if (existingId) {
            1 * aiInsightRepository.update(user, _) >> { args ->
                assert args[1].id == "existing_id_123"
                return args[1]
            }
            0 * aiInsightRepository.create(_, _)
        } else {
            // If not exists, should call create
            0 * aiInsightRepository.update(_, _)
            1 * aiInsightRepository.create(user, aiInsightEntity) >> aiInsightEntity
        }

        where:
        scenario                      | existingId
        "create_new_record_scenario"  | false
        "update_existing_record_scenario" | true
    }

    /**
     * Test description: Test saving AI insight when the insight entity is null (edge case)
     */
    def "saveTestWithNullEntity"() {
        given:
        def user = new User("12345", "1001")
        def aiInsightEntity = null

        when:
        aiInsightLogicService.save(user, aiInsightEntity)

        then:
        thrown(NullPointerException)
        // No need to verify mock interactions when exception is thrown
    }

    /**
     * Test description: Test save method handling with valid but incomplete data
     */
    def "saveTestWithValidIncompleteData"() {
        given:
        def user = new User("12345", "1001")
        def aiInsightEntity = new AiInsightEntity()
        // Set complete data
        aiInsightEntity.relateObjectApiName = "TestObject"
        aiInsightEntity.relateDataId = "123"
        aiInsightEntity.componentApiName = "Component"

        when:
        aiInsightLogicService.save(user, aiInsightEntity)

        then:
        1 * aiInsightRepository.findBy(_, _, AiInsightEntity.class) >> []
        1 * aiInsightRepository.create(user, aiInsightEntity) >> aiInsightEntity
    }

    /**
     * Test description: Test save method handling when relateObjectApiName is null
     */
    def "saveTestWithNullRelateObjectApiName"() {
        given:
        def user = new User("12345", "1001")
        def aiInsightEntity = new AiInsightEntity()
        aiInsightEntity.relateObjectApiName = null
        aiInsightEntity.relateDataId = "123"
        aiInsightEntity.componentApiName = "Component"
        
        // Mock to simulate NullPointerException when FilterExt.of is called with null parameter
        aiInsightRepository.findBy(_, _, _) >> {
            throw new NullPointerException("Cannot use null in FilterExt.of")
        }

        when:
        aiInsightLogicService.save(user, aiInsightEntity)

        then:
        thrown(NullPointerException)
    }

    /**
     * Test description: Test save method handling when relateDataId is null
     */
    def "saveTestWithNullRelateDataId"() {
        given:
        def user = new User("12345", "1001")
        def aiInsightEntity = new AiInsightEntity()
        aiInsightEntity.relateObjectApiName = "TestObject"
        aiInsightEntity.relateDataId = null
        aiInsightEntity.componentApiName = "Component"
        
        // Mock to simulate NullPointerException when FilterExt.of is called with null parameter
        aiInsightRepository.findBy(_, _, _) >> {
            throw new NullPointerException("Cannot use null in FilterExt.of")
        }

        when:
        aiInsightLogicService.save(user, aiInsightEntity)

        then:
        thrown(NullPointerException)
    }

    /**
     * Test description: Test save method handling when componentApiName is null
     */
    def "saveTestWithNullComponentApiName"() {
        given:
        def user = new User("12345", "1001")
        def aiInsightEntity = new AiInsightEntity()
        aiInsightEntity.relateObjectApiName = "TestObject"
        aiInsightEntity.relateDataId = "123"
        aiInsightEntity.componentApiName = null
        
        // Mock to simulate NullPointerException when FilterExt.of is called with null parameter
        aiInsightRepository.findBy(_, _, _) >> {
            throw new NullPointerException("Cannot use null in FilterExt.of")
        }

        when:
        aiInsightLogicService.save(user, aiInsightEntity)

        then:
        thrown(NullPointerException)
    }

    // Helper method: Create an AI insight entity
    private AiInsightEntity createAiInsightEntity() {
        return AiInsightEntity.builder()
                .relateObjectApiName("TestObject")
                .relateDataId("123456789")
                .componentApiName("TestComponent")
                .insightResult("{\"key\":\"value\"}")
                .generateTime(System.currentTimeMillis())
                .build()
    }
} 