package com.facishare.paas.appframework.metadata

import com.facishare.paas.appframework.core.model.User
import com.facishare.paas.metadata.api.service.IObjectArchiveRuleService
import com.facishare.paas.metadata.impl.ObjectArchiveRuleInfo
import com.facishare.paas.metadata.impl.describe.ObjectDescribe
import com.google.common.collect.Lists
import spock.lang.Specification

class ObjectArchiveServiceImplTest extends Specification {
    ObjectArchiveService objectArchiveService
    IObjectArchiveRuleService objectArchiveRuleService = Mock()
    DescribeLogicService describeLogicService = Mock()

    def setup() {
        objectArchiveService = new ObjectArchiveServiceImpl('objectArchiveRuleService':objectArchiveRuleService, 'describeLogicService':describeLogicService)
    }

    def 'test create'() {
        when:
        objectArchiveService.create(User.systemUser('1'), Lists.newArrayList())
        then:
        1 * objectArchiveRuleService.createArchiveRule(*_)
    }

    def 'test update'() {
        when:
        objectArchiveService.update(User.systemUser('1'), Lists.newArrayList())
        then:
        1 * objectArchiveRuleService.setArchiveRule(*_)
    }

    def 'test delete'() {
        when:
        objectArchiveService.delete(User.systemUser('1'), '1')
        then:
        1 * objectArchiveRuleService.bulkDeleteByRuleApiName(*_)
    }

    def 'test find'() {
        when:
        def objectArchiveRuleInfo = new ObjectArchiveRuleInfo()
        objectArchiveRuleInfo.setRuleApiName('1')
        objectArchiveRuleInfo.setTargetApiName("object2")
        objectArchiveRuleInfo.setSourceApiName("object1")
        objectArchiveRuleService.getObjectArchiveRuleByRuleApiName(*_) >> Lists.newArrayList(objectArchiveRuleInfo)

        describeLogicService.queryDisplayNameByApiNames(*_) >> ['object2':'对象2', 'object1':'对象1']
        then:
        def find = objectArchiveService.find(User.systemUser('1'), '1')
        find.size() == 1
        find[0].ruleApiName == '1'
        find[0].sourceDisplayName == '对象1'
        find[0].targetDisplayName == '对象2'
    }

    def 'test disable'() {
        when:
        objectArchiveService.disable(User.systemUser('1'), '1')
        then:
        1 * objectArchiveRuleService.invalidRule(*_)
    }

    def 'test enable'() {
        given:
        def objectArchiveRuleInfo = new ObjectArchiveRuleInfo()
        objectArchiveRuleInfo.setRuleApiName('1')
        objectArchiveRuleInfo.setTargetApiName("object2")
        objectArchiveRuleInfo.setSourceApiName("object1")

        when:
        describeLogicService.queryDisplayNameByApiNames(*_) >> ['object2':'对象2', 'object1':'对象1']
        describeLogicService.findObjectsWithoutCopy(*_) >> ['object1': new ObjectDescribe(['display_name':'对象1']), 'object2': new ObjectDescribe(['display_name':'对象2'])]
        objectArchiveRuleService.getObjectArchiveRuleByRuleApiName(*_) >> Lists.newArrayList(objectArchiveRuleInfo)
        objectArchiveService.enable(User.systemUser('1'), '1')
        then:
        1 * objectArchiveRuleService.validRule(*_)
    }

    def 'test list'() {
        when:
        def objectArchiveRuleInfo = new ObjectArchiveRuleInfo()
        objectArchiveRuleInfo.setRuleApiName('1')
        objectArchiveRuleInfo.setTargetApiName("object2")
        objectArchiveRuleInfo.setSourceApiName("object1")
        objectArchiveRuleService.getObjectArchiveRule(*_) >> Lists.newArrayList(objectArchiveRuleInfo)

        describeLogicService.queryDisplayNameByApiNames(*_) >> ['object2':'对象2', 'object1':'对象1']
        then:
        def list = objectArchiveService.list(User.systemUser('1'), 'object1', 'object2')
        list.size() == 1
        list[0].ruleApiName == '1'
        list[0].sourceDisplayName == '对象1'
        list[0].targetDisplayName == '对象2'
    }
}
