package com.facishare.paas.appframework.metadata.dto

import com.facishare.paas.metadata.impl.ObjectArchiveRuleInfo
import spock.lang.Specification

class ScheduleConfigDTOTest extends Specification {

    def "test convert2ScheduleConfig"() {
        given: "an instance of IObjectArchiveRuleInfo"
        def rule = new ObjectArchiveRuleInfo()
        def scheduleConfig = ["type"  : type, "executeTime": executeTime,
                              "weekly": weekly, "monthly": monthly]
        rule.setScheduleConfig(scheduleConfig)
        rule.set("time_zone", timeZone)

        when: "convert2ScheduleConfigDTO is called"
        ScheduleConfigDTO.convert2ScheduleConfig(rule)

        then: "the schedule config of the rule is updated"
        def scheduleConfigDocument = ScheduleConfigDocument.of(rule.getScheduleConfig())
        scheduleConfigDocument.type == type
        scheduleConfigDocument.getExecuteTime() == executeTimeString
        scheduleConfigDocument.onceExecuteTime == onceExecuteTime
        scheduleConfigDocument.weekly == weekly
        scheduleConfigDocument.monthly == monthly
        where:
        type      | executeTime | weekly    | monthly     | onceExecuteTime | timeZone        || executeTimeString
        "once"    | 0L          | []        | []          | 0L              | "UTC"           || null
        "weekly"  | 43200000L   | [1, 2, 3] | []          | 0L              | "UTC"           || "12:00:00"
        "monthly" | 86399000L   | []        | [1, 15, 30] | 0L              | "UTC"           || "23:59:59"
        "once"    | 1710819000L | []        | []          | 1710819000L     | "UTC"           || null
        "weekly"  | 43200000L   | [1, 2, 3] | []          | 0               | "Asia/Shanghai" || "20:00:00"
        "weekly"  | 43200000L   | [1, 2, 3] | []          | 0               | "Asia/Tokyo"    || "21:00:00"
        "weekly"  | 43200000   | [1, 2, 3] | []          | 0               | "Asia/Tokyo"    || "21:00:00"
    }
}
