# 详情页布局结构
```json
{
  "buttons": [],
  "components": [
    {
      "field_section": [],
      "buttons": [],
      "api_name": "relevant_team_component",
      "related_list_name": "",
      "header": "相关团队",
      "nameI18nKey": "paas.udobj.constant.relevant_team",
      "type": "user_list",
      "isSticky": false,
      "grayLimit": 1,
      "order": 2,
      "_id": "relevant_team_component"
    },
    {
      "field_section": [],
      "buttons": [
        {
          "action_type": "default",
          "api_name": "Edit_button_default",
          "label": "编辑"
        },
        {
          "action_type": "default",
          "api_name": "Dial_button_default",
          "label": "打电话"
        },
        {
          "action_type": "default",
          "api_name": "ChangeOwner_button_default",
          "label": "更换负责人"
        },
        {
          "action_type": "default",
          "api_name": "StartBPM_button_default",
          "label": "发起业务流程"
        },
        {
          "action_type": "default",
          "api_name": "Abolish_button_default",
          "label": "作废"
        },
        {
          "action_type": "default",
          "api_name": "StartStagePropellor_button_default",
          "label": "发起阶段推进器"
        },
        {
          "action_type": "default",
          "api_name": "Lock_button_default",
          "label": "锁定"
        },
        {
          "action_type": "default",
          "api_name": "Unlock_button_default",
          "label": "解锁"
        },
        {
          "action_type": "default",
          "api_name": "Clone_button_default",
          "label": "复制"
        },
        {
          "action_type": "default",
          "api_name": "ChangePartner_button_default",
          "label": "更换合作伙伴"
        },
        {
          "action_type": "default",
          "api_name": "ChangePartnerOwner_button_default",
          "label": "更换外部负责人"
        },
        {
          "action_type": "default",
          "api_name": "DeletePartner_button_default",
          "label": "移除合作伙伴"
        },
        {
          "action_type": "default",
          "api_name": "SendMail_button_default"
        },
        {
          "action_type": "default",
          "api_name": "Discuss_button_default"
        },
        {
          "action_type": "default",
          "api_name": "Remind_button_default"
        },
        {
          "action_type": "default",
          "api_name": "Schedule_button_default"
        },
        {
          "action_type": "default",
          "api_name": "Print_button_default"
        },
        {
          "action_type": "system",
          "api_name": "Unfollow_button_default",
          "label": "取消关注"
        },
        {
          "action_type": "system",
          "api_name": "Follow_button_default",
          "label": "关注"
        },
        {
          "tenant_id": "74255",
          "describe_api_name": "object_solitude__c",
          "is_active": true,
          "last_modified_time": 1702641543637,
          "create_time": 1702007105873,
          "action_type": "custom",
          "button_type": "common",
          "description": "",
          "param_form": [],
          "label": "业务按钮",
          "last_modified_by": "1000",
          "version": 2,
          "created_by": "1000",
          "jump_url": "",
          "is_deleted": false,
          "wheres": [],
          "api_name": "button_73c2C__c",
          "lock_data_show_button": false,
          "action": "button_73c2C__c",
          "define_type": "custom",
          "_id": "657291414b1edd0001005563",
          "use_pages": [
            "detail",
            "list",
            "list_batch"
          ],
          "redirect_type": "",
          "actions": [
            "657c3f86aad07f00015db96d"
          ]
        },
        {
          "action_type": "system",
          "api_name": "Transform_button_default",
          "action": "Transform",
          "label": "转换"
        },
        {
          "action_type": "default",
          "api_name": "EnterAccount_button_default",
          "action": "EnterAccount",
          "label": "入账"
        },
        {
          "action_type": "default",
          "api_name": "CancelEntry_button_default",
          "action": "CancelEntry",
          "label": "取消入账"
        },
        {
          "tenant_id": "74255",
          "describe_api_name": "object_solitude__c",
          "action_type": "custom",
          "button_type": "common",
          "description": "",
          "param_form": [
            {
              "is_required": false,
              "object_api_name": "object_solitude__c",
              "api_name": "form_field_wyOkp__c",
              "label": "单行文本",
              "type": "text"
            }
          ],
          "extend_info": "{\"enableValidateRule\":false}",
          "jump_url": "",
          "is_deleted": false,
          "wheres": [],
          "define_type": "custom",
          "action": "button_3vF87__c",
          "redirect_type": "",
          "last_modified_time": *************,
          "is_active": true,
          "create_time": *************,
          "label": "按钮名称(示例)",
          "last_modified_by": "1000",
          "created_by": "1000",
          "version": 1,
          "api_name": "button_3vF87__c",
          "lock_data_show_button": false,
          "_id": "67fcff44be99d700079ea49c",
          "use_pages": [
            "detail"
          ],
          "actions": [
            "67fcff44be99d700079ea49b"
          ]
        },
        {
          "tenant_id": "74255",
          "describe_api_name": "object_solitude__c",
          "last_modified_time": 1749110586435,
          "is_active": true,
          "create_time": 1747993277259,
          "action_type": "custom",
          "button_type": "redirect",
          "description": "",
          "param_form": [],
          "label": "跳转oneflow",
          "last_modified_by": "1000",
          "created_by": "1000",
          "version": 4,
          "jump_url": "",
          "is_deleted": false,
          "wheres": [],
          "api_name": "button_jump_to_oneflow__c",
          "lock_data_show_button": false,
          "define_type": "custom",
          "action": "button_jump_to_oneflow__c",
          "_id": "683042bdfa48c300070daac4",
          "use_pages": [
            "detail",
            "list",
            "related_list"
          ],
          "actions": [
            "68414f3999faae00076e45ff"
          ],
          "redirect_type": ""
        }
      ],
      "api_name": "head_info",
      "related_list_name": "",
      "header": "标题和按钮",
      "nameI18nKey": "paas.udobj.head_info",
      "exposedButton": 3,
      "type": "simple",
      "isSticky": false,
      "grayLimit": 1,
      "order": 3,
      "_id": "head_info"
    },
    {
      "field_section": [],
      "buttons": [],
      "api_name": "stage_component",
      "related_list_name": "",
      "header": "阶段推进器组件",
      "nameI18nKey": "paas.udobj.stage_component",
      "type": "stage_component",
      "isSticky": false,
      "grayLimit": 1,
      "order": 4,
      "_id": "stage_component"
    },
    {
      "field_section": [],
      "buttons": [],
      "api_name": "bpm_component",
      "related_list_name": "",
      "header": "业务流组件",
      "nameI18nKey": "paas.udobj.bpm_component",
      "type": "bpm_component",
      "isSticky": false,
      "grayLimit": 1,
      "order": 5,
      "_id": "bpm_component"
    },
    {
      "field_section": [
        {
          "field_name": "owner"
        },
        {
          "field_name": "owner_department"
        },
        {
          "field_name": "last_modified_time"
        },
        {
          "field_name": "record_type"
        },
        {
          "field_name": "out_owner"
        }
      ],
      "buttons": [],
      "api_name": "top_info",
      "related_list_name": "",
      "header": "摘要信息",
      "nameI18nKey": "paas.udobj.summary_info",
      "type": "top_info",
      "isSticky": false,
      "grayLimit": 1,
      "order": 6,
      "_id": "top_info"
    },
    {
      "components": [
        [
          "form_component"
        ],
        [
          "operation_log"
        ],
        [
          "BPM_related_list"
        ],
        [
          "Approval_related_list"
        ],
        [
          "object_dkOk1__c_field_m9v6f__c_related_list"
        ]
      ],
      "buttons": [],
      "api_name": "tabs_qF51p__c",
      "tabs": [
        {
          "api_name": "form_component_FY4Cv__c",
          "header": "详细信息",
          "nameI18nKey": "paas.udobj.detail_info"
        },
        {
          "api_name": "operation_log_2qbOJ__c",
          "header": "修改记录",
          "nameI18nKey": "paas.udobj.modify_log"
        },
        {
          "api_name": "BPM_related_list_R038y__c",
          "header": "业务流程",
          "nameI18nKey": "paas.udobj.process_list"
        },
        {
          "api_name": "Approval_related_list_3kOIp__c",
          "header": "审批流程",
          "nameI18nKey": "paas.udobj.approvalflow"
        },
        {
          "api_name": "tab_object_dkOk1__c_field_m9v6f__c_related_list",
          "header": "fj-显示字段不支持落地",
          "nameI18nKey": "object_dkOk1__c.field.field_m9v6f__c.reference_label"
        }
      ],
      "header": "页签容器",
      "type": "tabs",
      "isSticky": false,
      "order": 7,
      "_id": "tabs_qF51p__c"
    },
    {
      "field_section": [],
      "buttons": [],
      "api_name": "operation_log",
      "related_list_name": "",
      "header": "修改记录",
      "nameI18nKey": "paas.udobj.modify_log",
      "type": "related_record",
      "grayLimit": 1,
      "order": 9,
      "_id": "operation_log"
    },
    {
      "field_section": [],
      "buttons": [],
      "api_name": "approval_component",
      "related_list_name": "",
      "header": "审批流组件",
      "nameI18nKey": "paas.udobj.approval_component",
      "type": "approval_component",
      "isSticky": false,
      "grayLimit": 1,
      "order": 12,
      "_id": "approval_component"
    },
    {
      "field_section": [
        {
          "show_header": true,
          "form_fields": [
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "name"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "employee",
              "field_name": "owner"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "owner_department"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": true,
              "render_type": "record_type",
              "field_name": "record_type"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": true,
              "is_tiled": false,
              "render_type": "select_one",
              "field_name": "life_status"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "department",
              "field_name": "data_own_department"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "currency",
              "field_name": "field_G6oh5__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "rich_text",
              "field_name": "field_18Oqa__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "html_rich_text",
              "field_name": "field_3BC04__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "field_wyOkp__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "percentile",
              "field_name": "field_qcs89__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "percentile",
              "field_name": "field_59M16__c"
            },
            {
              "is_readonly": true,
              "full_line": false,
              "is_required": false,
              "render_type": "auto_number",
              "field_name": "field_2651X__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "partner_id"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "is_tiled": true,
              "render_type": "select_one",
              "field_name": "out_resources"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "field_Ujm7y__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "object_reference_many",
              "field_name": "field_ikI1Y__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "department",
              "field_name": "field_31Ihv__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "field_7HA6y__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "field_rFID2__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "is_tiled": true,
              "render_type": "select_many",
              "field_name": "field_9iAex__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "field_49vCl__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "image",
              "field_name": "field_image__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "employee",
              "field_name": "field_wqYJh__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "field_Wewf6__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "field_ciz1J__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9yl__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y2__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y3__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y4__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y5__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y6__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y7__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y8__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y9__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y1__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y11__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_OgZzz__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "count",
              "field_name": "field_Knze1__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y10__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "quote",
              "field_name": "field_5X73n__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "count",
              "field_name": "field_R2Qrv__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "quote",
              "field_name": "field_fWhg7__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "quote",
              "field_name": "reference_test_02__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "quote",
              "field_name": "reference_test_03__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "is_tiled": true,
              "render_type": "select_one",
              "field_name": "task_status__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "number",
              "field_name": "product_price__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "file_attachment",
              "field_name": "attachment__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "html_rich_text",
              "field_name": "rich_text__c"
            }
          ],
          "api_name": "base_field_section__c",
          "tab_index": "ltr",
          "column": 2,
          "header": "基本信息",
          "collapse": false
        },
        {
          "show_header": true,
          "form_fields": [
            {
              "is_readonly": true,
              "full_line": false,
              "is_required": false,
              "render_type": "employee",
              "field_name": "created_by"
            },
            {
              "is_readonly": true,
              "full_line": false,
              "is_required": false,
              "render_type": "date_time",
              "field_name": "create_time"
            },
            {
              "is_readonly": true,
              "full_line": false,
              "is_required": false,
              "render_type": "employee",
              "field_name": "last_modified_by"
            },
            {
              "is_readonly": true,
              "full_line": false,
              "is_required": false,
              "render_type": "date_time",
              "field_name": "last_modified_time"
            }
          ],
          "api_name": "group_e0Mj4__c",
          "tab_index": "ltr",
          "column": 2,
          "header": "系统信息",
          "collapse": true
        },
        {
          "show_header": true,
          "form_fields": [
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "country",
              "field_name": "field_92g6R__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "province",
              "field_name": "field_xG9wz__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "city",
              "field_name": "field_H1nS1__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "district",
              "field_name": "field_ASafr__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "town",
              "field_name": "field_BEIB3__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "village",
              "field_name": "field_94wZm__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "field_3N3pp__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "location",
              "field_name": "field_46w37__c"
            }
          ],
          "api_name": "field_2Sw1T__c",
          "tab_index": "ltr",
          "column": 2,
          "header": "地区定位",
          "is_show": true
        },
        {
          "show_header": true,
          "form_fields": [
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "country",
              "field_name": "field_51rg8__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "province",
              "field_name": "field_qNeNn__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "city",
              "field_name": "field_248aY__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "district",
              "field_name": "field_OySlA__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "town",
              "field_name": "field_F9cmW__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "village",
              "field_name": "field_u95Pt__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "field_cflJ8__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "location",
              "field_name": "field_W8z6n__c"
            }
          ],
          "api_name": "field_y6l7V__c",
          "tab_index": "ltr",
          "column": 2,
          "header": "地区定位1",
          "is_show": true
        },
        {
          "show_header": true,
          "form_fields": [
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "country",
              "field_name": "field_1Da4y__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "province",
              "field_name": "field_F16In__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "city",
              "field_name": "field_6dv1C__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "district",
              "field_name": "field_Q0hM9__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "field_Z0Ex5__c"
            },
            {
              "is_readonly": false,
              "full_line": false,
              "is_required": false,
              "render_type": "location",
              "field_name": "field_4H0am__c"
            }
          ],
          "api_name": "field_9M7oZ__c",
          "tab_index": "ltr",
          "column": 2,
          "header": "地区定位2",
          "is_show": true
        }
      ],
      "buttons": [],
      "related_list_name": "",
      "column": 2,
      "nameI18nKey": "paas.udobj.detail_info",
      "type": "form",
      "isSticky": false,
      "reference_field_config": "",
      "api_name": "form_component",
      "header": "详细信息",
      "_id": "form_component",
      "isDisabled": false,
      "grayLimit": 1,
      "order": 8,
      "i18nInfoList": [
        {
          "apiName": "base_field_section__c",
          "customKey": "layout.object_solitude__c.detail.layout_wh2R2__c.group.base_field_section__c.header",
          "defaultValue": "基本信息",
          "languageInfo": {
            "zh-CN": "基本信息"
          },
          "preKey": "paas.metadata.layout.base_info",
          "value": "基本信息"
        },
        {
          "apiName": "group_e0Mj4__c",
          "customKey": "layout.object_solitude__c.detail.layout_wh2R2__c.group.group_e0Mj4__c.header",
          "defaultValue": "系统信息",
          "languageInfo": {
            "zh-CN": "系统信息"
          },
          "value": "系统信息"
        },
        {
          "apiName": "field_2Sw1T__c",
          "customKey": "layout.object_solitude__c.detail.layout_wh2R2__c.group.field_2Sw1T__c.header",
          "defaultValue": "地区定位",
          "languageInfo": {
            "zh-CN": "地区定位"
          },
          "value": "地区定位"
        },
        {
          "apiName": "field_y6l7V__c",
          "customKey": "layout.object_solitude__c.detail.layout_wh2R2__c.group.field_y6l7V__c.header",
          "defaultValue": "地区定位1",
          "languageInfo": {
            "zh-CN": "地区定位1"
          },
          "value": "地区定位1"
        },
        {
          "apiName": "field_9M7oZ__c",
          "customKey": "layout.object_solitude__c.detail.layout_wh2R2__c.group.field_9M7oZ__c.header",
          "defaultValue": "地区定位2",
          "languageInfo": {
            "zh-CN": "地区定位2"
          },
          "value": "地区定位2"
        }
      ]
    },
    {
      "type": "relatedlist",
      "buttons": [],
      "relationType": 2,
      "api_name": "object_dkOk1__c_field_m9v6f__c_related_list",
      "header": "fj-显示字段不支持落地",
      "ref_object_api_name": "object_dkOk1__c",
      "related_list_name": "target_related_list_4T3mr__c",
      "field_api_name": "field_m9v6f__c",
      "nameI18nKey": "object_dkOk1__c.field.field_m9v6f__c.reference_label",
      "limit": 1,
      "original_describe_api_name": "udobj",
      "_id": "object_dkOk1__c_field_m9v6f__c_related_list"
    },
    {
      "type": "relatedlist",
      "buttons": [],
      "define_type": "general",
      "api_name": "BPM_related_list",
      "header": "业务流程",
      "nameI18nKey": "paas.udobj.process_list",
      "ref_object_api_name": "BPM",
      "related_list_name": "",
      "limit": 1,
      "field_section": [],
      "grayLimit": 1,
      "order": 10,
      "_id": "BPM_related_list"
    },
    {
      "type": "relatedlist",
      "buttons": [],
      "define_type": "general",
      "api_name": "Approval_related_list",
      "header": "审批流程",
      "nameI18nKey": "paas.udobj.approvalflow",
      "ref_object_api_name": "Approval",
      "related_list_name": "",
      "limit": 1,
      "field_section": [],
      "grayLimit": 1,
      "order": 11,
      "_id": "Approval_related_list"
    }
  ],
  "last_modified_time": 1749455715110,
  "is_deleted": false,
  "version": 204,
  "create_time": 1692776935068,
  "_id": "64e5b9e7c5f90a0001880fc0",
  "agent_type": null,
  "is_show_fieldname": null,
  "layout_description": "",
  "api_name": "layout_wh2R2__c",
  "i18nInfoList": [
    {
      "apiName": "display_name",
      "languageInfo": {
        "de": "Standardlayout",
        "pt": "disposição padrão",
        "sw-TZ": "Layout Mpya",
        "zh-TW": "預設布局",
        "ko-KR": "기본 배치",
        "pt-BR": "disposição padrão",
        "en": "Default Layout",
        "es-ES": "disposición predeterminada",
        "kk-KZ": "Стандартті түсінік",
        "zh-CN": "默认布局",
        "ur-PK": "میںزیکی طور پر ترتیب دیا جاتا ہے",
        "it-IT": "Disposizione predefinita",
        "ar": "تخطيط افتراضي",
        "pl-PL": "Domyślny układ",
        "ru-RU": "Стандартный макет",
        "nl-NL": "Standaard layout",
        "id-ID": "Layout Default",
        "tr-TR": "Varsayılan düzen",
        "fr-FR": "Disposition par défaut",
        "vi-VN": "Giao diện mặc định",
        "ja-JP": "デフォルトのレイアウト",
        "th-TH": "การตั้งค่าเริ่มต้น"
      }
    }
  ],
  "what_api_name": null,
  "default_component": "form_component",
  "layout_structure": {
    "layout": [
      {
        "components": [
          [
            "head_info"
          ]
        ],
        "columns": [
          {
            "width": "100%"
          }
        ]
      },
      {
        "components": [
          [
            "stage_component",
            "bpm_component",
            "top_info",
            "tabs_qF51p__c"
          ],
          [
            "approval_component",
            "relevant_team_component"
          ]
        ],
        "columns": [
          {
            "width": "auto"
          },
          {
            "width": "500px",
            "retractable": true
          }
        ]
      }
    ]
  },
  "created_by": "1000",
  "display_name": "默认布局",
  "is_default": true,
  "last_modified_by": "1000",
  "layout_type": "detail",
  "package": "CRM",
  "ref_object_api_name": "object_solitude__c",
  "tenant_id": "74255",
  "ui_event_ids": [],
  "hidden_buttons": [
    "SaleRecord_button_default"
  ],
  "hidden_components": [
    "payment_recordrelated_list_generate_by_UDObjectServer__c",
    "component_4fEPz__c",
    "component_SDWSM__c",
    "component_pJEzE__c",
    "test_mobile__c",
    "sale_log",
    "related_what_list",
    "biDashboardCom",
    "model_prediction"
  ],
  "namespace": null,
  "mobile_layout": {
    "components": [
      {
        "field_section": [],
        "buttons": [],
        "api_name": "relevant_team_component",
        "related_list_name": "",
        "is_hidden": false,
        "header": "相关团队",
        "nameI18nKey": "paas.udobj.constant.relevant_team",
        "type": "user_list",
        "isSticky": false,
        "grayLimit": 1,
        "order": 1
      },
      {
        "field_section": [],
        "buttons": [],
        "api_name": "top_info",
        "related_list_name": "",
        "is_hidden": false,
        "header": "摘要信息",
        "nameI18nKey": "paas.udobj.summary_info",
        "type": "top_info",
        "isSticky": false,
        "grayLimit": 1,
        "order": 2
      },
      {
        "field_section": [],
        "buttons": [],
        "related_list_name": "",
        "i18nInfoList": [],
        "column": 2,
        "is_hidden": false,
        "nameI18nKey": "paas.udobj.detail_info",
        "type": "form",
        "isSticky": false,
        "reference_field_config": "edit_layout_wh2R2__c",
        "is_show_header": true,
        "api_name": "form_component",
        "header": "详细信息",
        "isDisabled": true,
        "grayLimit": 1,
        "order": 3
      },
      {
        "field_section": [],
        "buttons": [],
        "api_name": "operation_log",
        "related_list_name": "",
        "is_hidden": false,
        "header": "修改记录",
        "nameI18nKey": "paas.udobj.modify_log",
        "type": "related_record",
        "grayLimit": 1,
        "order": 4
      },
      {
        "field_section": [],
        "buttons": [],
        "api_name": "approval_component",
        "related_list_name": "",
        "header": "审批流组件",
        "type": "approval_component",
        "grayLimit": 1
      },
      {
        "field_section": [],
        "buttons": [],
        "api_name": "stage_component",
        "related_list_name": "",
        "header": "阶段推进器组件",
        "type": "stage_component",
        "grayLimit": 1
      },
      {
        "field_section": [],
        "buttons": [],
        "api_name": "bpm_component",
        "related_list_name": "",
        "header": "业务流组件",
        "type": "bpm_component",
        "grayLimit": 1
      },
      {
        "components": [
          "form_component",
          "operation_log",
          "BPM_related_list",
          "Approval_related_list",
          "object_dkOk1__c_field_m9v6f__c_related_list"
        ],
        "navigation": [],
        "buttons": [],
        "is_show_icon": true,
        "api_name": "navigation",
        "header": "navigation",
        "type": "navigation"
      },
      {
        "field_section": [],
        "buttons": [],
        "api_name": "name_component",
        "related_list_name": "",
        "name": "",
        "is_hidden": false,
        "header": "标题",
        "type": "name_component",
        "grayLimit": 1,
        "order": 8
      },
      {
        "type": "relatedlist",
        "buttons": [],
        "relationType": 2,
        "api_name": "object_dkOk1__c_field_m9v6f__c_related_list",
        "header": "fj-显示字段不支持落地",
        "ref_object_api_name": "object_dkOk1__c",
        "related_list_name": "target_related_list_4T3mr__c",
        "field_api_name": "field_m9v6f__c",
        "nameI18nKey": "object_dkOk1__c.field.field_m9v6f__c.reference_label",
        "limit": 1,
        "original_describe_api_name": "udobj",
        "is_hidden": false,
        "order": 7
      },
      {
        "type": "relatedlist",
        "buttons": [],
        "define_type": "general",
        "api_name": "BPM_related_list",
        "header": "业务流程",
        "nameI18nKey": "paas.udobj.process_list",
        "ref_object_api_name": "BPM",
        "related_list_name": "",
        "limit": 1,
        "field_section": [],
        "is_hidden": false,
        "grayLimit": 1,
        "order": 5
      },
      {
        "type": "relatedlist",
        "buttons": [],
        "define_type": "general",
        "api_name": "Approval_related_list",
        "header": "审批流程",
        "nameI18nKey": "paas.udobj.approvalflow",
        "ref_object_api_name": "Approval",
        "related_list_name": "",
        "limit": 1,
        "field_section": [],
        "is_hidden": false,
        "grayLimit": 1,
        "order": 6
      }
    ],
    "buttons": [
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "Edit_button_default",
        "label": "编辑"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "Dial_button_default",
        "label": "打电话"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "ChangeOwner_button_default",
        "label": "更换负责人"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "StartBPM_button_default",
        "label": "发起业务流程"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "Abolish_button_default",
        "label": "作废"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "StartStagePropellor_button_default",
        "label": "发起阶段推进器"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "Lock_button_default",
        "label": "锁定"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "Unlock_button_default",
        "label": "解锁"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "Clone_button_default",
        "label": "复制"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "ChangePartner_button_default",
        "label": "更换合作伙伴"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "ChangePartnerOwner_button_default",
        "label": "更换外部负责人"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "DeletePartner_button_default",
        "label": "移除合作伙伴"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "SendMail_button_default"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "Discuss_button_default"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "Remind_button_default"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "Schedule_button_default"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "Print_button_default"
      },
      {
        "display_mode": "icon",
        "action_type": "system",
        "api_name": "Unfollow_button_default",
        "label": "取消关注"
      },
      {
        "display_mode": "icon",
        "action_type": "system",
        "api_name": "Follow_button_default",
        "label": "关注"
      },
      {
        "tenant_id": "74255",
        "describe_api_name": "object_solitude__c",
        "display_mode": "icon",
        "action_type": "custom",
        "button_type": "common",
        "description": "",
        "param_form": [],
        "jump_url": "",
        "is_deleted": false,
        "wheres": [],
        "action": "button_73c2C__c",
        "define_type": "custom",
        "redirect_type": "",
        "is_active": true,
        "last_modified_time": 1702641543637,
        "create_time": 1702007105873,
        "label": "业务按钮",
        "last_modified_by": "1000",
        "version": 2,
        "created_by": "1000",
        "api_name": "button_73c2C__c",
        "lock_data_show_button": false,
        "_id": "657291414b1edd0001005563",
        "use_pages": [
          "detail",
          "list",
          "list_batch"
        ],
        "actions": [
          "657c3f86aad07f00015db96d"
        ]
      },
      {
        "display_mode": "icon",
        "action_type": "system",
        "api_name": "Transform_button_default",
        "action": "Transform",
        "label": "转换"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "EnterAccount_button_default",
        "action": "EnterAccount",
        "label": "入账"
      },
      {
        "display_mode": "icon",
        "action_type": "default",
        "api_name": "CancelEntry_button_default",
        "action": "CancelEntry",
        "label": "取消入账"
      },
      {
        "tenant_id": "74255",
        "describe_api_name": "object_solitude__c",
        "action_type": "custom",
        "button_type": "common",
        "description": "",
        "param_form": [
          {
            "is_required": false,
            "object_api_name": "object_solitude__c",
            "api_name": "form_field_wyOkp__c",
            "label": "单行文本",
            "type": "text"
          }
        ],
        "extend_info": "{\"enableValidateRule\":false}",
        "jump_url": "",
        "is_deleted": false,
        "wheres": [],
        "define_type": "custom",
        "action": "button_3vF87__c",
        "redirect_type": "",
        "last_modified_time": *************,
        "is_active": true,
        "create_time": *************,
        "label": "按钮名称(示例)",
        "last_modified_by": "1000",
        "created_by": "1000",
        "version": 1,
        "api_name": "button_3vF87__c",
        "lock_data_show_button": false,
        "_id": "67fcff44be99d700079ea49c",
        "use_pages": [
          "detail"
        ],
        "actions": [
          "67fcff44be99d700079ea49b"
        ]
      },
      {
        "tenant_id": "74255",
        "describe_api_name": "object_solitude__c",
        "last_modified_time": 1749110586435,
        "is_active": true,
        "create_time": 1747993277259,
        "action_type": "custom",
        "button_type": "redirect",
        "description": "",
        "param_form": [],
        "label": "跳转oneflow",
        "last_modified_by": "1000",
        "created_by": "1000",
        "version": 4,
        "jump_url": "",
        "is_deleted": false,
        "wheres": [],
        "api_name": "button_jump_to_oneflow__c",
        "lock_data_show_button": false,
        "define_type": "custom",
        "action": "button_jump_to_oneflow__c",
        "_id": "683042bdfa48c300070daac4",
        "use_pages": [
          "detail",
          "list",
          "related_list"
        ],
        "actions": [
          "68414f3999faae00076e45ff"
        ],
        "redirect_type": ""
      }
    ],
    "button_style": {
      "edit_button_location": "top",
      "display_mode": "icon",
      "highlight_btn": [],
      "exposed_num": 3,
      "button_align": "left"
    },
    "general_info": [],
    "hidden_buttons": [
      "SaleRecord_button_default"
    ],
    "hidden_components": [
      "payment_recordrelated_list_generate_by_UDObjectServer__c",
      "component_4fEPz__c",
      "component_NZkap__c",
      "component_W4WjX__c",
      "component_pJEzE__c",
      "sale_log",
      "biDashboardCom",
      "model_prediction",
      "suspendedComponent"
    ],
    "suspendedComponent": [
      {
        "field_section": [],
        "buttons": [],
        "api_name": "suspendedComponent",
        "related_list_name": "",
        "i18nInfoList": [
          {
            "apiName": "name_hf0OqK5u2",
            "value": "落地-全部",
            "customKey": "bizComponent.object_solitude__c.layout_wh2R2__c.suspendedComponent.name_hf0OqK5u2"
          },
          {
            "apiName": "name_3w1x6DCtQ",
            "defaultValue": "落地-删除",
            "value": "落地-删除",
            "customKey": "bizComponent.object_solitude__c.layout_wh2R2__c.suspendedComponent.name_3w1x6DCtQ",
            "languageInfo": {
              "zh-TW": "落地-刪除",
              "en": "Landing - Delete",
              "zh-CN": "落地-删除"
            }
          },
          {
            "apiName": "name_Q6CoQj11s",
            "value": "落地-无权限",
            "customKey": "bizComponent.object_solitude__c.layout_wh2R2__c.suspendedComponent.name_Q6CoQj11s",
            "languageInfo": {
              "it-IT": "Atterramento - Nessun permesso",
              "de": "Landing - Keine Berechtigung",
              "ru-RU": "Лanding - нет прав",
              "pt": "Pousada - Sem Permissão",
              "zh-TW": "落地-無權限",
              "ko-KR": "지상-권한이 없습니다",
              "en": "Landing - No Permission",
              "es-ES": "Aterrizaje - Sin permisos",
              "fr-FR": "État de sollicitation - Aucune autorisation",
              "zh-CN": "落地-无权限",
              "ja-JP": "降落 - 無制限"
            }
          },
          {
            "apiName": "name_cYn17TeX7",
            "value": "打开百度",
            "customKey": "bizComponent.object_solitude__c.layout_wh2R2__c.suspendedComponent.name_cYn17TeX7"
          }
        ],
        "i18nProps": {
          "actions": [
            {
              "name": "name_hf0OqK5u2"
            },
            {},
            {
              "name": "name_cYn17TeX7"
            },
            {
              "name": "name_3w1x6DCtQ"
            },
            {
              "name": "name_Q6CoQj11s"
            }
          ]
        },
        "header": "悬浮组件",
        "nameI18nKey": "UIPaaS.suspensionWidget.name",
        "type": "suspendedComponent",
        "actions": [
          {
            "related_field_api_name": "field_m9v6f__c",
            "name": "落地-全部",
            "related_object_target_related_list_name": "target_related_list_4T3mr__c",
            "related_object_api_name": "object_dkOk1__c",
            "type": "new_object",
            "related_object_record_type": ""
          },
          {
            "type": "back_to_top"
          },
          {
            "name": "打开百度",
            "type": "url",
            "url": "https://www.baidu.com?name=${name}"
          },
          {
            "related_field_api_name": "field_m9v6f__c",
            "name": "落地-删除",
            "related_object_target_related_list_name": "target_related_list_4T3mr__c",
            "related_object_api_name": "object_dkOk1__c",
            "type": "new_object",
            "related_object_record_type": "record_YNnpx__c"
          },
          {
            "related_field_api_name": "field_m9v6f__c",
            "name": "落地-无权限",
            "related_object_target_related_list_name": "target_related_list_4T3mr__c",
            "related_object_api_name": "object_dkOk1__c",
            "type": "new_object",
            "related_object_record_type": "record_IT6Zr__c"
          }
        ],
        "grayLimit": 1
      }
    ],
    "layout_structure": {
      "layout": [
        {
          "components": [
            [
              "name_component",
              "top_info",
              "relevant_team_component",
              "approval_component",
              "stage_component",
              "bpm_component",
              "navigation"
            ]
          ],
          "columns": [
            {
              "width": "100%"
            }
          ]
        }
      ],
      "is_tile_help_text": false
    }
  },
  "enable_mobile_layout": true,
  "enable_sidebar_layout": null,
  "events": []
}
```
# 新建编辑页布局描述
```json

{
  "buttons": [],
  "components": [
    {
      "show_header": false,
      "field_section": [],
      "buttons": [],
      "related_list_name": "",
      "show_border": true,
      "show_field_background_color": false,
      "rows": [
        {
          "cells": [
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "doux9h",
              "col_span": 1
            },
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "b84fnp",
              "col_span": 1
            },
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "zawnbm",
              "col_span": 1
            },
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "g7sclz",
              "col_span": 1
            }
          ],
          "_id": "6g51ha"
        },
        {
          "cells": [
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "ech9qs",
              "col_span": 1
            },
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "qu1oq8",
              "col_span": 1
            },
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "agl6hk",
              "col_span": 1
            },
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "o5ttro",
              "col_span": 1
            }
          ],
          "_id": "zq5wxw"
        },
        {
          "cells": [
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "ieu33b",
              "col_span": 1
            },
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "56t84c",
              "col_span": 1
            },
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "o7x86x",
              "col_span": 1
            },
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "zhksuw",
              "col_span": 1
            }
          ],
          "_id": "3uox3v"
        },
        {
          "cells": [
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "rzl0xc",
              "col_span": 1
            },
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "41r7g0",
              "col_span": 1
            },
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "beg9nl",
              "col_span": 1
            },
            {
              "components": [],
              "row_span": 1,
              "style": {},
              "_id": "53b4ay",
              "col_span": 1
            }
          ],
          "_id": "iwcrb9"
        }
      ],
      "type": "form_table",
      "isSticky": false,
      "col_count": 4,
      "row_count": 4,
      "api_name": "form_table_REAPINAME_17113640512733492",
      "header": "表格组件",
      "field_background_color": "",
      "grayLimit": 1,
      "order": 4
    },
    {
      "field_section": [
        {
          "show_header": true,
          "form_fields": [
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "name"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "employee",
              "field_name": "owner"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "owner_department"
            },
            {
              "is_readonly": false,
              "is_required": true,
              "render_type": "record_type",
              "field_name": "record_type"
            },
            {
              "is_readonly": false,
              "is_required": true,
              "is_tiled": false,
              "render_type": "select_one",
              "field_name": "life_status"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "department",
              "field_name": "data_own_department"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "currency",
              "field_name": "field_G6oh5__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "rich_text",
              "field_name": "field_18Oqa__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "html_rich_text",
              "field_name": "field_3BC04__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "field_wyOkp__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "percentile",
              "field_name": "field_qcs89__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "percentile",
              "field_name": "field_59M16__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "auto_number",
              "field_name": "field_2651X__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "partner_id"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "is_tiled": true,
              "render_type": "select_one",
              "field_name": "out_resources"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "field_Ujm7y__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "object_reference_many",
              "field_name": "field_ikI1Y__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "department",
              "field_name": "field_31Ihv__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "field_7HA6y__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "field_rFID2__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "is_tiled": true,
              "render_type": "select_many",
              "field_name": "field_9iAex__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "field_49vCl__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "image",
              "field_name": "field_image__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "employee",
              "field_name": "field_wqYJh__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "object_reference",
              "field_name": "field_Wewf6__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "field_ciz1J__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9yl__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y2__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y3__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y4__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y5__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y6__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y7__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y8__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y9__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y1__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y11__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_OgZzz__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "count",
              "field_name": "field_Knze1__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "formula",
              "field_name": "field_uP9y10__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "quote",
              "field_name": "field_5X73n__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "count",
              "field_name": "field_R2Qrv__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "quote",
              "field_name": "field_fWhg7__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "quote",
              "field_name": "reference_test_02__c"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "quote",
              "field_name": "reference_test_03__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "is_tiled": true,
              "render_type": "select_one",
              "field_name": "task_status__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "number",
              "field_name": "product_price__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "file_attachment",
              "field_name": "attachment__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "html_rich_text",
              "field_name": "rich_text__c"
            }
          ],
          "api_name": "base_field_section__c",
          "tab_index": "ltr",
          "column": 2,
          "header": "基本信息",
          "collapse": false
        },
        {
          "show_header": true,
          "form_fields": [
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "employee",
              "field_name": "created_by"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "date_time",
              "field_name": "create_time"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "employee",
              "field_name": "last_modified_by"
            },
            {
              "is_readonly": true,
              "is_required": false,
              "render_type": "date_time",
              "field_name": "last_modified_time"
            }
          ],
          "api_name": "group_e0Mj4__c",
          "tab_index": "ltr",
          "column": 2,
          "header": "系统信息",
          "collapse": true
        },
        {
          "show_header": true,
          "form_fields": [
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "country",
              "field_name": "field_92g6R__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "province",
              "field_name": "field_xG9wz__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "city",
              "field_name": "field_H1nS1__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "district",
              "field_name": "field_ASafr__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "town",
              "field_name": "field_BEIB3__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "village",
              "field_name": "field_94wZm__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "field_3N3pp__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "location",
              "field_name": "field_46w37__c"
            }
          ],
          "api_name": "field_2Sw1T__c",
          "tab_index": "ltr",
          "column": 2,
          "header": "地区定位",
          "is_show": true
        },
        {
          "show_header": true,
          "form_fields": [
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "country",
              "field_name": "field_51rg8__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "province",
              "field_name": "field_qNeNn__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "city",
              "field_name": "field_248aY__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "district",
              "field_name": "field_OySlA__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "town",
              "field_name": "field_F9cmW__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "village",
              "field_name": "field_u95Pt__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "field_cflJ8__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "location",
              "field_name": "field_W8z6n__c"
            }
          ],
          "api_name": "field_y6l7V__c",
          "tab_index": "ltr",
          "column": 2,
          "header": "地区定位1",
          "is_show": true
        },
        {
          "show_header": true,
          "form_fields": [
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "country",
              "field_name": "field_1Da4y__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "province",
              "field_name": "field_F16In__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "city",
              "field_name": "field_6dv1C__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "district",
              "field_name": "field_Q0hM9__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "text",
              "field_name": "field_Z0Ex5__c"
            },
            {
              "is_readonly": false,
              "is_required": false,
              "render_type": "location",
              "field_name": "field_4H0am__c"
            }
          ],
          "api_name": "field_9M7oZ__c",
          "tab_index": "ltr",
          "column": 2,
          "header": "地区定位2",
          "is_show": true
        }
      ],
      "buttons": [],
      "api_name": "form_component",
      "related_list_name": "",
      "column": 2,
      "header": "表单组件",
      "nameI18nKey": "paas.udobj.form_component",
      "type": "form",
      "isSticky": false,
      "grayLimit": 1,
      "order": 6,
      "i18nInfoList": [
        {
          "apiName": "base_field_section__c",
          "customKey": "layout.object_solitude__c.edit.edit_layout_h5426__c.group.base_field_section__c.header",
          "defaultValue": "基本信息",
          "languageInfo": {
            "zh-CN": "基本信息"
          },
          "preKey": "paas.metadata.layout.base_info",
          "value": "基本信息"
        },
        {
          "apiName": "group_e0Mj4__c",
          "customKey": "layout.object_solitude__c.edit.edit_layout_h5426__c.group.group_e0Mj4__c.header",
          "defaultValue": "系统信息",
          "languageInfo": {
            "zh-CN": "系统信息"
          },
          "value": "系统信息"
        },
        {
          "apiName": "field_2Sw1T__c",
          "customKey": "layout.object_solitude__c.edit.edit_layout_h5426__c.group.field_2Sw1T__c.header",
          "defaultValue": "地区定位",
          "languageInfo": {
            "zh-CN": "地区定位"
          },
          "value": "地区定位"
        },
        {
          "apiName": "field_y6l7V__c",
          "customKey": "layout.object_solitude__c.edit.edit_layout_h5426__c.group.field_y6l7V__c.header",
          "defaultValue": "地区定位1",
          "languageInfo": {
            "zh-CN": "地区定位1"
          },
          "value": "地区定位1"
        },
        {
          "apiName": "field_9M7oZ__c",
          "customKey": "layout.object_solitude__c.edit.edit_layout_h5426__c.group.field_9M7oZ__c.header",
          "defaultValue": "地区定位2",
          "languageInfo": {
            "zh-CN": "地区定位2"
          },
          "value": "地区定位2"
        }
      ]
    },
    {
      "field_section": [],
      "buttons": [],
      "api_name": "head_info",
      "related_list_name": "",
      "button_info": [
        {
          "hidden": [],
          "page_type": "create",
          "render_type": "normal",
          "order": [
            "Add_Save_button_default",
            "Add_Save_Continue_button_default",
            "Add_Save_Draft_button_default",
            "button_new_edit_ui_button_flow__c"
          ]
        },
        {
          "hidden": [],
          "page_type": "edit",
          "render_type": "normal",
          "order": [
            "Edit_Save_button_default",
            "Edit_Save_Draft_button_default",
            "button_demo_example__c",
            "button_new_edit_ui_button_flow__c"
          ]
        }
      ],
      "header": "标题和按钮",
      "nameI18nKey": "paas.udobj.head_info",
      "type": "simple",
      "isSticky": false,
      "grayLimit": 1,
      "order": 3
    },
    {
      "field_section": [],
      "buttons": [],
      "api_name": "related_list_form_REAPINAME_17212084709611625",
      "related_list_name": "",
      "button_info": [
        {
          "hidden": [],
          "render_type": "list_normal",
          "order": [
            "Single_Add_button_default"
          ]
        }
      ],
      "ref_object_api_name": "object_dkOk1__c",
      "header": "fj-显示字段不支持落地",
      "type": "related_list_form",
      "isSticky": false,
      "grayLimit": 4,
      "field_api_name": "field_m9v6f__c",
      "order": 5
    }
  ],
  "last_modified_time": 1749455715085,
  "is_deleted": false,
  "version": 107,
  "create_time": 1721208453301,
  "_id": "66978e85d0f2b60006628456",
  "agent_type": null,
  "is_show_fieldname": null,
  "layout_description": "",
  "api_name": "edit_layout_h5426__c",
  "i18nInfoList": [
    {
      "apiName": "display_name",
      "languageInfo": {
        "zh-CN": "新建自定义布局(示例)(新建/编辑页)"
      }
    }
  ],
  "what_api_name": null,
  "default_component": "form_component",
  "layout_structure": {
    "layout": [
      {
        "components": [
          [
            "head_info"
          ]
        ],
        "columns": [
          {
            "width": "100%"
          }
        ]
      },
      {
        "components": [
          [
            "form_table_REAPINAME_17113640512733492",
            "related_list_form_REAPINAME_17212084709611625",
            "form_component"
          ]
        ],
        "columns": [
          {
            "width": "100%"
          }
        ]
      }
    ]
  },
  "created_by": "1000",
  "display_name": "新建自定义布局(示例)(新建/编辑页)",
  "is_default": false,
  "last_modified_by": "1000",
  "layout_type": "edit",
  "package": "CRM",
  "ref_object_api_name": "object_solitude__c",
  "tenant_id": "74255",
  "ui_event_ids": [],
  "hidden_buttons": [
    "SaleRecord_button_default"
  ],
  "hidden_components": [
    "richTextWidget",
    "sale_log"
  ],
  "namespace": null,
  "enable_mobile_layout": false,
  "enable_sidebar_layout": null,
  "events": []
}
```